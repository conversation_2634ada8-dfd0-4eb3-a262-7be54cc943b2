<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.5</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <name>oms-service</name>
    <description>oms-service</description>

    <groupId>com.lenskart</groupId>
    <artifactId>oms-service</artifactId>
    <version>1.1.8</version>

    <properties>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <java.version>1.8</java.version>
        <sonar.qualitygate.wait>false</sonar.qualitygate.wait>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-consul-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-consul-config</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test-autoconfigure</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>net.javacrumbs.shedlock</groupId>
            <artifactId>shedlock-spring</artifactId>
            <version>2.2.0</version>
        </dependency>

        <dependency>
            <groupId>net.javacrumbs.shedlock</groupId>
            <artifactId>shedlock-provider-jdbc-template</artifactId>
            <version>2.1.0</version>
        </dependency>

        <dependency>
            <groupId>com.sendgrid</groupId>
            <artifactId>sendgrid-java</artifactId>
            <version>2.2.2</version>
        </dependency>

        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-ui</artifactId>
            <version>1.7.0</version>
        </dependency>

        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.junit.vintage</groupId>
            <artifactId>junit-vintage-engine</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.hamcrest</groupId>
                    <artifactId>hamcrest-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.lenskart</groupId>
            <artifactId>oms-domain</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.lenskart.nexs</groupId>
            <artifactId>common-logger</artifactId>
            <version>1.4-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.lenskart.nexs</groupId>
            <artifactId>nexs-microservice-commons</artifactId>
            <version>1.0.8</version>
        </dependency>

        <dependency>
            <groupId>com.lenskart</groupId>
            <artifactId>optima-domain</artifactId>
            <version>0.0.6</version>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>1.5.2.Final</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>
        <dependency>
            <groupId>com.lenskart.nexs</groupId>
            <artifactId>redis-commons</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.lenskart</groupId>
            <artifactId>order-interceptor-domain</artifactId>
            <version>1.6.2</version>
        </dependency>
        <dependency>
            <groupId>com.lenskart</groupId>
            <artifactId>core</artifactId>
            <version>7.6.6</version>
        </dependency>
        <dependency>
            <groupId>com.lenskart.nexs</groupId>
            <artifactId>common-http</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-jaxb-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>com.lenskart.nexs</groupId>
            <artifactId>nexs-wms-common-model</artifactId>
            <version>2.0.10</version>
            <exclusions>
                <exclusion>
                    <groupId>com.lenskart.nexs.fms</groupId>
                    <artifactId>nexs-fms-model</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.lenskart.nexs.fms</groupId>
            <artifactId>nexs-fms-model</artifactId>
            <version>1.0.6</version>
        </dependency>
        <dependency>
            <groupId>com.lenskart.vsm</groupId>
            <artifactId>vsm-soap-api-domain</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.lenskart.nexs</groupId>
            <artifactId>common-mailer</artifactId>
            <version>0.0.3</version>
        </dependency>

        <dependency>
            <groupId>com.lenskart.nexs</groupId>
            <artifactId>nexs-ims-commons</artifactId>
            <version>1.12.5</version>
            <exclusions>
                <exclusion>
                    <groupId>com.lenskart.nexs</groupId>
                    <artifactId>web</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.lenskart.nexs</groupId>
                    <artifactId>es-restclient-commons</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>20230227</version>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-envers</artifactId>
            <version>5.6.9.Final</version>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.4</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-vault-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.newrelic.agent.java</groupId>
            <artifactId>newrelic-api</artifactId>
            <version>7.11.1</version>
        </dependency>
        <dependency>
            <groupId>com.lenskart.nexs</groupId>
            <artifactId>nexs-microservice-commons-client</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.opencsv</groupId>
            <artifactId>opencsv</artifactId>
            <version>5.8</version>
        </dependency>
        <dependency>
            <groupId>com.lenskart</groupId>
            <artifactId>fds-domain</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.lenskart</groupId>
            <artifactId>putaway-service-model</artifactId>
            <version>1.5.4</version>
        </dependency>
        <dependency>
            <groupId>com.lenskart</groupId>
            <artifactId>inventory-adapter-model</artifactId>
            <version>7.2.1</version>
        </dependency>
        <dependency>
            <groupId>com.lenskart</groupId>
            <artifactId>order-ops-model</artifactId>
            <version>8.9.9</version>
        </dependency>
        <dependency>
            <groupId>com.lenskart</groupId>
            <artifactId>graceful-terminator</artifactId>
            <version>1.0</version>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>2021.0.5</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.6</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>jacoco-report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>jacoco-check</id>
                        <goals>
                            <goal>check</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <rule>
                                    <element>PACKAGE</element>
                                    <limits>
                                        <limit>
                                            <counter>LINE</counter>
                                            <value>COVEREDRATIO</value>
                                            <minimum>0.0</minimum>
                                        </limit>
                                    </limits>
                                </rule>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.0.0-M5</version>
                <configuration>
                    <forkedProcessExitTimeoutInSeconds>60</forkedProcessExitTimeoutInSeconds>
                    <forkCount>1</forkCount>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>nexs-releases</id>
            <url>http://archiva-new.prod.internal:8080/repository/internal/nexs-releases</url>
        </repository>
        <repository>
            <id>internal</id>
            <url>http://archiva-new.prod.internal:8080/repository/internal</url>
        </repository>
    </repositories>

    <distributionManagement>
        <repository>
            <id>nexs-releases</id>
            <url>http://archiva-new.prod.internal:8080/repository/internal</url>
        </repository>
    </distributionManagement>
</project>
