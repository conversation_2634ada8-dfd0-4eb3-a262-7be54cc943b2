package com.lenskart.oms.repository.order;


import com.lenskart.oms.entity.Order;
import com.lenskart.oms.entity.OrderAddress;
import com.lenskart.oms.entity.OrderItems;
import com.lenskart.oms.repository.OrderRepository;
import com.lenskart.oms.testdata.TestData;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager;

import java.util.Collections;

@Slf4j
@DataJpaTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
class OrderRepositoryTest {

    @Autowired
    private TestEntityManager entityManager;

    @Autowired
    private OrderRepository orderRepository;

//    @Test
//    void saveTest() {
//
//        Order order = TestData.getOrder();
//        OrderItems orderItem = TestData.getOrderItem(order);
////        order.setOrderItems(Collections.singletonList(orderItem));
////
////        order = orderRepository.save(order);
////
////        log.info("{}", order);
////        log.info("{}", order.getOrderItems());
////        log.info("{}", order.getOrderMetaData());
////
////        Assertions.assertNotNull(order);
////
////        Assertions.assertNotNull(order.getOrderItems());
////        Assertions.assertNotNull(order.getOrderMetaData());
//
//    }

}
