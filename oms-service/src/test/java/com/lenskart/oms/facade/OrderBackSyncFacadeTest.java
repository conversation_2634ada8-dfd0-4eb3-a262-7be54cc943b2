package com.lenskart.oms.facade;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

import com.lenskart.oms.connector.OrderOpsConnector;
import com.lenskart.oms.dto.OrderBackSyncTrackingDto;
import com.lenskart.oms.enums.BackSyncEntityType;
import com.lenskart.oms.enums.BackSyncEventName;
import com.lenskart.oms.enums.BackSyncEventStatus;
import com.lenskart.oms.enums.BackSyncSystem;
import com.lenskart.oms.mapper.OmsOrderEventMapper;
import com.lenskart.oms.mapper.OrderBackSyncMapper;
import com.lenskart.oms.producer.BackSyncTrackingEventProducer;
import com.lenskart.oms.producer.OrderOpsBackSyncProducer;
import com.lenskart.oms.service.OrderBackSyncTrackingService;
import com.lenskart.oms.service.OrderItemService;
import com.lenskart.oms.service.OrderService;
import com.lenskart.oms.service.ShipmentService;
import com.lenskart.oms.strategy.impl.CreateOrderStrategy;
import com.lenskart.oms.utils.OmsTransitionUtil;
import com.lenskart.oms.utils.OrderBackSyncUtil;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

import org.apache.logging.log4j.LogManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ContextConfiguration(classes = {OrderBackSyncFacade.class})
@ExtendWith(SpringExtension.class)
class OrderBackSyncFacadeTest {
    @MockBean
    private BackSyncTrackingEventProducer backSyncTrackingEventProducer;

    @MockBean
    private CreateOrderStrategy createOrderStrategy;

    @MockBean
    private OmsOrderEventMapper omsOrderEventMapper;

    @MockBean
    private OmsTransitionUtil omsTransitionUtil;

    @Autowired
    private OrderBackSyncFacade orderBackSyncFacade;

    @MockBean
    private OrderBackSyncMapper orderBackSyncMapper;

    @MockBean
    private OrderBackSyncTrackingService orderBackSyncTrackingService;

    @MockBean
    private OrderBackSyncUtil orderBackSyncUtil;

    @MockBean
    private OrderItemService orderItemService;

    @MockBean
    private OrderOpsBackSyncProducer orderOpsBackSyncProducer;

    @MockBean
    private OrderOpsConnector orderOpsConnector;

    @MockBean
    private OrderService orderService;

    @MockBean
    private ShipmentService shipmentService;

    @BeforeEach
    void setUp() {
        orderBackSyncFacade.setLogger(LogManager.getLogger());
    }

    /**
     * Method under test: {@link OrderBackSyncFacade#updateCreateOrderTrackingIfRequired(OrderBackSyncTrackingDto)}
     */
    @Test
    void testSuccessUpdateCreateOrderTrackingIfRequired() {
        OrderBackSyncTrackingDto orderBackSyncTrackingDto = new OrderBackSyncTrackingDto();
        orderBackSyncTrackingDto.setBackSyncSystem(BackSyncSystem.VSM);
        LocalDateTime atStartOfDayResult = LocalDate.of(1970, 1, 1).atStartOfDay();
        orderBackSyncTrackingDto.setCreatedAt(Date.from(atStartOfDayResult.atZone(ZoneId.of("UTC")).toInstant()));
        orderBackSyncTrackingDto.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        orderBackSyncTrackingDto.setEntityId("42");
        orderBackSyncTrackingDto.setEntityType(BackSyncEntityType.INCREMENT_ID);
        orderBackSyncTrackingDto.setEventName(BackSyncEventName.FACILITY_CODE_UPDATE);
        orderBackSyncTrackingDto.setEventStatus(BackSyncEventStatus.SUCCESS);
        orderBackSyncTrackingDto.setId(123L);
        orderBackSyncTrackingDto.setMessage("Not all who wander are lost");
        LocalDateTime atStartOfDayResult1 = LocalDate.of(1970, 1, 1).atStartOfDay();
        orderBackSyncTrackingDto.setUpdatedAt(Date.from(atStartOfDayResult1.atZone(ZoneId.of("UTC")).toInstant()));
        orderBackSyncTrackingDto.setUpdatedBy("2020-03-01");
        orderBackSyncTrackingDto.setVersion(1L);
        when(orderBackSyncTrackingService.findBySearchTerms(any())).thenReturn(orderBackSyncTrackingDto);
        this.orderBackSyncFacade.updateCreateOrderTrackingIfRequired(orderBackSyncTrackingDto);
        verify(orderBackSyncTrackingService, times(0)).save(any());
    }

    /**
     * Method under test: {@link OrderBackSyncFacade#updateCreateOrderTrackingIfRequired(OrderBackSyncTrackingDto)}
     */
    @Test
    void testFailureUpdateCreateOrderTrackingIfRequired() {
        OrderBackSyncTrackingDto orderBackSyncTrackingDto = new OrderBackSyncTrackingDto();
        orderBackSyncTrackingDto.setBackSyncSystem(BackSyncSystem.VSM);
        LocalDateTime atStartOfDayResult = LocalDate.of(1970, 1, 1).atStartOfDay();
        orderBackSyncTrackingDto.setCreatedAt(Date.from(atStartOfDayResult.atZone(ZoneId.of("UTC")).toInstant()));
        orderBackSyncTrackingDto.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        orderBackSyncTrackingDto.setEntityId("42");
        orderBackSyncTrackingDto.setEntityType(BackSyncEntityType.INCREMENT_ID);
        orderBackSyncTrackingDto.setEventName(BackSyncEventName.FACILITY_CODE_UPDATE);
        orderBackSyncTrackingDto.setEventStatus(BackSyncEventStatus.PENDING);
        orderBackSyncTrackingDto.setId(123L);
        orderBackSyncTrackingDto.setMessage("Not all who wander are lost");
        LocalDateTime atStartOfDayResult1 = LocalDate.of(1970, 1, 1).atStartOfDay();
        orderBackSyncTrackingDto.setUpdatedAt(Date.from(atStartOfDayResult1.atZone(ZoneId.of("UTC")).toInstant()));
        orderBackSyncTrackingDto.setUpdatedBy("2020-03-01");
        orderBackSyncTrackingDto.setVersion(1L);
        when(orderBackSyncTrackingService.findBySearchTerms(any())).thenReturn(orderBackSyncTrackingDto);
        this.orderBackSyncFacade.updateCreateOrderTrackingIfRequired(orderBackSyncTrackingDto);
        verify(orderBackSyncTrackingService, atLeastOnce()).save(any());
    }
}

