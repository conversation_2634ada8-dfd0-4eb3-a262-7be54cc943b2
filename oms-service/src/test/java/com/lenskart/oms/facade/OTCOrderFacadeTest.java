package com.lenskart.oms.facade;

import com.lenskart.oms.service.ShipmentService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.junit4.SpringRunner;


@RunWith(SpringRunner.class)
public class OTCOrderFacadeTest {
    @InjectMocks
    private OTCOrderFacade otcOrderFacade;
    @Mock
    private ShipmentService shipmentService;


    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

//    @Test
//    public void testProcessOTCOrder_CreatedState() throws Exception {
//        ShipmentDto shipmentDto = new ShipmentDto();
//        shipmentDto.setShipmentStatus(ShipmentStatus.CREATED);
//
//        when(shipmentService.findById(anyLong())).thenReturn(shipmentDto);
//        otcOrderFacade.processOTCOrder(new OTCEventPayload(ShipmentStatus.CREATED,"123"));
//        verify(otcOrderFacade.createdState).trigger(shipmentDto);
//    }
//
//    @Test
//    public void testProcessOTCOrder_InvoicedState() throws Exception {
//        ShipmentDto shipmentDto = new ShipmentDto();
//        shipmentDto.setShipmentStatus(ShipmentStatus.INVOICED);
//
//        when(shipmentService.findById(anyLong())).thenReturn(shipmentDto);
//
//        otcOrderFacade.processOTCOrder("456");
//
//        // Verify that the invoicedState.trigger method was called
//        verify(otcOrderFacade.invoicedState).trigger(shipmentDto);
//    }
}
