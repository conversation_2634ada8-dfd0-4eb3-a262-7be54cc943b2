package com.lenskart.oms.strategy.impl;

import com.lenskart.oms.connector.NexsImsConnector;
import com.lenskart.oms.connector.OptimaConnector;
import com.lenskart.oms.dto.*;
import com.lenskart.oms.enums.*;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.facade.OptimaFacade;
import com.lenskart.oms.facade.OrderBackSyncFacade;
import com.lenskart.oms.facade.ReassignOmsFacade;
import com.lenskart.oms.mapper.WmsOrderEventMapper;
import com.lenskart.oms.request.OmsOrderEvent;
import com.lenskart.optima.enums.ItemType;
import com.lenskart.optima.enums.ShipmentType;
import com.lenskart.optima.request.AssignShipmentFullfillerRequest;
import com.lenskart.optima.request.ShipmentItem;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Metrics;
import lombok.AccessLevel;
import lombok.Setter;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.regex.Pattern;

import static org.mockito.Mockito.mock;


@ContextConfiguration(classes = {CreateOrderProcessorStrategy.class})
@ExtendWith(SpringExtension.class)
class CreateOrderProcessorStrategyTest {

    private static final Counter RELEASE_INV_ERROR_COUNTER = Metrics.counter("release.inv", "result", "failure");

    @Value("#{'${nexs.order.criteria.facilities}'.split(',')}")
    @Setter(AccessLevel.NONE)
    private Set<String> nexsOrderCriteriaFacilities;
    @Value("${nexs.facility.new.facility.unicommordercode.regex}")
    @Setter(AccessLevel.NONE)
    private String unicommonOrderCodeRegex;
    @Setter(AccessLevel.NONE)
    private Pattern optimaEligiblityPattern;
    @MockBean
    private OptimaConnector optimaConnector;
    @MockBean
    private OptimaFacade optimaFacade;
    @MockBean
    private NexsImsConnector nexsImsConnector;
    @MockBean
    private ReassignOmsFacade reassignOmsFacade;
    @MockBean
    private OrderBackSyncFacade orderBackSyncFacade;
    @MockBean
    private WmsOrderEventMapper wmsOrderEventMapper;

    @Autowired
    private CreateOrderProcessorStrategy createOrderProcessorStrategy;

    @Test
    void testExceute() throws Exception {
        OmsOrderEvent omsOrderEvent = new OmsOrderEvent();
        omsOrderEvent.setIncrementId(1232142353L);
        omsOrderEvent.setEventType(OrderEventType.CREATE_ORDER);
        OrderDto orderDto = new OrderDto();
        orderDto.setIncrementId(1232142353L);
        orderDto.setJunoOrderId(32634645L);
        orderDto.setOrderStatus(OrderStatus.CREATED);
        orderDto.setOrderSubStatus(OrderSubStatus.PROCESSING);
        orderDto.setOrderDate(new Date());
        orderDto.setLkCountry("INDIA");
        orderDto.setCurrencyCode("INR");
        orderDto.setCustomerId(1241212L);
        orderDto.setMerchantId("213524535");
        orderDto.setOrderSource("POS");
        orderDto.setStoreId(523453L);
        orderDto.setExchangeFlag(false);
        orderDto.setExchangeItemId(2354234534L);
        orderDto.setPaymentCaptured(true);
        orderDto.setPaymentMethod("juspay");
        orderDto.setIsOnHold(false);
        orderDto.setOnHoldReasonId(234L);
        OrderItemDto orderItemDto = new OrderItemDto();
        orderItemDto.setShipmentId(1243322L);
        orderItemDto.setOrderId(523453L);
        orderItemDto.setMagentoItemId(345354L);
        orderItemDto.setParentMagentoItemId(34534534534L);
        orderItemDto.setItemStatus(OrderItemStatus.CREATED);
        orderItemDto.setItemSubStatus(OrderItemSubStatus.CREATED);
        orderItemDto.setProductId(131932L);
        orderItemDto.setItemBarcode("nfgjberj423");
        orderItemDto.setFittingId(3523456L);
        orderItemDto.setFulfillmentType(FulfillmentType.LOCAL_FITTING);
        orderItemDto.setB2bReferenceItemId(5234532523434L);
        orderItemDto.setChannel(Channel.JOHNJACOBS);
        orderItemDto.setProductDeliveryType(ProductDeliveryType.DTC);
        orderItemDto.setItemPower(new OrderItemPowerDto());
        orderItemDto.setOrderItemPrice(new OrderItemPricesDto());
        orderDto.setOrderMetaData(new ArrayList<>());
        List<OrderItemDto> orderItemDtos = new ArrayList<>();
        orderItemDtos.add(orderItemDto);
        orderDto.setOrderItems(orderItemDtos);
        List<OrderMetaDataDto> orderMetaDataDtoList = new ArrayList<>();
        orderMetaDataDtoList.add(new OrderMetaDataDto());
        orderDto.setOrderMetaData(orderMetaDataDtoList);
        createOrderProcessorStrategy.execute(omsOrderEvent);
    }

    @Test
    void testBuildAssignmentFulfillerRequest(){
        AssignShipmentFullfillerRequest assignShipmentFullfillerRequest = mock(AssignShipmentFullfillerRequest.class);
        ShipmentDto shipmentDto = new ShipmentDto();
        shipmentDto.setWmsOrderCode("132423435");
        shipmentDto.setWmsShippingPackageId("SNX13534545367");
        shipmentDto.setShipmentStatus(ShipmentStatus.CREATED);
        shipmentDto.setShipmentSubStatus(ShipmentSubStatus.PROCESSING);
        shipmentDto.setFacility("NXS1");
        shipmentDto.setShipmentType("shipmentType");
        shipmentDto.setShipmentSubType("shipmentSubType");
        shipmentDto.setAwbNumber("qewfb23412");
        shipmentDto.setInvoiceNumber("123423123");
        shipmentDto.setManifestNumber("21342jbg34");
        shipmentDto.setActualShipDate(new Date());
        shipmentDto.setActualDeliveryDate(new Date());
        shipmentDto.setExpectedDeliveryDate(new Date());
        shipmentDto.setExpectedShipDate(new Date());
        shipmentDto.setShippingAddress(new OrderAddressDto());
        shipmentDto.setBillingAddress(new OrderAddressDto());
        OrderItemDto orderItemDto = new OrderItemDto();
        orderItemDto.setShipmentId(1243322L);
        orderItemDto.setOrderId(523453L);
        orderItemDto.setMagentoItemId(345354L);
        orderItemDto.setParentMagentoItemId(34534534534L);
        orderItemDto.setItemStatus(OrderItemStatus.CREATED);
        orderItemDto.setItemSubStatus(OrderItemSubStatus.CREATED);
        orderItemDto.setProductId(131932L);
        orderItemDto.setItemBarcode("nfgjberj423");
        orderItemDto.setFittingId(3523456L);
        orderItemDto.setFulfillmentType(FulfillmentType.LOCAL_FITTING);
        orderItemDto.setB2bReferenceItemId(5234532523434L);
        orderItemDto.setChannel(Channel.JOHNJACOBS);
        orderItemDto.setProductDeliveryType(ProductDeliveryType.DTC);
        orderItemDto.setItemPower(new OrderItemPowerDto());
        orderItemDto.setOrderItemPrice(new OrderItemPricesDto());
        List<OrderItemDto> orderItemDtos = new ArrayList<>();
        orderItemDtos.add(orderItemDto);
        shipmentDto.setOrderItems(orderItemDtos);
        shipmentDto.setShipmentMetaData(new ArrayList<>());
        Assert.assertNotNull(createOrderProcessorStrategy.buildAssignmentFulfillerRequest(shipmentDto));
    }

    @Test
    void testAssignWarehouseAndBlockInventoryUsingOptima(){
        OmsOrderEvent omsOrderEvent = new OmsOrderEvent();
        omsOrderEvent.setIncrementId(1232142353L);
        omsOrderEvent.setEventType(OrderEventType.CREATE_ORDER);
        OrderDto orderDto = new OrderDto();
        orderDto.setIncrementId(1232142353L);
        orderDto.setJunoOrderId(32634645L);
        orderDto.setOrderStatus(OrderStatus.CREATED);
        orderDto.setOrderSubStatus(OrderSubStatus.PROCESSING);
        orderDto.setOrderDate(new Date());
        orderDto.setLkCountry("INDIA");
        orderDto.setCurrencyCode("INR");
        orderDto.setCustomerId(1241212L);
        orderDto.setMerchantId("213524535");
        orderDto.setOrderSource("POS");
        orderDto.setStoreId(523453L);
        orderDto.setExchangeFlag(false);
        orderDto.setExchangeItemId(2354234534L);
        orderDto.setPaymentCaptured(true);
        orderDto.setPaymentMethod("juspay");
        orderDto.setIsOnHold(false);
        orderDto.setOnHoldReasonId(234L);
        OrderItemDto orderItemDto = new OrderItemDto();
        orderItemDto.setShipmentId(1243322L);
        orderItemDto.setOrderId(523453L);
        orderItemDto.setMagentoItemId(345354L);
        orderItemDto.setParentMagentoItemId(34534534534L);
        orderItemDto.setItemStatus(OrderItemStatus.CREATED);
        orderItemDto.setItemSubStatus(OrderItemSubStatus.CREATED);
        orderItemDto.setProductId(131932L);
        orderItemDto.setItemBarcode("nfgjberj423");
        orderItemDto.setFittingId(3523456L);
        orderItemDto.setFulfillmentType(FulfillmentType.LOCAL_FITTING);
        orderItemDto.setB2bReferenceItemId(5234532523434L);
        orderItemDto.setChannel(Channel.JOHNJACOBS);
        orderItemDto.setProductDeliveryType(ProductDeliveryType.DTC);
        orderItemDto.setItemPower(new OrderItemPowerDto());
        orderItemDto.setOrderItemPrice(new OrderItemPricesDto());
        orderDto.setOrderMetaData(new ArrayList<>());
        List<OrderItemDto> orderItemDtos = new ArrayList<>();
        orderItemDtos.add(orderItemDto);
        orderDto.setOrderItems(orderItemDtos);
        List<OrderMetaDataDto> orderMetaDataDtoList = new ArrayList<>();
        orderMetaDataDtoList.add(new OrderMetaDataDto());
        orderDto.setOrderMetaData(orderMetaDataDtoList);
        ShipmentDto shipmentDto = new ShipmentDto();
        shipmentDto.setWmsOrderCode("132423435");
        shipmentDto.setWmsShippingPackageId("SNX13534545367");
        shipmentDto.setShipmentStatus(ShipmentStatus.CREATED);
        shipmentDto.setShipmentSubStatus(ShipmentSubStatus.PROCESSING);
        shipmentDto.setFacility("NXS1");
        shipmentDto.setShipmentType("shipmentType");
        shipmentDto.setShipmentSubType("shipmentSubType");
        shipmentDto.setAwbNumber("qewfb23412");
        shipmentDto.setInvoiceNumber("123423123");
        shipmentDto.setManifestNumber("21342jbg34");
        shipmentDto.setActualShipDate(new Date());
        shipmentDto.setActualDeliveryDate(new Date());
        shipmentDto.setExpectedDeliveryDate(new Date());
        shipmentDto.setExpectedShipDate(new Date());
        shipmentDto.setShippingAddress(new OrderAddressDto());
        shipmentDto.setBillingAddress(new OrderAddressDto());
        OrderItemDto orderItemDto1 = new OrderItemDto();
        orderItemDto1.setShipmentId(1243322L);
        orderItemDto1.setOrderId(523453L);
        orderItemDto1.setMagentoItemId(345354L);
        orderItemDto1.setParentMagentoItemId(34534534534L);
        orderItemDto1.setItemStatus(OrderItemStatus.CREATED);
        orderItemDto1.setItemSubStatus(OrderItemSubStatus.CREATED);
        orderItemDto1.setProductId(131932L);
        orderItemDto1.setItemBarcode("nfgjberj423");
        orderItemDto1.setFittingId(3523456L);
        orderItemDto1.setFulfillmentType(FulfillmentType.LOCAL_FITTING);
        orderItemDto1.setB2bReferenceItemId(5234532523434L);
        orderItemDto1.setChannel(Channel.JOHNJACOBS);
        orderItemDto1.setProductDeliveryType(ProductDeliveryType.DTC);
        orderItemDto1.setItemPower(new OrderItemPowerDto());
        orderItemDto1.setOrderItemPrice(new OrderItemPricesDto());
        List<OrderItemDto> orderItemDtoArrayList = new ArrayList<>();
        orderItemDtoArrayList.add(orderItemDto1);
        shipmentDto.setOrderItems(orderItemDtoArrayList);
        shipmentDto.setShipmentMetaData(new ArrayList<>());
        Assert.assertNotNull(createOrderProcessorStrategy.assignWarehouseAndBlockInventoryUsingOptima(shipmentDto, omsOrderEvent));
    }
}