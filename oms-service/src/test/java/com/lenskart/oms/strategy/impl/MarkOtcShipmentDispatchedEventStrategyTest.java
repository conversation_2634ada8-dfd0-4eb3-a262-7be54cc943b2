package com.lenskart.oms.strategy.impl;

import com.lenskart.nexs.ims.response.ItemStockUpdateResponseV2;
import com.lenskart.nexs.ims.response.UpdateStocksResponseV2;
import com.lenskart.oms.connector.FdsConnector;
import com.lenskart.oms.connector.ImsConnector;
import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.enums.*;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.exception.DuplicateEventException;
import com.lenskart.oms.mapper.WmsOrderEventMapper;
import com.lenskart.oms.model.Action;
import com.lenskart.oms.model.OrderItemTransitions;
import com.lenskart.oms.model.OrderTransitions;
import com.lenskart.oms.model.ShipmentTransitions;
import com.lenskart.oms.producer.CommonKafkaProducer;
import com.lenskart.oms.producer.OrderEventWmsProducer;
import com.lenskart.oms.producer.ShipmentEventOmsProducer;
import com.lenskart.oms.request.OtcShipmentEvent;
import com.lenskart.oms.request.ShipmentItemUpdate;
import com.lenskart.oms.request.ShipmentUpdateEvent;
import com.lenskart.oms.service.*;
import com.lenskart.oms.utils.OmsTransitionUtil;
import org.apache.logging.log4j.Logger;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertThrows;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

@RunWith(SpringRunner.class)
public class MarkOtcShipmentDispatchedEventStrategyTest {
    @Mock
    private FdsConnector fdsConnector;
    @Mock
    private CommonKafkaProducer commonKafkaProducer;
    @Mock
    private OrderBackSyncTrackingService orderBackSyncTrackingService;
    @Mock
    private ShipmentEventOmsProducer shipmentEventOmsProducer;
    @Mock
    private ShipmentService shipmentService;
    @Mock
    private ImsConnector imsConnector;
    @Mock
    private OmsTransitionUtil omsTransitionUtil;
    @Mock
    private ShipmentTimelineService shipmentTimelineService;
    @Mock
    private OrderItemService orderItemService;
    @Mock
    protected Logger logger;
    @Mock
    private OrderService orderService;
    @Mock
    private OrderEventWmsProducer orderEventWmsProducer;
    @Mock
    private WmsOrderEventMapper wmsOrderEventMapper;
    @InjectMocks
    private MarkOtcShipmentDispatchedEventStrategy markOtcShipmentDispatchedEventStrategy;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testExecute_SuccessfulCreationAndUpdate() throws Exception {
        OtcShipmentEvent otcShipmentEvent = new OtcShipmentEvent(OtcShipmentEventType.DISPATCHED,1L);
        ShipmentDto shipmentDto = new ShipmentDto();
        shipmentDto.setWmsOrderCode("123");
        OrderItemDto orderItemDto = new OrderItemDto();
        orderItemDto.setItemStatus(OrderItemStatus.CREATED);
        orderItemDto.setItemBarcode("OTC");
        orderItemDto.setProductId(123L);
        shipmentDto.setOrderItems(Arrays.asList(new OrderItemDto[]{orderItemDto}));
        ItemStockUpdateResponseV2 responseV2 = new ItemStockUpdateResponseV2();
        responseV2.setSuccess(true);
        UpdateStocksResponseV2 updateStocksResponseV2 = new UpdateStocksResponseV2();
        updateStocksResponseV2.setItemStockUpdateResponseV2List(Arrays.asList(new ItemStockUpdateResponseV2[]{responseV2}));
        when(shipmentService.findById(anyLong())).thenReturn(shipmentDto);
        when(imsConnector.updateBarcodeStatus(any())).thenReturn(updateStocksResponseV2);
        Action action = new Action();
        OrderItemTransitions orderItemTransitions = new OrderItemTransitions();
        ShipmentTransitions shipmentStatus = new ShipmentTransitions();
        OrderTransitions orderStatus = new OrderTransitions();
        orderItemTransitions.setItemStatus(OrderItemStatus.INVOICED);
        orderItemTransitions.setItemSubStatus(OrderItemSubStatus.INVOICED);
        shipmentStatus.setShipmentStatus(ShipmentStatus.INVOICED);
        shipmentStatus.setShipmentSubStatus(ShipmentSubStatus.INVOICED);
        orderStatus.setOrderStatus(OrderStatus.DISPATCHED);
        orderStatus.setOrderSubStatus(OrderSubStatus.DISPATCHED);
        action.setItemStatus(orderItemTransitions);
        action.setShipmentStatus(shipmentStatus);
        action.setOrderStatus(orderStatus);

        when(omsTransitionUtil.getTransitionActionByOperationAndStatus(any(),any())).thenReturn(action);
        markOtcShipmentDispatchedEventStrategy.execute(otcShipmentEvent);
        verify(orderItemService, times(1)).update(any(), any());
        verify(shipmentService, times(1)).update(any(), any());
    }

    @Test
    public void testValidateOtcEvent_ShouldThrowDuplicateEventException() {
        OtcShipmentEvent otcShipmentEvent = new OtcShipmentEvent();
        otcShipmentEvent.setShipmentId(123L);

        ShipmentDto shipmentDto = new ShipmentDto();
        shipmentDto.setShipmentStatus(ShipmentStatus.DISPATCHED);

        when(shipmentService.findById(123L)).thenReturn(shipmentDto);

        assertThrows(DuplicateEventException.class, () -> markOtcShipmentDispatchedEventStrategy.validateOtcEvent(otcShipmentEvent));
    }

    @Test
    public void testValidateOtcEvent_ShouldThrowApplicationException() {
        OtcShipmentEvent otcShipmentEvent = new OtcShipmentEvent();
        otcShipmentEvent.setShipmentId(123L);

        ShipmentDto shipmentDto = new ShipmentDto();
        shipmentDto.setShipmentStatus(ShipmentStatus.CREATED); // Assuming status other than INVOICED

        when(shipmentService.findById(123L)).thenReturn(shipmentDto);

        assertThrows(ApplicationException.class, () -> markOtcShipmentDispatchedEventStrategy.validateOtcEvent(otcShipmentEvent));
    }

    @Test
    public void testValidateOtcEvent_ShouldPass() {
        OtcShipmentEvent otcShipmentEvent = new OtcShipmentEvent();
        otcShipmentEvent.setShipmentId(123L);

        ShipmentDto shipmentDto = new ShipmentDto();
        shipmentDto.setShipmentStatus(ShipmentStatus.INVOICED);

        when(shipmentService.findById(123L)).thenReturn(shipmentDto);
        assertDoesNotThrow(() -> markOtcShipmentDispatchedEventStrategy.validateOtcEvent(otcShipmentEvent));
    }

    @Test
    public void testPostExecute() throws Exception {
        OtcShipmentEvent otcShipmentEvent = new OtcShipmentEvent();
        otcShipmentEvent.setShipmentId(123L);
        ShipmentDto shipmentDto = new ShipmentDto();
        shipmentDto.setId(123L);
        shipmentDto.setWmsOrderCode("WMS123");
        ShipmentUpdateEvent shipmentUpdateEvent = new ShipmentUpdateEvent();
        shipmentUpdateEvent.setShipmentEvent(ShipmentEvent.CREATE_SHIPMENT_INVOICE);
        shipmentUpdateEvent.setWmsOrderCode(shipmentDto.getWmsOrderCode());
        List<ShipmentItemUpdate> shipmentItemUpdates = new ArrayList<>();
        ShipmentItemUpdate shipmentItemUpdate = new ShipmentItemUpdate();
        shipmentItemUpdate.setUnicomShipmentStatus(null);
        shipmentItemUpdate.setOrderOpsShipmentStatus("PACKED");
        shipmentItemUpdates.add(shipmentItemUpdate);
        shipmentUpdateEvent.setOrderItemList(shipmentItemUpdates);
        OrderItemDto orderItemDto = new OrderItemDto();
        orderItemDto.setOrderId(1234L);
        OrderDto orderDto = new OrderDto();
        shipmentDto.setOrderItems(Arrays.asList(new OrderItemDto[]{orderItemDto}));
        when(shipmentService.findById(123L)).thenReturn(shipmentDto);
        when(orderService.findById(anyLong())).thenReturn(orderDto);
        markOtcShipmentDispatchedEventStrategy.postExecute(otcShipmentEvent);
        verify(shipmentService).findById(123L);
        verify(shipmentEventOmsProducer, times(2)).sendMessage(any(), any(), any(), eq("WMS123"));
    }

}
