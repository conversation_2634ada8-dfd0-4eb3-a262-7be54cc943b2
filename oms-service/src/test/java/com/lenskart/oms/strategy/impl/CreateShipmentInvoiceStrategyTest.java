package com.lenskart.oms.strategy.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.lenskart.oms.connector.OrderOpsConnector;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.OrderItemMetaDataDto;
import com.lenskart.oms.dto.OrderItemPowerDto;
import com.lenskart.oms.dto.OrderItemPricesDto;
import com.lenskart.oms.enums.Channel;
import com.lenskart.oms.enums.DeliveryPriority;
import com.lenskart.oms.enums.FittingType;
import com.lenskart.oms.enums.FulfillmentType;
import com.lenskart.oms.enums.ItemType;
import com.lenskart.oms.enums.NavChannel;
import com.lenskart.oms.enums.OrderItemStatus;
import com.lenskart.oms.enums.OrderItemSubStatus;
import com.lenskart.oms.enums.ProductDeliveryType;
import com.lenskart.oms.producer.ShipmentEventOmsProducer;
import com.lenskart.oms.service.OrderBackSyncTrackingService;
import com.lenskart.oms.service.OrderItemService;
import com.lenskart.oms.service.OrderService;
import com.lenskart.oms.service.ShipmentService;
import com.lenskart.oms.service.ShipmentTimelineService;
import com.lenskart.oms.utils.OmsTransitionUtil;
import com.lenskart.oms.validators.ShipmentEventValidator;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ContextConfiguration(classes = {CreateShipmentInvoiceStrategy.class})
@ExtendWith(SpringExtension.class)
class CreateShipmentInvoiceStrategyTest {
    @Autowired
    private CreateShipmentInvoiceStrategy createShipmentInvoiceStrategy;

    @MockBean
    private OmsTransitionUtil omsTransitionUtil;

    @MockBean
    private OrderBackSyncTrackingService orderBackSyncTrackingService;

    @MockBean
    private OrderItemService orderItemService;

    @MockBean
    private OrderOpsConnector orderOpsConnector;

    @MockBean
    private OrderService orderService;

    @MockBean
    private ShipmentEventOmsProducer shipmentEventOmsProducer;

    @MockBean
    private ShipmentEventValidator shipmentEventValidator;

    @MockBean
    private ShipmentService shipmentService;

    @MockBean
    private ShipmentTimelineService shipmentTimelineService;

    /**
     * Method under test: {@link CreateShipmentInvoiceStrategy#updateLoyaltyItemBarcodeIfRequired(OrderItemDto)}
     */
    @Test
    void testFailureUpdateLoyaltyItemBarcodeIfRequired() {
        OrderItemPowerDto orderItemPowerDto = new OrderItemPowerDto();
        orderItemPowerDto.setAp("Ap");
        orderItemPowerDto.setAxis("Axis");
        orderItemPowerDto.setBaseCurve("Base Curve");
        orderItemPowerDto.setBottomDistance("Bottom Distance");
        orderItemPowerDto.setBoxQty(1);
        orderItemPowerDto.setCoatingName("Coating Name");
        orderItemPowerDto.setCoatingOid("Coating Oid");
        orderItemPowerDto.setColor("Color");
        LocalDateTime atStartOfDayResult = LocalDate.of(1970, 1, 1).atStartOfDay();
        orderItemPowerDto.setCreatedAt(Date.from(atStartOfDayResult.atZone(ZoneId.of("UTC")).toInstant()));
        orderItemPowerDto.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        orderItemPowerDto.setCyl("Cyl");
        orderItemPowerDto.setEdgeDistance("Edge Distance");
        orderItemPowerDto.setEffectiveDia("Effective Dia");
        orderItemPowerDto.setId(123L);
        orderItemPowerDto.setLensHeight("Lens Height");
        orderItemPowerDto.setLensIndex("Lens Index");
        orderItemPowerDto.setLensPackage("java.text");
        orderItemPowerDto.setLensPackageType("java.text");
        orderItemPowerDto.setLensWidth("Lens Width");
        orderItemPowerDto.setNearPD("Near PD");
        orderItemPowerDto.setPackageName("java.text");
        orderItemPowerDto.setPackagePrice(10.0d);
        orderItemPowerDto.setPatientComments("Patient Comments");
        orderItemPowerDto.setPatientName("Patient Name");
        orderItemPowerDto.setPd("Pd");
        orderItemPowerDto.setPowerType("Power Type");
        orderItemPowerDto.setPrescriptionUrl("https://example.org/example");
        orderItemPowerDto.setProductId(123L);
        orderItemPowerDto.setShellId(123L);
        orderItemPowerDto.setSph("Sph");
        orderItemPowerDto.setSpvd("Spvd");
        orderItemPowerDto.setThickness("Thickness");
        orderItemPowerDto.setTint("Tint");
        orderItemPowerDto.setTopDistance("Top Distance");
        LocalDateTime atStartOfDayResult1 = LocalDate.of(1970, 1, 1).atStartOfDay();
        orderItemPowerDto.setUpdatedAt(Date.from(atStartOfDayResult1.atZone(ZoneId.of("UTC")).toInstant()));
        orderItemPowerDto.setUpdatedBy("2020-03-01");
        orderItemPowerDto.setVersion(1L);
        orderItemPowerDto.setWebPackage("java.text");

        OrderItemPricesDto orderItemPricesDto = new OrderItemPricesDto();
        orderItemPricesDto.setCouponDiscount(10.0d);
        LocalDateTime atStartOfDayResult2 = LocalDate.of(1970, 1, 1).atStartOfDay();
        orderItemPricesDto.setCreatedAt(Date.from(atStartOfDayResult2.atZone(ZoneId.of("UTC")).toInstant()));
        orderItemPricesDto.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        orderItemPricesDto.setExchangeDiscount(10.0d);
        orderItemPricesDto.setFcDiscount(10.0d);
        orderItemPricesDto.setGvDiscount(10.0d);
        orderItemPricesDto.setId(123L);
        orderItemPricesDto.setImplicitDiscount(10.0d);
        orderItemPricesDto.setItemGrandTotal(10.0d);
        orderItemPricesDto.setItemTotalAfterDiscount(10.0d);
        orderItemPricesDto.setLenskartDiscount(10.0d);
        orderItemPricesDto.setLenskartPlusDiscount(10.0d);
        orderItemPricesDto.setPrepaidDiscount(10.0d);
        orderItemPricesDto.setScDiscount(10.0d);
        orderItemPricesDto.setShippingCharges(10.0d);
        orderItemPricesDto.setTaxCollected(10.0d);
        LocalDateTime atStartOfDayResult3 = LocalDate.of(1970, 1, 1).atStartOfDay();
        orderItemPricesDto.setUpdatedAt(Date.from(atStartOfDayResult3.atZone(ZoneId.of("UTC")).toInstant()));
        orderItemPricesDto.setUpdatedBy("2020-03-01");
        orderItemPricesDto.setVersion(1L);
        orderItemPricesDto.setWalletDiscount(10.0d);
        orderItemPricesDto.setWalletPlusDiscount(10.0d);

        OrderItemDto orderItemDto = new OrderItemDto();
        orderItemDto.setB2bReferenceItemId(123L);
        orderItemDto.setChannel(Channel.EXCHANGE);
        LocalDateTime atStartOfDayResult4 = LocalDate.of(1970, 1, 1).atStartOfDay();
        Date fromResult = Date.from(atStartOfDayResult4.atZone(ZoneId.of("UTC")).toInstant());
        orderItemDto.setCreatedAt(fromResult);
        orderItemDto.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        orderItemDto.setDeliveryCountry("GB");
        orderItemDto.setDeliveryPriority(DeliveryPriority.EXPRESS);
        orderItemDto.setDeliveryStoreId("42");
        orderItemDto.setDeliveryZipCode("Delivery Zip Code");
        orderItemDto.setFittingId(123L);
        orderItemDto.setFittingType(FittingType.REQD);
        orderItemDto.setFulfillmentType(FulfillmentType.LOCAL_FITTING);
        orderItemDto.setId(123L);
        orderItemDto.setItemPower(orderItemPowerDto);
        orderItemDto.setItemStatus(OrderItemStatus.DEFAULT);
        orderItemDto.setItemSubStatus(OrderItemSubStatus.PROCESSING_PENDING);
        orderItemDto.setItemType(ItemType.EYEFRAME);
        orderItemDto.setJitStatus("Jit Status");
        orderItemDto.setNavChannel(NavChannel.WEBDTC);
        orderItemDto.setOmaStatus("Oma Status");
        orderItemDto.setOrderId(123L);
        ArrayList<OrderItemMetaDataDto> orderItemMetaDataDtoList = new ArrayList<>();
        orderItemDto.setOrderItemMetaData(orderItemMetaDataDtoList);
        orderItemDto.setOrderItemPrice(orderItemPricesDto);
        orderItemDto.setProductDeliveryType(ProductDeliveryType.DTC);
        orderItemDto.setProductId(123L);
        LocalDateTime atStartOfDayResult5 = LocalDate.of(1970, 1, 1).atStartOfDay();
        Date fromResult1 = Date.from(atStartOfDayResult5.atZone(ZoneId.of("UTC")).toInstant());
        orderItemDto.setPromisedDeliveryDate(fromResult1);
        LocalDateTime atStartOfDayResult6 = LocalDate.of(1970, 1, 1).atStartOfDay();
        Date fromResult2 = Date.from(atStartOfDayResult6.atZone(ZoneId.of("UTC")).toInstant());
        orderItemDto.setPromisedShipDate(fromResult2);
        orderItemDto.setShipmentId(123L);
        orderItemDto.setShippingDestinationType("Shipping Destination Type");
        LocalDateTime atStartOfDayResult7 = LocalDate.of(1970, 1, 1).atStartOfDay();
        Date fromResult3 = Date.from(atStartOfDayResult7.atZone(ZoneId.of("UTC")).toInstant());
        orderItemDto.setUpdatedAt(fromResult3);
        orderItemDto.setUpdatedBy("2020-03-01");
        orderItemDto.setUwItemId(123L);
        orderItemDto.setVersion(1L);
        this.createShipmentInvoiceStrategy.updateLoyaltyItemBarcodeIfRequired(orderItemDto);
        assertNull(orderItemDto.getItemBarcode());
    }

    /**
     * Method under test: {@link CreateShipmentInvoiceStrategy#updateLoyaltyItemBarcodeIfRequired(OrderItemDto)}
     */
    @Test
    void testSuccessUpdateLoyaltyItemBarcodeIfRequired() {
        OrderItemPowerDto orderItemPowerDto = new OrderItemPowerDto();
        orderItemPowerDto.setAp("Ap");
        orderItemPowerDto.setAxis("Axis");
        orderItemPowerDto.setBaseCurve("Base Curve");
        orderItemPowerDto.setBottomDistance("Bottom Distance");
        orderItemPowerDto.setBoxQty(1);
        orderItemPowerDto.setCoatingName("Coating Name");
        orderItemPowerDto.setCoatingOid("Coating Oid");
        orderItemPowerDto.setColor("Color");
        LocalDateTime atStartOfDayResult = LocalDate.of(1970, 1, 1).atStartOfDay();
        orderItemPowerDto.setCreatedAt(Date.from(atStartOfDayResult.atZone(ZoneId.of("UTC")).toInstant()));
        orderItemPowerDto.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        orderItemPowerDto.setCyl("Cyl");
        orderItemPowerDto.setEdgeDistance("Edge Distance");
        orderItemPowerDto.setEffectiveDia("Effective Dia");
        orderItemPowerDto.setId(123L);
        orderItemPowerDto.setLensHeight("Lens Height");
        orderItemPowerDto.setLensIndex("Lens Index");
        orderItemPowerDto.setLensPackage("java.text");
        orderItemPowerDto.setLensPackageType("java.text");
        orderItemPowerDto.setLensWidth("Lens Width");
        orderItemPowerDto.setNearPD("Near PD");
        orderItemPowerDto.setPackageName("java.text");
        orderItemPowerDto.setPackagePrice(10.0d);
        orderItemPowerDto.setPatientComments("Patient Comments");
        orderItemPowerDto.setPatientName("Patient Name");
        orderItemPowerDto.setPd("Pd");
        orderItemPowerDto.setPowerType("Power Type");
        orderItemPowerDto.setPrescriptionUrl("https://example.org/example");
        orderItemPowerDto.setProductId(123L);
        orderItemPowerDto.setShellId(123L);
        orderItemPowerDto.setSph("Sph");
        orderItemPowerDto.setSpvd("Spvd");
        orderItemPowerDto.setThickness("Thickness");
        orderItemPowerDto.setTint("Tint");
        orderItemPowerDto.setTopDistance("Top Distance");
        LocalDateTime atStartOfDayResult1 = LocalDate.of(1970, 1, 1).atStartOfDay();
        orderItemPowerDto.setUpdatedAt(Date.from(atStartOfDayResult1.atZone(ZoneId.of("UTC")).toInstant()));
        orderItemPowerDto.setUpdatedBy("2020-03-01");
        orderItemPowerDto.setVersion(1L);
        orderItemPowerDto.setWebPackage("java.text");

        OrderItemPricesDto orderItemPricesDto = new OrderItemPricesDto();
        orderItemPricesDto.setCouponDiscount(10.0d);
        LocalDateTime atStartOfDayResult2 = LocalDate.of(1970, 1, 1).atStartOfDay();
        orderItemPricesDto.setCreatedAt(Date.from(atStartOfDayResult2.atZone(ZoneId.of("UTC")).toInstant()));
        orderItemPricesDto.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        orderItemPricesDto.setExchangeDiscount(10.0d);
        orderItemPricesDto.setFcDiscount(10.0d);
        orderItemPricesDto.setGvDiscount(10.0d);
        orderItemPricesDto.setId(123L);
        orderItemPricesDto.setImplicitDiscount(10.0d);
        orderItemPricesDto.setItemGrandTotal(10.0d);
        orderItemPricesDto.setItemTotalAfterDiscount(10.0d);
        orderItemPricesDto.setLenskartDiscount(10.0d);
        orderItemPricesDto.setLenskartPlusDiscount(10.0d);
        orderItemPricesDto.setPrepaidDiscount(10.0d);
        orderItemPricesDto.setScDiscount(10.0d);
        orderItemPricesDto.setShippingCharges(10.0d);
        orderItemPricesDto.setTaxCollected(10.0d);
        LocalDateTime atStartOfDayResult3 = LocalDate.of(1970, 1, 1).atStartOfDay();
        orderItemPricesDto.setUpdatedAt(Date.from(atStartOfDayResult3.atZone(ZoneId.of("UTC")).toInstant()));
        orderItemPricesDto.setUpdatedBy("2020-03-01");
        orderItemPricesDto.setVersion(1L);
        orderItemPricesDto.setWalletDiscount(10.0d);
        orderItemPricesDto.setWalletPlusDiscount(10.0d);

        OrderItemDto orderItemDto = new OrderItemDto();
        orderItemDto.setB2bReferenceItemId(123L);
        orderItemDto.setChannel(Channel.EXCHANGE);
        LocalDateTime atStartOfDayResult4 = LocalDate.of(1970, 1, 1).atStartOfDay();
        Date fromResult = Date.from(atStartOfDayResult4.atZone(ZoneId.of("UTC")).toInstant());
        orderItemDto.setCreatedAt(fromResult);
        orderItemDto.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        orderItemDto.setDeliveryCountry("GB");
        orderItemDto.setDeliveryPriority(DeliveryPriority.EXPRESS);
        orderItemDto.setDeliveryStoreId("42");
        orderItemDto.setDeliveryZipCode("Delivery Zip Code");
        orderItemDto.setFittingId(123L);
        orderItemDto.setFittingType(FittingType.REQD);
        orderItemDto.setFulfillmentType(FulfillmentType.LOCAL_FITTING);
        orderItemDto.setId(123L);
        orderItemDto.setItemPower(orderItemPowerDto);
        orderItemDto.setItemStatus(OrderItemStatus.DEFAULT);
        orderItemDto.setItemSubStatus(OrderItemSubStatus.PROCESSING_PENDING);
        orderItemDto.setItemType(ItemType.LOYALTY_SERVICES);
        orderItemDto.setJitStatus("Jit Status");
        orderItemDto.setNavChannel(NavChannel.WEBDTC);
        orderItemDto.setOmaStatus("Oma Status");
        orderItemDto.setOrderId(123L);
        ArrayList<OrderItemMetaDataDto> orderItemMetaDataDtoList = new ArrayList<>();
        orderItemDto.setOrderItemMetaData(orderItemMetaDataDtoList);
        orderItemDto.setOrderItemPrice(orderItemPricesDto);
        orderItemDto.setProductDeliveryType(ProductDeliveryType.DTC);
        orderItemDto.setProductId(123L);
        LocalDateTime atStartOfDayResult5 = LocalDate.of(1970, 1, 1).atStartOfDay();
        Date fromResult1 = Date.from(atStartOfDayResult5.atZone(ZoneId.of("UTC")).toInstant());
        orderItemDto.setPromisedDeliveryDate(fromResult1);
        LocalDateTime atStartOfDayResult6 = LocalDate.of(1970, 1, 1).atStartOfDay();
        Date fromResult2 = Date.from(atStartOfDayResult6.atZone(ZoneId.of("UTC")).toInstant());
        orderItemDto.setPromisedShipDate(fromResult2);
        orderItemDto.setShipmentId(123L);
        orderItemDto.setShippingDestinationType("Shipping Destination Type");
        LocalDateTime atStartOfDayResult7 = LocalDate.of(1970, 1, 1).atStartOfDay();
        Date fromResult3 = Date.from(atStartOfDayResult7.atZone(ZoneId.of("UTC")).toInstant());
        orderItemDto.setUpdatedAt(fromResult3);
        orderItemDto.setUpdatedBy("2020-03-01");
        orderItemDto.setUwItemId(123L);
        orderItemDto.setVersion(1L);
        this.createShipmentInvoiceStrategy.updateLoyaltyItemBarcodeIfRequired(orderItemDto);
        assertEquals("LP123", orderItemDto.getItemBarcode());
    }
}

