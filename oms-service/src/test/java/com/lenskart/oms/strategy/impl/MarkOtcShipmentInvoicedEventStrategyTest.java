package com.lenskart.oms.strategy.impl;

import com.lenskart.nexs.ims.response.ItemStockUpdateResponseV2;
import com.lenskart.nexs.ims.response.UpdateStocksResponseV2;
import com.lenskart.oms.connector.FdsConnector;
import com.lenskart.oms.connector.ImsConnector;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.enums.*;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.exception.DuplicateEventException;
import com.lenskart.oms.model.Action;
import com.lenskart.oms.model.OrderItemTransitions;
import com.lenskart.oms.model.OrderTransitions;
import com.lenskart.oms.model.ShipmentTransitions;
import com.lenskart.oms.producer.CommonKafkaProducer;
import com.lenskart.oms.producer.ShipmentEventOmsProducer;
import com.lenskart.oms.request.OtcShipmentEvent;
import com.lenskart.oms.request.ShipmentItemUpdate;
import com.lenskart.oms.request.ShipmentUpdateEvent;
import com.lenskart.oms.service.OrderBackSyncTrackingService;
import com.lenskart.oms.service.OrderItemService;
import com.lenskart.oms.service.ShipmentService;
import com.lenskart.oms.service.ShipmentTimelineService;
import com.lenskart.oms.utils.OmsTransitionUtil;
import org.apache.logging.log4j.Logger;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertThrows;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(SpringRunner.class)
public class MarkOtcShipmentInvoicedEventStrategyTest {
    @Mock
    private FdsConnector fdsConnector;
    @Mock
    private CommonKafkaProducer commonKafkaProducer;
    @Mock
    private OrderBackSyncTrackingService orderBackSyncTrackingService;
    @Mock
    private ShipmentEventOmsProducer shipmentEventOmsProducer;
    @Mock
    private ShipmentService shipmentService;
    @Mock
    private ImsConnector imsConnector;
    @Mock
    private OmsTransitionUtil omsTransitionUtil;
    @Mock
    private ShipmentTimelineService shipmentTimelineService;
    @Mock
    private OrderItemService orderItemService;
    @Mock
    protected Logger logger;;
    @InjectMocks
    private MarkOtcShipmentInvoicedEventStrategy markOtcShipmentInvoicedEventStrategy;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testExecute_SuccessfulCreationAndUpdate() throws Exception {
        OtcShipmentEvent otcShipmentEvent = new OtcShipmentEvent(OtcShipmentEventType.INVOICED,1L);
        ShipmentDto shipmentDto = new ShipmentDto();
        shipmentDto.setWmsOrderCode("123");
        OrderItemDto orderItemDto = new OrderItemDto();
        orderItemDto.setItemStatus(OrderItemStatus.CREATED);
        orderItemDto.setItemBarcode("OTC");
        orderItemDto.setProductId(123L);
        shipmentDto.setOrderItems(Arrays.asList(new OrderItemDto[]{orderItemDto}));
        ItemStockUpdateResponseV2 responseV2 = new ItemStockUpdateResponseV2();
        responseV2.setSuccess(true);
        UpdateStocksResponseV2 updateStocksResponseV2 = new UpdateStocksResponseV2();
        updateStocksResponseV2.setItemStockUpdateResponseV2List(Arrays.asList(new ItemStockUpdateResponseV2[]{responseV2}));
        when(shipmentService.findById(anyLong())).thenReturn(shipmentDto);
        when(fdsConnector.createDocument(any())).thenReturn("123");
        when(imsConnector.updateBarcodeStatus(any())).thenReturn(updateStocksResponseV2);
        Action action = new Action();
        OrderItemTransitions orderItemTransitions = new OrderItemTransitions();
        ShipmentTransitions shipmentStatus = new ShipmentTransitions();
        OrderTransitions orderStatus = new OrderTransitions();
        orderItemTransitions.setItemStatus(OrderItemStatus.INVOICED);
        orderItemTransitions.setItemSubStatus(OrderItemSubStatus.INVOICED);
        shipmentStatus.setShipmentStatus(ShipmentStatus.INVOICED);
        shipmentStatus.setShipmentSubStatus(ShipmentSubStatus.INVOICED);
        action.setItemStatus(orderItemTransitions);
        action.setShipmentStatus(shipmentStatus);

        when(omsTransitionUtil.getTransitionActionByOperationAndStatus(any(),any())).thenReturn(action);
        markOtcShipmentInvoicedEventStrategy.execute(otcShipmentEvent);
        verify(orderItemService, times(1)).update(any(), any());
        verify(shipmentService, times(1)).update(any(), any());
    }

    @Test
    public void testExecute_FailedInvoiceCreation() throws Exception {
        OtcShipmentEvent otcShipmentEvent = new OtcShipmentEvent(OtcShipmentEventType.INVOICED,1L);
        ShipmentDto shipmentDto = new ShipmentDto();
        shipmentDto.setWmsOrderCode("123");
        when(shipmentService.findById(anyLong())).thenReturn(shipmentDto);

        when(fdsConnector.createDocument(any())).thenReturn("");

        assertThrows(ApplicationException.class, () -> {
            markOtcShipmentInvoicedEventStrategy.execute(otcShipmentEvent);
        });

        verify(orderItemService, never()).update(any(), any());
        verify(shipmentService, never()).update(any(), any());
        verify(shipmentTimelineService, never()).update(any(), any());
    }

    @Test
    public void testPostExecute() throws Exception {
        OtcShipmentEvent otcShipmentEvent = new OtcShipmentEvent();
        otcShipmentEvent.setShipmentId(123L);
        ShipmentDto shipmentDto = new ShipmentDto();
        shipmentDto.setId(123L);
        shipmentDto.setWmsOrderCode("WMS123");
        ShipmentUpdateEvent shipmentUpdateEvent = new ShipmentUpdateEvent();
        shipmentUpdateEvent.setShipmentEvent(ShipmentEvent.CREATE_SHIPMENT_INVOICE);
        shipmentUpdateEvent.setWmsOrderCode(shipmentDto.getWmsOrderCode());
        List<ShipmentItemUpdate> shipmentItemUpdates = new ArrayList<>();
        ShipmentItemUpdate shipmentItemUpdate = new ShipmentItemUpdate();
        shipmentItemUpdate.setUnicomShipmentStatus(null);
        shipmentItemUpdate.setOrderOpsShipmentStatus("PACKED");
        shipmentItemUpdates.add(shipmentItemUpdate);
        shipmentUpdateEvent.setOrderItemList(shipmentItemUpdates);
        OrderItemDto orderItemDto = new OrderItemDto();
        orderItemDto.setOrderId(1234L);
        shipmentDto.setOrderItems(Arrays.asList(new OrderItemDto[]{orderItemDto}));
        when(shipmentService.findById(123L)).thenReturn(shipmentDto);
        markOtcShipmentInvoicedEventStrategy.postExecute(otcShipmentEvent);
        verify(shipmentService).findById(123L);
        verify(shipmentEventOmsProducer, times(2)).sendMessage(any(), any(), any(), eq("WMS123"));
        verify(commonKafkaProducer).sendMessage(anyString(), eq("1234"), anyString(), eq("123"));
    }

    @Test
    public void testValidateOtcEvent_ShouldThrowDuplicateEventException() {
        OtcShipmentEvent otcShipmentEvent = new OtcShipmentEvent();
        otcShipmentEvent.setShipmentId(123L);
        ShipmentDto shipmentDto = new ShipmentDto();
        shipmentDto.setShipmentStatus(ShipmentStatus.INVOICED);
        when(shipmentService.findById(123L)).thenReturn(shipmentDto);
        assertThrows(DuplicateEventException.class, () -> markOtcShipmentInvoicedEventStrategy.preExecute(otcShipmentEvent));
    }

    @Test
    public void testValidateOtcEvent_ShouldThrowApplicationException() {
        OtcShipmentEvent otcShipmentEvent = new OtcShipmentEvent();
        otcShipmentEvent.setShipmentId(123L);
        ShipmentDto shipmentDto = new ShipmentDto();
        shipmentDto.setShipmentStatus(ShipmentStatus.DISPATCHED);
        when(shipmentService.findById(123L)).thenReturn(shipmentDto);
        assertThrows(ApplicationException.class, () -> markOtcShipmentInvoicedEventStrategy.preExecute(otcShipmentEvent));
    }

    @Test
    public void testValidateOtcEvent_ShouldPass() {
        OtcShipmentEvent otcShipmentEvent = new OtcShipmentEvent();
        otcShipmentEvent.setShipmentId(123L);
        ShipmentDto shipmentDto = new ShipmentDto();
        shipmentDto.setShipmentStatus(ShipmentStatus.CREATED);
        when(shipmentService.findById(123L)).thenReturn(shipmentDto);
        assertDoesNotThrow(() -> markOtcShipmentInvoicedEventStrategy.preExecute(otcShipmentEvent));
    }
}


