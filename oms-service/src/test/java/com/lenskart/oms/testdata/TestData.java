package com.lenskart.oms.testdata;

import com.lenskart.oms.entity.Order;
import com.lenskart.oms.entity.OrderAddress;
import com.lenskart.oms.entity.OrderItems;
import com.lenskart.oms.enums.AddressType;
import com.lenskart.oms.enums.DeliveryPriority;
import com.lenskart.oms.enums.OrderStatus;
import com.lenskart.oms.enums.OrderSubStatus;

import java.util.Date;

public final class TestData {

    public static Order getOrder() {
        Order order = new Order();
        order.setIncrementId(123445L);
        order.setJunoOrderId(1223L);
        order.setOrderDate(new Date());
        order.setCustomerId(1234L);
        order.setCurrencyCode("INR");
        order.setOrderStatus(OrderStatus.CREATED);
        order.setOrderSubStatus(OrderSubStatus.PROCESSING);
        order.setLkCountry("india");
        order.setStoreId(1L);
        order.setCreatedBy("test");
        order.setCreatedAt(new Date());
        order.setUpdatedBy("test");
        return order;
    }

    public static OrderAddress getOrderAddress(Order order) {
        OrderAddress orderAddress = new OrderAddress();
        orderAddress.setAddressType(AddressType.SHIPPING);
        orderAddress.setCity("test");
        orderAddress.setCountry("india");
        orderAddress.setEmail("test");
        orderAddress.setFirstName("test");
        orderAddress.setPostcode("123");
        orderAddress.setCreatedBy("test");
        orderAddress.setTelephone("ttt");
        orderAddress.setCreatedAt(new Date());
        orderAddress.setUpdatedBy("test");
//        orderAddress.setShipment(order);
        return orderAddress;
    }

    public static OrderItems getOrderItem(Order order) {
        OrderItems orderItem = new OrderItems();
        orderItem.setProductId(123L);
        orderItem.setCreatedBy("test");
        orderItem.setCreatedAt(new Date());
        orderItem.setUpdatedBy("test");
        orderItem.setOrderId(order.getId());
        return orderItem;
    }
}
