package com.lenskart.oms.service.impl;

import com.lenskart.oms.dto.DistributorCustomerAddressDetailsDto;
import com.lenskart.oms.dto.DistributorCustomerDetailsDto;
import com.lenskart.oms.entity.DistributorCustomerAddressDetails;
import com.lenskart.oms.entity.DistributorCustomerDetails;
import com.lenskart.oms.enums.AddressType;
import com.lenskart.oms.service.DistributorCustomerAddressDetailsService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class DistributorDistributorCustomerDetailsServiceImplTest {

    @Spy
    @InjectMocks
    DistributorCustomerDetailsServiceImpl customerDetailsService;

    @Mock
    DistributorCustomerAddressDetailsService distributorCustomerAddressDetailsService;

    private DistributorCustomerDetailsDto dto;
    private DistributorCustomerDetailsDto existingDto;
    private DistributorCustomerDetails entity;
    private DistributorCustomerAddressDetailsDto customerBillingAddressDetailsDto;
    private DistributorCustomerAddressDetailsDto customerShippingAddressDetailsDto;
    private DistributorCustomerAddressDetailsDto customerBillingAddressDetailsExistingDto;
    private DistributorCustomerAddressDetailsDto customerShippingAddressDetailsExistingDto;
    private DistributorCustomerAddressDetails customerBillingAddressDetails;
    private DistributorCustomerAddressDetails customerShippingAddressDetails;


    @BeforeEach
    public void setUp() throws Exception {
        dto = new DistributorCustomerDetailsDto();
        existingDto = new DistributorCustomerDetailsDto();
        entity = new DistributorCustomerDetails();

        entity.setId(34l);
        entity.setCreatedAt(new Date());
        entity.setUpdatedAt(new Date());
        entity.setName("Amazon Company");
        entity.setCode("AMAZON");
        entity.setMobile("+91-8299784022");

        customerShippingAddressDetails = new DistributorCustomerAddressDetails();
        customerShippingAddressDetails.setCustomerDetailsId(1l);
        customerShippingAddressDetails.setAddressType(AddressType.SHIPPING);
        customerShippingAddressDetails.setAddressLine1("Address1");
        customerShippingAddressDetails.setAddressLine2("Address2");
        customerShippingAddressDetails.setCity("Hyderabad");
        customerShippingAddressDetails.setCountry("IN");
        customerShippingAddressDetails.setPincode(500080);
        customerShippingAddressDetails.setPhone("+91-8299784022");
        customerShippingAddressDetails.setState("Telangana");

        customerBillingAddressDetails = new DistributorCustomerAddressDetails();
        customerBillingAddressDetails.setCustomerDetailsId(1l);
        customerBillingAddressDetails.setAddressType(AddressType.BILLING);
        customerBillingAddressDetails.setAddressLine1("Address1");
        customerBillingAddressDetails.setAddressLine2("Address2");
        customerBillingAddressDetails.setCity("Hyderabad");
        customerBillingAddressDetails.setCountry("IN");
        customerBillingAddressDetails.setPincode(500080);
        customerBillingAddressDetails.setPhone("+91-8299784022");
        customerBillingAddressDetails.setState("Telangana");

        existingDto.setId(null);
        existingDto.setCreatedAt(new Date());
        existingDto.setUpdatedAt(new Date());
        existingDto.setName("Amazon Company");
        existingDto.setCode("AMAZON");
        existingDto.setMobile("+91-8299784022");

        customerBillingAddressDetailsExistingDto = new DistributorCustomerAddressDetailsDto();
        customerBillingAddressDetailsExistingDto.setAddressType(AddressType.BILLING);
        customerBillingAddressDetailsExistingDto.setAddressLine1("Address1");
        customerBillingAddressDetailsExistingDto.setAddressLine2("Address2");
        customerBillingAddressDetailsExistingDto.setCity("Hyderabad");
        customerBillingAddressDetailsExistingDto.setCountry("IN");
        customerBillingAddressDetailsExistingDto.setPincode(500080);
        customerBillingAddressDetailsExistingDto.setPhone("+91-8299784022");
        customerBillingAddressDetailsExistingDto.setState("Telangana");

        customerShippingAddressDetailsExistingDto = new DistributorCustomerAddressDetailsDto();
        customerShippingAddressDetailsExistingDto.setAddressType(AddressType.SHIPPING);
        customerShippingAddressDetailsExistingDto.setAddressLine1("Address1");
        customerShippingAddressDetailsExistingDto.setAddressLine2("Address2");
        customerShippingAddressDetailsExistingDto.setCity("Hyderabad");
        customerShippingAddressDetailsExistingDto.setCountry("IN");
        customerShippingAddressDetailsExistingDto.setPincode(500080);
        customerShippingAddressDetailsExistingDto.setPhone("+91-8299784022");
        customerShippingAddressDetailsExistingDto.setState("Telangana");

        dto.setId(null);
        dto.setCreatedAt(new Date());
        dto.setUpdatedAt(new Date());
        dto.setName("Amazon Company");
        dto.setCode("AMAZON");
        dto.setMobile("+91-8299784022");

        customerBillingAddressDetailsDto = new DistributorCustomerAddressDetailsDto();
        customerBillingAddressDetailsDto.setCustomerDetailsId(1l);
        customerBillingAddressDetailsDto.setAddressType(AddressType.BILLING);
        customerBillingAddressDetailsDto.setAddressLine1("Address1");
        customerBillingAddressDetailsDto.setAddressLine2("Address2");
        customerBillingAddressDetailsDto.setCity("Hyderabad");
        customerBillingAddressDetailsDto.setCountry("IN");
        customerBillingAddressDetailsDto.setPincode(500080);
        customerBillingAddressDetailsDto.setPhone("+91-8299784022");
        customerBillingAddressDetailsDto.setState("Telangana");

        customerShippingAddressDetailsDto = new DistributorCustomerAddressDetailsDto();
        customerShippingAddressDetailsDto.setAddressType(AddressType.SHIPPING);
        customerShippingAddressDetailsDto.setAddressLine1("Address1");
        customerShippingAddressDetailsDto.setAddressLine2("Address2");
        customerShippingAddressDetailsDto.setCity("Hyderabad");
        customerShippingAddressDetailsDto.setCountry("IN");
        customerShippingAddressDetailsDto.setPincode(500080);
        customerShippingAddressDetailsDto.setPhone("+91-8299784022");
        customerShippingAddressDetailsDto.setState("Telangana");


        List<DistributorCustomerAddressDetails> distributorCustomerAddressDetailsList = new ArrayList<>();
        distributorCustomerAddressDetailsList.add(customerBillingAddressDetails);
        distributorCustomerAddressDetailsList.add(customerShippingAddressDetails);
        entity.setDistributorCustomerAddressDetails(distributorCustomerAddressDetailsList);

        List<DistributorCustomerAddressDetailsDto> customerAddressDetailsListDTO = new ArrayList<>();
        customerAddressDetailsListDTO.add(customerBillingAddressDetailsDto);
        customerAddressDetailsListDTO.add(customerShippingAddressDetailsDto);
        dto.setCustomerAddressDetails(customerAddressDetailsListDTO);

        List<DistributorCustomerAddressDetailsDto> customerAddressDetailsListExistingDTO = new ArrayList<>();
        customerAddressDetailsListExistingDTO.add(customerBillingAddressDetailsExistingDto);
        customerAddressDetailsListExistingDTO.add(customerShippingAddressDetailsExistingDto);
        existingDto.setCustomerAddressDetails(customerAddressDetailsListExistingDTO);
    }

    @Test
    void convertToEntityAddressUpdated() {
        DistributorCustomerAddressDetailsDto inputDTO = dto.getCustomerAddressDetails().get(0);
        inputDTO.setAddressLine1("Updated Address Line 1");
        doReturn(existingDto).when(customerDetailsService).findById(any());
        customerBillingAddressDetails.setAddressLine1("Updated Address Line 1");
        when(distributorCustomerAddressDetailsService.convertToEntity(any(),any())).thenReturn(customerBillingAddressDetails);
        DistributorCustomerDetails response = customerDetailsService.convertToEntity(dto,entity);
        assertEquals("Amazon Company",response.getName());
        assertEquals("AMAZON",response.getCode());
        assertEquals(inputDTO.getAddressLine1(),response.getDistributorCustomerAddressDetails().get(0).getAddressLine1());
    }

    @Test
    void convertToEntityCustomerUpdated() {
        dto.setName("Amazon");
        doReturn(existingDto).when(customerDetailsService).findById(any());
        DistributorCustomerDetails response = customerDetailsService.convertToEntity(dto,entity);
        assertEquals("Amazon",response.getName());
        assertEquals("AMAZON",response.getCode());
    }

    @Test
    void convertToEntityNewCustomer() {
        existingDto = null;
        doReturn(existingDto).when(customerDetailsService).findById(any());
        when(distributorCustomerAddressDetailsService.convertToEntity(any(),any())).thenReturn(customerBillingAddressDetails);
        DistributorCustomerDetails response = customerDetailsService.convertToEntity(dto,entity);
        assertEquals("Amazon Company",response.getName());
        assertEquals("AMAZON",response.getCode());
        assertEquals(dto.getCustomerAddressDetails().size(),2);
        assertEquals(dto.getCustomerAddressDetails().get(0).getAddressLine1(),response.getDistributorCustomerAddressDetails().get(0).getAddressLine1());
        assertEquals(dto.getCustomerAddressDetails().get(0).getAddressType(),response.getDistributorCustomerAddressDetails().get(0).getAddressType());
    }

    @Test
    void toggleCustomer() {
        doReturn(existingDto).when(customerDetailsService).findById(any());
        existingDto.setCustomerEnabled(true);
        doReturn(existingDto).when(customerDetailsService).save(any());
        DistributorCustomerDetailsDto response = customerDetailsService.toggleCustomer(1l,true);
        assertEquals(true,response.isCustomerEnabled());
    }

}