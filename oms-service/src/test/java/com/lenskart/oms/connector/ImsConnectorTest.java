package com.lenskart.oms.connector;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.ims.request.UpdateStocksRequestV2;
import com.lenskart.nexs.ims.response.UpdateStocksResponseV2;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.*;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.client.RestTemplate;

import java.util.Optional;
import java.util.logging.Logger;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThrows;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(SpringRunner.class)
public class ImsConnectorTest {

    @Mock
    @CustomLogger
    private Logger logger;

    @Mock
    private RestTemplate restTemplate;

    @InjectMocks
    private ImsConnector imsConnector;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testUpdateBarcodeStatus_SuccessfulResponse() throws Exception {

        UpdateStocksRequestV2 request = new UpdateStocksRequestV2();
        UpdateStocksResponseV2 expectedResponse = new UpdateStocksResponseV2();


        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<UpdateStocksRequestV2> requestEntity = new HttpEntity<>(request, headers);
        when(restTemplate.exchange(anyString(), eq(HttpMethod.POST), eq(requestEntity), eq(String.class), Optional.ofNullable(any())))
                .thenReturn(new ResponseEntity<>("{\"data\":{}}", HttpStatus.OK));

        UpdateStocksResponseV2 actualResponse = imsConnector.updateBarcodeStatus(request);


        // Verify restTemplate call
        verify(restTemplate, times(1)).exchange(anyString(), eq(HttpMethod.POST), eq(requestEntity), eq(String.class), Optional.ofNullable(any()));

        // Assert expected response matches actual response
        assertEquals(expectedResponse, actualResponse);
    }

    @Test
    public void testUpdateBarcodeStatus_UnsuccessfulResponse() {

        UpdateStocksRequestV2 request = new UpdateStocksRequestV2();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<UpdateStocksRequestV2> requestEntity = new HttpEntity<>(request, headers);
        when(restTemplate.exchange(anyString(), eq(HttpMethod.POST), eq(requestEntity), eq(String.class), Optional.ofNullable(any())))
                .thenReturn(new ResponseEntity<>("", HttpStatus.INTERNAL_SERVER_ERROR));


        assertThrows(Exception.class, () -> imsConnector.updateBarcodeStatus(request));
    }

    @Test
    public void testUpdateBarcodeStatus_ExceptionThrownByRestTemplate() {

        UpdateStocksRequestV2 request = new UpdateStocksRequestV2();


        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<UpdateStocksRequestV2> requestEntity = new HttpEntity<>(request, headers);
        when(restTemplate.exchange(anyString(), eq(HttpMethod.POST), eq(requestEntity), eq(String.class), Optional.ofNullable(any())))
                .thenThrow(new RuntimeException("Connection timeout"));

        assertThrows(Exception.class, () -> imsConnector.updateBarcodeStatus(request));


    }
}
