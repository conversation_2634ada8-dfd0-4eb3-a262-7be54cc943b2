package com.lenskart.oms.connector;

import com.lenskart.fds.request.CreateDocumentRequest;
import com.lenskart.oms.exception.ApplicationException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestTemplate;

import java.util.Optional;
import java.util.logging.Logger;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(SpringRunner.class)
public class FDSConnectorTest {

    @Mock
    private RestTemplate restTemplate;
    private int timeout;
    private String imsHost;
    private String createInvoiceUrl;
    @InjectMocks
    private FdsConnector fdsConnector;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testCreateInvoice_SuccessfulResponse() throws ApplicationException {
        CreateDocumentRequest request = new CreateDocumentRequest();
        request.setDocumentSourceReferenceId("123");

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<CreateDocumentRequest> entity = new HttpEntity<>(request, headers);

        String responseBody = "{\"data\": \"invoiceId\"}";
        ResponseEntity<String> responseEntity = new ResponseEntity<>(responseBody, HttpStatus.OK);
        when(restTemplate.exchange(anyString(), eq(HttpMethod.POST), eq(entity), eq(String.class), Optional.ofNullable(any())))
                .thenReturn(responseEntity);

        String invoiceId = fdsConnector.createDocument(request);

        assertEquals("invoiceId", invoiceId);

    }

    @Test(expected = ApplicationException.class)
    public void testCreateInvoice_FailedResponse() throws ApplicationException {
        CreateDocumentRequest request = new CreateDocumentRequest();
        request.setDocumentSourceReferenceId("123");

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<CreateDocumentRequest> entity = new HttpEntity<>(request, headers);

        String responseBody = "{\"error\": \"some error message\"}";
        ResponseEntity<String> responseEntity = new ResponseEntity<>(responseBody, HttpStatus.INTERNAL_SERVER_ERROR);

        when(restTemplate.exchange(anyString(), eq(HttpMethod.POST), eq(entity), eq(String.class), Optional.ofNullable(any())))
                .thenReturn(responseEntity);

        fdsConnector.createDocument(request);
    }
}
