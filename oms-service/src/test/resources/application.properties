spring.profiles.active=${profile:test}
spring.application.name=oms
spring.config.import=consul:
spring.cloud.loadbalancer.ribbon.enabled=false
spring.cloud.consul.host=${consul-server:localhost}
spring.cloud.consul.port=${consul-port:8500}
spring.cloud.consul.enabled=true
spring.cloud.consul.config.enabled=true
spring.cloud.consul.config.prefix=scm-config
spring.cloud.consul.config.name=${spring.application.name:oms},${profile:preprod-k8s}
spring.cloud.consul.config.format=PROPERTIES
spring.cloud.consul.config.data-key=${kv-version:v1}
spring.cloud.consul.config.default-context=${spring.application.name:oms},${profile:preprod-k8s}
spring.cloud.consul.config.profile-separator=,
spring.cloud.consul.discovery.instance-id=${spring.application.name:oms}:${random.value}
spring.cloud.consul.discovery.prefer-ip-address=true
spring.cloud.consul.discovery.ip-address=${discovery_dns}

