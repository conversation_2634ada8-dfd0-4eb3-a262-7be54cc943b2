-- Create outbox_events table for implementing transactional outbox pattern
CREATE TABLE IF NOT EXISTS outbox_events (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    aggregate_id VARCHAR(255) NOT NULL,
    aggregate_type VARCHAR(100) NOT NULL,
    event_type VARCHAR(100) NOT NULL,
    event_status VARCHAR(50) NOT NULL DEFAULT 'PENDING',
    topic_name VARCHAR(255) NOT NULL,
    partition_key VARCHAR(255),
    event_payload LONGTEXT NOT NULL,
    event_headers TEXT,
    retry_count INT NOT NULL DEFAULT 0,
    max_retry_count INT NOT NULL DEFAULT 3,
    scheduled_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP NULL,
    error_message TEXT,
    idempotency_key VARCHAR(500),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(100) NOT NULL DEFAULT 'system',
    updated_by VARCHAR(100) NOT NULL DEFAULT 'system',
    version INT NOT NULL DEFAULT 1
);

-- Create indexes for performance
CREATE INDEX idx_outbox_status_created ON outbox_events(event_status, created_at);
CREATE INDEX idx_outbox_aggregate_id ON outbox_events(aggregate_id);
CREATE INDEX idx_outbox_event_type ON outbox_events(event_type);
CREATE INDEX idx_outbox_scheduled_at ON outbox_events(scheduled_at);
CREATE INDEX idx_outbox_idempotency_key ON outbox_events(idempotency_key);
CREATE INDEX idx_outbox_status_retry ON outbox_events(event_status, retry_count, max_retry_count);
CREATE INDEX idx_outbox_processed_at ON outbox_events(processed_at);

-- Create unique index for idempotency
CREATE UNIQUE INDEX idx_outbox_idempotency_unique ON outbox_events(idempotency_key);

-- Add comments for documentation
ALTER TABLE outbox_events COMMENT = 'Outbox events table for implementing transactional outbox pattern for reliable Kafka event publishing';
ALTER TABLE outbox_events MODIFY COLUMN aggregate_id VARCHAR(255) COMMENT 'ID of the aggregate that generated the event';
ALTER TABLE outbox_events MODIFY COLUMN aggregate_type VARCHAR(100) COMMENT 'Type of aggregate (ORDER, SHIPMENT, etc.)';
ALTER TABLE outbox_events MODIFY COLUMN event_type VARCHAR(100) COMMENT 'Type of event (ORDER_CREATED, SHIPMENT_UPDATED, etc.)';
ALTER TABLE outbox_events MODIFY COLUMN event_status VARCHAR(50) COMMENT 'Status of event processing (PENDING, PROCESSING, PROCESSED, FAILED, DEAD_LETTER)';
ALTER TABLE outbox_events MODIFY COLUMN topic_name VARCHAR(255) COMMENT 'Kafka topic name where event should be published';
ALTER TABLE outbox_events MODIFY COLUMN partition_key VARCHAR(255) COMMENT 'Kafka partition key for event ordering';
ALTER TABLE outbox_events MODIFY COLUMN event_payload LONGTEXT COMMENT 'JSON payload of the event to be published';
ALTER TABLE outbox_events MODIFY COLUMN event_headers TEXT COMMENT 'JSON map of Kafka headers';
ALTER TABLE outbox_events MODIFY COLUMN retry_count INT COMMENT 'Number of retry attempts made';
ALTER TABLE outbox_events MODIFY COLUMN max_retry_count INT COMMENT 'Maximum number of retry attempts allowed';
ALTER TABLE outbox_events MODIFY COLUMN scheduled_at TIMESTAMP COMMENT 'When the event is scheduled to be processed';
ALTER TABLE outbox_events MODIFY COLUMN processed_at TIMESTAMP COMMENT 'When the event was successfully processed';
ALTER TABLE outbox_events MODIFY COLUMN error_message TEXT COMMENT 'Error message if event processing failed';
ALTER TABLE outbox_events MODIFY COLUMN idempotency_key VARCHAR(500) COMMENT 'Unique key to prevent duplicate events';
