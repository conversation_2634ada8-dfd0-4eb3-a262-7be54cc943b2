<!DOCTYPE html>
<html xmlns:th="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta content="width=device-width,initial-scale=1" name="viewport" />
    <meta content="IE=edge" http-equiv="X-UA-Compatible" />
    <meta content="telephone=no" name="format-detection" />
    <style>
        @media(max-width:480px) {
            .tableBlock {
                width: 100%!important;
                Margin: 0 auto!important;
                text-align: center !important;
                display: inline-block!important;
                box-sizing: border-box;
                -webkit-box-sizing: border-box;
            }
            .tableBlockText{
                width: 100%!important;
                Margin: 0 auto!important;
                display: inline-block!important;
                box-sizing: border-box;
                -webkit-box-sizing: border-box;
            }
        }
        img {
            border: 0!important;
        }
    </style>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
</head>

<body style="background-color:rgb(237, 242, 245);background-image:none">
<table cellpadding="0" cellspacing="0" style="width: 100%; table-layout: fixed; Margin: 0px auto; box-sizing: border-box;" align="center" border="0" class="">
    <tbody>
    <tr>
        <td style="padding: 0px; width: 100%; height: 100%; Margin: 0px auto; background-color: rgb(237, 242, 245); box-sizing: border-box;" bg_type="backgroundColor" class="">
            <table cellpadding="0" cellspacing="0" style="width: 100%; table-layout: fixed; Margin: 0px auto; box-sizing: border-box;" align="center" border="0">
                <tbody>
                <tr>
                    <!--TODO : to be uncommented-->
                    <!--<td style="width: 100%; padding: 2px; Margin: 0px auto; vertical-align: top; color: rgb(0, 0, 0); box-sizing: border-box;" open="" sans= ",=" " arial,=" " helvetica,=" " sans-serif;=" " line-height:=" " 1.2;="-->
                    <!--" box-sizing:=" " border-box;"="" class=""></td>-->
                    <table cellpadding="0" cellspacing="0" style="width: 100%; max-width: 600px; table-layout: fixed; Margin: auto; box-sizing: border-box;" align="center" bo_type="gridBox" border="0" class="">
                        <tbody>
                        <tr>
                            <td style="width: 100%; text-align: center; padding: 20px 0px; background-color: rgb(255, 255, 255); vertical-align: top; line-height: 0; box-sizing: border-box;" align="center" bg_type="backgroundColor" bo_type="gridInnerBox"
                                class="">
                                <div style="line-height: 0; display: inline-block;">
                                     <span th:if="${channel == 'ODONLINE'}">
                                        <a href="https://www.owndays.com/sg/en/" style="display: inline-block; text-decoration: none; outline: 0px; border: none; border-collapse: collapse;" bo_type="image" class=""><img src="https://static5.lenskart.com/media/uploads/Square_logo.jpeg" style="border:none;max-width:25%" tempid="tmp1534830965961"></img></a>
                                    </span>
                                    <span th:if="${storeId == 4}">
                                        <a href="https://www.johnjacobseyewear.com/" style="display: inline-block; text-decoration: none; outline: 0px; border: none; border-collapse: collapse;" bo_type="image" class=""><img src="https://static.lenskart.com/images/cust_mailer/10-Oct-18/JJ_Logo.png" style="border:none;max-width:100%" tempid="tmp1534830965961"></img></a>
                                    </span>
                                    <span th:if="${storeId == 4 and channel == 'ODONLINE'}">
                                        <a href="https://www.lenskart.com/?utm_source=Automated&amp;utm_campaign=Email-Automated-order-confirmation-cod-20092018&amp;utm_medium=email&amp;utm_mcid=eml;" style="display: inline-block; text-decoration: none; outline: 0px; border: none; border-collapse: collapse;" bo_type="image" class=""><img src="https://static.lenskart.com/images/cust_mailer/Automated-order-confirmation/images/order_confirmation_600_021.jpg" style="border:none;max-width:100%" tempid="tmp1534830965961"></img></a>
                                    </span>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <table cellpadding="0" cellspacing="0" style="width: 100%; max-width: 600px; table-layout: fixed; Margin: auto; box-sizing: border-box;" align="center" bo_type="gridBox" border="0" class="">
                        <tbody>
                        <tr>
                            <td style="width: 100%; text-align: center; background-color: rgb(255, 255, 255); vertical-align: top; line-height: 0; box-sizing: border-box;" align="center" bg_type="backgroundColor" bo_type="gridInnerBox"
                                class="">

                                <div th:unless="${channel == 'ODONLINE'}" style="line-height: 0; display: inline-block;">
                                    <a href="#?utm_source=Automated&amp;utm_campaign=Email-Automated-order-confirmation-cod-20092018&amp;utm_medium=email&amp;utm_mcid=eml" style="display: inline-block; text-decoration: none; outline: 0px; border: none; border-collapse: collapse;" bo_type="image" class=""><img src="https://static1.lenskart.com/images/cust_mailer/8-Sep-18/invoice.png" style="border:none;max-width:100%" tempid="tmp1534830965961"></img></a>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <table cellpadding="0" cellspacing="0" style="width: 100%; max-width: 600px; table-layout: fixed; Margin: auto; box-sizing: border-box;" align="center" bo_type="gridBox" border="0" class="">
                        <tbody>
                        <tr>
                            <td style="background: rgb(255, 255, 255); width: 100%; text-align: center; padding: 15px; vertical-align: top; box-sizing: border-box;" bg_type="backgroundColor" bo_type="gridInnerBox" class="">
                                <table cellpadding="0" cellspacing="0" style="width: 80%; table-layout: fixed; box-sizing: border-box;" align="center" border="0">
                                    <tbody>
                                    <tr>
                                        <td style="width: 100%; font-size: 18px; text-align: left; color: rgb(71, 71, 71); box-sizing: border-box;">
                                            <div bo_type="text" class="" style="text-align: center; border-collapse: collapse; padding: 8px 15px;">
                                                <div th:unless="${channel == 'ODONLINE'}" class="" style=""><b><font style="font-size: 26px;" face="Verdana" color="#525f68">Here's Proof</font></b></div>
                                                <div th:if="${channel == 'ODONLINE'}" style="font-weight: normal; font-size: 14px; padding-top: 15px;font-family:verdana;color:707070;line-height:23px;" face="Verdana" class="">
                                                    <b>Dear <span th:text="${shippingAddress.firstName}"></span>, </b>
                                                    <p style="text-align: left">Thank you for your purchase with OWNDAYS Online Store!</p>
                                                    <p style="text-align: left"> We are pleased to inform you that your OWNDAYS invoice for the order : <b><span th:text="${incrementId}"></span></b> is ready!</p>
                                                    <p  style="text-align: left"> Please find it attached to this email.</p>
                                                    <span style="text-align: left;"><p>Best regards, <br>OWNDAYS Online Store</p><p>This is an auto-generated email - Do not Reply</p></span>
                                                </div>
                                                <div th:unless="${channel == 'ODONLINE'}" style="font-weight: normal; font-size: 15px; padding-top: 15px;font-family:verdana;color:707070;line-height:23px;" face="Verdana" class=""><b>Hi <span th:text="${shippingAddress.firstName}"></span>,</b> we have generated the invoice for the Order ID <b><span th:text="${incrementId}"></span></b>. Please find it attached with this mail. </b></div>
                                            </div>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>

                                <table cellpadding="0" cellspacing="0" style="width: 80%; table-layout: fixed; box-sizing: border-box;" align="center" border="0">
                                    <tbody>
                                    <tr>
                                        <td style="width: 100%; font-size: 18px; text-align: left; color: rgb(71, 71, 71); box-sizing: border-box;">
                                            <div bo_type="text" class="" style="text-align: center; border-collapse: collapse; padding: 8px 15px;">
                                                <div style="font-weight: normal; font-size: 15px; padding-top: 15px;font-family:verdana;color:707070;line-height:23px;" face="Verdana" class=""><b>If you have any questions, call us on <a th:href="@{${callUs}}" rel="nofollow" style="text-decoration:none;" target="_blank"><font color="#00b2ac" face="Verdana"><span th:text="${callUs}"></span></font></a>.</b></div></div>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>

                            </td>
                        </tr>
                        </tbody>
                    </table>


                    <div th:if="${channel != 'ODONLINE'}">
                        <table cellpadding="0" cellspacing="0" style="width: 100%; max-width: 600px; table-layout: fixed; Margin: auto; box-sizing: border-box;" align="center" bo_type="gridBox" border="0" class="">
                            <tbody>
                            <tr>
                                <td style="width: 100%; text-align: center; padding: 20px 0px; background-color: rgb(255, 255, 255); vertical-align: top; line-height: 0; box-sizing: border-box;" align="center" bg_type="backgroundColor" bo_type="gridInnerBox"
                                    class="">
                                    <div style="line-height: 0; display: inline-block;">
                                        <a href="https://www.lenskart.com/?utm_source=Automated&amp;utm_campaign=Email-Automated-order-confirmation-cod-20092018&amp;utm_medium=email&amp;utm_mcid=eml" style="display: inline-block; text-decoration: none; outline: 0px; border: none; border-collapse: collapse;" bo_type="image" class=""><img src="https://static.lenskart.com/images/cust_mailer/order_confirmation_6001_02.jpg" style="border:none;max-width:100%" tempid="tmp1534830965961"></img></a>
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <table align="center" border="0" cellpadding="0" cellspacing="0" style="max-width: 600px; width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;" bo-variation="true" bo-variation-class="email" bo-variation-name="grid" bo_type="gridBox"
                               class="">
                            <tbody>
                            <tr>
                                <td align="center" style="width: 100%; background: #fff; box-sizing: border-box;" bg_type="backgroundColor">
                                    <table align="center" border="0" cellpadding="0" cellspacing="0" style="width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;">
                                        <tbody>
                                        <tr>
                                            <td align="center" cellpadding="0" bo_type="gridInnerBox" style="padding: 0px; width: 100%; box-sizing: border-box;" class="">
                                                <table align="center" border="0" cellpadding="0" cellspacing="0" style="max-width: 600px; width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;" bo-variation="true" bo-variation-class="email">
                                                    <tbody>
                                                    <tr>
                                                        <td align="center" style="width: 100%; padding: 0px; box-sizing: border-box;">
                                                            <div bo-variation-group="typography">
                                                                <table align="center" border="0" cellpadding="0" cellspacing="0" style="word-wrap: break-word; width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;">
                                                                    <tbody>
                                                                    <tr>
                                                                        <td align="center" style="width: 100%; font-family: verdana; box-sizing: border-box;">
                                                                            <div bo_type="text" style="font-size: 17px; padding: 0px; text-align: center; border-collapse: collapse; outline: none;" class=""><span style="font-weight: bold;"><font style="font-size: 20px;color:#515d69;" class="" face="Verdana">&nbsp;
                                                                        ORDER DETAILS</font></span></div>
                                                                            <hr style="width: 15%" />
                                                                        </td>
                                                                    </tr>
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                            </tbody>
                        </table>

                        <div th:each="productDetail : ${productDetails}">
                            <!-- Product 1 -->
                            <table cellpadding="0" cellspacing="0" style="width: 100%; max-width: 600px; table-layout: fixed; Margin: auto; box-sizing: border-box;" align="center" bo_type="gridBox" border="0" class="">
                                <tbody>
                                <tr>
                                    <td style="background: rgb(255, 255, 255); text-align: center; padding: 15px; vertical-align: top; line-height: 0; box-sizing: border-box;" align="center" bg_type="backgroundColor" bo_type="gridInnerBox" class="">
                                        <div style="line-height: 0; display: inline-block;">
                                            <!--<a href="https://www.lenskart.com/?utm_source=Automated&amp;utm_campaign=Email-Automated-order-confirmation-cod-20092018&amp;utm_medium=email&amp;utm_mcid=eml" style="display: inline-block; text-decoration: none; outline: 0px; border: none; border-collapse: collapse;" bo_type="image" class=""><img th:src="@{${mediaServerHostname} + ${productDetail.product.productImage} }"  style="width:100%;border:none;max-width:100%;text-align:center;vertical-align:middle"-->
                                            <!--tempid="tmp1534831793578" class=""/>-->
                                            <!--</a>-->
                                            <a th:href="@{${productUrl}}" style="display: inline-block; text-decoration: none; outline: 0px; border: none; border-collapse: collapse;" bo_type="image" class=""><img th:src="@{${mediaServerHostname} + ${productDetail.product.productImage} }"  style="width:100%;border:none;max-width:100%;text-align:center;vertical-align:middle"
                                                                                                                                                                                                                     tempid="tmp1534831793578" class=""/>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                            <table cellpadding="0" cellspacing="0" style="width: 100%; max-width: 600px; table-layout: fixed; Margin: auto; box-sizing: border-box;" align="center" bo_type="gridBox" border="0" class="">
                                <tbody>
                                <tr>
                                    <td style="background: rgb(255, 255, 255); width: 100%; text-align: center; padding: 15px; vertical-align: top; box-sizing: border-box;" bg_type="backgroundColor" bo_type="gridInnerBox" class="">
                                        <table cellpadding="0" cellspacing="0" style="width: 100%; table-layout: fixed; box-sizing: border-box;padding-bottom:20px;" align="center" border="0">
                                            <tbody>
                                            <tr>
                                                <td style="width: 100%; font-size: 18px; text-align: left; color: rgb(71, 71, 71); box-sizing: border-box;">
                                                    <div bo_type="text" class="" style="text-align: center; border-collapse: collapse; padding-top: 12px; padding-bottom: 12px; outline: none;line-height:27px;">
                                                        <div class="" style="">
                                                            <font face="Verdana"><b class="" style=""><font style="font-size: 24px;" class=""><span th:text="${productDetail.product.brand}"></span></font><span style="font-size: 36px;" class="">&nbsp;</span></b></font>
                                                        </div>
                                                        <div class="" style="">
                                                            <font face="Verdana" style="font-size: 16px;"><span class="" style="font-size: 16px;"><span style="font-size: 16px;" class=""><span th:text="${productDetail.product.value}"></span></span></span>
                                                            </font>
                                                        </div>
                                                        <!-- <div class="" style="">
                                                            <font color="#525f68" face="Verdana" style="font-size: 16px;"><span class="" style="font-size: 16px;"><span style="font-size: 16px;" class="">Premium Anti-Glare</span></span>
                                                            </font>
                                                        </div> -->
                                                        <div class="" style="">
                                                            <font color="#525f68" face="Verdana" style="font-size: 16px;"><b class="" style="font-size: 16px;"><span style="font-size: 16px;" class=""><span th:text="${orderCurrency}"> </span><span th:text="${productDetail.itemPrice}"></span></span></b></font>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                                </tbody>
                            </table>

                            <table align="center" border="0" cellpadding="0" cellspacing="0" style="max-width: 600px; width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;border-color:#fff;" bo-variation="true" bo-variation-class="email" bo-variation-name="grid" bo_type="gridBox"
                                   class="">
                                <tbody>

                                <tr style="">
                                    <td style="width: 100%; background: rgb(255, 255, 255); box-sizing: border-box;padding-left:46px;" bg_type="backgroundColor">
                                        <table th:if="${productDetail.powerDetails}" class="" style="width:100%;font-family:verdana;font-size:14px;border-color:#d0d0d0;" rules="cols">
                                            <tr>
                                                <th style="background:#fff;padding:1%;color:#000;width:40%;text-align:left;padding-left:4%;">Power Details</th>
                                                <th style="background:#fff;color:#505052;width:30%;text-align:center;font-weight:normal;">Right Eye</th>
                                                <th style="background:#fff;color:#505052;width:30%;padding-right:4%;text-align:center;font-weight:normal;">Left Eye</th>
                                            </tr>
                                            <tr>
                                                <th style="background:#fff;padding:1%;color:#505052;width:40%;text-align:left;padding-left:4%;font-weight:normal;">Spherical (SPH)</th>
                                                <th style="background:#fff;color:#505052;width:30%;text-align:center;font-weight:normal;"><span th:text="${productDetail?.powerDetails?.get('rightSph')}"></span></th>
                                                <th style="background:#fff;color:#505052;width:30%;padding-right:4%;text-align:center;font-weight:normal;"><span th:text="${productDetail?.powerDetails?.get('leftSph')}"></span></th>
                                            </tr>
                                            <tr>
                                                <th style="background:#fff;padding:1%;color:#505052;width:40%;text-align:left;padding-left:4%;font-weight:normal;">Cylindrical (CYL)</th>
                                                <th style="background:#fff;color:#505052;width:30%;text-align:center;font-weight:normal;"><span th:text="${productDetail?.powerDetails?.get('rightCyl')}"></span></th>
                                                <th style="background:#fff;color:#505052;width:30%;padding-right:4%;text-align:center;font-weight:normal;"><span th:text="${productDetail?.powerDetails?.get('leftCyl')}"></span></th>
                                            </tr>
                                            <tr>
                                                <th style="background:#fff;padding:1%;color:#505052;width:40%;text-align:left;padding-left:4%;font-weight:normal;">Axis</th>
                                                <th style="background:#fff;color:#505052;width:30%;text-align:center;font-weight:normal;"><span th:text="${productDetail?.powerDetails?.get('rightAxis')}"></span></th>
                                                <th style="background:#fff;color:#505052;width:30%;padding-right:4%;text-align:center;font-weight:normal;"><span th:text="${productDetail?.powerDetails?.get('leftAxis')}"></span></th>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                                </tbody>
                            </table>

                        </div>

                        <!-- Product -->

                        <!-- <table cellpadding="0" cellspacing="0" style="width: 100%; max-width: 600px; table-layout: fixed; Margin: auto; box-sizing: border-box;" align="center" bo_type="gridBox" border="0" class="">
                                                            <tbody>
                                                                <tr>
                                                                    <td style="width: 100%; text-align: center; padding: 20px 0px; background-color: rgb(255, 255, 255); vertical-align: top; line-height: 0; box-sizing: border-box;" align="center" bg_type="backgroundColor" bo_type="gridInnerBox"
                                                                        class="">
                                                                        <div style="line-height: 0; display: inline-block;">
                                                                            <a href="https://www.lenskart.com/?utm_source=Automated&amp;utm_campaign=Email-Automated-order-confirmation-cod-20092018&amp;utm_medium=email&amp;utm_mcid=eml" style="display: inline-block; text-decoration: none; outline: 0px; border: none; border-collapse: collapse;" bo_type="image" class=""><img src="https://static.lenskart.com/images/cust_mailer/order_confirmation_6001_02.jpg" style="border:none;max-width:100%" tempid="tmp1534830965961"></img></a>
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                        </table> -->

                        <!-- billing section -->
                        <table cellpadding="0" cellspacing="0" style="width: 100%; max-width: 600px; table-layout: fixed; Margin: auto; box-sizing: border-box;" align="center" bo_type="gridBox" border="0" class="">
                            <tbody>
                            <tr>
                                <td style="width: 100%; text-align: center; padding: 20px 0px; background-color: rgb(255, 255, 255); vertical-align: top; line-height: 0; box-sizing: border-box;" align="center" bg_type="backgroundColor" bo_type="gridInnerBox"
                                    class="">
                                    <div style="line-height: 0; display: inline-block;">
                                        <a href="https://www.lenskart.com/?utm_source=Automated&amp;utm_campaign=Email-Automated-order-confirmation-cod-20092018&amp;utm_medium=email&amp;utm_mcid=eml" style="display: inline-block; text-decoration: none; outline: 0px; border: none; border-collapse: collapse;" bo_type="image" class=""><img src="https://static.lenskart.com/images/cust_mailer/order_confirmation_6001_02.jpg" style="border:none;max-width:100%" tempid="tmp1534830965961"></img></a>
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>



                        <table align="center" border="0" cellpadding="0" cellspacing="0" style="max-width: 600px; width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;border-color:#fff;" bo-variation="true" bo-variation-class="email" bo-variation-name="grid" bo_type="gridBox"
                               class="">
                            <tbody>

                            <tr>
                                <td style="width: 100%; background: rgb(255, 255, 255); box-sizing: border-box;padding:30px;" bg_type="backgroundColor">
                                    <table class="" style="width:100%;font-family:verdana;font-size:14px;padding:15px;border:1px solid #d0d0d0;line-height:25px;">
                                        <tr>
                                            <th style="background:#fff;padding:0.5%;color:#505052;width:40%;text-align:left;padding-left:4%;font-weight:normal;">SUBTOTAL</th>
                                            <th style="background:#fff;color:#000;width:50%;padding-right:4%;text-align:right;font-weight:normal;color:#505052;"><span th:text="${orderCurrency}"> </span><span th:text="${subtotal}"></span></th>

                                        </tr>
                                        <tr>
                                            <th style="background:#fff;padding:0.5%;color:#505052;width:50%;text-align:left;padding-left:4%;font-weight:normal;">DISCOUNT</th>
                                            <th style="background:#fff;color:#000;width:50%;padding-right:4%;text-align:right;font-weight:normal;color:#505052;">- <span th:text="${orderCurrency}"> </span><span th:text="${discount}"></span></th>

                                        </tr>
                                        <!-- <tr>
                                            <th style="background:#fff;padding:0.5%;color:#505052;width:50%;text-align:left;padding-left:4%;font-weight:normal;">LKCASH</th>
                                            <th style="background:#fff;color:#000;width:50%;padding-right:4%;text-align:right;font-weight:normal;color:#505052;">- Rs.100</th>

                                        </tr> -->
                                        <tr>
                                            <th style="background:#fff;padding:0.5%;width:50%;color:#505052;text-align:left;padding-left:4%;font-weight:normal;">TAXES</th>
                                            <th style="background:#fff;vertical-align:top;width:50%;padding-right:4%;text-align:right;font-weight:normal;color:#505052;">+ <span th:text="${orderCurrency}"> </span><span th:text="${taxes}"></span></th>

                                        </tr>
                                        <tr>
                                            <th style="background:#fff;padding:0.5%;color:#505052;width:40%;text-align:left;padding-left:4%;font-weight:normal;">SHIPPING FEE</th>
                                            <th style="background:#fff;color:#000;width:50%;padding-right:4%;text-align:right;font-weight:normal;color:#505052;"><span th:text="${orderCurrency}"> </span><span th:text="${shipping}"></span></th>

                                        </tr>


                                        <tr>
                                            <th style="background:#fff;padding:0.5%;width:50%;color:#000;text-align:left;padding-left:4%;">TOTAL AMOUNT</th>
                                            <th style="background:#fff;vertical-align:top;width:50%;padding-right:4%;text-align:right;"><span th:text="${orderCurrency}"> </span><span th:text="${orderTotal}"></span></th>

                                        </tr>
                                    </table>
                                </td>
                            </tr>
                            </tbody>
                        </table>


                        <table align="center" border="0" cellpadding="0" cellspacing="0" style="max-width: 600px; width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;border-color:#fff;" bo-variation="true" bo-variation-class="email" bo-variation-name="grid" bo_type="gridBox"
                               class="">
                            <tbody>

                            <tr>
                                <td style="width: 100%; background: #f8f8f8; box-sizing: border-box;" bg_type="backgroundColor">
                                    <table class="" style="width:100%;font-family:verdana;font-size:14px;padding-top:20px;">
                                        <tr>
                                            <th style="background:#f8f8f8;padding:1%;color:#000;width:50%">ESTIMATED DELIVERY*</th>
                                            <th style="background:#f8f8f8;color:#000;width:50%;text-align:right;padding-right:8%;font-weight:normal;">Order: <b>#<span th:text="${incrementId}"></span></b></th>

                                        </tr>
                                        <tr>
                                            <th style="background:#f8f8f8;padding:1%;width:50%;color:#0eb7b0;"><span th:text="${#dates.dayOfWeekName(expectedDeliveryDate)}"></span>, <span th:text="${#dates.monthNameShort(expectedDeliveryDate)}"></span> <span th:text="${#dates.day(expectedDeliveryDate)}"></span></th>
                                            <th style="background:#f8f8f8;color:#000;width:50%;text-align:right;padding-right:8%;font-weight:normal;">Order Date: <span th:text="${#dates.format(orderDate, 'dd-MMM-yyyy')}"></span></th>

                                        </tr>

                                    </table>
                                </td>
                            </tr>
                            </tbody>
                        </table>

                        <table align="center" border="0" cellpadding="0" cellspacing="0" style="max-width: 600px; width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;" bo-variation="true" bo-variation-class="email" bo-variation-name="grid" bo_type="gridBox"
                               class="">
                            <tbody>
                            <tr>
                                <td align="center" style="width: 100%; background:#f8f8f8; box-sizing: border-box;" bg_type="backgroundColor">
                                    <table align="center" border="0" cellpadding="0" cellspacing="0" style="width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;">
                                        <tbody>
                                        <tr>
                                            <td align="center" cellpadding="0" bo_type="gridInnerBox" style="padding: 0px; width: 100%; box-sizing: border-box;" class="">
                                                <table align="center" border="0" cellpadding="0" cellspacing="0" style="max-width: 600px; width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;" bo-variation="true" bo-variation-class="email">
                                                    <tbody>
                                                    <tr>
                                                        <td align="center" style="width: 100%; padding: 15px; box-sizing: border-box;">
                                                            <div bo-variation-group="typography">
                                                                <table align="center" border="0" cellpadding="0" cellspacing="0" style="word-wrap: break-word; width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;">
                                                                    <tbody>
                                                                    <tr>
                                                                        <td align="center" style="width: 100%; font-family: verdana; box-sizing: border-box;padding-left:46px;">
                                                                            <div bo_type="text" style="font-size: 17px; padding: 3px 0px; text-align: left; border-collapse: collapse; line-height: 26px;" class=""><b><font style="font-size: 14px;" face="Verdana">SHIPPING ADDRESS</font></b>
                                                                                <div class="">
                                                                                    <font style="font-size: 14px;color:#505052;" face="Verdana"><span th:text="${shippingAddress.firstName}"></span> <span th:text="${shippingAddress.lastName}"></span></font>
                                                                                </div>
                                                                                <div class="">
                                                                                    <font style="font-size: 14px;color:#505052;" face="Verdana"><span th:text="${shippingAddress.street}"></span></font>
                                                                                </div>
                                                                                <div class="">
                                                                                    <font style="font-size: 14px;color:#505052;" face="Verdana"><span th:text="${shippingAddress.city}"></span>, <span th:text="${shippingAddress.region}"></span>, <span th:text="${shippingAddress.postcode}"></span></font>
                                                                                </div>
                                                                                <div class="">
                                                                                    <font style="font-size: 14px;color:#505052;" face="Verdana"><span th:text="${shippingAddress.countryCode}"></span></font>
                                                                                </div>
                                                                                <div class="">
                                                                                    <font style="font-size: 14px;color:#505052;" face="Verdana">T: <span th:text="${shippingAddress.telephone}"></span></font>
                                                                                </div>
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <table align="center" border="0" cellpadding="0" cellspacing="0" style="max-width: 600px; width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;" bo-variation="true" bo-variation-class="email" bo-variation-name="grid" bo_type="gridBox"
                               class="">
                            <tbody>
                            <tr>
                                <td align="center" style="width: 100%; background:#f8f8f8; box-sizing: border-box;" bg_type="backgroundColor">
                                    <table align="center" border="0" cellpadding="0" cellspacing="0" style="width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;">
                                        <tbody>
                                        <tr>
                                            <td align="center" cellpadding="0" bo_type="gridInnerBox" style="padding: 0px; width: 100%; box-sizing: border-box;" class="">
                                                <table align="center" border="0" cellpadding="0" cellspacing="0" style="max-width: 600px; width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;" bo-variation="true" bo-variation-class="email">
                                                    <tbody>
                                                    <tr>
                                                        <td align="center" style="width: 100%; padding: 15px; box-sizing: border-box;">
                                                            <div bo-variation-group="typography">
                                                                <table align="center" border="0" cellpadding="0" cellspacing="0" style="word-wrap: break-word; width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;">
                                                                    <tbody>
                                                                    <tr>
                                                                        <td align="center" style="width: 100%; font-family: verdana; box-sizing: border-box;padding-left:46px;">
                                                                            <div bo_type="text" style="font-size: 16px; padding: 3px 0px; text-align: left; border-collapse: collapse; line-height: 24px;" class=""><b><font style="font-size: 14px;" face="Verdana">BILLING ADDRESS</font></b>
                                                                                <div class="">
                                                                                    <font style="font-size: 14px;color:#505052;" face="Verdana"><span th:text="${billingAddress.firstName}"></span></font>
                                                                                </div>
                                                                                <div class="">
                                                                                    <font style="font-size: 14px;color:#505052;" face="Verdana"><span th:text="${billingAddress.street}"></span></font>
                                                                                </div>
                                                                                <div class="">
                                                                                    <font style="font-size: 14px;color:#505052;" face="Verdana"><span th:text="${billingAddress.city}"></span>, <span th:text="${billingAddress.region}"></span>, <span th:text="${billingAddress.postcode}"></span></font>
                                                                                </div>
                                                                                <div class="">
                                                                                    <font style="font-size: 14px;color:#505052;" face="Verdana"><span th:text="${billingAddress.countryCode}"></span></font>
                                                                                </div>
                                                                                <div class="">
                                                                                    <font style="font-size: 14px;color:#505052;" face="Verdana">T: <span th:text="${billingAddress.telephone}"></span></font>
                                                                                </div>
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <table align="center" border="0" cellpadding="0" cellspacing="0" style="max-width: 600px; width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;" bo-variation="true" bo-variation-class="email" bo-variation-name="grid" bo_type="gridBox"
                               class="">
                            <tbody>
                            <tr>
                                <td align="center" style="width: 100%; background:#f8f8f8; box-sizing: border-box;" bg_type="backgroundColor">
                                    <table align="center" border="0" cellpadding="0" cellspacing="0" style="width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;">
                                        <tbody>
                                        <tr>
                                            <td align="center" cellpadding="0" bo_type="gridInnerBox" style="padding: 0px; width: 100%; box-sizing: border-box;" class="">
                                                <table align="center" border="0" cellpadding="0" cellspacing="0" style="max-width: 600px; width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;" bo-variation="true" bo-variation-class="email">
                                                    <tbody>
                                                    <tr>
                                                        <td align="center" style="width: 100%; padding: 15px; box-sizing: border-box;">
                                                            <div bo-variation-group="typography">
                                                                <table align="center" border="0" cellpadding="0" cellspacing="0" style="word-wrap: break-word; width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;">
                                                                    <tbody>
                                                                    <tr>
                                                                        <td align="center" style="width: 100%; font-family: verdana; box-sizing: border-box;padding-left:46px;">
                                                                            <div bo_type="text" style="font-size: 17px; padding: 3px 0px; text-align: left; border-collapse: collapse; line-height: 26px;" class="">
                                                                                <font face="Verdana" style="font-size: 14px;"><b style="font-size: 14px;">PAYMENT MODE</b></font><br></br>
                                                                                <div class="">
                                                                                    <font face="Verdana"><span style="font-size: 14px;color:#505052;" class=""><span th:text="${paymentMethod}"></span></span></font>
                                                                                </div>
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <table align="center" border="0" cellpadding="0" cellspacing="0" style="max-width: 600px; width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;" bo-variation="true" bo-variation-class="email" bo-variation-name="grid" bo_type="gridBox"
                               class="">
                            <tbody>
                            <tr>
                                <td align="center" style="width: 100%; background: rgb(255, 255, 255); box-sizing: border-box;" bg_type="backgroundColor">
                                    <table align="center" border="0" cellpadding="0" cellspacing="0" style="width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;">
                                        <tbody>
                                        <tr>
                                            <td align="center" cellpadding="0" bo_type="gridInnerBox" style="padding: 0px; width: 100%; box-sizing: border-box;" class="">
                                                <table align="center" border="0" cellpadding="0" cellspacing="0" style="max-width: 600px; width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;" bo-variation="true" bo-variation-class="email">
                                                    <tbody>
                                                    <tr>
                                                        <td align="center" style="width: 100%; padding: 15px; box-sizing: border-box;">
                                                            <div bo-variation-group="typography">
                                                                <div style="width:100%;text-align:center; padding: 5px;padding-top:15px;">
                                                                    <a bo_type="button" th:href="@{${trackYourOrder}}" style="max-width: 100%; display: inline-block; font-family: verdana; color: rgb(255, 255, 255); border-radius:5px; padding: 13px 32px; text-decoration: none; outline: 0px; border: none; font-size: 16px; background-color: #06b2b6; text-align: center; border-collapse: collapse;" class="tiny-focus"><b>Track Your Order</b></a>
                                                                </div>
                                                                <table cellpadding="0" cellspacing="0" style="width: 100%; max-width: 600px; table-layout: fixed; Margin: auto; box-sizing: border-box;" align="center" bo_type="gridBox" border="0" class="">
                                                                    <tbody>
                                                                    <tr>
                                                                        <td style="width: 100%; text-align: center; padding: 20px 0px; background-color: rgb(255, 255, 255); vertical-align: top; line-height: 0; box-sizing: border-box;" align="center" bg_type="backgroundColor" bo_type="gridInnerBox"
                                                                            class="">
                                                                            <div style="line-height: 0; display: inline-block;">
                                                                                <a href="https://www.lenskart.com/?utm_source=Automated&amp;utm_campaign=Email-Automated-order-confirmation-cod-20092018&amp;utm_medium=email&amp;utm_mcid=eml" style="display: inline-block; text-decoration: none; outline: 0px; border: none; border-collapse: collapse;" bo_type="image" class=""><img src="https://static.lenskart.com/images/cust_mailer/order_confirmation_6001_02.jpg" style="border:none;max-width:100%" tempid="tmp1534830965961"></img></a>
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                    </tbody>
                                                                </table>
                                                                <table align="center" border="0" cellpadding="0" cellspacing="0" style="word-wrap: break-word; width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;">
                                                                    <tbody>
                                                                    <tr>
                                                                        <td align="center" style="width: 100%; font-family: verdana; box-sizing: border-box;">
                                                                            <div bo_type="text" style="font-size: 17px; padding: 15px 0px 0px 0px; text-align: center; border-collapse: collapse; outline: none;" class="">
                                                                                <font face="Verdana"><span style="font-size: 20px;color:#515d69;"><b>Need help?</b></span></font><br></br></div>
                                                                        </td>
                                                                    </tr>
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                            </tbody>
                        </table>

                        <table align="center" border="0" cellpadding="0" cellspacing="0" style="max-width: 600px; width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;" bo-variation="true" bo-variation-class="email" bo-variation-name="grid" bo_type="gridBox"
                               class="">
                            <tbody>
                            <tr>
                                <td align="center" style="width: 100%; background: rgb(255, 255, 255); box-sizing: border-box;" bg_type="backgroundColor">
                                    <table align="center" border="0" cellpadding="0" cellspacing="0" style="width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;">
                                        <tbody>
                                        <tr>
                                            <td align="center" cellpadding="0" bo_type="gridInnerBox" style="padding: 0px; width: 100%; box-sizing: border-box;" class="">
                                                <table align="center" border="0" cellpadding="0" cellspacing="0" style="max-width: 600px; width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;" bo-variation="true" bo-variation-class="email">
                                                    <tbody>
                                                    <tr>
                                                        <td align="center" style="width: 100%; box-sizing: border-box; padding-bottom:20px;">
                                                            <div bo-variation-group="typography">
                                                                <table align="center" border="0" cellpadding="0" cellspacing="0" style="word-wrap: break-word; width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;">
                                                                    <tbody>
                                                                    <tr>
                                                                        <td align="center" style="width: 100%; font-family: verdana; box-sizing: border-box;">
                                                                            <div bo_type="text" style="font-size: 17px; border-collapse: collapse; line-height: 28px;color:#7e858d" class="">
                                                                                <font style="font-size: 16px;">We're available by phone Monday-Saturday, 9 a.m.-8 p.m.</font>
                                                                                <div class="">
                                                                                    <font style="font-size: 16px;">Please feel free to call us if you have any queries.</font>
                                                                                </div>
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <table align="center" border="0" cellpadding="0" cellspacing="0" style="max-width: 600px; width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;" bo-variation-name="grid" bo_type="gridBox" class="">
                            <tbody>
                            <tr>
                                <td align="center" style="width: 100%; background-position: initial; background-size: initial; background-repeat: initial; background-attachment: initial; background-origin: initial; background-clip: initial; background-color: rgb(255, 255, 255); box-sizing: border-box;"
                                    bg_type="backgroundColor" background="">
                                    <table align="center" border="0" cellpadding="0" cellspacing="0" style="width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;">
                                        <tbody>
                                        <tr>
                                            <td align="center" cellpadding="5" style="padding: 5px 0px; width: 100%; box-sizing: border-box;"><span style="display:inline-block;text-align: center;width:32%;max-width:600px;min-width: 190px;Margin: 0 auto;vertical-align: top;" class="itemBlock"><table align="left" border="0" cellpadding="0" cellspacing="0" style="width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;" class="table-container template-image"><tbody><tr><td align="center" style="width: 100%; box-sizing: border-box;" bo_type="gridInnerBox" class=""><table align="center" border="0" cellpadding="0" cellspacing="0" style="max-width: 600px; width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;"><tbody><tr><td align="center" style="width: 100%; background: rgb(255, 255, 255); box-sizing: border-box;" bg_type="backgroundColor"><table align="center" border="0" cellpadding="0" cellspacing="0" style="width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;"><tbody><tr><td align="center" style="width: 100%; padding: 10px; line-height: 0; display: inline-block; box-sizing: border-box;"><a bo_type="image" th:href="@{${supportMail}}" style="display: inline-block; text-decoration: none; outline: 0px; border: none; border-collapse: collapse;" class=""><img src="https://static.lenskart.com/images/cust_mailer/Automated-order-confirmation/images/order_confirmation_600_09.jpg" style="max-width:100%;align:center;" tempid="tmp1534837350147"></img></a></td></tr><tr><td align="center" style="padding: 10px; font-size: 16px; color: rgb(0, 0, 0); width: 100%; border-collapse: collapse; outline: none; box-sizing: border-box; text-align: center;" bo_type="text" class=""><font color="#00b2ac" class="" face="Verdana"><a th:href="@{${supportMail}}" rel="nofollow" style="text-decoration:none;" target="_blank"><b class=""><font color="#00b2ac" face="Verdana">Email us</font></b></a></font></td></tr></tbody></table></td></tr></tbody></table></td></tr></tbody></table></span>
                                                <span
                                                        style="display:inline-block;text-align: center;width:32%;max-width:600px;min-width: 190px;Margin: 0 auto;vertical-align: top;" class="itemBlock">
                                        <table align="left" border="0" cellpadding="0" cellspacing="0" style="width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;" class="table-container template-image">
                                            <tbody>
                                                <tr>
                                                    <td align="center" style="width: 100%; box-sizing: border-box;" bo_type="gridInnerBox" class="">
                                                        <table align="center" border="0" cellpadding="0" cellspacing="0" style="max-width: 600px; width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;">
                                                            <tbody>
                                                                <tr>
                                                                    <td align="center" style="width: 100%; background: rgb(255, 255, 255); box-sizing: border-box;" bg_type="backgroundColor">
                                                                        <table align="center" border="0" cellpadding="0" cellspacing="0" style="width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;">
                                                                            <tbody>
                                                                                <tr>
                                                                                    <td align="center" style="width: 100%; padding: 10px; line-height: 0; display: inline-block; box-sizing: border-box;">
                                                                                        <a bo_type="image" href="tel:9999899998" style="display: inline-block; text-decoration: none; outline: 0px; border: none; border-collapse: collapse;" class=""><img src="https://static.lenskart.com/images/cust_mailer/Automated-order-confirmation/images/order_confirmation_600_11.jpg" style="max-width:100%;align:center;" tempid="tmp1534837241118"></img></a>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td align="center" style="padding: 10px; font-size: 16px; color: rgb(0, 0, 0); width: 100%; border-collapse: collapse; box-sizing: border-box;" bo_type="text" class="">
                                                                                        <a th:href="@{${callUs}}" rel="nofollow" style="text-decoration:none;" target="_blank"><font color="#00b2ac" face="Verdana"><b>Call us</b></font></a>
                                                                                    </td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                        </span>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                            </tbody>
                        </table>


                        <table align="center" border="0" cellpadding="0" cellspacing="0" style="max-width: 600px; width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;" bo-variation="true" bo-variation-class="email" bo-variation-name="grid" bo_type="gridBox"
                               class="">
                            <tbody>
                            <tr>
                                <td align="center" style="width: 100%; background: rgb(255, 255, 255); box-sizing: border-box;" bg_type="backgroundColor">
                                    <table align="center" border="0" cellpadding="0" cellspacing="0" style="width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;">
                                        <tbody>
                                        <tr>
                                            <td align="center" cellpadding="0" bo_type="gridInnerBox" style="padding: 0px; width: 100%; box-sizing: border-box;" class="">
                                                <table align="center" border="0" cellpadding="0" cellspacing="0" style="max-width: 600px; width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;" bo-variation="true" bo-variation-class="email">
                                                    <tbody>
                                                    <tr>
                                                        <td align="center" style="width: 100%; padding: 15px; box-sizing: border-box;">
                                                            <div bo-variation-group="typography">
                                                                <table align="center" border="0" cellpadding="0" cellspacing="0" style="word-wrap: break-word; width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;">
                                                                    <tbody>
                                                                    <tr>
                                                                        <td align="center" style="width: 100%; font-family: verdana; box-sizing: border-box;">
                                                                            <div bo_type="text" style="font-size: 17px; padding: 24px 0px; text-align: center; border-collapse: collapse; outline: none;" class=""><span style="font-family: Verdana; font-size: 20px; font-weight: 700;color:#535f6b">Worth a look</span><br></br></div>
                                                                        </td>
                                                                    </tr>
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <table align="center" border="0" cellpadding="0" cellspacing="0" style="max-width: 600px; width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;" bo-variation-name="grid" bo_type="gridBox" class="">
                            <tbody>
                            <tr>
                                <td align="center" style="width: 100%; background-position: initial; background-size: initial; background-repeat: initial; background-attachment: initial; background-origin: initial; background-clip: initial; background-color: rgb(255, 255, 255); box-sizing: border-box;"
                                    bg_type="backgroundColor" background="">
                                    <table align="center" border="0" cellpadding="0" cellspacing="0" style="width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;">
                                        <tbody>
                                        <tr>
                                            <td align="center" cellpadding="5" style="padding: 5px 0px; width: 100%; box-sizing: border-box;"><span style="display:inline-block;text-align: center;max-width:600px;width:48%;min-width: 270px;Margin: 0 auto;vertical-align: top;" class="itemBlock"><table align="left" border="0" cellpadding="0" cellspacing="0" style="width: 100%; table-layout: fixed; vertical-align: top; Margin: auto !important; box-sizing: border-box;" class="table-container template-image"><tbody><tr><td align="center" style="width: 100%; box-sizing: border-box;" bo_type="gridInnerBox" class=""><table align="center" cellpadding="0" cellspacing="0" border="0" style="table-layout: fixed; line-height: 1.2; word-break: break-word; max-width: 600px; Margin: 0px auto; background-color: rgb(255, 255, 255); width: 100%; box-sizing: border-box;" bg_type="backgroundColor"><tbody><tr><td style="text-align: center; padding: 0px 5px 20px; box-sizing: border-box;" bo_type="gridInnerBox" class=""><span style="width: 100%; display: inline-block; padding: 0px; text-align: center; line-height: 0;"><a th:href="@{${checkEmOut}}" bo_type="image" class="" style="border-collapse: collapse;"><img src="https://static.lenskart.com/images/cust_mailer/Automated-order-confirmation/images/order_confirmation_600_19.jpg" alt="watch" style="max-width: 100%;" tempid="tmp1534837983571" class=""></img></a></span>
                                    <span
                                            style="font-size: 18px; font-weight: 600; padding: 10px 5px 10px 10px; display: inline-block; border-collapse: collapse;" bo_type="text" class="">
                                        <font style="font-size: 14px;color:#374756" class="" face="Verdana">We have sunglasses too!</font>
                                        </span>
										 <span
                                                 style="font-size: 18px; font-weight: 600;  display: inline-block; border-collapse: collapse;" bo_type="text" class="">
                                        <font style="font-size: 14px;color:#1ebfb9" class="" face="Verdana"><a th:href="@{${checkEmOut}}" bo_type="image" class="" style="border-collapse: collapse;text-decoration:none;color:#1ebfb9;">Check'em out</a></font>
                                        </span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </td>
            </tr>
        </tbody>
    </table>
    </span><span style="display:inline-block;text-align: center;max-width:600px;width:48%;min-width: 270px;Margin: 0 auto;vertical-align: top;" class="itemBlock"><table align="left" border="0" cellpadding="0" cellspacing="0" style="width: 100%; table-layout: fixed; vertical-align: top; Margin: auto !important; box-sizing: border-box;" class="table-container template-image"><tbody><tr><td align="center" style="width: 100%; box-sizing: border-box;" bo_type="gridInnerBox" class=""><table align="center" cellpadding="0" cellspacing="0" border="0" style="table-layout: fixed; line-height: 1.2; word-break: break-word; max-width: 600px; Margin: 0px auto; background-color: rgb(255, 255, 255); width: 100%; box-sizing: border-box;" bg_type="backgroundColor"><tbody><tr><td style="text-align: center; padding: 0px 5px 20px; box-sizing: border-box;" bo_type="gridInnerBox" class=""><table align="center" cellpadding="0" cellspacing="0" border="0" style="table-layout: fixed; line-height: 1.2; word-break: break-word; max-width: 600px; Margin: 0px auto; background-color: rgb(255, 255, 255); width: 100%; box-sizing: border-box;" bg_type="backgroundColor"><tbody><tr><td style="text-align: center; padding: 0px 5px 20px; box-sizing: border-box;" bo_type="gridInnerBox" class=""><span style="width: 100%; display: inline-block; padding: 0px; text-align: center; line-height: 0;"><a href="https://store.lenskart.com/?utm_source=Automated&amp;utm_campaign=Email-Automated-order-confirmation-cod-20092018&amp;utm_medium=email&amp;utm_mcid=eml" bo_type="image" class="" style="border-collapse: collapse;"><img src="https://static.lenskart.com/images/cust_mailer/Automated-order-confirmation/images/order_confirmation_600_21.jpg" alt="watch" style="max-width: 100%;" tempid="tmp1534838062310"></img></a></span>
    <span
            style="font-size: 18px; font-weight: 600; padding: 10px 5px 10px 10px; display: inline-block; border-collapse: collapse;" bo_type="text" class="">
        <font style="font-size: 14px;color:#374756" face="Verdana">Visit a nearby store</font>
        </span>
		 <span style="font-size: 18px; font-weight: 600;  display: inline-block; border-collapse: collapse;" bo_type="text" class="">
             <font style="font-size: 14px;color:#1ebfb9" class="" face="Verdana"><a th:href="@{${visitNearByUrl}}" bo_type="image" class="" style="border-collapse: collapse;text-decoration:none;color:#1ebfb9;">Find a location</a></font>
         </span>
        </td>
        </tr>
        </tbody>
        </table>
        </td>
        </tr>
        </tbody>
        </table>
        </td>
        </tr>
        </tbody>
        </table>
        </span>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <table cellpadding="0" cellspacing="0" style="max-width: 600px; width: 100%; table-layout: fixed; Margin: 0px auto; box-sizing: border-box;" align="center" border="0" bo_type="gridBox" class="">
                            <tbody>
                            <tr>
                                <td style="width: 100%; text-align: center; color: rgb(70, 70, 70); background-color: rgb(255, 255, 255); Margin: 0px auto; box-sizing: border-box; line-height: 0; display: inline-block;" align="center" bg_type="backgroundColor" bo_type="gridInnerBox"
                                    class="" background="">
                                    <a th:href="@{${instagramUrl}}" style="display: inline-block; text-align: center; padding: 20px 15px; text-decoration: none; outline: 0px; border: none; border-collapse: collapse;" bo_type="image" class=""><img src="https://static.lenskart.com/images/cust_mailer/Automated-order-confirmation/images/ic_insta.png" style="border:none;max-width:42px" tempid="tmp1534838076860"></img></a>
                                    <a th:href="@{${twitterUrl}}" style="display: inline-block; text-align: center; padding: 20px 15px; text-decoration: none; outline: 0px; border: none; border-collapse: collapse;"
                                       bo_type="image" class=""><img src="https://static.lenskart.com/images/cust_mailer/Automated-order-confirmation/images/ic_tw.png" style="border:none;max-width:42px" tempid="tmp1534838093509"></img></a>
                                    <a th:href="@{${facebookUrl}}" style="display: inline-block; text-align: center; padding: 20px 15px; text-decoration: none; outline: 0px; border: none; border-collapse: collapse;"
                                       bo_type="image" class=""><img src="https://static.lenskart.com/images/cust_mailer/Automated-order-confirmation/images/ic_fb.png" style="border:none;max-width:42px" tempid="tmp1534838116330"></img></a>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <table align="center" border="0" cellpadding="0" cellspacing="0" style="max-width: 600px; width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;" bo-variation="true" bo-variation-class="email" bo-variation-name="grid"
                               bo_type="gridBox" class="">
                            <tbody>
                            <tr>
                                <td align="center" style="width: 100%; background: rgb(255, 255, 255); box-sizing: border-box;" bg_type="backgroundColor">
                                    <table align="center" border="0" cellpadding="0" cellspacing="0" style="width: 100%; table-layout: fixed; Margin: auto !important; box-sizing: border-box;">
                                        <tbody>
                                        <tr>
                                            <td align="center" cellpadding="0" bo_type="gridInnerBox" style="padding: 0px; width: 100%; box-sizing: border-box;" class="">
                                                <table border="0" cellpadding="0" cellspacing="0" style="max-width: 600px; background-color: rgb(255, 255, 255); width: 100%; word-wrap: break-word; line-height: 1.2; table-layout: fixed; Margin: auto; box-sizing: border-box;"
                                                       bg_type="backgroundColor" class="">
                                                    <tbody>
                                                    <tr>
                                                        <td align="center" style="padding-top: 15px; line-height: 0; display: inline-block; box-sizing: border-box;text-align:center;" width="15%" bo_type="gridInnerBox" class="">
                                                        </td>
                                                        <td align="center" style="padding-top: 15px; line-height: 0; display: inline-block; box-sizing: border-box;text-align:center;" width="70%" bo_type="gridInnerBox" class="">
                                                            <div bo_type="text" style="font-family: Helvetica, Arial, sans-serif; font-weight: 400; padding: 3px 20px; font-size: 11px; line-height: 18px; color: rgb(119, 119, 119); border-collapse: collapse;" class="">*This estimated delivery date is subject to power being submitted and payment being completed within 24 hours of order placement.<br><br></br></br>
                                                                You received this mail because you are registered with <span th:text="${registeredTo}"></span>.<br></br>To unsubscribe, <a th:href="@{${unsubscribe}}" data-mce-style="text-decoration: underline; color: #3498db;" style="text-decoration:underline;color:#3498db">click here</a> it may take upto 72 hours to reflect. <br></br></div>
                                                        </td>
                                                        <td align="center" style="padding-top: 15px; line-height: 0; display: inline-block; box-sizing: border-box;text-align:center;" width="15%" bo_type="gridInnerBox" class="">
                                                        </td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <!--</td>-->
                </tr>
                </tbody>
            </table>
        </td>
    </tr>
    </tbody>
</table>
</div>
</body>

</html>
