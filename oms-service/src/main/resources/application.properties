spring.profiles.active=${profile:preprod-k8s}
spring.application.name=order-sensei
spring.config.import=consul:,vault:
spring.cloud.loadbalancer.ribbon.enabled=false
spring.cloud.consul.host=${consul-server:localhost}
spring.cloud.consul.port=${consul-port:8500}
spring.cloud.consul.enabled=true
spring.cloud.consul.config.enabled=true
spring.cloud.consul.config.prefix=scm-config
spring.cloud.consul.config.name=${spring.application.name:order-sensei},${profile:preprod-k8s}
spring.cloud.consul.config.format=YAML
spring.cloud.consul.config.data-key=${kv-version:v1}
spring.cloud.consul.config.default-context=${spring.application.name:order-sensei},${profile:preprod-k8s}
spring.cloud.consul.config.profile-separator=,
spring.cloud.consul.config.acl-token=${acl-token:dummyToken}
spring.cloud.consul.discovery.enabled=false
spring.cloud.consul.discovery.register=false
spring.cloud.consul.config.watch.enabled=false
spring.cloud.consul.service-registry.enabled=false
spring.mvc.pathmatch.matching-strategy=ANT_PATH_MATCHER
spring.cloud.vault.enabled=${VAULT_ENABLED:true}
spring.cloud.vault.uri=${VAULT_URI:http://vault.infra.svc.cluster.local:8200}
spring.cloud.vault.authentication=${VAULT_AUTHENTICATION:KUBERNETES}
spring.cloud.vault.kubernetes.role=${VAULT_ROLE:vault-c2d-config-order-sensei-auth-role}
spring.cloud.vault.kubernetes.kubernetes-path=${VAULT_KUBERNETES-PATH:kubernetes}
spring.cloud.vault.kubernetes.service-account-token-file=${VAULT_SERVICE-ACCOUNT-TOKEN-FILE:/var/run/secrets/kubernetes.io/serviceaccount/token}
spring.cloud.vault.kv.enabled=true
spring.cloud.vault.kv.backend=${VAULT_BACKEND:lenskart/c2d/c2d-config}
spring.cloud.vault.kv.default-context=${VAULT_DEFAULT_CONTEXT:scm-order-sensei}
spring.cloud.vault.kv.application-name=${VAULT_DEFAULT_CONTEXT:scm-order-sensei}
spring.cloud.vault.kv.profiles=''
spring.cloud.vault.generic.enabled=false
management.health.vault.enabled=false
