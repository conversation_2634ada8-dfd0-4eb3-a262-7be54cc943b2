package com.lenskart.oms.service;

import com.lenskart.nexs.commons.service.BaseService;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.entity.Shipment;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

public interface ShipmentService extends BaseService<ShipmentDto, Shipment> {
    List<ShipmentDto> findByShipmentStatusAndUpdatedAtBetween(String orderStatus, Date startHour, Date endHour);
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    void updateAssignedFacility(Long shipmentId, String facility);
}
