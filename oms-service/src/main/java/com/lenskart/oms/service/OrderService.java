package com.lenskart.oms.service;

import com.lenskart.nexs.commons.service.BaseService;
import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.entity.Order;
import com.lenskart.oms.enums.OrderStatus;
import com.lenskart.oms.enums.OrderSubStatus;
import com.lenskart.oms.enums.ShipmentStatus;
import com.lenskart.oms.enums.ShipmentSubStatus;
import com.lenskart.oms.request.CreateInvoiceForLocalFitting;
import com.lenskart.oms.response.OrderShipmentAndShipmentTimelineResponse;

import java.util.Date;
import java.util.List;
import java.util.Set;

public interface OrderService extends BaseService<OrderDto, Order> {
    OrderDto findByIncrementId(Long incrementId);
    List<OrderDto> findByOrderStatusAndOrderSubStatusAndUpdatedAtBetween(OrderStatus orderStatus, OrderSubStatus orderSubStatus, Date startHour, Date endHour);
    List<OrderDto> findByOrderStatusAndUpdatedAtBetween(OrderStatus orderStatus, Date startHour, Date endHour);
    List<OrderDto> findByOrderStatusInAndCreatedAtBetween(Set<OrderStatus> orderStatus, Date startHour, Date endHour);
    List<OrderDto> findByOrderStatusInAndUpdatedAtBetween(Set<OrderStatus> orderStatus, Date startHour, Date endHour);
    List<Long> findByOrderStatusInAndUpdatedAt(Set<String> orderStatus, Date endHour);

    void updateStatus(Long orderId, OrderStatus status, OrderSubStatus subStatus);
    void attachBarcode(CreateInvoiceForLocalFitting createInvoiceForLocalFitting) throws Exception;

    List<OrderShipmentAndShipmentTimelineResponse> findOrderShipmentAndShipmentTimelineDetails(Long incrementId);
}
