package com.lenskart.oms.service.impl;

import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.lenskart.nexs.commons.service.impl.BaseServiceImp;
import com.lenskart.oms.dto.OrderBackSyncTrackingDto;
import com.lenskart.oms.entity.OrderBackSyncTracking;
import com.lenskart.oms.enums.*;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.mapper.OrderBackSyncMapper;
import com.lenskart.oms.request.ShipmentUpdateEvent;
import com.lenskart.oms.service.OrderBackSyncTrackingService;
import com.lenskart.oms.utils.ObjectHelper;
import com.lenskart.vsm.soap.api.markitemfulfillable.MarkItemFulfillableResponse;
import com.lenskart.vsm.soap.api.markordercomplete.MarkOrderCompleteResponse;
import com.lenskart.vsm.soap.api.markorderdispatched.MarkOrderDispatchedResponse;
import com.lenskart.vsm.soap.api.markorderqc.MarkOrderQcResponse;
import com.lenskart.vsm.soap.api.markorderstockout.MarkOrderStockoutResponse;
import com.lenskart.vsm.soap.api.markupdateshippingpackage.MarkUpdateShippingPackageResponse;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.xml.stream.XMLInputFactory;
import javax.xml.stream.XMLStreamException;
import javax.xml.stream.XMLStreamReader;
import java.io.IOException;
import java.io.StringReader;
import java.util.Arrays;

@Service
public class OrderBackSyncTrackingServiceImpl extends BaseServiceImp<OrderBackSyncTrackingDto, OrderBackSyncTracking> implements OrderBackSyncTrackingService {

    private static final String UPDATE_SHIPPING_PACKAGE_ID_RESPONSE = "MarkUpdateShippingPackageResponse";
    private static final String MARK_ORDER_QC = "MarkOrderQcResponse";
    private static final String MARK_ORDER_COMPLETE_RESPONSE = "MarkOrderCompleteResponse";
    private static final String MARK_ORDER_DISPATCHED_RESPONSE = "MarkOrderDispatchedResponse";
    private static final String MARK_ORDER_STOCK_RESPONSE = "MarkOrderStockoutResponse";

    @Autowired
    private OrderBackSyncMapper orderBackSyncMapper;

    public void persistRequestInTracking(BackSyncEventName eventName, String wmsOrderCode, BackSyncSystem backSyncSystem) {
        StringBuilder searchTerm = new StringBuilder("entityId.eq:")
                .append(wmsOrderCode)
                .append("___")
                .append("eventName.eq:")
                .append(eventName.name());
        OrderBackSyncTrackingDto orderBackSyncTrackingDto = findBySearchTerms(searchTerm.toString());
        if (orderBackSyncTrackingDto == null) {
            orderBackSyncTrackingDto = orderBackSyncMapper.populateBackSyncTrackingDto(eventName, wmsOrderCode, BackSyncEventStatus.PENDING, (backSyncSystem != null ? backSyncSystem : BackSyncSystem.OMS), BackSyncEntityType.WMS_ORDER_CODE, null);
        } else {
            orderBackSyncTrackingDto.setEventStatus(BackSyncEventStatus.PENDING);
        }
        save(orderBackSyncTrackingDto);
    }

    public void persistResponseInTracking(String apiResponse, ShipmentUpdateEvent shipmentUpdateEvent, BackSyncEventName backSyncEventName, String wmsOrderCode) throws ApplicationException {
        try {
            String finalWmsOrderCode;
            String finalEventName;
            if (shipmentUpdateEvent != null) {
                finalEventName = shipmentUpdateEvent.getShipmentEvent().name();
                finalWmsOrderCode = shipmentUpdateEvent.getWmsOrderCode();
            } else {
                finalEventName = backSyncEventName.name();
                finalWmsOrderCode = wmsOrderCode;
            }
            StringBuilder searchTerm = new StringBuilder("entityId.eq:")
                    .append(finalWmsOrderCode)
                    .append("___")
                    .append("eventName.eq:")
                    .append(finalEventName);
            OrderBackSyncTrackingDto orderBackSyncTrackingDto = findBySearchTerms(searchTerm.toString());
            if (orderBackSyncTrackingDto != null) {
                orderBackSyncTrackingDto.setMessage(apiResponse);
                orderBackSyncTrackingDto.setEventStatus(getStatusCode(apiResponse, shipmentUpdateEvent));
                save(orderBackSyncTrackingDto);
            } else
                throw new ApplicationException(HttpStatus.UNPROCESSABLE_ENTITY.value(), finalEventName + " Event Not Found For WmsOrderCode " + finalWmsOrderCode);

        } catch (Exception exception) {
            logger.info("ShipmentEventsFacade.persistResponseInTracking - Request - ApiResponse : {}, ShipmentUpdateEvent : {}, BackSyncEventName : {}, WmsOrderCode : {}", apiResponse, shipmentUpdateEvent, backSyncEventName, wmsOrderCode);
            logger.error("ShipmentEventsFacade.persistResponseInTracking - Exception For API Response : " + apiResponse, exception);
            throw new ApplicationException("ShipmentEventsFacade.persistResponseInTracking", exception);
        }
    }

    private BackSyncEventStatus getStatusCode(String apiResponse, ShipmentUpdateEvent shipmentUpdateEvent) throws XMLStreamException, IOException {
        if (StringUtils.isBlank(apiResponse))
            return BackSyncEventStatus.SUCCESS;
        if (shipmentUpdateEvent != null) {
            // INVOICE & AWB - OO
            if (Arrays.asList(ShipmentEvent.CREATE_SHIPMENT_INVOICE, ShipmentEvent.CREATE_SHIPMENT_AWB).contains(shipmentUpdateEvent.getShipmentEvent()) && StringUtils.isNotBlank(apiResponse)) {
                JSONObject responseObject = new JSONObject(apiResponse);
                if (responseObject.has("successful")) {
                    if (Boolean.TRUE.equals(responseObject.getBoolean("successful")))
                        return BackSyncEventStatus.SUCCESS;
                    else
                        return BackSyncEventStatus.FAILED;
                } else
                    return BackSyncEventStatus.FAILED;
            }
            if (ShipmentEvent.MARK_SHIPMENT_DISPATCH.equals(shipmentUpdateEvent.getShipmentEvent())) {
                if (StringUtils.isNotBlank(apiResponse))
                    return BackSyncEventStatus.SUCCESS;
                else
                    return BackSyncEventStatus.FAILED;
            }
            // VSM Responses
            String vsmResponseStatusCode = convertXmlResponse(apiResponse, shipmentUpdateEvent.getShipmentEvent());
            if (StringUtils.isBlank(vsmResponseStatusCode) || BackSyncEventStatus.SUCCESS.name().equalsIgnoreCase(vsmResponseStatusCode))
                return BackSyncEventStatus.SUCCESS;
            else if ("FAILURE".equalsIgnoreCase(vsmResponseStatusCode))
                return BackSyncEventStatus.FAILED;
            else
                return BackSyncEventStatus.FAILED;
        } else {
            if (apiResponse.contains("Failure") || apiResponse.contains("error"))
                return BackSyncEventStatus.FAILED;
            else
                return BackSyncEventStatus.SUCCESS;
        }
    }

    private String convertXmlResponse(String response, ShipmentEvent shipmentEvent) throws IOException, XMLStreamException {
        XmlMapper xmlMapper = ObjectHelper.getXmlMapper();
        XMLInputFactory f = XMLInputFactory.newFactory();
        XMLStreamReader sr = f.createXMLStreamReader(new StringReader(response));
        while (sr.hasNext()) {
            int type = sr.next();
            if (shipmentEvent.equals(ShipmentEvent.MARK_CREATE_SHIPMENT)) {
                if (type == XMLStreamReader.START_ELEMENT && UPDATE_SHIPPING_PACKAGE_ID_RESPONSE.equals(sr.getLocalName()))
                    return xmlMapper.readValue(sr, MarkUpdateShippingPackageResponse.class).getReturn().getSuccessCode();
            } else if (shipmentEvent.equals(ShipmentEvent.MARK_ITEM_FULFILLABLE)) {
                if (type == XMLStreamReader.START_ELEMENT && "body".equalsIgnoreCase(sr.getLocalName()))
                    return xmlMapper.readValue(sr, MarkItemFulfillableResponse.class).getReturn().getSuccessCode();
            } else if (shipmentEvent.equals(ShipmentEvent.MARK_SHIPMENT_QC)) {
                if (type == XMLStreamReader.START_ELEMENT && MARK_ORDER_QC.equalsIgnoreCase(sr.getLocalName()))
                    return xmlMapper.readValue(sr, MarkOrderQcResponse.class).getReturn().getSuccessCode();
            } else if (shipmentEvent.equals(ShipmentEvent.MARK_SHIPMENT_MANIFEST)) {
                if (type == XMLStreamReader.START_ELEMENT && MARK_ORDER_COMPLETE_RESPONSE.equalsIgnoreCase(sr.getLocalName()))
                    return xmlMapper.readValue(sr, MarkOrderCompleteResponse.class).getReturn().getSuccessCode();
            } else if (shipmentEvent.equals(ShipmentEvent.MARK_SHIPMENT_DISPATCH)) {
                if (type == XMLStreamReader.START_ELEMENT && MARK_ORDER_DISPATCHED_RESPONSE.equalsIgnoreCase(sr.getLocalName()))
                    return xmlMapper.readValue(sr, MarkOrderDispatchedResponse.class).getReturn().getSuccessCode();
            } else if (shipmentEvent.equals(ShipmentEvent.MARK_SHIPMENT_PICKED)) {
                if (type == XMLStreamReader.START_ELEMENT && MARK_ORDER_STOCK_RESPONSE.equalsIgnoreCase(sr.getLocalName()))
                    return xmlMapper.readValue(sr, MarkOrderStockoutResponse.class).getReturn().getSuccessCode();
            }
        }
        return null;
    }

}
