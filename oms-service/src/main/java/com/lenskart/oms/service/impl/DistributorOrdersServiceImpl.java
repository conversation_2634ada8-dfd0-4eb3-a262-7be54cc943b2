package com.lenskart.oms.service.impl;

import com.lenskart.nexs.commons.service.impl.BaseServiceImp;
import com.lenskart.oms.dto.*;
import com.lenskart.oms.entity.*;
import com.lenskart.oms.repository.DistributorOrdersRepository;
import com.lenskart.oms.service.DistributorCustomerDetailsService;
import com.lenskart.oms.service.DistributorOrderItemsService;
import com.lenskart.oms.service.DistributorOrdersService;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@Setter(onMethod__ = {@Autowired})
public class DistributorOrdersServiceImpl extends BaseServiceImp<DistributorOrdersDto, DistributorOrders> implements DistributorOrdersService {

    @Setter(onMethod__ = {@Autowired})
    private DistributorOrderItemsService distributorOrderItemsService;

    @Setter(onMethod__ = {@Autowired})
    private DistributorOrdersRepository distributorOrdersRepository;

    @Setter(onMethod__ = {@Autowired})
    private DistributorCustomerDetailsService distributorCustomerDetailsService;

    public DistributorOrders findByJunoOrderId(String junoOrderId) {
        return distributorOrdersRepository.findByJunoOrderId(junoOrderId);
    }

    public DistributorOrders findByIncrementId(String incrementId) {
        return distributorOrdersRepository.findByIncrementId(incrementId);
    }

    public List<Long> findAllUnsyncedApprovedDO() {
        return distributorOrdersRepository.findAllUnsyncedApprovedDO();
    }

    @Override
    public DistributorOrders convertToEntity(DistributorOrdersDto dto, DistributorOrders entity) {
        entity = super.convertToEntity(dto, entity);
        if (dto.getCustomer() != null)
            entity.setCustomerId(dto.getCustomer().getId());
        if (!org.springframework.util.CollectionUtils.isEmpty(dto.getOrderItems())) {
            List<DistributorOrderItems> orderItemsList = new ArrayList<>();
            for (DistributorOrderItemsDto distributorOrderItemsDto : dto.getOrderItems()) {
                orderItemsList.add(distributorOrderItemsService.convertToEntity(distributorOrderItemsDto, new DistributorOrderItems()));
            }
            entity.setOrderItems(orderItemsList);
        }
        return entity;
    }

    @Override
    public DistributorOrdersDto convertToDto(DistributorOrders entity, DistributorOrdersDto dto) {
        dto = super.convertToDto(entity, dto);
        DistributorCustomerDetailsDto customerDetailsDto = distributorCustomerDetailsService.findById(entity.getCustomerId());
        if (customerDetailsDto != null) {
            customerDetailsDto.setCustomerAddressDetails(null);
            dto.setCustomer(customerDetailsDto);
        }
        if (!org.springframework.util.CollectionUtils.isEmpty(entity.getOrderItems())) {
            List<DistributorOrderItemsDto> distributorOrderItemsList = new ArrayList<>();
            for (DistributorOrderItems distributorOrderItem : entity.getOrderItems()) {
                distributorOrderItem.setOrderId(entity.getId());
                distributorOrderItemsList.add(distributorOrderItemsService.convertToDto(distributorOrderItem, new DistributorOrderItemsDto()));
            }
            dto.setOrderItems(distributorOrderItemsList);
        }
        return dto;
    }
}
