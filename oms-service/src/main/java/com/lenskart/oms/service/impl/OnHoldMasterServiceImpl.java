package com.lenskart.oms.service.impl;

import com.lenskart.nexs.commons.service.impl.BaseServiceImp;
import com.lenskart.oms.dto.OnHoldMasterDto;
import com.lenskart.oms.entity.OnHoldMaster;
import com.lenskart.oms.service.OnHoldMasterService;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Setter(onMethod__ = {@Autowired})
@Service
public class OnHoldMasterServiceImpl extends BaseServiceImp<OnHoldMasterDto, OnHoldMaster> implements OnHoldMasterService {
}
