package com.lenskart.oms.service.impl;

import com.lenskart.nexs.commons.service.impl.BaseServiceImp;
import com.lenskart.oms.dto.DistributorCustomerAddressDetailsDto;
import com.lenskart.oms.entity.DistributorCustomerAddressDetails;
import com.lenskart.oms.service.DistributorCustomerAddressDetailsService;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Setter(onMethod__ = {@Autowired})
@Service
public class CustomerAddressDetailsServiceImpl extends BaseServiceImp<DistributorCustomerAddressDetailsDto, DistributorCustomerAddressDetails>  implements DistributorCustomerAddressDetailsService {
}
