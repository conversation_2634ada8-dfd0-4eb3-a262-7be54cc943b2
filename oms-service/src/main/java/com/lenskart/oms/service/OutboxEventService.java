package com.lenskart.oms.service;

import com.lenskart.nexs.commons.service.BaseService;
import com.lenskart.oms.dto.OutboxEventDto;
import com.lenskart.oms.entity.OutboxEvent;
import com.lenskart.oms.enums.OutboxEventAggregateType;
import com.lenskart.oms.enums.OutboxEventStatus;
import com.lenskart.oms.enums.OutboxEventType;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Service interface for managing outbox events
 */
public interface OutboxEventService extends BaseService<OutboxEventDto, OutboxEvent> {


    /**
     * Create outbox event with custom scheduling
     */
    @Transactional(rollbackFor = Exception.class)
    OutboxEventDto createOutboxEvent(String aggregateId,
                                     OutboxEventAggregateType aggregateType,
                                   OutboxEventType eventType,
                                   String topicName,
                                   String partitionKey,
                                   String eventPayload,
                                   Map<String, String> headers,
                                   Date scheduledAt);

    /**
     * Find pending events ready for processing
     */
    List<OutboxEventDto> findPendingEvents(int limit);

    /**
     * Find events that can be retried
     */
    List<OutboxEventDto> findRetryableEvents(int limit);

    /**
     * Mark event as processed successfully
     */
    @Transactional(rollbackFor = Exception.class)
    void markAsProcessed(Long eventId);

    /**
     * Mark event as failed with error message
     */
    @Transactional(rollbackFor = Exception.class)
    void markAsFailed(Long eventId, String errorMessage);

    /**
     * Mark event as dead letter (max retries exceeded)
     */
    @Transactional(rollbackFor = Exception.class)
    void markAsDeadLetter(Long eventId, String errorMessage);

    /**
     * Increment retry count and schedule next retry
     */
    @Transactional(rollbackFor = Exception.class)
    void incrementRetryCount(Long eventId, Date nextRetryTime);

    /**
     * Find event by idempotency key
     */
    Optional<OutboxEventDto> findByIdempotencyKey(String idempotencyKey);

    /**
     * Find events by aggregate ID and type
     */
    List<OutboxEventDto> findByAggregateIdAndEventType(String aggregateId, OutboxEventType eventType);

    /**
     * Find stale processing events that need to be reset
     */
    List<OutboxEventDto> findStaleProcessingEvents(Date staleTime);

    /**
     * Reset stale processing events back to pending
     */
    @Transactional(rollbackFor = Exception.class)
    void resetStaleProcessingEvents();

    /**
     * Get event statistics for monitoring
     */
    Map<OutboxEventStatus, Long> getEventStatistics();

    /**
     * Find events by status and date range for monitoring
     */
    List<OutboxEventDto> findByStatusAndDateRange(OutboxEventStatus status, Date startDate, Date endDate);

    long countByEventStatus(OutboxEventStatus outboxEventStatus);

    /**
     * Process pending events
     */
    void processPendingEvents();
    
    /**
     * Process retryable events
     */
    void processRetryEvents();
    
    
    
}
