package com.lenskart.oms.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.nexs.commons.service.impl.BaseServiceImp;
import com.lenskart.oms.dto.OutboxEventDto;
import com.lenskart.oms.entity.OutboxEvent;
import com.lenskart.oms.enums.OutboxEventAggregateType;
import com.lenskart.oms.enums.OutboxEventStatus;
import com.lenskart.oms.enums.OutboxEventType;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.repository.OutboxEventRepository;
import com.lenskart.oms.facade.OutboxEventPublisherFacade;
import com.lenskart.oms.service.OutboxEventService;
import lombok.AccessLevel;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@Setter(onMethod__ = {@Autowired})
public class OutboxEventServiceImpl extends BaseServiceImp<OutboxEventDto, OutboxEvent> implements OutboxEventService {

    @Value("${outbox.publisher.batch.size:50}")
    @Setter(AccessLevel.NONE)
    private int batchSize;

    @Value("${outbox.publisher.stale.processing.minutes:30}")
    @Setter(AccessLevel.NONE)
    private int staleProcessingMinutes;

    private OutboxEventRepository outboxEventRepository;
    private ObjectMapper objectMapper;
    private OutboxEventPublisherFacade outboxEventPublisherFacade;


    @Override
    public OutboxEventDto createOutboxEvent(String aggregateId,
                                            OutboxEventAggregateType aggregateType,
                                            OutboxEventType eventType,
                                            String topicName,
                                            String partitionKey,
                                            String eventPayload,
                                            Map<String, String> headers,
                                            Date scheduledAt) {
        try {
            // Generate idempotency key
            String idempotencyKey = generateIdempotencyKey(aggregateId, eventType, eventPayload);

            // Check if event already exists
            Optional<OutboxEventDto> existingEvent = findByIdempotencyKey(idempotencyKey);
            if (existingEvent.isPresent()) {
                logger.info("[createOutboxEvent] Event already exists with idempotency key: {}", idempotencyKey);
                return existingEvent.get();
            }

            OutboxEvent outboxEvent = new OutboxEvent();
            outboxEvent.setAggregateId(aggregateId);
            outboxEvent.setAggregateType(aggregateType);
            outboxEvent.setEventType(eventType);
            outboxEvent.setEventStatus(OutboxEventStatus.PENDING);
            outboxEvent.setTopicName(topicName);
            outboxEvent.setPartitionKey(partitionKey);
            outboxEvent.setEventPayload(eventPayload);
            outboxEvent.setScheduledAt(scheduledAt);
            outboxEvent.setIdempotencyKey(idempotencyKey);
            outboxEvent.setRetryCount(0);
            outboxEvent.setMaxRetryCount(3);
            outboxEvent.setCreatedAt(new Date());
            outboxEvent.setUpdatedAt(new Date());
            outboxEvent.setCreatedBy("system");
            outboxEvent.setUpdatedBy("system");

            // Serialize headers to JSON
            if (headers != null && !headers.isEmpty()) {
                outboxEvent.setEventHeaders(objectMapper.writeValueAsString(headers));
            }

            OutboxEvent savedEvent = outboxEventRepository.save(outboxEvent);
            logger.info("[createOutboxEvent] Created outbox event: {} for aggregate: {}",
                    savedEvent.getId(), aggregateId);

            return convertToDto(savedEvent, new OutboxEventDto());

        } catch (Exception e) {
            logger.error("[createOutboxEvent] Failed to create outbox event for aggregate: {}", aggregateId, e);
            throw new ApplicationException("Failed to create outbox event", e);
        }
    }

    @Override
    public List<OutboxEventDto> findPendingEvents(int limit) {
        List<OutboxEventStatus> statuses = Arrays.asList(OutboxEventStatus.PENDING, OutboxEventStatus.FAILED);
        List<OutboxEvent> events = outboxEventRepository.findPendingEvents(statuses, new Date());

        return events.stream()
                .limit(limit)
                .map(event -> convertToDto(event, new OutboxEventDto()))
                .collect(Collectors.toList());
    }

    @Override
    public List<OutboxEventDto> findRetryableEvents(int limit) {
        List<OutboxEvent> events = outboxEventRepository.findRetryableEvents(OutboxEventStatus.FAILED, new Date());

        return events.stream()
                .limit(limit)
                .map(event -> convertToDto(event, new OutboxEventDto()))
                .collect(Collectors.toList());
    }

    @Override
    public void markAsProcessed(Long eventId) {
        outboxEventRepository.updateEventStatus(eventId, OutboxEventStatus.PROCESSED, new Date(), null);
        logger.debug("[markAsProcessed] Marked event {} as processed", eventId);
    }

    @Override
    public void markAsFailed(Long eventId, String errorMessage) {
        outboxEventRepository.updateEventStatus(eventId, OutboxEventStatus.FAILED, new Date(), errorMessage);
        logger.warn("[markAsFailed] Marked event {} as failed: {}", eventId, errorMessage);
    }

    @Override
    public void markAsDeadLetter(Long eventId, String errorMessage) {
        outboxEventRepository.updateEventStatus(eventId, OutboxEventStatus.DEAD_LETTER, new Date(), errorMessage);
        logger.error("[markAsDeadLetter] Marked event {} as dead letter: {}", eventId, errorMessage);
    }

    @Override
    public void incrementRetryCount(Long eventId, Date nextRetryTime) {
        outboxEventRepository.incrementRetryCount(eventId, OutboxEventStatus.FAILED, nextRetryTime);
        logger.debug("[incrementRetryCount] Incremented retry count for event {}, next retry: {}", eventId, nextRetryTime);
    }

    @Override
    public Optional<OutboxEventDto> findByIdempotencyKey(String idempotencyKey) {
        return outboxEventRepository.findByIdempotencyKey(idempotencyKey)
                .map(event -> convertToDto(event, new OutboxEventDto()));
    }

    @Override
    public List<OutboxEventDto> findByAggregateIdAndEventType(String aggregateId, OutboxEventType eventType) {
        return outboxEventRepository.findByAggregateIdAndEventTypeOrderByCreatedAtDesc(aggregateId, eventType)
                .stream()
                .map(event -> convertToDto(event, new OutboxEventDto()))
                .collect(Collectors.toList());
    }

    @Override
    public List<OutboxEventDto> findStaleProcessingEvents(Date staleTime) {
        return outboxEventRepository.findStaleProcessingEvents(OutboxEventStatus.PROCESSING, staleTime)
                .stream()
                .map(event -> convertToDto(event, new OutboxEventDto()))
                .collect(Collectors.toList());
    }

    @Override
    public void resetStaleProcessingEvents() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MINUTE, -staleProcessingMinutes);
        Date staleTime = calendar.getTime();
        List<OutboxEvent> staleEvents = outboxEventRepository.findStaleProcessingEvents(OutboxEventStatus.PROCESSING, staleTime);
        for (OutboxEvent event : staleEvents) {
            outboxEventRepository.updateEventStatus(event.getId(), OutboxEventStatus.PENDING, new Date(),
                    "Reset from stale processing state");
        }
        logger.info("[resetStaleProcessingEvents] Reset {} stale processing events", staleEvents.size());
    }

    @Override
    public Map<OutboxEventStatus, Long> getEventStatistics() {
        Map<OutboxEventStatus, Long> stats = new HashMap<>();
        for (OutboxEventStatus status : OutboxEventStatus.values()) {
            stats.put(status, outboxEventRepository.countByEventStatus(status));
        }
        return stats;
    }

    @Override
    public List<OutboxEventDto> findByStatusAndDateRange(OutboxEventStatus status, Date startDate, Date endDate) {
        return outboxEventRepository.findByStatusAndDateRange(status, startDate, endDate)
                .stream()
                .map(event -> convertToDto(event, new OutboxEventDto()))
                .collect(Collectors.toList());
    }

    @Override
    public long countByEventStatus(OutboxEventStatus outboxEventStatus) {
        return outboxEventRepository.countByEventStatus(outboxEventStatus);
    }

    @Override
    public void processPendingEvents() {
        log.debug("[processPendingEvents] Starting to process pending outbox events");

        List<OutboxEventDto> pendingEvents = findPendingEvents(batchSize);
        if (pendingEvents.isEmpty()) {
            log.debug("[processPendingEvents] No pending events found");
            return;
        }

        log.info("[processPendingEvents] Found {} pending events to process", pendingEvents.size());

        for (OutboxEventDto event : pendingEvents) {
            outboxEventPublisherFacade.processEvent(event);
        }

    }

    @Override
    public void processRetryEvents() {
        log.debug("[processRetryEvents] Starting to process retry events");

        List<OutboxEventDto> retryEvents = findRetryableEvents(batchSize);
        if (retryEvents.isEmpty()) {
            log.debug("[processRetryEvents] No retry events found");
            return;
        }

        log.info("[processRetryEvents] Found {} retry events to process", retryEvents.size());

        for (OutboxEventDto event : retryEvents) {
            outboxEventPublisherFacade.processEvent(event);
        }
    }

    /**
     * Generate idempotency key for event deduplication
     */
    private String generateIdempotencyKey(String aggregateId, OutboxEventType eventType, String eventPayload) {
        try {
            String combined = aggregateId + "_" + eventType.name() + "_" + eventPayload.hashCode();
            return Base64.getEncoder().encodeToString(combined.getBytes());
        } catch (Exception e) {
            // Fallback to simple concatenation
            return aggregateId + "_" + eventType.name() + "_" + System.currentTimeMillis();
        }
    }

}
