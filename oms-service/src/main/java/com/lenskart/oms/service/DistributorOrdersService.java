package com.lenskart.oms.service;

import com.lenskart.nexs.commons.service.BaseService;
import com.lenskart.oms.dto.DistributorOrdersDto;
import com.lenskart.oms.entity.DistributorOrders;

import java.util.List;

public interface DistributorOrdersService extends BaseService<DistributorOrdersDto, DistributorOrders> {
    DistributorOrders findByJunoOrderId(String junoOrderId);
    public DistributorOrders findByIncrementId(String incrementId);
    public List<Long> findAllUnsyncedApprovedDO();
}
