package com.lenskart.oms.service;

import com.lenskart.nexs.commons.service.BaseService;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.ShipmentTimelineDto;
import com.lenskart.oms.entity.ShipmentTimeline;

import java.util.List;

public interface ShipmentTimelineService extends BaseService<ShipmentTimelineDto, ShipmentTimeline> {

    public List<ShipmentTimelineDto> findAssociatedShipmentTimelines(OrderItemDto currentItemDto);

    List<ShipmentTimelineDto> findByShipmentId(Long shipmentId);
}
