package com.lenskart.oms.service.impl;

import com.lenskart.nexs.commons.service.impl.BaseServiceImp;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.constants.KafkaConstants;
import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.OrderMetaDataDto;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.entity.Order;
import com.lenskart.oms.entity.OrderItems;
import com.lenskart.oms.entity.OrderMetaData;
import com.lenskart.oms.enums.OrderStatus;
import com.lenskart.oms.enums.OrderSubStatus;
import com.lenskart.oms.enums.OtcShipmentEventType;
import com.lenskart.oms.enums.ShipmentStatus;
import com.lenskart.oms.enums.ShipmentSubStatus;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.producer.CommonKafkaProducer;
import com.lenskart.oms.repository.OrderRepository;
import com.lenskart.oms.request.CreateInvoiceForLocalFitting;
import com.lenskart.oms.request.Item;
import com.lenskart.oms.request.OtcShipmentEvent;
import com.lenskart.oms.response.OrderShipmentAndShipmentTimelineResponse;
import com.lenskart.oms.service.OrderItemService;
import com.lenskart.oms.service.OrderMetaService;
import com.lenskart.oms.service.OrderService;
import com.lenskart.oms.service.ShipmentService;
import com.lenskart.oms.utils.ObjectHelper;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigInteger;
import java.util.*;

import javax.persistence.Tuple;


@Service
@Setter(onMethod__ = {@Autowired})
public class OrderServiceImpl extends BaseServiceImp<OrderDto, Order> implements OrderService {

    private OrderRepository orderRepository;
    private OrderItemService orderItemService;
    private OrderMetaService orderMetaService;
    private ShipmentService shipmentService;
    private CommonKafkaProducer commonKafkaProducer;

    @Override
    public OrderDto findByIncrementId(Long incrementId) {
        return findBySearchTerms("incrementId.eq:" + incrementId);
    }

    @Override
    public List<OrderDto> findByOrderStatusAndOrderSubStatusAndUpdatedAtBetween(OrderStatus orderStatus, OrderSubStatus orderSubStatus, Date startHour, Date endHour) {
        List<Order> orderList = orderRepository.findByOrderStatusAndOrderSubStatusAndUpdatedAtBetween(orderStatus, orderSubStatus, startHour, endHour);

        List<OrderDto> orderDtoList = new ArrayList<>();
        for (Order order : orderList) {
            OrderDto orderDto = new OrderDto();
            convertToDto(order, orderDto);
            orderDtoList.add(orderDto);
        }

        return orderDtoList;
    }

    @Override
    public List<OrderDto> findByOrderStatusAndUpdatedAtBetween(OrderStatus orderStatus, Date startHour, Date endHour) {
        List<Order> orderList = orderRepository.findByOrderStatusAndUpdatedAtBetween(orderStatus, startHour, endHour);

        List<OrderDto> orderDtoList = new ArrayList<>();
        for (Order order : orderList) {
            OrderDto orderDto = new OrderDto();
            convertToDto(order, orderDto);
            orderDtoList.add(orderDto);
        }

        return orderDtoList;
    }

    @Override
    public List<OrderDto> findByOrderStatusInAndCreatedAtBetween(Set<OrderStatus> orderStatus, Date startHour, Date endHour) {
        List<Order> orderList = orderRepository.findByOrderStatusInAndCreatedAtBetween(orderStatus, startHour, endHour);

        List<OrderDto> orderDtoList = new ArrayList<>();
        for (Order order : orderList) {
            OrderDto orderDto = new OrderDto();
            convertToDto(order, orderDto);
            orderDtoList.add(orderDto);
        }

        return orderDtoList;
    }

    @Override
    public List<OrderDto> findByOrderStatusInAndUpdatedAtBetween(Set<OrderStatus> orderStatus, Date startHour, Date endHour) {
        List<Order> orderList = orderRepository.findByOrderStatusInAndUpdatedAtBetween(orderStatus, startHour, endHour);

        List<OrderDto> orderDtoList = new ArrayList<>();
        for (Order order : orderList) {
            OrderDto orderDto = new OrderDto();
            convertToDto(order, orderDto);
            orderDtoList.add(orderDto);
        }

        return orderDtoList;
    }

    @Override
    public List<Long> findByOrderStatusInAndUpdatedAt(Set<String> orderStatus, Date endHour) {
        return orderRepository.findByStatusInAndUpdatedAt(endHour, orderStatus);
    }

    @Override
    public void updateStatus(Long orderId, OrderStatus status, OrderSubStatus subStatus) {
        ((OrderRepository)repository).updateStatus(orderId, status, subStatus);
    }

    @Override
    public Order convertToEntity(OrderDto dto, Order entity) {
        entity = super.convertToEntity(dto, entity);

        if (!CollectionUtils.isEmpty(dto.getOrderItems())) {
            List<OrderItems> orderItemsList = new ArrayList<>();
            for (OrderItemDto orderItemDto : dto.getOrderItems()) {
                orderItemsList.add(orderItemService.convertToEntity(orderItemDto, new OrderItems()));
            }
            entity.setOrderItems(orderItemsList);
        }
        if (!CollectionUtils.isEmpty(dto.getOrderMetaData())) {
            List<OrderMetaData> orderMetaDataList = new ArrayList<>();
            for (OrderMetaDataDto orderMetaDataDto : dto.getOrderMetaData()) {
                orderMetaDataList.add(orderMetaService.convertToEntity(orderMetaDataDto, new OrderMetaData()));
            }
            entity.setOrderMetaData(orderMetaDataList);
        }

        return entity;
    }

    @Override
    public OrderDto convertToDto(Order entity, OrderDto dto) {
        dto = super.convertToDto(entity, dto);

        if (!CollectionUtils.isEmpty(entity.getOrderItems())) {
            List<OrderItemDto> orderItemDtoList = new ArrayList<>();
            for (OrderItems orderItem : entity.getOrderItems()) {
                orderItem.setOrderId(entity.getId());
                orderItemDtoList.add(orderItemService.convertToDto(orderItem, new OrderItemDto()));
            }
            dto.setOrderItems(orderItemDtoList);
        }

        if (!CollectionUtils.isEmpty(entity.getOrderMetaData())) {
            List<OrderMetaDataDto> orderMetaDataDtoList = new ArrayList<>();
            for (OrderMetaData orderMetaData : entity.getOrderMetaData()) {
                orderMetaDataDtoList.add(orderMetaService.convertToDto(orderMetaData, new OrderMetaDataDto()));
            }
            dto.setOrderMetaData(orderMetaDataDtoList);
        }

        return dto;
    }

    @Override
    public OrderDto convertToDto(Order entity, OrderDto dto, Set<String> exclusionChildProperties) {
        dto = super.convertToDto(entity, dto, exclusionChildProperties);

        if (!exclusionChildProperties.contains(ApplicationConstants.CHILD_ENTITIES.ORDER_ITEMS) && !CollectionUtils.isEmpty(entity.getOrderItems())) {
            List<OrderItemDto> orderItemDtoList = new ArrayList<>();
            for (OrderItems orderItem : entity.getOrderItems()) {
                orderItem.setOrderId(entity.getId());
                orderItemDtoList.add(orderItemService.convertToDto(orderItem, new OrderItemDto(), exclusionChildProperties));
            }
            dto.setOrderItems(orderItemDtoList);
        }

        if (!CollectionUtils.isEmpty(entity.getOrderMetaData())) {
            List<OrderMetaDataDto> orderMetaDataDtoList = new ArrayList<>();
            for (OrderMetaData orderMetaData : entity.getOrderMetaData()) {
                orderMetaDataDtoList.add(orderMetaService.convertToDto(orderMetaData, new OrderMetaDataDto(), exclusionChildProperties));
            }
            dto.setOrderMetaData(orderMetaDataDtoList);
        }

        return dto;
    }

    @Override
    @Transactional(readOnly = false,rollbackFor = Exception.class)
    public void attachBarcode(CreateInvoiceForLocalFitting createInvoiceForLocalFitting) throws Exception{
        List<ShipmentDto> shipmentDtoList = getShipmentDtos(createInvoiceForLocalFitting);

        OtcShipmentEvent otcShipmentEvent = new OtcShipmentEvent();
        otcShipmentEvent.setShipmentId(shipmentDtoList.get(0).getId());
        otcShipmentEvent.setEventType(OtcShipmentEventType.INVOICED);
        commonKafkaProducer.sendMessage(
                KafkaConstants.OMS_OTC_ORDER_EVENTS_PROCESS_TOPIC,
                String.valueOf(shipmentDtoList.get(0).getOrderItems().get(0).getOrderId()),
                ObjectHelper.convertToString(new OtcShipmentEvent(OtcShipmentEventType.INVOICED, shipmentDtoList.get(0).getId())),
                String.valueOf(shipmentDtoList.get(0).getId())
        );
    }

    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW, readOnly = false)
    private List<ShipmentDto> getShipmentDtos(CreateInvoiceForLocalFitting createInvoiceForLocalFitting) {
        List<ShipmentDto> shipmentDtoList = shipmentService.search("wmsOrderCode.eq:" + createInvoiceForLocalFitting.getPoNum());
        List<Item> items = createInvoiceForLocalFitting.getItems();
        for(OrderItemDto orderItemDto : shipmentDtoList.get(0).getOrderItems()){
            for(Item item : items){
                logger.info("[OrderServiceImpl -> attachBarcode] item:{} and orderItemDto:{}",item,orderItemDto);
                if(Objects.equals(item.getOrderItemCode(), orderItemDto.getId())){
                    orderItemDto.setItemBarcode(item.getBarcode());
                    orderItemService.save(orderItemDto);
                }
            }
        }
        return shipmentDtoList;
    }

    @Override
    public List<OrderShipmentAndShipmentTimelineResponse> findOrderShipmentAndShipmentTimelineDetails(Long incrementId) {
        try {
            List<Tuple> orderShipmentAndShipmentTimelineResponse = orderRepository.findOrderShipmentAndShipmentTimelineDetails(incrementId);
            return mapOrderShipmentAndShipmentTimelineTuples(orderShipmentAndShipmentTimelineResponse);
        } catch (Exception e) {
            throw new ApplicationException("Exception while fetching OrderShipmentAndShipmentTimelineDto" + e + "for incrementId" + incrementId);
        }
    }

    private List<OrderShipmentAndShipmentTimelineResponse> mapOrderShipmentAndShipmentTimelineTuples(List<Tuple> orderShipmentAndShipmentTimelineResponse) {
        List<OrderShipmentAndShipmentTimelineResponse> orderShipmentAndShipmentTimelineResponseList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(orderShipmentAndShipmentTimelineResponse)) {
            for (Tuple currentTuple : orderShipmentAndShipmentTimelineResponse) {
                OrderShipmentAndShipmentTimelineResponse newOrderShipmentAndShipmentTimelineResponse = new OrderShipmentAndShipmentTimelineResponse();
                newOrderShipmentAndShipmentTimelineResponse.setJunoOrderId(Optional.ofNullable(currentTuple.get("juno_order_id", BigInteger.class)).orElse(null));
                newOrderShipmentAndShipmentTimelineResponse.setOrderStatus(Optional.ofNullable(currentTuple.get("order_status", String.class)).map(OrderStatus::valueOf).orElse(null));
                newOrderShipmentAndShipmentTimelineResponse.setOrderSubStatus(Optional.ofNullable(currentTuple.get("order_sub_status", String.class)).map(OrderSubStatus::valueOf).orElse(null));
                newOrderShipmentAndShipmentTimelineResponse.setWmsOrderCode(Optional.ofNullable(currentTuple.get("wms_order_code", String.class)).orElse(null));
                newOrderShipmentAndShipmentTimelineResponse.setWmsShippingPackageId(Optional.ofNullable(currentTuple.get("wms_shipping_package_id", String.class)).orElse(null));
                newOrderShipmentAndShipmentTimelineResponse.setShipmentType(Optional.ofNullable(currentTuple.get("shipment_type", String.class)).orElse(null));
                newOrderShipmentAndShipmentTimelineResponse.setShipmentSubType(Optional.ofNullable(currentTuple.get("shipment_sub_type", String.class)).orElse(null));
                newOrderShipmentAndShipmentTimelineResponse.setShipmentStatus(Optional.ofNullable(currentTuple.get("shipment_status", String.class)).map(ShipmentStatus::valueOf).orElse(null));
                newOrderShipmentAndShipmentTimelineResponse.setShipmentSubStatus(Optional.ofNullable(currentTuple.get("shipment_sub_status", String.class)).map(ShipmentSubStatus::valueOf).orElse(null));
                newOrderShipmentAndShipmentTimelineResponse.setInvoiceTime(Optional.ofNullable(currentTuple.get("invoice_time", Date.class)).orElse(null));
                newOrderShipmentAndShipmentTimelineResponse.setManifestTime(Optional.ofNullable(currentTuple.get("manifest_time", Date.class)).orElse(null));
                newOrderShipmentAndShipmentTimelineResponse.setDispatchTime(Optional.ofNullable(currentTuple.get("dispatch_time", Date.class)).orElse(null));
                newOrderShipmentAndShipmentTimelineResponse.setDeliveredTime(Optional.ofNullable(currentTuple.get("delivered_time", Date.class)).orElse(null));
                newOrderShipmentAndShipmentTimelineResponse.setOrderCreatedAt(Optional.ofNullable(currentTuple.get("order_created_at", Date.class)).orElse(null));
                newOrderShipmentAndShipmentTimelineResponse.setShipmentTimeLineCreatedAt(Optional.ofNullable(currentTuple.get("shipment_timeline_created_at", Date.class)).orElse(null));
                orderShipmentAndShipmentTimelineResponseList.add(newOrderShipmentAndShipmentTimelineResponse);
            }
        }
        return orderShipmentAndShipmentTimelineResponseList;
    }

}
