package com.lenskart.oms.service.impl;

import com.lenskart.nexs.commons.service.impl.BaseServiceImp;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.dto.*;
import com.lenskart.oms.entity.*;
import com.lenskart.oms.enums.OrderStatus;
import com.lenskart.oms.repository.ShipmentRepository;
import com.lenskart.oms.enums.OrderItemStatus;
import com.lenskart.oms.service.*;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Setter(onMethod__ = {@Autowired})
public class ShipmentServiceImpl extends BaseServiceImp<ShipmentDto, Shipment> implements ShipmentService {

    private OrderItemService orderItemService;
    private ShipmentMetaService shipmentMetaService;
    private OrderAddressService orderAddressService;
    private ShipmentRepository shipmentRepository;
    private OrderService orderService;

    @Override
    public Shipment convertToEntity(ShipmentDto dto, Shipment entity) {
        entity = super.convertToEntity(dto, entity);

        if (!CollectionUtils.isEmpty(dto.getOrderItems())) {
            List<OrderItems> orderItemsList = new ArrayList<>();
            for (OrderItemDto orderItemDto : dto.getOrderItems()) {
                orderItemsList.add(orderItemService.convertToEntity(orderItemDto, new OrderItems()));
            }
            entity.setOrderItems(orderItemsList);
        }

        if (!CollectionUtils.isEmpty(dto.getShipmentMetaData())) {
            List<ShipmentMetaData> shipmentMetaDataList = new ArrayList<>();
            for (ShipmentMetaDataDto shipmentMetaDataDto : dto.getShipmentMetaData()) {
                shipmentMetaDataList.add(shipmentMetaService.convertToEntity(shipmentMetaDataDto, new ShipmentMetaData()));
            }
            entity.setShipmentMetaData(shipmentMetaDataList);
        }
        if (Objects.nonNull(dto.getShippingAddress())) {
            entity.setShippingAddress(orderAddressService.convertToEntity(dto.getShippingAddress(), new OrderAddress()));
        }
        if (Objects.nonNull(dto.getBillingAddress())) {
            entity.setBillingAddress(orderAddressService.convertToEntity(dto.getBillingAddress(), new OrderAddress()));
        }

        return entity;
    }

    @Override
    public ShipmentDto convertToDto(Shipment entity, ShipmentDto dto) {
        dto = super.convertToDto(entity, dto);

        if (!CollectionUtils.isEmpty(entity.getOrderItems())) {
            List<OrderItemDto> orderItemDtoList = new ArrayList<>();
            for (OrderItems orderItem : entity.getOrderItems()) {
                if (!orderItem.getItemStatus().equals(OrderItemStatus.SKIPPED)) {
                    orderItem.setShipmentId(entity.getId());
                }
                orderItemDtoList.add(orderItemService.convertToDto(orderItem, new OrderItemDto()));
            }
            dto.setOrderItems(orderItemDtoList);
        }

        if (!CollectionUtils.isEmpty(entity.getShipmentMetaData())) {
            List<ShipmentMetaDataDto> shipmentMetaDataDtoList = new ArrayList<>();
            for (ShipmentMetaData shipmentMetaData : entity.getShipmentMetaData()) {
                shipmentMetaDataDtoList.add(shipmentMetaService.convertToDto(shipmentMetaData, new ShipmentMetaDataDto()));
            }
            dto.setShipmentMetaData(shipmentMetaDataDtoList);
        }
        if (Objects.nonNull(entity.getShippingAddress())) {
            dto.setShippingAddress(orderAddressService.convertToDto(entity.getShippingAddress(), new OrderAddressDto()));
        }
        if (Objects.nonNull(entity.getBillingAddress())) {
            dto.setBillingAddress(orderAddressService.convertToDto(entity.getBillingAddress(), new OrderAddressDto()));
        }

        return dto;
    }

    @Override
    public ShipmentDto convertToDto(Shipment entity, ShipmentDto dto, Set<String> exclusionChildProperties) {
        dto = super.convertToDto(entity, dto, exclusionChildProperties);

        if (!exclusionChildProperties.contains(ApplicationConstants.CHILD_ENTITIES.ORDER_ITEMS) && !CollectionUtils.isEmpty(entity.getOrderItems())) {
            List<OrderItemDto> orderItemDtoList = new ArrayList<>();
            for (OrderItems orderItem : entity.getOrderItems()) {
                if (!orderItem.getItemStatus().equals(OrderItemStatus.SKIPPED)) {
                    orderItem.setShipmentId(entity.getId());
                }
                orderItemDtoList.add(orderItemService.convertToDto(orderItem, new OrderItemDto(), exclusionChildProperties));
            }
            dto.setOrderItems(orderItemDtoList);
        }

        if (!exclusionChildProperties.contains(ApplicationConstants.CHILD_ENTITIES.SHIPMENT_META_DATA) && !CollectionUtils.isEmpty(entity.getShipmentMetaData())) {
            List<ShipmentMetaDataDto> shipmentMetaDataDtoList = new ArrayList<>();
            for (ShipmentMetaData shipmentMetaData : entity.getShipmentMetaData()) {
                shipmentMetaDataDtoList.add(shipmentMetaService.convertToDto(shipmentMetaData, new ShipmentMetaDataDto(), exclusionChildProperties));
            }
            dto.setShipmentMetaData(shipmentMetaDataDtoList);
        }
        if (Objects.nonNull(entity.getShippingAddress())) {
            dto.setShippingAddress(orderAddressService.convertToDto(entity.getShippingAddress(), new OrderAddressDto(), exclusionChildProperties));
        }
        if (Objects.nonNull(entity.getBillingAddress())) {
            dto.setBillingAddress(orderAddressService.convertToDto(entity.getBillingAddress(), new OrderAddressDto(), exclusionChildProperties));
        }

        return dto;
    }

    @Override
    public List<ShipmentDto> findByShipmentStatusAndUpdatedAtBetween(String orderStatus, Date startHour, Date endHour) {
        List<Shipment> shipments = shipmentRepository.findByShipmentStatusAndUpdatedAtBetween(orderStatus, startHour, endHour);
        List<ShipmentDto> partiallyReassignedShipments = new ArrayList<>();
        if(!CollectionUtils.isEmpty(shipments)){
            partiallyReassignedShipments = shipments.stream()
                    .filter(shipment -> {
                        Long orderId = shipment.getOrderItems().get(0).getOrderId();
                        OrderDto orderDto = orderService.findById(orderId);
                        return OrderStatus.OMS_REASSIGNED == orderDto.getOrderStatus();
                    }).map(s -> convertToDto(s,new ShipmentDto()))
                    .collect(Collectors.toList());
        }
        return partiallyReassignedShipments;
    }

    @Override
    public void updateAssignedFacility(Long shipmentId, String facility) {
        shipmentRepository.updateAssignedFacility(facility, shipmentId);
        logger.info("[ShipmentServiceImpl] updated facility {} for shipment {}", facility, shipmentId);
    }
}
