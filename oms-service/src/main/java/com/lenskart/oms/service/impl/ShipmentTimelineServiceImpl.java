package com.lenskart.oms.service.impl;

import com.lenskart.nexs.commons.service.impl.BaseServiceImp;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.dto.OrderAddressDto;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.ShipmentTimelineDto;
import com.lenskart.oms.entity.ShipmentTimeline;
import com.lenskart.oms.service.ShipmentTimelineService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ShipmentTimelineServiceImpl extends BaseServiceImp<ShipmentTimelineDto, ShipmentTimeline> implements ShipmentTimelineService {

    public List<ShipmentTimelineDto> findAssociatedShipmentTimelines(OrderItemDto currentItemDto) {
        String queryString = "shipmentId.eq:" + currentItemDto.getShipmentId() + ApplicationConstants.AND_OPERATION + "orderItemId.eq:" + currentItemDto.getId() + ApplicationConstants.AND_OPERATION + "orderId.eq:" + currentItemDto.getOrderId();
        return search(queryString);
    }

    public List<ShipmentTimelineDto> findByShipmentId(Long shipmentId) {
        String queryString = "shipmentId.eq:" + shipmentId;
        return search(queryString);
    }

}
