package com.lenskart.oms.service.impl;

import com.lenskart.nexs.commons.service.impl.BaseServiceImp;
import com.lenskart.oms.dto.DistributorOrderItemsDto;
import com.lenskart.oms.entity.DistributorOrderItems;
import com.lenskart.oms.service.DistributorOrderItemsService;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
@Setter(onMethod__ = {@Autowired})
public class DistributorOrderItemsServiceImpl extends BaseServiceImp<DistributorOrderItemsDto, DistributorOrderItems> implements DistributorOrderItemsService {
}
