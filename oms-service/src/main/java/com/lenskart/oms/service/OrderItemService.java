package com.lenskart.oms.service;

import com.lenskart.nexs.commons.service.BaseService;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.entity.OrderItems;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.request.LensOnlyPidUpdateRequest;
import com.lenskart.oms.request.NavChannelRequest;
import com.lenskart.oms.request.OtcOrderItemRequest;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Tuple;
import java.util.List;
import java.util.Map;

public interface OrderItemService extends BaseService<OrderItemDto, OrderItems> {

    String getNavChannel(NavChannelRequest navChannelRequest);
    Integer getDistinctShipmentCount(Long orderId);

    void markOtcShipmentInvoice(OtcOrderItemRequest orderItemRequest);
    void updateFittingId(Long fittingId, Long orderItemId);
    void updateLensOnlyFrameBarcode(OrderItemDto orderItemDto,String unicomOrderCode) throws ApplicationException;
    void updateB2BRefId(Long orderItemId, Long b2bRefId);
    List<Tuple> getOrderItems(String wmsOrderCode);
    void updateSkippedItems(List<Long> orderItemIds, String itemStatus, String itemSubStatus, Long shipmentId, Long orderId);

    OrderItemDto updateLensOnlyFramePid(LensOnlyPidUpdateRequest lensOnlyPidUpdateRequest);
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    void updateFulfillableOrderItems(ShipmentDto shipmentDto, Map<Integer, Boolean> orderItemsFfMap);
}
