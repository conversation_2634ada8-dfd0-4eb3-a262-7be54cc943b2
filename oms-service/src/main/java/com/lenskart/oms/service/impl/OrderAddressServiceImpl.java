package com.lenskart.oms.service.impl;

import com.lenskart.nexs.commons.service.impl.BaseServiceImp;
import com.lenskart.oms.dto.OrderAddressDto;
import com.lenskart.oms.entity.OrderAddress;
import com.lenskart.oms.service.OrderAddressService;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Setter(onMethod__ = {@Autowired})
@Service
public class OrderAddressServiceImpl extends BaseServiceImp<OrderAddressDto, OrderAddress> implements OrderAddressService {

    @Override
    public OrderAddressDto findByOrderId(Long orderId) {
        return findBySearchTerms("orderId.eq:" + orderId);
    }
}
