package com.lenskart.oms.service.impl;

import com.lenskart.nexs.commons.service.impl.BaseServiceImp;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.dto.ShipmentMetaDataDto;
import com.lenskart.oms.entity.ShipmentMetaData;
import com.lenskart.oms.repository.ShipmentMetaRepository;
import com.lenskart.oms.service.ShipmentMetaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Optional;

@Service
public class ShipmentMetaServiceImpl extends BaseServiceImp<ShipmentMetaDataDto, ShipmentMetaData> implements ShipmentMetaService {

    @Autowired
    private ShipmentMetaRepository shipmentMetaRepository;

    @Override
    public void saveOrUpdateByShipmentIdAndKey(Long shipmentId, String entityKey, String entityValue) {
        Optional<ShipmentMetaData> shipmentMetaDataOptional = shipmentMetaRepository.findByShipmentIdAndEntityKey(shipmentId, entityKey);
        ShipmentMetaData shipmentMetaData;
        if(shipmentMetaDataOptional.isPresent()) {
            shipmentMetaData = shipmentMetaDataOptional.get();
            logger.info("[ShipmentMetaServiceImpl] Updating d365_published_dispatch for shipmentID: {} from {} to {}", shipmentId, shipmentMetaData.getEntityValue(), entityValue);
            shipmentMetaData.setEntityValue(entityValue);
            shipmentMetaData.setUpdatedAt(new Date());
        } else {
            shipmentMetaData = new ShipmentMetaData();
            shipmentMetaData.setShipmentId(shipmentId);
            shipmentMetaData.setEntityKey(entityKey);
            shipmentMetaData.setEntityValue(entityValue);
            shipmentMetaData.setCreatedBy(ApplicationConstants.DEFAULT_OMS_USER);
            shipmentMetaData.setUpdatedBy(ApplicationConstants.DEFAULT_OMS_USER);
            shipmentMetaData.setCreatedAt(new Date());
            shipmentMetaData.setUpdatedAt(new Date());
            logger.info("[ShipmentMetaServiceImpl] Creating new entry(d365_published_dispatch) for shipmentID: {} with value: {}", shipmentId, entityValue);
        }
        shipmentMetaRepository.save(shipmentMetaData);
    }
}
