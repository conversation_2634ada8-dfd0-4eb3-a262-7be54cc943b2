package com.lenskart.oms.service.impl;

import com.lenskart.nexs.commons.service.impl.BaseServiceImp;
import com.lenskart.oms.dto.*;
import com.lenskart.oms.entity.*;
import com.lenskart.oms.service.DistributorCustomerAddressDetailsService;
import com.lenskart.oms.service.DistributorCustomerDetailsService;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Setter(onMethod__ = {@Autowired})
@Service
public class DistributorCustomerDetailsServiceImpl extends BaseServiceImp<DistributorCustomerDetailsDto, DistributorCustomerDetails>  implements DistributorCustomerDetailsService {

    private DistributorCustomerAddressDetailsService distributorCustomerAddressDetailsService;

    @Override
    @Transactional
    public DistributorCustomerDetailsDto toggleCustomer(Long id, Boolean flag) {
       DistributorCustomerDetailsDto distributorCustomerDetailsDto = findById(id);
       distributorCustomerDetailsDto.setCustomerEnabled(flag);
       return save(distributorCustomerDetailsDto);
    }

    @Override
    public DistributorCustomerDetails convertToEntity(DistributorCustomerDetailsDto dto, DistributorCustomerDetails entity) {
        entity = super.convertToEntity(dto, entity);
        DistributorCustomerDetailsDto distributorCustomerDetailsDtoExisting = null;
        boolean addressChanged = false;
        List<DistributorCustomerAddressDetails> distributorCustomerAddressDetailsList = null;
        int index = 0;
        if(entity.getId() != null) {
            distributorCustomerDetailsDtoExisting = findById(entity.getId());
        }
        if (!CollectionUtils.isEmpty(dto.getCustomerAddressDetails()) && distributorCustomerDetailsDtoExisting == null) {
            distributorCustomerAddressDetailsList = new ArrayList<>();
            for (DistributorCustomerAddressDetailsDto distributorCustomerAddressDetailsDto : dto.getCustomerAddressDetails()) {
                distributorCustomerAddressDetailsList.add(distributorCustomerAddressDetailsService.convertToEntity(distributorCustomerAddressDetailsDto, new DistributorCustomerAddressDetails()));
            }
            entity.setDistributorCustomerAddressDetails(distributorCustomerAddressDetailsList);
        } else if (!CollectionUtils.isEmpty(dto.getCustomerAddressDetails())){
            distributorCustomerAddressDetailsList = entity.getDistributorCustomerAddressDetails();
            for (DistributorCustomerAddressDetailsDto distributorCustomerAddressDetailsDto : dto.getCustomerAddressDetails()) {
                if(!distributorCustomerAddressDetailsDto.equals(distributorCustomerDetailsDtoExisting.getCustomerAddressDetails().get(index))){
                    DistributorCustomerAddressDetails current = distributorCustomerAddressDetailsList.get(index);
                    distributorCustomerAddressDetailsList.set(index, distributorCustomerAddressDetailsService.convertToEntity(distributorCustomerAddressDetailsDto, current));
                    addressChanged = true;
                }
                index++;
            }
            if(addressChanged){entity.setDistributorCustomerAddressDetails(distributorCustomerAddressDetailsList);}
        }
        return entity;
    }

    @Override
    public DistributorCustomerDetailsDto convertToDto(DistributorCustomerDetails entity, DistributorCustomerDetailsDto dto) {
        dto = super.convertToDto(entity, dto);

        if (!CollectionUtils.isEmpty(entity.getDistributorCustomerAddressDetails())) {
            List<DistributorCustomerAddressDetailsDto> distributorCustomerAddressDetailsDtoList = new ArrayList<>();
            for (DistributorCustomerAddressDetails distributorCustomerAddressDetails : entity.getDistributorCustomerAddressDetails()) {
                distributorCustomerAddressDetails.setCustomerDetailsId(entity.getId());
                distributorCustomerAddressDetailsDtoList.add(distributorCustomerAddressDetailsService.convertToDto(distributorCustomerAddressDetails, new DistributorCustomerAddressDetailsDto()));
            }
            dto.setCustomerAddressDetails(distributorCustomerAddressDetailsDtoList);
        }
        return dto;
    }
}
