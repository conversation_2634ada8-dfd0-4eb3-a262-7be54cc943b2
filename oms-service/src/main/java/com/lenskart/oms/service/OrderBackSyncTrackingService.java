package com.lenskart.oms.service;

import com.lenskart.nexs.commons.service.BaseService;
import com.lenskart.oms.dto.OrderBackSyncTrackingDto;
import com.lenskart.oms.entity.OrderBackSyncTracking;
import com.lenskart.oms.enums.BackSyncEventName;
import com.lenskart.oms.enums.BackSyncSystem;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.request.ShipmentUpdateEvent;

public interface OrderBackSyncTrackingService extends BaseService<OrderBackSyncTrackingDto, OrderBackSyncTracking> {

    public void persistRequestInTracking(BackSyncEventName eventName, String wmsOrderCode, BackSyncSystem backSyncSystem);

    public void persistResponseInTracking(String apiResponse, ShipmentUpdateEvent shipmentUpdateEvent, BackSyncEventName backSyncEventName, String wmsOrderCode) throws ApplicationException;
}
