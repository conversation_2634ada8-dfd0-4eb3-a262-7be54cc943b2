package com.lenskart.oms.service.impl;

import com.lenskart.nexs.commons.service.impl.BaseServiceImp;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.constants.KafkaConstants;
import com.lenskart.oms.dto.*;
import com.lenskart.oms.entity.*;
import com.lenskart.oms.enums.*;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.repository.OrderItemRepository;
import com.lenskart.oms.request.*;
import com.lenskart.oms.service.*;
import com.lenskart.oms.producer.CommonKafkaProducer;
import com.lenskart.oms.repository.ShipmentRepository;
import com.lenskart.oms.request.NavChannelRequest;
import com.lenskart.oms.service.OrderItemMetaService;
import com.lenskart.oms.service.OrderItemPowerService;
import com.lenskart.oms.service.OrderItemPriceService;
import com.lenskart.oms.service.OrderItemService;
import com.lenskart.oms.utils.ObjectHelper;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.persistence.Tuple;
import java.util.*;
import java.util.stream.Collectors;

import static com.lenskart.oms.constants.ApplicationConstants.JJ_STORE_TYPE;

@Service
@Setter(onMethod__ = {@Autowired})
public class OrderItemServiceImpl extends BaseServiceImp<OrderItemDto, OrderItems> implements OrderItemService {

    @Value("#{'${ojos.facility.codes}'.split(',')}")
    @Setter(AccessLevel.NONE)
    private List<String> ojosFacilityCodes;

    private OrderItemMetaService orderItemMetaService;
    private OrderItemPowerService orderItemPowerService;
    private OrderItemPriceService orderItemPriceService;
    @Autowired
    private OrderItemRepository orderItemRepository;
    private CommonKafkaProducer commonKafkaProducer;
    private ShipmentService shipmentService;
    OrderItemService orderItemService;

    @Autowired
    private ShipmentRepository shipmentRepository;
    @Override
    public OrderItems convertToEntity(OrderItemDto dto, OrderItems entity) {
        entity = super.convertToEntity(dto, entity);

        if (Objects.isNull(dto.getOrderItemMetaData()) || Objects.isNull(dto.getOrderItemPrice()) || Objects.isNull(dto.getItemPower())) {
            throw new ApplicationException("Mandatory child entities can not be null");
        }
        if (!CollectionUtils.isEmpty(dto.getOrderItemMetaData())) {
            List<OrderItemMetaData> orderItemMetaDataList = new ArrayList<>();
            for (OrderItemMetaDataDto orderItemMetaDataDto : dto.getOrderItemMetaData()) {
                orderItemMetaDataList.add(orderItemMetaService.convertToEntity(orderItemMetaDataDto, new OrderItemMetaData()));
            }
            entity.setOrderItemMetaData(orderItemMetaDataList);
        }
        if (Objects.nonNull(dto.getOrderItemPrice())) {
            entity.setOrderItemPrice(orderItemPriceService.convertToEntity(dto.getOrderItemPrice(), new OrderItemPrice()));
        }
        if (Objects.nonNull(dto.getItemPower())) {
            entity.setItemPower(orderItemPowerService.convertToEntity(dto.getItemPower(), new OrderItemPower()));
        }
        if (dto.getItemStatus().equals(OrderItemStatus.SKIPPED)) {
            entity.setShipmentId(null);
            entity.setOrderId(null);
        }
        return entity;
    }

    @Override
    public OrderItemDto convertToDto(OrderItems entity, OrderItemDto dto) {
        dto = super.convertToDto(entity, dto);

        if (!CollectionUtils.isEmpty(entity.getOrderItemMetaData())) {
            List<OrderItemMetaDataDto> orderMetaDataDtoList = new ArrayList<>();
            for (OrderItemMetaData orderItemMetaData : entity.getOrderItemMetaData()) {
                orderMetaDataDtoList.add(orderItemMetaService.convertToDto(orderItemMetaData, new OrderItemMetaDataDto()));
            }
            dto.setOrderItemMetaData(orderMetaDataDtoList);
        }
        dto.setOrderId(entity.getOrderId());
        dto.setShipmentId(entity.getShipmentId());

        if (Objects.nonNull(entity.getOrderItemPrice())) {
            dto.setOrderItemPrice(orderItemPriceService.convertToDto(entity.getOrderItemPrice(), new OrderItemPricesDto()));
        }
        if (Objects.nonNull(entity.getItemPower())) {
            dto.setItemPower(orderItemPowerService.convertToDto(entity.getItemPower(), new OrderItemPowerDto()));
        }
        if (entity.getItemStatus().equals(OrderItemStatus.SKIPPED)) {
            dto.setShipmentId(null);
            dto.setOrderId(null);
        }
        return dto;
    }

    @Override
    public OrderItemDto convertToDto(OrderItems entity, OrderItemDto dto, Set<String> exclusionChildProperties) {
        dto = super.convertToDto(entity, dto);

        if (!exclusionChildProperties.contains(ApplicationConstants.CHILD_ENTITIES.ORDER_ITEM_META_DATA) && !CollectionUtils.isEmpty(entity.getOrderItemMetaData())) {
            List<OrderItemMetaDataDto> orderMetaDataDtoList = new ArrayList<>();
            for (OrderItemMetaData orderItemMetaData : entity.getOrderItemMetaData()) {
                orderMetaDataDtoList.add(orderItemMetaService.convertToDto(orderItemMetaData, new OrderItemMetaDataDto(), exclusionChildProperties));
            }
            dto.setOrderItemMetaData(orderMetaDataDtoList);
        }

        dto.setOrderId(entity.getOrderId());
        dto.setShipmentId(entity.getShipmentId());

        if (!exclusionChildProperties.contains(ApplicationConstants.CHILD_ENTITIES.ORDER_ITEM_PRICE) && Objects.nonNull(entity.getOrderItemPrice())) {
            dto.setOrderItemPrice(orderItemPriceService.convertToDto(entity.getOrderItemPrice(), new OrderItemPricesDto(), exclusionChildProperties));
        }

        if (!exclusionChildProperties.contains(ApplicationConstants.CHILD_ENTITIES.ITEM_POWER) && Objects.nonNull(entity.getItemPower())) {
            dto.setItemPower(orderItemPowerService.convertToDto(entity.getItemPower(), new OrderItemPowerDto(), exclusionChildProperties));
        }
        if (entity.getItemStatus().equals(OrderItemStatus.SKIPPED)) {
            dto.setShipmentId(null);
            dto.setOrderId(null);
        }
        return dto;
    }

    @Override
    public String getNavChannel(NavChannelRequest navChannelRequest) {

        String navisionChannel = StringUtils.EMPTY;
        String productDeliveryType = navChannelRequest.getProductDeliveryType();
        String facilityCode = navChannelRequest.getFacilityCode();
        String storeType = navChannelRequest.getStoreType();
        Integer storeId = navChannelRequest.getStoreId();
        String clientOrg = navChannelRequest.getClientOrg();
        boolean isBulkOrder = navChannelRequest.getIsBulkOrder() != null && navChannelRequest.getIsBulkOrder();
        if(navChannelRequest.getGetIsDistributorJitOrder() || navChannelRequest.getIsDistributorOrder()){
            navisionChannel = NavChannel.DODTC.name();
        }
        else if (facilityCode != null && facilityCode.equalsIgnoreCase(ApplicationConstants.FACILITY_CODE_ONLINE)) {
            navisionChannel = NavChannel.WEBDTC.name();
            navisionChannel = getNavChannelFromStoreType(storeType, ProductDeliveryType.DTC.name(), navisionChannel);
            if (StoreId.JJONLINE_STOREID.getValue().equals(storeId) && ProductDeliveryType.B2B.name().equalsIgnoreCase(productDeliveryType)) {
                navisionChannel = NavChannel.JJONLINEB2B.name();
            } else if (StringUtils.isNotBlank(facilityCode) && ojosFacilityCodes.contains(facilityCode.toUpperCase())
                    && ProductDeliveryType.B2B.name().equalsIgnoreCase(productDeliveryType)) {
                navisionChannel = NavChannel.OJOSB2B.name();
            } else if (StoreId.JJONLINE_STOREID.getValue().equals(storeId)) {
                navisionChannel = NavChannel.JJONLINEDTC.name();
            } else if (StringUtils.isNotBlank(facilityCode) && ojosFacilityCodes.contains(facilityCode.toUpperCase())) {
                navisionChannel = NavChannel.OJOSDTC.name();
            } else if (ProductDeliveryType.B2B.name().equalsIgnoreCase(productDeliveryType) && ApplicationConstants.OD_CLIENT_ORG.equalsIgnoreCase(clientOrg)) {
                navisionChannel = NavChannel.ODONLINEB2B.name();
            } else if (ProductDeliveryType.B2B.name().equalsIgnoreCase(productDeliveryType)) {
                navisionChannel = NavChannel.WEBB2B.name();
            } else if (ProductDeliveryType.OTC.name().equalsIgnoreCase(productDeliveryType)) {
                navisionChannel = NavChannel.WEBOTC.name();
            }
        } else {
             if (isBulkOrder) {
                navisionChannel = getNavChannelFromStoreType(storeType, NavChannelPrefix.BULK.name(), navisionChannel);
                if (StringUtils.isNotBlank(facilityCode) && ojosFacilityCodes.contains(facilityCode.toUpperCase())) {
                    navisionChannel = NavChannel.OJOSBULK.name();
                }
            } else if (ProductDeliveryType.B2B.name().equalsIgnoreCase(productDeliveryType)) {
                navisionChannel = getNavChannelFromStoreType(storeType, ProductDeliveryType.B2B.name(), navisionChannel);
                if ((StoreId.JJONLINE_STOREID.getValue().equals(storeId))
                        && (storeType != null && !StoreType.COCO_JJ_STORE.name().equalsIgnoreCase(storeType))) {
                    navisionChannel = NavChannel.JJONLINEB2B.name();
                }
                if (StringUtils.isNotBlank(facilityCode) && ojosFacilityCodes.contains(facilityCode.toUpperCase())) {
                    navisionChannel = NavChannel.OJOSB2B.name();
                }
                if(ApplicationConstants.OD_CLIENT_ORG.equalsIgnoreCase(clientOrg)) {
                    navisionChannel = NavChannel.ODONLINEB2B.name();
                }
            } else if (ProductDeliveryType.OTC.name().equalsIgnoreCase(productDeliveryType)) {
                navisionChannel = getNavChannelFromStoreType(storeType, ProductDeliveryType.OTC.name(), navisionChannel);
                if (StringUtils.isNotBlank(facilityCode) && ojosFacilityCodes.contains(facilityCode.toUpperCase())) {
                    navisionChannel = NavChannel.OJOSOTC.name();
                }
            } else {
                navisionChannel = getNavChannelFromStoreType(storeType, ProductDeliveryType.DTC.name(), navisionChannel);
                if ((storeType != null && !StoreType.COCO_JJ_STORE.name().equalsIgnoreCase(storeType))
                        && (StoreId.JJONLINE_STOREID.getValue().equals(storeId))) {
                    navisionChannel = NavChannel.JJONLINEDTC.name();
                }
                if (StringUtils.isNotBlank(facilityCode) && ojosFacilityCodes.contains(facilityCode.toUpperCase())) {
                    navisionChannel = NavChannel.OJOSDTC.name();
                }
            }
        }
        return navisionChannel;
    }

    @Override
    public Integer getDistinctShipmentCount(Long orderId) {
        return orderItemRepository.getDistinctShipmentCount(orderId);
    }

    @Override
    public void updateB2BRefId(Long orderItemId, Long b2bRefId) {
        orderItemRepository.updateB2BRefId(orderItemId, b2bRefId);
    }

    @Override
    public List<Tuple> getOrderItems(String wmsOrderCode) {
        return orderItemRepository.getOrderItems(wmsOrderCode);
    }

    @Override
    public void updateSkippedItems(List<Long> orderItemIds, String itemStatus, String itemSubStatus, Long shipmentId, Long orderId) {
        orderItemRepository.updateSkippedItems(orderItemIds,itemStatus,itemSubStatus,shipmentId,orderId);
    }

    private String getNavChannelFromStoreType(String storeType, String navChannelSuffix, String navisionChannelOriginal) {
        String navisionChannel;
        if (storeType == null || JJ_STORE_TYPE.equalsIgnoreCase(storeType)) {
            return navisionChannelOriginal;
        }
        switch (StoreType.valueOf(storeType)) {
            case COCO_LENSKART_STORE :
            case COCO_INTERNATIONAL:
                navisionChannel = NavChannelPrefix.COCO.name().concat(navChannelSuffix);
                break;
            case FOFO_WITH_SC:
            case FOFO_WITHOUT_SC:
                navisionChannel = NavChannelPrefix.FOFO.name().concat(navChannelSuffix);
                break;
            case HEC:
                navisionChannel = NavChannelPrefix.HEC.name().concat(navChannelSuffix);
                break;
            case COCO_JJ_STORE:
                navisionChannel = NavChannelPrefix.JJ.name().concat(navChannelSuffix);
                break;
            case HUB_INTERNATIONAL:
                navisionChannel = NavChannelPrefix.HUB.name().concat(navChannelSuffix);
                break;
            default:
                navisionChannel = navisionChannelOriginal;
                break;
        }
        return navisionChannel;
    }

    @Override
    public void markOtcShipmentInvoice(OtcOrderItemRequest orderItemRequest) {
        logger.info("Got request for wmsOrderCode {} ",orderItemRequest.getUnicomOrderCode());
        List<OrderItems> orderItemsList = orderItemRepository.findByMagentoItemId(orderItemRequest.getMagentoItemId());
        logger.info("OrderItems size is {} for magentoItemId {}",orderItemsList.size(), orderItemRequest.getMagentoItemId());
        for(OrderItemForOtcOrder itemRequest : orderItemRequest.getItems()){
           OrderItems items = orderItemsList.stream().filter(oi -> oi.getProductId().equals(itemRequest.getPid())).findFirst().orElse(null);
           logger.info("Item id is: {} uwItemId: {}", items.getUwItemId(), items.getUwItemId());
           items.setItemBarcode(itemRequest.getBarcode());
           orderItemRepository.save(items);
           logger.info("Order item id {} detail saved with barcode {} for local fitting", items.getUwItemId(), items.getItemBarcode());
        }
        Shipment shipment = shipmentRepository.findByWmsOrderCode(orderItemRequest.getUnicomOrderCode());
        commonKafkaProducer.sendMessage(
                KafkaConstants.OMS_OTC_ORDER_EVENTS_PROCESS_TOPIC,
                String.valueOf(orderItemsList.get(0).getOrderId()),
                ObjectHelper.convertToString(new OtcShipmentEvent(OtcShipmentEventType.INVOICED, shipment.getId())),
                String.valueOf(shipment.getId()));
        logger.info("Invoice call executed for wmsOrderCode {} ", orderItemRequest.getUnicomOrderCode());
    }

    @Override
    public void updateFittingId(Long fittingId, Long orderItemId){
        orderItemRepository.updateFittingId(fittingId, orderItemId);
    }

    @Override
    @Transactional(readOnly = false,rollbackFor = Exception.class)
    public void updateLensOnlyFrameBarcode(OrderItemDto orderItemDto, String unicomOrderCode){
        if (StringUtils.isBlank(unicomOrderCode) || StringUtils.isBlank(orderItemDto.getItemBarcode())
                || orderItemDto.getProductId() == null) {
            throw new ApplicationException("unicom order code / barcode / product id can not be null or empty");
        }
        ShipmentDto shipmentDto = shipmentService.findBySearchTerms("wmsOrderCode.eq:" + unicomOrderCode);
        List<OrderItemDto> orderItemsList = shipmentDto.getOrderItems();
        logger.info("[OrderItemServiceImpl -> updateLensOnlyFrameBarcode] orderItems :{}", orderItemsList);
        if(!FulfillmentType.LOCAL_FITTING.equals(orderItemsList.get(0).getFulfillmentType())){
            throw new ApplicationException("not a local fitting order");
        }
        for(OrderItemDto orderItems : orderItemsList){
            logger.info("[OrderItemServiceImpl -> updateLensOnlyFrameBarcode] orderItemDto :{}", orderItems );
            if(ItemType.getFrameItemTypes().contains(orderItems.getItemType().name())){
                orderItems.setItemBarcode(orderItemDto.getItemBarcode());
                orderItems.setProductId(orderItemDto.getProductId());
                orderItems = orderItemService.save(orderItems);
                logger.info("[OrderItemServiceImpl -> updateLensOnlyFrameBarcode] item saved succesfully :{}", orderItems);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderItemDto updateLensOnlyFramePid(LensOnlyPidUpdateRequest lensOnlyPidUpdateRequest) {
        validateLensOnlyUpdateRequest(lensOnlyPidUpdateRequest);
        Long productId = lensOnlyPidUpdateRequest.getProductId();
        Long magentoItemId = lensOnlyPidUpdateRequest.getMagentoItemId();
        ItemType itemType = lensOnlyPidUpdateRequest.getItemType();
        List<OrderItemDto> orderItemDtoList = search("magentoItemId.eq:" + magentoItemId + "___itemType.eq:" + itemType.name());

        if(!CollectionUtils.isEmpty(orderItemDtoList)) {
            logger.info("[updateLensOnlyFramePid] Updating pid for lens only frame for Id : {}, magentoItemId: {}, productId :{}", orderItemDtoList.stream().map(i  ->i.getId()).collect(Collectors.toSet()), magentoItemId, productId);
            for(OrderItemDto orderItemDto: orderItemDtoList){
                orderItemDto.setProductId(productId);
                save(orderItemDto);
            }

        }

        return orderItemDtoList.get(0);
    }

    private void validateLensOnlyUpdateRequest(LensOnlyPidUpdateRequest request) {
        if (request == null ||
                request.getProductId() == null ||
                request.getMagentoItemId() == null ||
                request.getItemType() == null) {
            throw new ApplicationException("Invalid lens only frame pid update request");
        }
    }

    @Override
    public void updateFulfillableOrderItems(ShipmentDto shipmentDto, Map<Integer, Boolean> orderItemsFfMap) {
        List<Long> uffOrderItemIds = new ArrayList<>();
        List<OrderItemDto> removeableOrderItems = new ArrayList<>();
        for (OrderItemDto orderItemDto : shipmentDto.getOrderItems()) {
            if (orderItemsFfMap.containsKey(orderItemDto.getUwItemId().intValue()) && !orderItemsFfMap.get(orderItemDto.getUwItemId().intValue())) {
                uffOrderItemIds.add(orderItemDto.getUwItemId());
                orderItemDto.setShipmentId(null);
                orderItemDto.setOrderId(null);
                orderItemDto.setItemStatus(OrderItemStatus.SKIPPED);
                orderItemDto.setItemSubStatus(OrderItemSubStatus.SKIPPED);
                orderItemService.save(orderItemDto);
                removeableOrderItems.add(orderItemDto);
                logger.info("updateFulfillableOrderItems - Marking OrderItemId {} SKIPPED for wmsOrderCode {} because of inventory unavailability.", orderItemDto.getUwItemId(), shipmentDto.getWmsOrderCode());
            }
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(uffOrderItemIds)) {
            shipmentDto.getOrderItems().removeAll(removeableOrderItems);
        }
    }
}
