package com.lenskart.oms.validators;

import com.lenskart.core.model.Order;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.connector.FsClientConnector;
import com.lenskart.oms.connector.OrderOpsConnector;
import com.lenskart.oms.connector.CatalogOpsConnector;
import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.enums.*;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.service.OrderMetaService;
import com.lenskart.oms.service.OrderService;
import com.lenskart.oms.utils.OmsCommonUtil;
import com.lenskart.order.interceptor.enums.createorder.Status;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Metrics;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;

import static com.lenskart.oms.constants.ApplicationConstants.*;

@Component
@Setter(onMethod__ = {@Autowired})
public class OrderSyncValidator {

    private static final Counter PAYMENT_FAILURE_COUNTER = Metrics.counter("invalid_payment");

    private OrderService orderService;
    private OmsCommonUtil omsCommonUtil;
    private OrderOpsConnector orderOpsConnector;
    private FsClientConnector fsClientConnector;


    @Setter(AccessLevel.NONE)
    @Value("#{'${order.valid.payment.methods:cashondelivery,paytbyb,payzero,offlinecash,offlinecard,storecredit,giftcard,giftvoucher,franchisecredit,offlinepart,checkmo,ccavenuepay,seamless,payucheckout_shared,citrus,payu_shared,emi_shared,payback,payzippy,payumoney,wallet,mswipe,paytm_cc,pogo,olamoney,purchaseorder,storecard,storecash,storemisc,storemomoe,exchangep,offlinepaytm,offlineairtel,storepaytm,lenskartwallet,storeairtel,storephonepe,paypal,aqualensrazorpay,lkusstripe,juspay,ezetapcard,paylater,marketplace,medibuddy,primer}'.split(',')}")
    private Set<String> orderValidPaymentMethods;

    @Setter(AccessLevel.NONE)
    @Value("#{'${franchise.order.valid.payment.methods:storecard,storecash,storemisc,storemomoe,franchisecredit,offlinemomoe,storepaytm,lenskartwallet,storeairtel,storephonepe}'.split(',')}")
    private Set<String> franchiseOrderValidPaymentMethods;

    @CustomLogger
    @Setter(AccessLevel.NONE)
    protected Logger logger;


    public boolean isFraudOrder(ShipmentDto shipmentDto, OrderDto orderDto) {
        boolean isErisBlackListUser = erisBlackListCheck(
                omsCommonUtil.getOrderMetaValueFromKey(orderDto.getOrderMetaData(), ORDER_META_KEY_CUSTOMER_EMAIL)
        );

        if (isErisBlackListUser) {
            String salesCode = omsCommonUtil.getOrderMetaValueFromKey(orderDto.getOrderMetaData(), ORDER_META_KEY_SALES_CODE);
            if (!StringUtils.hasLength(salesCode)) {
                return isBlackListOrder(shipmentDto, orderDto);
            }
        }

        logger.info("[ValidateOrderSyncStrategy -> isFraudOrder] isErisBlackListUser -> {} for order {}", isErisBlackListUser, orderDto.getIncrementId());
        return isErisBlackListUser;
    }

    public boolean isPaymentCaptured(OrderDto orderDto) {
        if(omsCommonUtil.isDistributorOrder(orderDto)){
            return orderDto.getPaymentCaptured();
        }
        if (!SKIP_PAYMENT_METHOD.contains(orderDto.getPaymentMethod())) {
            return paymentValidationByContext(orderDto);
        } else {
            logger.info("[ValidateOrderSyncStrategy -> isPaymentCaptured] payment validation check passed for order {}", orderDto.getIncrementId());
            return true;
        }
    }

    public boolean isPowerWiseProductIdsAssigned(Long incrementId){
        logger.info("[isPowerWiseProductIdsAssigned] isPowerWiseProductIdsAssigned check for increment id: {}", incrementId);
        OrderDto order = orderService.findByIncrementId(incrementId);
        List<OrderItemDto> orderItems = order.getOrderItems();

        for(OrderItemDto orderItem: orderItems){
            if(omsCommonUtil.isNonWarehouseProcessingOrder(orderItem)){
                logger.info("[isPowerWiseProductIdsAssigned] skipping check for otc orderItemId {}", orderItem.getId());
                continue;
            }
            if(isPowerWiseIdNotAssigned(order, orderItem)){
                logger.info("[isPowerWiseProductIdsAssigned] power wise id not assigned for orderItem: {}", orderItem);
                return false;
            }
        }
        return true;
    }

    private static boolean isPowerWiseIdNotAssigned(OrderDto order, OrderItemDto orderItem) {
        return ((orderItem.getProductId().equals(Long.valueOf(TEMP_LEFT_LENS_PID)) && orderItem.getItemType().equals(ItemType.LEFT_LENS))
                || (orderItem.getProductId().equals(Long.valueOf(TEMP_RIGHT_LENS_PID)) && orderItem.getItemType().equals(ItemType.RIGHT_LENS)))
                && !Boolean.TRUE.equals(order.getIsOnHold())
                && (OrderStatus.getNonProcessingStatus().contains(order.getOrderStatus().name()));
    }

    private Boolean erisBlackListCheck(String customerEmail) {
        return customerEmail.contains("@eris.com");
    }

    public boolean isBlackListOrder(ShipmentDto shipmentDto, OrderDto orderDto) {
        boolean validityCheckStatusFlag = Status.PROCESSING.getValue()
                .equalsIgnoreCase(omsCommonUtil.getOrderMetaValueFromKey(orderDto.getOrderMetaData(), ORDER_META_KEY_JUNO_INITIAL_STATUS));
        if (validityCheckStatusFlag && Long.valueOf(StoreId.JJONLINE_STOREID.getValue()).equals(orderDto.getStoreId())) {
            return isInvalidOrderPayload(shipmentDto, orderDto);
        }

        return false;
    }

    public Boolean isInvalidOrderPayload(ShipmentDto shipmentDto, OrderDto orderDto) {
        try {
            Boolean isPriceValid = true;
            Boolean isCustomerValid = true;

            if (BLACKLIST_PASS_PAYMENT_METHODS.contains(orderDto.getPaymentMethod())) {
                return true;
            }

            String orderGrandTotal = omsCommonUtil.getOrderMetaValueFromKey(orderDto.getOrderMetaData(), ORDER_META_KEY_GRAND_TOTAL);
            String offer3OrFree = omsCommonUtil.getOrderMetaValueFromKey(orderDto.getOrderMetaData(), ORDER_META_KEY_OFFER_3ORFREE);
            String customerId = omsCommonUtil.getOrderMetaValueFromKey(orderDto.getOrderMetaData(), ORDER_META_KEY_CUSTOMER_ID);
            boolean paymentMethodHtoOrderFlag = true;
            boolean isHECOrder = false;
            for (OrderItemDto orderItem : shipmentDto.getOrderItems()) {
                if (HOME_EYE_CHECK_UP_PROGRAM.equals(orderItem.getProductId())) {
                    paymentMethodHtoOrderFlag = false;
                    isHECOrder = true;
                    break;
                }
            }

            if (!isHECOrder) {
                isPriceValid = validatePrice(shipmentDto, orderDto, orderGrandTotal, customerId);
            }

            boolean paymentMethodChainFlag = PaymentMethod.CASHONDELIVERY.getValue().equalsIgnoreCase(orderDto.getPaymentMethod())
                    || PaymentMethod.PAYTBYB.getValue().equalsIgnoreCase(orderDto.getPaymentMethod())
                    || PaymentMethod.GIFTVOUCHER.getValue().equalsIgnoreCase(orderDto.getPaymentMethod())
                    || PaymentMethod.LKWALLET.getValue().equalsIgnoreCase(orderDto.getPaymentMethod())
                    || (StringUtils.hasLength(orderGrandTotal) && orderGrandTotal.equalsIgnoreCase("0"));

            boolean paymentMethodFranchiseFlag = FRANCHISE_PAYMENT_METHODS.contains(orderDto.getPaymentMethod());
            boolean paymentMethodStoreCreditFlag = PaymentMethod.STORECREDIT.getValue().equalsIgnoreCase(orderDto.getPaymentMethod());
            boolean paymentMethodGet3orFreeFlag = StringUtils.hasLength(offer3OrFree) && offer3OrFree.substring(offer3OrFree.length() - 4, 1).equalsIgnoreCase("1");

            if (paymentMethodChainFlag
                    && paymentMethodFranchiseFlag
                    && paymentMethodStoreCreditFlag
                    && paymentMethodGet3orFreeFlag
                    && paymentMethodHtoOrderFlag
                    && StringUtils.hasLength(customerId)) {
                isCustomerValid = validateCustomer(Long.valueOf(customerId));
            }

            return !(isCustomerValid && isPriceValid);
        } catch (Exception e) {
            return true;
        }
    }

    private Boolean validatePrice(ShipmentDto shipmentDto, OrderDto orderDto, String orderGrandTotal, String customerId) throws ApplicationException {
        boolean productIdCheckFlag;
        double storeCreditAmount = 100.0;

        List<Integer> productIds = new ArrayList<>();
        for (OrderItemDto orderItem : shipmentDto.getOrderItems()) {
            productIds.add(Math.toIntExact(orderItem.getProductId()));
        }
        productIdCheckFlag = setProductIdCheckFlag(productIds);

        List<Integer> orderIdCount = new ArrayList<>();
        if (StringUtils.hasLength(customerId)) {
            orderIdCount = countOrderIdsByCustomer(customerId);
        }

        String storeType = omsCommonUtil.getOrderMetaValueFromKey(orderDto.getOrderMetaData(), ORDER_META_KEY_STORE_TYPE);
        String shippingCharges = omsCommonUtil.getOrderMetaValueFromKey(orderDto.getOrderMetaData(), ORDER_META_KEY_SHIPPING_CHARGE);
        String subTotal = omsCommonUtil.getOrderMetaValueFromKey(orderDto.getOrderMetaData(), ORDER_META_KEY_SUB_TOTAL);
        String totalDiscount = omsCommonUtil.getOrderMetaValueFromKey(orderDto.getOrderMetaData(), ORDER_META_KEY_TOTAL_DISCOUNT);
        String isBulkOrder = omsCommonUtil.getOrderMetaValueFromKey(orderDto.getOrderMetaData(), ORDER_META_KEY_BULK_ORDER);

        boolean multiStoreCreditFlag = orderIdCount.size() > 3;
        Boolean maxDiscountCheck = getMaxDiscountOrderCheck(Double.parseDouble(orderGrandTotal), Double.parseDouble(subTotal), Double.parseDouble(shippingCharges), storeCreditAmount);
        boolean isOnlyHECOrderFlag = StoreType.HEC.name().equalsIgnoreCase(storeType);
        boolean isPosOrderFlag = StringUtils.hasLength(storeType);
        boolean isInternationalBulkOrder = StoreType.COCO_INTERNATIONAL.name().equalsIgnoreCase(storeType) && (StringUtils.hasLength(isBulkOrder) && Boolean.valueOf(isBulkOrder));
        String OrderPaymentMethod = orderDto.getPaymentMethod();

        double totalOrderValue = (StringUtils.hasLength(orderGrandTotal) ? Double.parseDouble(orderGrandTotal) : 0) + storeCreditAmount;
        if  ((getMaxSeventyFivePercentOrderCheck(Double.valueOf(totalDiscount), Double.valueOf(subTotal)) || subTotal.equals(totalDiscount))
                && !isOnlyHECOrderFlag
                && isInternationalBulkOrder
        ) {
            return false;
        } else if (!Channel.EXCHANGE.name().equalsIgnoreCase(shipmentDto.getOrderItems().get(0).getChannel().name())
                && multiStoreCreditFlag
                && !FRANCHISE_PAYMENT_METHODS.contains(OrderPaymentMethod)
                && !isOnlyHECOrderFlag
        ) {
            return false;
        } else if (totalOrderValue == 0
                && !PaymentMethod.EXCHANGEP.getValue().equals(OrderPaymentMethod)
                && !PaymentMethod.STORECREDIT.getValue().equals(OrderPaymentMethod)
                && !PaymentMethod.PAYTBYB.getValue().equals(OrderPaymentMethod)
                && !FRANCHISE_PAYMENT_METHODS.contains(OrderPaymentMethod)
                && !isOnlyHECOrderFlag
        ) {
            return false;
        } else if (!PaymentMethod.PAYTBYB.getValue().equals(OrderPaymentMethod)
                && maxDiscountCheck
                && !FRANCHISE_PAYMENT_METHODS.contains(OrderPaymentMethod)
                && !isPosOrderFlag
                && productIdCheckFlag
        ) {
            return false;
        } else {
            return true;
        }
    }

    private Boolean setProductIdCheckFlag(List<Integer> productIds){
        boolean productIdCheckFlag = true;

        for (Integer productId : productIds) {
            if (Arrays.asList(Arrays.asList(BLACKLIST_PRODUCT_ID.split(","))
                            .stream()
                            .map(String::trim)
                            .mapToInt(Integer::parseInt)
                            .toArray())
                    .contains(productId)
            ) {
                productIdCheckFlag = false;
            }
        }

        return productIdCheckFlag;
    }

    private Boolean getMaxDiscountOrderCheck(Double grandTotal, Double subTotal, Double shippingCharge, Double storeCreditAmount) {
        boolean maxDiscountOrderCheck = false;
        double divisor = subTotal + shippingCharge;

        if (divisor != 0.0 && ((grandTotal + storeCreditAmount) / divisor) * 100 < 25.0) {
            maxDiscountOrderCheck = true;
        }

        return maxDiscountOrderCheck;
    }

    private Boolean getMaxSeventyFivePercentOrderCheck(Double totalDiscount, Double subTotal) {
        boolean seventyFivePercentCheck = ((totalDiscount / subTotal) * 100) > 75.0;

        return seventyFivePercentCheck;
    }

    private List<Integer> countOrderIdsByCustomer(String customerId) throws ApplicationException {
        return orderOpsConnector.countStoreCreditOrdersByCustomerId(Long.valueOf(customerId));
    }

    private Boolean validateCustomer(Long customerId) throws ApplicationException {
        Integer blacklistCustomerCount = orderOpsConnector.countCustomerBlacklistFlag(customerId);
        return !(blacklistCustomerCount > 0);
    }

    private Boolean paymentValidationByContext(OrderDto orderDto) {
        logger.info("[paymentValidationByContext] going to check payment validation for order {} with payment mode {}", orderDto.getIncrementId(), orderDto.getPaymentMethod());

        switch (orderDto.getPaymentMethod()) {
            case "paytbyb":
                return payTBYBPaymentValidation(orderDto);

            /**  cashOnDelivery Fraud check not required */
            case "cashondelivery":
                return true;
//                return cashOnDeliveryPaymentValidation(orderDto);

            case "offlinecash":

            case "offlinecard":

            case "offlinepart":

            case "offlinepaytm":

            case "offlineairtel":
                return offlinePaymentValidation(orderDto);

            case "payzero":
                return payZeroPaymentValidation(orderDto);

            case "storecredit":
                return storeCreditPaymentValidation(orderDto);

//    TODO - GV might not be required. confirm and remove the method
//            case "giftvoucher":
//                return giftVoucherPaymentValidation(orderDto);

            case "franchisecredit":
                return franchiseCreditPaymentValidation(orderDto);

            case "lenskartwallet":
                return lenskartWalletPaymentValidation(orderDto);

            default:
                return invalidPaymentValidation(orderDto);
        }
    }

    private Boolean offlinePaymentValidation(OrderDto orderDto) {
        String storeType = omsCommonUtil.getOrderMetaValueFromKey(orderDto.getOrderMetaData(), ORDER_META_KEY_STORE_TYPE);
        if (storeType == null || !OWN_STORE_OR_JJ_FRANCHISE.contains(storeType)) {
            return false;
        }

        return true;
    }

    private Boolean lenskartWalletPaymentValidation(OrderDto orderDto) {
        BigDecimal lkWalletAmount = fsClientConnector.isEntryInLkWallet(orderDto.getIncrementId());
        double comparisonAmount = 0;
        if (!(lkWalletAmount.compareTo(BigDecimal.valueOf(comparisonAmount)) > 0)) {
            return false;
        }

        return true;
    }

    private Boolean franchiseCreditPaymentValidation(OrderDto orderDto) {
        String isDualCompanyEnabled = omsCommonUtil.getOrderMetaValueFromKey(orderDto.getOrderMetaData(), ORDER_META_KEY_DUAL_COMPANY_ENABLED);
        Integer distinctIncrementIdCount = fsClientConnector.isFranchiseCreditUsed(orderDto.getIncrementId());
        if (distinctIncrementIdCount == 0
                && !(StringUtils.hasLength(isDualCompanyEnabled) && Boolean.TRUE.toString().equalsIgnoreCase(isDualCompanyEnabled))
        ) {
            return false;
        }

        return true;
    }

//    TODO - GV might not be required. confirm and remove the method
//    private Boolean giftVoucherPaymentValidation(OrderDto orderDto) {
//        PrevProcessingOrdersDetails prevProcessingOrdersDetails = new PrevProcessingOrdersDetails();
//        if (prevProcessingOrdersDetails.getPrevProcessingOrders(orderPayload, incrementId)) {
//            return false;
//        }
//
//        return true;
//    }

    private Boolean storeCreditPaymentValidation(OrderDto orderDto) {
        return true;
//        TODO - NEED TO FIX THIS 0 STORE CREDIT USED ONCE POS FIXED THIS ISSUE

//        BigDecimal orderAmount =  fsClientConnector.isStoreCreditUsed(orderDto.getIncrementId());
//        double comparisonAmount = 0.0;
//        if (!(orderAmount.compareTo(BigDecimal.valueOf(comparisonAmount)) > 0)) {
//            return false;
//        }
//
//        return true;
    }

    private Boolean payZeroPaymentValidation(OrderDto orderDto) {
        String grandTotal = omsCommonUtil.getOrderMetaValueFromKey(orderDto.getOrderMetaData(), ORDER_META_KEY_GRAND_TOTAL);
        if (StringUtils.hasLength(grandTotal) && Double.parseDouble(grandTotal) > 1) {
            return false;
        }

        return true;
    }

    private Boolean cashOnDeliveryPaymentValidation(OrderDto orderDto) {
        try {
            boolean partialPaymentFlag = false;
            String grandTotal = omsCommonUtil.getOrderMetaValueFromKey(orderDto.getOrderMetaData(), ORDER_META_KEY_GRAND_TOTAL);
            String customerId = omsCommonUtil.getOrderMetaValueFromKey(orderDto.getOrderMetaData(), ORDER_META_KEY_CUSTOMER_ID);
            Integer tokaiCount = orderOpsConnector.countTokaiItemsForOrder(orderDto.getJunoOrderId());

            if (tokaiCount > 0) {
                return false;
            } else if (StringUtils.hasLength(grandTotal) && Double.parseDouble(grandTotal) > 4000) {
                try {
                    Integer partialPaymentCount = fsClientConnector.isPartPaymentOrder(orderDto.getIncrementId());
                    if (partialPaymentCount > 0) {
                        partialPaymentFlag = true;
                    }
                } catch (Exception e) {
                    partialPaymentFlag = true;
                }

                Order lastOrderForCustomer = orderOpsConnector.findLastOrderByCustomerId(Long.valueOf(customerId));
                if (lastOrderForCustomer != null) {
                    Boolean lastOrderValidityFlag = lastOrderForCustomer.getGrandTotal() != null && lastOrderForCustomer.getGrandTotal().intValue() > 1000
                            && OrderStatus.DELIVERED.name().equalsIgnoreCase(lastOrderForCustomer.getStatus()) ? true : false;

                    if (!lastOrderValidityFlag && !partialPaymentFlag) {
                        return false;
                    }
                }
            }

            return true;
        } catch (Exception e) {
            logger.error("[cashOnDeliveryPaymentValidation] payment validation failed for order {} with exception {}", orderDto.getIncrementId(), e);
            return false;
        }
    }

    private Boolean invalidPaymentValidation(OrderDto orderDto) {
        String storeType = omsCommonUtil.getOrderMetaValueFromKey(orderDto.getOrderMetaData(), ORDER_META_KEY_STORE_TYPE);

        if (orderDto.getPaymentMethod() != null) {
            if (!orderValidPaymentMethods.contains(orderDto.getPaymentMethod())) {
                PAYMENT_FAILURE_COUNTER.increment();
                logger.error("not a valid payment for order processing for incrementId: {}", orderDto.getIncrementId());
                return false;
            }
            if (franchiseOrderValidPaymentMethods.contains(orderDto.getPaymentMethod())
                    && (!StringUtils.hasLength(storeType) || !COCO_FOFO_STORES.contains(storeType.toUpperCase()))
            ) {
                if(!STORE_PHONE_PE.equalsIgnoreCase(orderDto.getPaymentMethod())) {
                    return false;
                }
            }
        }

        return true;
    }

    private Boolean payTBYBPaymentValidation(OrderDto orderDto) {
        try {
            String customerId = omsCommonUtil.getOrderMetaValueFromKey(orderDto.getOrderMetaData(), ORDER_META_KEY_CUSTOMER_ID);
            if (StringUtils.hasLength(customerId)) {
                Integer distinctIncrementIdCount = orderOpsConnector.countOrderByCustomerIdAndStateAndOffer_3OrFree(Long.valueOf(customerId));
                if (Channel.TBYB.equals(orderDto.getOrderItems().get(0).getChannel())) {
                    return false;
                } else if (distinctIncrementIdCount > 2) {
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            logger.error("[payTBYBPaymentValidation] payment validation failed for order {} with exception {}", orderDto.getIncrementId(), e);
            return false;
        }
    }
}
