package com.lenskart.oms.validators;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.request.OmsOrderEvent;
import com.lenskart.oms.service.OrderService;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Setter(onMethod__ = {@Autowired})
public class UpdateAddressEventValidator {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    private OrderService orderService;

    /**
     *
     * @param orderRequest
     * @throws com.lenskart.oms.exception.ApplicationException if the validation fails
     */
    public void validateUpdateAddressEvent(OmsOrderEvent orderRequest) throws ApplicationException {
        validateOmsOrderEvent(orderRequest);

        if (orderRequest.getShipmentDtoList().get(0).getBillingAddress() != null)
            validateUpdateAddress(orderRequest.getIncrementId());
        if (orderRequest.getShipmentDtoList().get(0).getShippingAddress() != null)
            validateUpdateAddress(orderRequest.getIncrementId());
    }

    /**
     *
     * @param orderRequest
     * @throws ApplicationException
     */

    public void validateOmsOrderEvent(OmsOrderEvent orderRequest) throws ApplicationException {
        if(orderRequest.getShipmentDtoList().get(0).getShippingAddress() == null && orderRequest.getShipmentDtoList().get(0).getBillingAddress() == null) {
            logger.error("[validateUpdateAddress] Billing address and Shipping address information missing");
            throw new ApplicationException("Billing address and Shipping address information missing", null);
        }
    }

    /**
     *
     * @param incrementId
     * @throws ApplicationException
     */

    public void validateUpdateAddress(Long incrementId) throws ApplicationException {
        OrderDto orderDto = orderService.findByIncrementId(incrementId);

        if (orderDto == null){
            logger.error("[validateUpdateAddress] Invalid orderId, no order exists for orderId {}", incrementId);
            throw new ApplicationException("Invalid UpdateAddressRequest as no order exists for orderId", null);
        }
    }
}
