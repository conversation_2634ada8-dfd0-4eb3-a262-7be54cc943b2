package com.lenskart.oms.validators;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.OrderItemMetaDataDto;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.enums.ShipmentStatus;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.request.OmsOrderEvent;
import com.lenskart.oms.service.OrderService;
import com.lenskart.oms.service.ShipmentService;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Optional;

import static com.lenskart.oms.constants.ApplicationConstants.*;

@Component
@Setter(onMethod__ = {@Autowired})
public class CancelOrderEventValidator {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    private ShipmentService shipmentService;

    private OrderService orderService;

    public void validateCancelOrderEvent(OmsOrderEvent orderRequest) throws ApplicationException{
        if(orderRequest.getCancelOrderRequest() == null || orderRequest.getCancelOrderRequest().getOrderItems() == null){
            logger.error("[validateCancelOrder] Invalid request, no items given for Cancellation {}", orderRequest);
            throw new ApplicationException("[validateCancelOrder] Invalid request, no items given for Cancellation", null);
        }
        for(OrderItemDto orderItemDto : orderRequest.getCancelOrderRequest().getOrderItems()){
            validateOrderCancellable(orderRequest.getIncrementId());
            validateOTCOrder(orderItemDto);
        }
    }

    private void validateOTCOrder(OrderItemDto orderItemDto) throws ApplicationException {
        Optional<String> optionalOrderItemMetaDataDto =  orderItemDto.getOrderItemMetaData()
                .stream()
                .filter(oi -> OREDER_ITEM_META_KEY_CENTRAL_FACILITY_CODE.equals(oi.getEntityKey()))
                .map(OrderItemMetaDataDto::getEntityValue)
                .filter(StringUtils::hasLength)
                .findFirst();
        if (OTC.equalsIgnoreCase(orderItemDto.getProductDeliveryType().name())
                && orderItemDto.getFulfillmentType() == null
                && !optionalOrderItemMetaDataDto.isPresent()) {
            logger.error("[validateCancelOrder] OTC orders Cancellation not allowed for orderItem", orderItemDto);
            throw new ApplicationException("[validateCancelOrder] OTC orders Cancellation not allowed", null);
        }
    }

    private void validateOrderCancellable(Long incrementId) throws ApplicationException {
        OrderDto orderDto = orderService.findByIncrementId(incrementId);
        if(orderDto == null){
            logger.error("[validateCancelOrder] Invalid orderId, no order exists for orderId {}", incrementId);
            throw new ApplicationException("Invalid CancelOrderRequest as no order exists for orderId", null);
        }
    }
}
