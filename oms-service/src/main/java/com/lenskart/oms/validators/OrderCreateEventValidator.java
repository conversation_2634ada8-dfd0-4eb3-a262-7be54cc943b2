package com.lenskart.oms.validators;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.dto.*;
import com.lenskart.oms.enums.ItemType;
import com.lenskart.oms.enums.ProcessingType;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.request.OmsOrderEvent;
import com.lenskart.oms.utils.OmsCommonUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Optional;

import static com.lenskart.oms.constants.ApplicationConstants.ORDER_ITEM_META_KEY_PROCESSING_TYPE;

/**
 * The Class OrderEventValidator validates the request payloads for order create and update events.
 */
@Component
public class OrderCreateEventValidator {

	@CustomLogger
	private Logger logger;

	@Autowired
	private OmsCommonUtil omsCommonUtil;

	/**
	 * Validate create order event.
	 *
	 * @param omsOrderEvent the request payload after transformation
	 * @throws com.lenskart.oms.exception.ApplicationException if any of the validation fails
	 */
	public void validateCreateOrderEvent(OmsOrderEvent omsOrderEvent) throws ApplicationException {
		validateOrderId(omsOrderEvent.getOrderDto());
		validateDeliveryPriority(omsOrderEvent.getOrderDto().getOrderItems());
		validateProductDeliveryType(omsOrderEvent.getOrderDto().getOrderItems());
		validateSourceName(omsOrderEvent.getOrderDto());
		validateStoreId(omsOrderEvent.getOrderDto());
		validateOrderItems(omsOrderEvent.getOrderDto());
		validatePackageForPower(omsOrderEvent.getOrderDto());
		validateCurrencyCode(omsOrderEvent.getOrderDto());
		validatePayments(omsOrderEvent.getOrderDto());
		validateBillingAddress(omsOrderEvent.getShipmentDtoList());
		validateShippingAddress(omsOrderEvent.getShipmentDtoList());
	}

	/**
	 * Validate order id.
	 *
	 * @param orderPayload the order payload
	 * @throws com.lenskart.oms.exception.ApplicationException if the validation fails
	 */
	public void validateOrderId(OrderDto orderPayload) throws ApplicationException {
		if (orderPayload.getJunoOrderId() == null || orderPayload.getJunoOrderId().intValue() <= 0) {
			logger.error("[validateCreateOrderEvent -> validateOrderId] order id is invalid for payload {}", orderPayload);
			throw new ApplicationException("Invalid order id", null);
		}
	}

	/**
	 * Validate delivery priority.
	 *
	 * @param orderItemPayloadList the order item payload list
	 * @throws com.lenskart.oms.exception.ApplicationException if the validation fails
	 */
	public void validateDeliveryPriority(List<OrderItemDto> orderItemPayloadList) throws ApplicationException {
		for (OrderItemDto orderItemPayload : orderItemPayloadList) {
			if (orderItemPayload.getDeliveryPriority() == null) {
				logger.error("[validateCreateOrderEvent -> validateDeliveryPriority] Delivery Priority is invalid for payload {}", orderItemPayload);
				throw new ApplicationException("Invalid delivery priority", null);
			}
		}
	}

	/**
	 * Validate product delivery type.
	 *
	 * @param orderItemPayloadList the order item payload list
	 * @throws com.lenskart.oms.exception.ApplicationException if the validation fails
	 */
	private void validateProductDeliveryType(List<OrderItemDto> orderItemPayloadList) throws ApplicationException {
		for (OrderItemDto orderItemPayload : orderItemPayloadList) {
			if (orderItemPayload.getProductDeliveryType() == null
					&& !ItemType.getLensTypesToSkip().contains(orderItemPayload.getItemType().name())
			) {
				logger.error("[validateCreateOrderEvent -> validateProductDeliveryType] Product Delivery Type is NULL for payload {}", orderItemPayload);
				throw new ApplicationException("Product Delivery Type is NULL", null);
			}
		}
	}

	/**
	 * Validate source name.
	 *
	 * @param orderPayload the order payload
	 * @throws com.lenskart.oms.exception.ApplicationException if the validation fails
	 */
	public void validateSourceName(OrderDto orderPayload) throws ApplicationException {
		if ((orderPayload.getOrderSource() == null)) {
			logger.error("[validateCreateOrderEvent -> validateSourceName] Source name is invalid for payload {}", orderPayload);
			throw new ApplicationException("Invalid source name", null);
		}
	}

	/**
	 * Validate store id.
	 *
	 * @param orderPayload the order payload
	 * @throws com.lenskart.oms.exception.ApplicationException if the validation fails
	 */
	public void validateStoreId(OrderDto orderPayload) throws ApplicationException {
		if (orderPayload.getStoreId() == null) {
			logger.error("[validateCreateOrderEvent -> validateStoreId] Store Id is invalid for payload {}", orderPayload);
			throw new ApplicationException("Invalid store id", null);
		}
	}

	/**
	 * Validate order items.
	 *
	 * @param orderPayload the order payload
	 * @throws com.lenskart.oms.exception.ApplicationException if the validation fails
	 */
	private void validateOrderItems(OrderDto orderPayload) throws ApplicationException {
		if (CollectionUtils.isEmpty(orderPayload.getOrderItems())) {
			logger.error("[validateCreateOrderEvent -> validateOrderItems] Order Items payload is missing for payload {}", orderPayload);
			throw new ApplicationException("Order Items payload is missing", null);
		}

		for (OrderItemDto orderItemPayload : orderPayload.getOrderItems()) {
			if (orderItemPayload.getMagentoItemId() == null) {
				logger.error("[validateCreateOrderEvent -> validateOrderItems] Order Item Id is missing for payload {}", orderPayload);
				throw new ApplicationException("Order Item Id is missing", null);
			}
			if (orderItemPayload.getProductId() == null) {
				logger.error("[validateCreateOrderEvent -> validateOrderItems] Order Item SKU is missing for payload {}", orderPayload);
				throw new ApplicationException("Order Item SKU is missing", null);
			}
			if (orderItemPayload.getItemType() == null) {
				logger.error("[validateCreateOrderEvent -> validateOrderItems] Order Item type is missing for payload {}", orderPayload);
				throw new ApplicationException("Order Item type is missing", null);
			}
		}
	}

	/**
	 * Validate Product with power and package.
	 *
	 * @param orderPayload the order payload
	 * @throws com.lenskart.oms.exception.ApplicationException if the validation fails
	 */
	private void validatePackageForPower(OrderDto orderPayload) throws ApplicationException {
		List<OrderItemDto> orderItemPayloadList = orderPayload.getOrderItems();

		for (OrderItemDto orderItemPayload : orderItemPayloadList) {
			int packageCount = 0;
			if ((ItemType.EYEFRAME.equals(orderItemPayload.getItemType())
					|| ItemType.LENS_ONLY.equals(orderItemPayload.getItemType())
					|| ItemType.SUNGLASS.equals(orderItemPayload.getItemType())
					|| ItemType.SMART_GLASSES.equals(orderItemPayload.getItemType())
				)
					&& orderItemPayload.getItemPower() != null
					&& !isFR0Order(orderItemPayload)
					&& !omsCommonUtil.isDistributorOrder(orderPayload)
					&& !orderItemPayload.getItemPower().getPowerType().equalsIgnoreCase("FRAME_ONLY")
			) {
				packageCount = getPackageCountByOrderItem(orderItemPayloadList, orderItemPayload, packageCount);

				if (packageCount == 0 || packageCount > 1) {
					logger.error("[validateCreateOrderEvent -> validatePackageForPower] Product with power and package mismatch for payload {}", orderPayload);
					throw new ApplicationException("Product with power and package mismatch", null);
				}
			}
		}
	}

	private int getPackageCountByOrderItem(List<OrderItemDto> orderItemPayloadList, OrderItemDto orderItemPayload, int packageCount) {
		for (OrderItemDto childOrderItemPayload : orderItemPayloadList) {
			if (childOrderItemPayload == orderItemPayload) {
				continue;
			}

			if (childOrderItemPayload.getParentMagentoItemId() != null
					&& orderItemPayload.getMagentoItemId().equals(childOrderItemPayload.getParentMagentoItemId())
					&& ItemType.PACKAGE.equals(childOrderItemPayload.getItemType())) {
				packageCount++;
			}
		}
		return packageCount;
	}

	/**
	 * Validate currency code.
	 *
	 * @param orderPayload the order payload
	 * @throws com.lenskart.oms.exception.ApplicationException if the validation fails
	 */
	public void validateCurrencyCode(OrderDto orderPayload) throws ApplicationException {
		if (orderPayload.getCurrencyCode() == null) {
			logger.error("[validateCreateOrderEvent -> validateCurrencyCode] currency code is invalid for payload {}", orderPayload);
			throw new ApplicationException("Invalid currency code", null);
		}
	}

	/**
	 * Validate payments.
	 *
	 * @param orderPayload the order payload
	 * @throws com.lenskart.oms.exception.ApplicationException if the validation fails
	 */
	private void validatePayments(OrderDto orderPayload) throws ApplicationException {
		if (orderPayload.getPaymentMethod() == null) {
			logger.error("[validateCreateOrderEvent -> validatePayments] Payment method is missing for payload {}", orderPayload);
			throw new ApplicationException("Payment method is missing", null);
		}
	}

	/**
	 * Validate billing address.
	 *
	 * @param shipmentPayloadList the shipment payload list
	 * @throws com.lenskart.oms.exception.ApplicationException if the validation fails
	 */
	private void validateBillingAddress(List<ShipmentDto> shipmentPayloadList) throws ApplicationException {
		if (CollectionUtils.isEmpty(shipmentPayloadList)) {
			logger.error("[validateCreateOrderEvent -> validateBillingAddress] shipment data is missing.");
			throw new ApplicationException("shipment data is missing.", null);
		}

		ShipmentDto shipmentPayload = shipmentPayloadList.get(0);
		OrderAddressDto billingAddress = shipmentPayload.getBillingAddress();
		if (billingAddress == null) {
			logger.error("[validateCreateOrderEvent -> validateBillingAddress] Billing address information is missing for payload {}", shipmentPayload);
			throw new ApplicationException("Billing address information is missing", null);
		}

		if (StringUtils.isBlank(billingAddress.getStreet()) || StringUtils.isBlank(billingAddress.getCity())
//				|| StringUtils.isBlank(billingAddress.getStateCode())
				|| StringUtils.isBlank(billingAddress.getCountry())
				|| StringUtils.isBlank(billingAddress.getPostcode())
		) {
			logger.error("[validateCreateOrderEvent -> validateBillingAddress] Invalid billing address for payload {}", shipmentPayload);
			throw new ApplicationException("Invalid billing address", null);
		}
		if (StringUtils.isBlank(billingAddress.getEmail())) {
			logger.error("[validateCreateOrderEvent -> validateBillingAddress] Invalid emailId in the billing address for payload {}", shipmentPayload);
			throw new ApplicationException("Invalid emailId in the billing address", null);
		}
		if (StringUtils.isBlank(billingAddress.getFirstName())) {
			logger.error("[validateCreateOrderEvent -> validateBillingAddress] Invalid first name in the billing address for payload {}", shipmentPayload);
			throw new ApplicationException("Invalid first name in the billing address", null);
		}
	}

	/**
	 * Validate shipping address.
	 *
	 * @param shipmentPayloadList the shipment payload list
	 * @throws com.lenskart.oms.exception.ApplicationException if the validation fails
	 */
	private void validateShippingAddress(List<ShipmentDto> shipmentPayloadList) throws ApplicationException {
		if (CollectionUtils.isEmpty(shipmentPayloadList)) {
			logger.error("[validateCreateOrderEvent -> validateShippingAddress] shipment data is missing.");
			throw new ApplicationException("shipment data is missing.", null);
		}

		ShipmentDto shipmentPayload = shipmentPayloadList.get(0);
		OrderAddressDto shippingAddress = shipmentPayload.getShippingAddress();
		if (shippingAddress == null) {
			logger.error("[validateCreateOrderEvent -> validateShippingAddress] Shipping address information is missing for payload {}", shipmentPayload);
			throw new ApplicationException("Shipping address information is missing", null);
		}

		if (StringUtils.isBlank(shippingAddress.getStreet()) || StringUtils.isBlank(shippingAddress.getCity())
//				|| StringUtils.isBlank(shippingAddress.getStateCode())
				|| StringUtils.isBlank(shippingAddress.getCountry())
				|| StringUtils.isBlank(shippingAddress.getPostcode())
		) {
			logger.error("[validateCreateOrderEvent -> validateShippingAddress] Invalid shipping address for payload {}", shipmentPayload);
			throw new ApplicationException("Invalid shipping address", null);
		}
		if (StringUtils.isBlank(shippingAddress.getEmail())) {
			logger.error("[validateCreateOrderEvent -> validateShippingAddress] Invalid emailId in the shipping address for payload {}", shipmentPayload);
			throw new ApplicationException("Invalid emailId in the shipping address", null);
		}
		if (StringUtils.isBlank(shippingAddress.getFirstName())) {
			logger.error("[validateCreateOrderEvent -> validateShippingAddress] Invalid first name in the shipping address for payload {}", shipmentPayload);
			throw new ApplicationException("Invalid first name in the shipping address", null);
		}
	}

	private boolean isFR0Order(OrderItemDto orderItemPayload) {
		Optional<OrderItemMetaDataDto> orderItemMetaDataDtoOptional = orderItemPayload.getOrderItemMetaData().stream().filter(o -> ORDER_ITEM_META_KEY_PROCESSING_TYPE.equals(o.getEntityKey())).findFirst();
		if(orderItemMetaDataDtoOptional.isPresent()) {
			return orderItemMetaDataDtoOptional.get().getEntityValue().equals(ProcessingType.FR0.name());
		}
		return false;
	}
}
