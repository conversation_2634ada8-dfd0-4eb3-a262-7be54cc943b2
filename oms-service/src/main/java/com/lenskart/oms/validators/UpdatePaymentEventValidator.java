package com.lenskart.oms.validators;

import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.service.OrderService;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.request.OmsOrderEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
@Setter(onMethod__ = {@Autowired})
public class UpdatePaymentEventValidator {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private  Logger logger;
    private OrderService orderService;

    public void validateUpdatePaymentEvent(OmsOrderEvent paymentUpdateOmsEvent) throws ApplicationException{
        logger.info("validating payment update request for order with incrementId : {}", paymentUpdateOmsEvent.getOrderDto().getIncrementId());

        OrderDto persistedOrderDto = orderService.findBySearchTerms("incrementId.eq:"+paymentUpdateOmsEvent.getOrderDto().getIncrementId());

        if(persistedOrderDto == null){
            logger.error("persistedOrderDto is null for order having incrementId :  {}",paymentUpdateOmsEvent.getOrderDto().getIncrementId());
            throw new ApplicationException("persistedOrderDto is null for order having incrementId : "+paymentUpdateOmsEvent.getOrderDto().getIncrementId(), null);
        }

        OrderItemDto orderItemDto = persistedOrderDto.getOrderItems().get(0);

        if(orderItemDto == null){
            logger.error("Failed to find order event in the orders table with incrementId : {}",paymentUpdateOmsEvent.getOrderDto().getIncrementId());
            throw new ApplicationException("order does not exist in the orders table , having incrementId : "+paymentUpdateOmsEvent.getOrderDto().getIncrementId(), null);
        }

        if(Objects.isNull(paymentUpdateOmsEvent.getOrderDto().getPaymentCaptured())){
            throw new ApplicationException("payment capture flag can not be null for increment id: " + paymentUpdateOmsEvent.getIncrementId());
        }
    }

}
