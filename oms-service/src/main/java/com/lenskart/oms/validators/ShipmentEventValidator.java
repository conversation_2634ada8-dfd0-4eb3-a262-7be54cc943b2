package com.lenskart.oms.validators;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.enums.EventToOperationMap;
import com.lenskart.oms.enums.OrderItemStatus;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.model.OmsEvent;
import com.lenskart.oms.model.OmsEvents;
import com.lenskart.oms.request.ShipmentItemUpdate;
import com.lenskart.oms.request.ShipmentUpdateEvent;
import com.lenskart.oms.utils.OmsTransitionUtil;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Setter(onMethod__ = {@Autowired})
public class ShipmentEventValidator {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    @Setter(onMethod__ = {@Autowired})
    private OmsEvents omsEvents;

    private OmsTransitionUtil omsTransitionUtil;

    public void validateQcEvent(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        if (StringUtils.isBlank(shipmentUpdateEvent.getWmsOrderCode()))
            logMessageAndThrowException("WMSOrderCode is NULL/Empty", "WMSOrderCode is NULL/Empty", shipmentUpdateEvent);
        if (!CollectionUtils.isEmpty(shipmentUpdateEvent.getOrderItemList())) {
            for (ShipmentItemUpdate item : shipmentUpdateEvent.getOrderItemList()) {
                if (item.getOrderItemId() == null)
                    logMessageAndThrowException("OrderItemId is NULL/Empty", "OrderItemId is NULL/Empty", shipmentUpdateEvent);
                if (StringUtils.isBlank(item.getUnicomShipmentStatus()))
                    logMessageAndThrowException("UnicomShipmentStatus is NULL/Empty", "UnicomShipmentStatus is NULL/Empty", shipmentUpdateEvent);
                if (item.getEventTime() == null)
                    logMessageAndThrowException("QcTime is NULL/Empty", "QcTime is NULL/Empty", shipmentUpdateEvent);
            }
        }
    }

    public void validateCreateShippingPackageEvent(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        if (StringUtils.isBlank(shipmentUpdateEvent.getWmsOrderCode()) || StringUtils.isBlank(shipmentUpdateEvent.getEntityId()))
            logMessageAndThrowException("WMSOrderCode/ShippingPackageId is NULL/Empty", "WMSOrderCode/ShippingPackageId is NULL/Empty", shipmentUpdateEvent);
        if (shipmentUpdateEvent.getEventTime() == null)
            logMessageAndThrowException("CreateShipmentEventTime is NULL/Empty", "CreateShipmentEventTime is NULL/Empty", shipmentUpdateEvent);
    }

    public void validateMarkItemFulfillable(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        if (StringUtils.isBlank(shipmentUpdateEvent.getWmsOrderCode()))
            logMessageAndThrowException("WMSOrderCode is NULL/Empty", "WMSOrderCode is NULL/Empty", shipmentUpdateEvent);
        if (shipmentUpdateEvent.getEventTime() == null)
            logMessageAndThrowException("MarkItemFulfillableEventTime is NULL/Empty", "MarkItemFulfillableEventTime is NULL/Empty", shipmentUpdateEvent);
        if (!CollectionUtils.isEmpty(shipmentUpdateEvent.getOrderItemList())) {
            for (ShipmentItemUpdate item : shipmentUpdateEvent.getOrderItemList()) {
                if (item.getOrderItemId() == null)
                    logMessageAndThrowException("OrderItemId is NULL/Empty", "OrderItemId is NULL/Empty", shipmentUpdateEvent);
            }
        }
    }

    public void validateManifestEvent(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        if (StringUtils.isBlank(shipmentUpdateEvent.getWmsOrderCode()))
            logMessageAndThrowException("WMSOrderCode is NULL/Empty", "WMSOrderCode is NULL/Empty", shipmentUpdateEvent);
        if (StringUtils.isBlank(shipmentUpdateEvent.getEntityId()))
            logMessageAndThrowException("ManifestNumber is NULL/Empty", "ManifestNumber is NULL/Empty", shipmentUpdateEvent);
        if (shipmentUpdateEvent.getEventTime() == null)
            logMessageAndThrowException("ManifestEventTime is NULL/Empty", "ManifestEventTime is NULL/Empty", shipmentUpdateEvent);
        if (!CollectionUtils.isEmpty(shipmentUpdateEvent.getOrderItemList())) {
            for (ShipmentItemUpdate item : shipmentUpdateEvent.getOrderItemList()) {
                if (item.getOrderItemId() == null)
                    logMessageAndThrowException("OrderItemId is NULL/Empty", "OrderItemId is NULL/Empty", shipmentUpdateEvent);
                if (StringUtils.isBlank(item.getUnicomShipmentStatus()))
                    logMessageAndThrowException("UnicomShipmentStatus is NULL/Empty", "UnicomShipmentStatus is NULL/Empty", shipmentUpdateEvent);
                if (StringUtils.isBlank(item.getOrderOpsShipmentState()))
                    logMessageAndThrowException("OrderShipmentState is NULL/Empty", "OrderShipmentState is NULL/Empty", shipmentUpdateEvent);
                if (StringUtils.isBlank(item.getOrderOpsShipmentStatus()))
                    logMessageAndThrowException("OrderShipmentStatus is NULL/Empty", "OrderShipmentStatus is NULL/Empty", shipmentUpdateEvent);
            }
        }
    }

    public void validateDispatchEvent(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        if (StringUtils.isBlank(shipmentUpdateEvent.getWmsOrderCode()))
            logMessageAndThrowException("WMSOrderCode is NULL/Empty", "WMSOrderCode is NULL/Empty", shipmentUpdateEvent);
        if (!CollectionUtils.isEmpty(shipmentUpdateEvent.getOrderItemList())) {
            for (ShipmentItemUpdate item : shipmentUpdateEvent.getOrderItemList()) {
                if (item.getOrderItemId() == null)
                    logMessageAndThrowException("OrderItemId is NULL/Empty", "OrderItemId is NULL/Empty", shipmentUpdateEvent);
            }
        }
    }

    private void logMessageAndThrowException(String logMessage, String exceptionMessage, ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        logger.error("{} for payload {}", logMessage, shipmentUpdateEvent);
        throw new ApplicationException(exceptionMessage, null);
    }

    public void validateTransition(ShipmentUpdateEvent shipmentUpdateEvent, Map<Long, OrderItemDto> persistedOrderItemMap) throws ApplicationException {
        if (!CollectionUtils.isEmpty(shipmentUpdateEvent.getOrderItemList())) {
            for (ShipmentItemUpdate requestItem : shipmentUpdateEvent.getOrderItemList()) {
                if (requestItem.getOrderItemId() != null && persistedOrderItemMap.containsKey(requestItem.getOrderItemId())) {
                    OrderItemDto shipmentItem = persistedOrderItemMap.get(requestItem.getOrderItemId());
                    omsTransitionUtil.getTransitionActionByOperationAndStatus(
                            EventToOperationMap.valueOf(shipmentUpdateEvent.getShipmentEvent().name()).getOperation(), shipmentItem.getItemStatus()
                    );
                }
            }
        }
    }

    public void validateRequestPayload(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        if (StringUtils.isBlank(shipmentUpdateEvent.getWmsOrderCode())) {
            logMessageAndThrowException("WMSOrderCode is NULL/Empty", "WMSOrderCode is NULL/Empty", shipmentUpdateEvent);
        }

        if (shipmentUpdateEvent.getShipmentEvent() == null) {
            logMessageAndThrowException("ShipmentEvent is NULL/Empty", "ShipmentEvent is NULL/Empty", shipmentUpdateEvent);
        }
    }

    public boolean isDuplicateEvent(ShipmentUpdateEvent shipmentUpdateEvent, Map<Long, OrderItemDto> persistedOrderItemMap) throws ApplicationException {
        if (!CollectionUtils.isEmpty(shipmentUpdateEvent.getOrderItemList())) {
            for (ShipmentItemUpdate requestItem : shipmentUpdateEvent.getOrderItemList()) {
                if (requestItem.getOrderItemId() != null && persistedOrderItemMap.containsKey(requestItem.getOrderItemId())) {
                    OrderItemDto shipmentItem = persistedOrderItemMap.get(requestItem.getOrderItemId());
                    if (verifyDuplicateEvent(EventToOperationMap.valueOf(shipmentUpdateEvent.getShipmentEvent().name()).getOperation(), shipmentItem)) {
                        logger.info("[isEventValid -> verifyDuplicateEvent] Idempotent event {}", EventToOperationMap.valueOf(shipmentUpdateEvent.getShipmentEvent().name()).getOperation());
                        return true;
                    }
                }
            }
        } else {
            for (OrderItemDto shipmentItem : persistedOrderItemMap.values()) {
                if (verifyDuplicateEvent(EventToOperationMap.valueOf(shipmentUpdateEvent.getShipmentEvent().name()).getOperation(), shipmentItem)) {
                    logger.info("[isEventValid -> verifyDuplicateEvent] Idempotent event {}", EventToOperationMap.valueOf(shipmentUpdateEvent.getShipmentEvent().name()).getOperation());
                    return true;
                }
            }
        }

        return false;
    }

    private boolean verifyDuplicateEvent(String operation, OrderItemDto shipmentItem) throws ApplicationException {
        OmsEvent omsEvent = omsEvents.getOmsEvents().get(operation);
        if (Objects.isNull(omsEvent)) {
            logger.error("[verifyDuplicateEvent] No Transition available with operation name {} and state {}", operation);
            throw new ApplicationException(HttpStatus.SC_BAD_REQUEST, "No Transition available with operation name " + operation, null);
        }

        if (omsEvent.isDuplicateTransitionAllowed()) {
            return false;
        }

        Set<OrderItemStatus> itemStatusSet = omsEvent.getTransition().values()
                .stream()
                .map(action -> action.getItemStatus().getItemStatus())
                .collect(Collectors.toSet());
        if (itemStatusSet.contains(shipmentItem.getItemStatus())) {
            return true;
        }

        return false;
    }

    public void validateShipmentPickingEvent(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        if (StringUtils.isBlank(shipmentUpdateEvent.getWmsOrderCode()))
            logMessageAndThrowException("WMSOrderCode is NULL/Empty", "WMSOrderCode is NULL/Empty", shipmentUpdateEvent);
        if (!CollectionUtils.isEmpty(shipmentUpdateEvent.getOrderItemList())) {
            for (ShipmentItemUpdate item : shipmentUpdateEvent.getOrderItemList()) {
                if (item.getOrderItemId() == null)
                    logMessageAndThrowException("OrderItemId is NULL/Empty", "OrderItemId is NULL/Empty", shipmentUpdateEvent);
                if (StringUtils.isBlank(item.getEntityId()))
                    logMessageAndThrowException("BarCode is NULL/Empty", "BarCode is NULL/Empty", shipmentUpdateEvent);
                if (StringUtils.isBlank(item.getUnicomShipmentStatus()))
                    logMessageAndThrowException("UnicomShipmentStatus is NULL/Empty", "UnicomShipmentStatus is NULL/Empty", shipmentUpdateEvent);
            }
        }
    }
    public void validateItemSkipEvent(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        if (StringUtils.isBlank(shipmentUpdateEvent.getWmsOrderCode()))
            logMessageAndThrowException("WMSOrderCode is NULL/Empty", "WMSOrderCode is NULL/Empty", shipmentUpdateEvent);
        if (!CollectionUtils.isEmpty(shipmentUpdateEvent.getOrderItemList())) {
            for (ShipmentItemUpdate item : shipmentUpdateEvent.getOrderItemList()) {
                if (item.getOrderItemId() == null)
                    logMessageAndThrowException("OrderItemId is NULL/Empty", "OrderItemId is NULL/Empty", shipmentUpdateEvent);
                if (StringUtils.isBlank(item.getUnicomShipmentStatus()))
                    logMessageAndThrowException("UnicomShipmentStatus is NULL/Empty", "UnicomShipmentStatus is NULL/Empty", shipmentUpdateEvent);
            }
        }
    }

    public void validateShipmentEvent(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        if (StringUtils.isBlank(shipmentUpdateEvent.getWmsOrderCode()) || StringUtils.isBlank(shipmentUpdateEvent.getEntityId()))
            logMessageAndThrowException("WMSOrderCode or EntityId is NULL/Empty", "WMSOrderCode or EntityId is NULL/Empty", shipmentUpdateEvent);
    }

    public void validateShipmentAwbEvent(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        if (StringUtils.isBlank(shipmentUpdateEvent.getWmsOrderCode()))
            logMessageAndThrowException("WMSOrderCode is NULL/Empty", "WMSOrderCode is NULL/Empty", shipmentUpdateEvent);
        if (StringUtils.isBlank(shipmentUpdateEvent.getEntityId()))
            logMessageAndThrowException("AWB Number is NULL/Empty", "AWB Number is NULL/Empty", shipmentUpdateEvent);
        if (StringUtils.isBlank(shipmentUpdateEvent.getCourierCode()))
            logMessageAndThrowException("CourierCode is NULL/Empty", "CourierCode is NULL/Empty", shipmentUpdateEvent);
        if (shipmentUpdateEvent.getEventTime() == null)
            logMessageAndThrowException("AwbEventTime is NULL/Empty", "AwbEventTime is NULL/Empty", shipmentUpdateEvent);
        if (!CollectionUtils.isEmpty(shipmentUpdateEvent.getOrderItemList())) {
            for (ShipmentItemUpdate item : shipmentUpdateEvent.getOrderItemList()) {
                if (item.getOrderItemId() == null)
                    logMessageAndThrowException("OrderItemId is NULL/Empty", "OrderItemId is NULL/Empty", shipmentUpdateEvent);
                if (StringUtils.isBlank(item.getUnicomShipmentStatus()))
                    logMessageAndThrowException("UnicomShipmentStatus is NULL/Empty", "UnicomShipmentStatus is NULL/Empty", shipmentUpdateEvent);
            }
        }
    }

    public void validateShipmentInvoiceEvent(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        if (StringUtils.isBlank(shipmentUpdateEvent.getWmsOrderCode()))
            logMessageAndThrowException("WMSOrderCode is NULL/Empty", "WMSOrderCode is NULL/Empty", shipmentUpdateEvent);
        if (StringUtils.isBlank(shipmentUpdateEvent.getEntityId()))
            logMessageAndThrowException("Invoice Number is NULL/Empty", "Invoice Number is NULL/Empty", shipmentUpdateEvent);
        if (shipmentUpdateEvent.getEventTime() == null)
            logMessageAndThrowException("InvoiceEventTime is NULL/Empty", "InvoiceEventTime is NULL/Empty", shipmentUpdateEvent);
        if (!CollectionUtils.isEmpty(shipmentUpdateEvent.getOrderItemList())) {
            for (ShipmentItemUpdate item : shipmentUpdateEvent.getOrderItemList()) {
                if (item.getOrderItemId() == null)
                    logMessageAndThrowException("OrderItemId is NULL/Empty", "OrderItemId is NULL/Empty", shipmentUpdateEvent);
                if (StringUtils.isBlank(item.getUnicomShipmentStatus()))
                    logMessageAndThrowException("UnicomShipmentStatus is NULL/Empty", "UnicomShipmentStatus is NULL/Empty", shipmentUpdateEvent);
            }
        }
    }

    public void validateItemReturnEvent(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        if (StringUtils.isBlank(shipmentUpdateEvent.getWmsOrderCode()))
            logMessageAndThrowException("WMSOrderCode is NULL/Empty", "WMSOrderCode is NULL/Empty", shipmentUpdateEvent);
        if (StringUtils.isBlank(shipmentUpdateEvent.getEntityId()))
            logMessageAndThrowException("Invoice Number is NULL/Empty", "Invoice Number is NULL/Empty", shipmentUpdateEvent);
        if (!CollectionUtils.isEmpty(shipmentUpdateEvent.getOrderItemList())) {
            for (ShipmentItemUpdate item : shipmentUpdateEvent.getOrderItemList()) {
                if (item.getOrderItemId() == null)
                    logMessageAndThrowException("OrderItemId is NULL/Empty", "OrderItemId is NULL/Empty", shipmentUpdateEvent);
            }
        }
    }
}
