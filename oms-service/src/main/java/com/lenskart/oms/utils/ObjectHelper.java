package com.lenskart.oms.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.fasterxml.jackson.module.jaxb.JaxbAnnotationModule;
import com.lenskart.oms.exception.ApplicationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Calendar;

public class ObjectHelper {
    private static final Logger logger = LoggerFactory.getLogger(ObjectHelper.class);

    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final XmlMapper xmlMapper = new XmlMapper();
    private static final ObjectMapper objectMapperNonNull = new ObjectMapper();

    static {
        objectMapperNonNull.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    private ObjectHelper() {
    }

    public static ObjectMapper getObjectMapper() {
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return objectMapper;
    }

    public static <T> T readValue(String data, Class<T> clazz) throws ApplicationException {
        try {
            return objectMapper.readValue(data, clazz);
        } catch (IOException ex) {
            logger.error("ObjectHelper: could not convert object from {} to {}", data, clazz, ex);
            throw new ApplicationException("ObjectHelper: could not convert object from " + data + " to " + clazz, null);
        }
    }

    public static <T> T readNonNullValue(String data, Class<T> clazz) throws ApplicationException {
        try {
            return objectMapperNonNull.readValue(data, clazz);
        } catch (IOException ex) {
            logger.error("ObjectHelper: could not convert object from {} to {}", data, clazz, ex);
            throw new ApplicationException("ObjectHelper: could not convert object from " + data + " to " + clazz, null);
        }
    }

    public static <T> String writeValue(T data) throws ApplicationException {
        try {
            return objectMapper.writeValueAsString(data);
        } catch (IOException e) {
            logger.error("ObjectHelper: could not convert data from {} to string", data.getClass(), e);
            throw new ApplicationException("ObjectHelper: could not convert data from " + data.getClass() + " to string", null);
        }
    }

    public static <T> String writeNonNullValue(T data) throws ApplicationException {
        try {
            return objectMapperNonNull.writeValueAsString(data);
        } catch (IOException e) {
            logger.error("ObjectHelper: could not convert data from {} to string", data.getClass(), e);
            throw new ApplicationException("ObjectHelper: could not convert data from " + data.getClass() + " to string", null);
        }
    }

    public static <T> T convertNonNullValue(Object obj, Class<T> c) {
        return objectMapperNonNull.convertValue(obj, c);
    }

    public static String convertToString(Object obj) throws ApplicationException {
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (IOException e) {
            logger.error("ObjectHelper: could not convert data from {} to string", obj.getClass(), e);
            throw new ApplicationException("ObjectHelper: could not convert data from " + obj.getClass() + " to string", null);
        }
    }

    public static long getMiliSecToMidNight() {
        Calendar c = Calendar.getInstance();
        c.add(Calendar.DAY_OF_MONTH, 1);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        long howMany = (c.getTimeInMillis()-System.currentTimeMillis());
        logger.info("[getMiliSecToMidNight] Time till midnight in milliseconds is:"+ howMany);
        return howMany;
    }

    public static XmlMapper getXmlMapper() {
        xmlMapper.registerModule(new JaxbAnnotationModule());
        xmlMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        return xmlMapper;
    }
}
