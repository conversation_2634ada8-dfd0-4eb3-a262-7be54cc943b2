package com.lenskart.oms.utils;

import com.lenskart.nexs.cid.request.StockBlockOrderItemRequest;
import com.lenskart.nexs.cid.request.StockBlockOrderRequest;
import com.lenskart.nexs.ims.request.StockCheck;
import com.lenskart.oms.dto.ShipmentDto;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Setter(onMethod__ = {@Autowired})
public class BulkStockBlockUtil {

    @Value("#{${so.nexsFacilityLegalOwnerMapping}}")
    @Setter(AccessLevel.NONE)
    private Map<String, String> nexsFacilityLegalOwnerMapping;

    @Value("#{${do.nexs.uff.blocking:false}}")
    @Setter(AccessLevel.NONE)
    private boolean enableUffBlocking;

    public StockBlockOrderRequest generateBulkStockBlockRequest(ShipmentDto shipmentDto, List<StockCheck> stockCheckList, Long orderId) {
        StockBlockOrderRequest stockBlockOrderRequest = new StockBlockOrderRequest();
        Map<Integer, List<Integer>> pidItemIdMap = generatePidItemsMapping(stockCheckList);
        if (!pidItemIdMap.isEmpty()) {
            List<StockBlockOrderItemRequest> stockBlockItemRequestList = new ArrayList<>();
            for (Integer productId : pidItemIdMap.keySet()) {
                StockBlockOrderItemRequest stockBlockItemRequest = createStockCheckObject(productId, pidItemIdMap.get(productId).size(), pidItemIdMap.get(productId));
                stockBlockItemRequestList.add(stockBlockItemRequest);
            }
            if (CollectionUtils.isNotEmpty(stockBlockItemRequestList)) {
                stockBlockOrderRequest = getOrderStockCheckRequest(stockBlockItemRequestList, orderId.intValue(), shipmentDto.getFacility());
            }
        }
        stockBlockOrderRequest.setUnfulfillableBlockingAllowed(enableUffBlocking);
        stockBlockOrderRequest.setPartialBlockingAllowed(true);
        return stockBlockOrderRequest;
    }

    private Map<Integer, List<Integer>> generatePidItemsMapping(List<StockCheck> stockCheckList) {
        Map<Integer, List<Integer>> pidItemIdMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(stockCheckList)) {
            for (StockCheck item : stockCheckList) {
                if (pidItemIdMap.containsKey(item.getPid()))
                    pidItemIdMap.get(item.getPid()).addAll(item.getOrderItems());
                pidItemIdMap.putIfAbsent(item.getPid(), item.getOrderItems());
            }
        }
        return pidItemIdMap;
    }

    private StockBlockOrderItemRequest createStockCheckObject(int productId, int inventoryCount, List<Integer> uwItemIdList) {
        StockBlockOrderItemRequest stockBlockItemRequest = new StockBlockOrderItemRequest();
        stockBlockItemRequest.setProductId((long) productId);
        stockBlockItemRequest.setRequiredQuantity(inventoryCount);
        stockBlockItemRequest.setOrderItems(uwItemIdList);
        return stockBlockItemRequest;
    }

    private StockBlockOrderRequest getOrderStockCheckRequest(List<StockBlockOrderItemRequest> stockBlockItemRequestList, Integer incrementId, String facilityCode) {
        StockBlockOrderRequest stockBlockOrderRequest = new StockBlockOrderRequest();
        stockBlockOrderRequest.setOrderId(incrementId);
        stockBlockOrderRequest.setFacility(facilityCode);
        stockBlockOrderRequest.setItems(stockBlockItemRequestList);
        stockBlockOrderRequest.setUpdatedBy("Order-Sensei");
        stockBlockOrderRequest.setLegalOwner(nexsFacilityLegalOwnerMapping.getOrDefault(facilityCode, "LKIN"));
        return stockBlockOrderRequest;
    }
}
