package com.lenskart.oms.utils;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.dto.OrderBackSyncTrackingDto;
import com.lenskart.oms.enums.BackSyncEntityType;
import com.lenskart.oms.enums.BackSyncEventName;
import com.lenskart.oms.enums.BackSyncEventStatus;
import com.lenskart.oms.enums.BackSyncSystem;
import com.lenskart.oms.mapper.OrderBackSyncMapper;
import com.lenskart.oms.request.OmsOrderEvent;
import com.lenskart.oms.service.OrderBackSyncTrackingService;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Component
@Setter(onMethod__ = {@Autowired})
public class OrderBackSyncUtil {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    protected Logger logger;

    private OrderBackSyncMapper orderBackSyncMapper;
    private OrderBackSyncTrackingService orderBackSyncTrackingService;

    public OrderBackSyncTrackingDto getAndMarkPendingBackSyncTrackingDto(Long incrementId, BackSyncEventName eventName) {
        StringBuilder searchTerm = new StringBuilder("entityId.eq:")
                .append(incrementId)
                .append("___")
                .append("eventName.eq:")
                .append(eventName);
        OrderBackSyncTrackingDto orderBackSyncTrackingDto = orderBackSyncTrackingService.findBySearchTerms(searchTerm.toString());
        if (orderBackSyncTrackingDto == null) {
            orderBackSyncTrackingDto = orderBackSyncMapper.populateBackSyncTrackingDto(
                    eventName, String.valueOf(incrementId),
                    BackSyncEventStatus.PENDING, BackSyncSystem.ORDER_OPS, BackSyncEntityType.INCREMENT_ID, null);
        }
        return orderBackSyncTrackingService.save(orderBackSyncTrackingDto);
    }
}
