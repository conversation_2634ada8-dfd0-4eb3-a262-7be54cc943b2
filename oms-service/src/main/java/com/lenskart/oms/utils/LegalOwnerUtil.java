package com.lenskart.oms.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.ims.connector.FMSConnector;
import com.lenskart.nexs.ims.response.FacilityDetails;
import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.OrderItemMetaDataDto;
import com.lenskart.oms.dto.ShipmentDto;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;

import static com.lenskart.oms.constants.ApplicationConstants.ORDER_ITEM_META_KEY_HUB_CODE;

@Component
public class LegalOwnerUtil {
    @Value("#{${hubCode.legalOwner.mapping}}")
    private Map<String, Map<String, String>> hubcodeLegalOwnerMap;

    @Value("#{${lkCountry.virtualFacility.mapping}}")
    private Map<String, String> lkCountryVirtualFacilityMap;

    @Value("${lkCountry.virtualFacility.default}")
    private String defaultVirtualFacility;

    @Value("${fms-connector.base.url}")
    private String fmsConnectorBaseUrl;

    @Value("${fms-connector.facility.details.url}")
    private String fmsConnectorFacilityDetailsUrl;

    @CustomLogger
    @Setter(AccessLevel.NONE)
    protected Logger logger;

    @Autowired
    private FMSConnector fmsConnector;

    @PostConstruct
    private void init() {
        logger.info("[LegalOwnerUtil] hubcodeLegalOwnerMap {}", hubcodeLegalOwnerMap);
        logger.info("[LegalOwnerUtil] defaultVirtualFacility {}", defaultVirtualFacility);
    }

    public String getLegalOwner(String facilityCode, String hubCode, String country) {
        try {
            FacilityDetails facilityDetails = StringUtils.isEmpty(facilityCode)
                    ? null
                    : fmsConnector.fetchLegalOwner(facilityCode, fmsConnectorBaseUrl + fmsConnectorFacilityDetailsUrl);            if (!Objects.isNull(facilityDetails)) {
                return facilityDetails.getLegalOwner();
            } else {
                logger.info("fetching facility details on hub code {} and country {}", hubCode, country);
                Map<String, String> hubcodeMap = hubcodeLegalOwnerMap.get(country);
                if (hubcodeMap.isEmpty()) {
                    return null;
                }
                return hubcodeMap.get(hubCode);
            }
        } catch (Exception e) {
            return null;
        }
    }

    public String getHubCode(ShipmentDto shipmentDto) {
        String hubCode = null;
        Optional<String> optHubCode = Optional.ofNullable(shipmentDto)
                .map(ShipmentDto::getOrderItems)
                .filter(list -> !list.isEmpty())
                .map(list -> list.get(0))
                .map(OrderItemDto::getOrderItemMetaData)
                .orElse(Collections.emptyList())
                .stream()
                .filter(meta -> ORDER_ITEM_META_KEY_HUB_CODE.equals(meta.getEntityKey()))
                .map(OrderItemMetaDataDto::getEntityValue)
                .filter(Objects::nonNull)
                .findFirst();

        if(optHubCode.isPresent()) {
            hubCode = optHubCode.get();
            logger.info("[getLegalOwner] hub code : {} for shipment id : {}", hubCode, shipmentDto.getWmsShippingPackageId());
        }
        return hubCode;
    }

    public String getVirtualFacility(String lkCountry) {
        return lkCountryVirtualFacilityMap.getOrDefault(lkCountry, defaultVirtualFacility);
    }
}
