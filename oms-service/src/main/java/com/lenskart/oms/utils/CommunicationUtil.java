package com.lenskart.oms.utils;

import com.lenskart.core.model.Product;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.connector.CatalogOpsConnector;
import com.lenskart.oms.connector.CommunicationBridgeSmsConnector;
import com.lenskart.oms.connector.CommunicationSmsConnector;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.dto.OrderAddressDto;
import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.request.LineItemPayloadEvent;
import com.lenskart.oms.request.SmsRequest;
import com.lenskart.oms.response.SmsResponse;
import com.lenskart.oms.service.OrderAddressService;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;
import java.net.URLEncoder;
import java.util.*;

@Component
@Setter(onMethod__ = {@Autowired})
public class CommunicationUtil{

    @Setter(AccessLevel.NONE)
    @Value("${short.url.service.base.url}")
    private String shortUrlServiceBaseUrl;

    @Setter(AccessLevel.NONE)
    @Value("${item.types.to.exclude.for.sms}")
    private Set<String> itemTypesToExcludeForSMS;

    private OrderAddressService orderAddressService;

    private final String SMS_TYPE_IMPORTANT = "important";

    @Setter(AccessLevel.NONE)
    @Value("${communication.bridge.flag}")
    private boolean isBridgeEnabled;

    @Setter(AccessLevel.NONE)
    @Value("${bridge.template.id}")
    private String bridgeTemplateId;

    private CommunicationSmsConnector communicationSmsConnector;

    private CommunicationBridgeSmsConnector communicationBridgeSmsConnector;

    @CustomLogger
    @Setter(AccessLevel.NONE)
    protected Logger logger;

    private CatalogOpsConnector catalogOpsConnector;

    public void sendPowerFollowUpSMS(OrderDto order, LineItemPayloadEvent lineItem, Set<Long> productIds, OrderAddressDto address) throws ApplicationException {
        String errorMessage = "";
        if(Objects.nonNull(address)){
            List<OrderItemDto> orderItems = order.getOrderItems();
            if(!CollectionUtils.isEmpty(orderItems)) {
                Integer qtyOrdered = (int) orderItems.stream().filter(orderItemDto -> !itemTypesToExcludeForSMS.contains(orderItemDto.getItemType().name())).count();
                String appendMsg = "";
                if (qtyOrdered > 1) {
                    qtyOrdered = qtyOrdered - 1;
                    appendMsg = " & " + qtyOrdered + " other";
                }
                String mask = "LENSKT";
                String kart = "Lenskart.com";
                String customerCare = "(0)9999899998";
                String smsId = "54";
                if(order.getStoreId() == 6) {
                    mask = "JJACOB";
                    kart = "john-jacobs.com";
                    customerCare = "011-66570606";
                }

                String orderId = Base64.getEncoder().encodeToString(order.getIncrementId().toString().getBytes());
                String sha1Email = DigestUtils.sha1Hex(address.getEmail());
                String longUrl = "www.lenskart.com/checkout/onepage/uploadprescsms?o="+orderId+"&m="+sha1Email;
                String shortUrl = getShortUrl(longUrl);

                Map<String,String> hashMap = selectHighestValueBrandAndCategoryName(Math.toIntExact(order.getIncrementId()),lineItem.getItemType().name(),productIds);
                Map<String, String> params = new HashMap<>();
                params.put("incrementId",String.valueOf(order.getIncrementId()));
                params.put("brand",hashMap.get("brand"));
                params.put("category",hashMap.get("category"));
                params.put("kart",kart);
                params.put("appendMsg",appendMsg);
                params.put("shortUrl",shortUrl);
                params.put("contact",customerCare);
                params.put("template_id",smsId);
                params.put("uploadPrescriptionUrl", shortUrl);
                sendSms(SMS_TYPE_IMPORTANT, address.getTelephone(), String.valueOf(order.getIncrementId()), params, mask, order.getIncrementId());
            } else {
                errorMessage = "[sendPowerFollowUpSMS] unable to send sms to user for increment id " + order.getIncrementId() + " shipping address is not available";
                logger.error(errorMessage);
                throw new ApplicationException("[sendPowerFollowUpSMS] unable to send sms to user for increment id " + order.getIncrementId()+" error: "+errorMessage);
            }
        } else {
            errorMessage = "[sendPowerFollowUpSMS] unable to send sms to user for increment id " + order.getIncrementId() + " orders details are not available";
            logger.error(errorMessage);
            throw new ApplicationException("[sendPowerFollowUpSMS] unable to send sms to user for increment id " + order.getIncrementId()+" error: "+errorMessage);
        }
    }

    public Map<String,String> selectHighestValueBrandAndCategoryName(Integer incrementId, String smsType, Set<Long> productIds) throws ApplicationException {

        Map<String,String> hashMap = new HashMap<>();
        Product product = null;
        List<Product> productDetails = null;
        Integer classification = smsType.equalsIgnoreCase("eyeframe") ? 11355 : (smsType.equalsIgnoreCase("contact-lens") ? 11354 : (smsType.equalsIgnoreCase("SUNGLASSES") ? 11357 : 0));
        productDetails = catalogOpsConnector.getProductDetailsByPIdsAndClassification(productIds,classification,"NA");
        if(CollectionUtils.isEmpty(productDetails) || productDetails.size()>1) {
            logger.error("[selectHighestValueBrandAndCategoryName] Product details fetch returned more than 1 product details for increment id: {}",incrementId);
            throw new ApplicationException("Product details fetch returned more than 1 product details for increment id: " + incrementId);
        }

        product = productDetails.get(0);

        if(null != product){
            String category = null;
            String highestValProdName = product.getValue();
            if(highestValProdName.toLowerCase().contains("eyeglass")) {
                category = "Eyeglasses";
            }
            if(highestValProdName.toLowerCase().contains("sunglass")) {
                category = "Sunglasses";
            }
            if(highestValProdName.toLowerCase().contains("contact")) {
                category = "Contact Lenses";
            }

            hashMap.put("brand",product.getBrand());
            hashMap.put("category", category);
        }
        logger.info("[selectHighestValueBrandAndCategoryName] hashMap: {}",hashMap);
        return hashMap;
    }

    public void sendSms(String description, String mobile, String orderId, Map<String, String> params, String mask, Long incrementId) throws ApplicationException {
        try {
            if (description != null && mobile != null && orderId != null && params != null) {
                params.put("template_id", bridgeTemplateId);
                logger.info("[sendSms] Order-Ops : SendSms : {}", orderId);
                Map<String, Object> payload = new HashMap<>();
                payload.put("desc", description);
                payload.put("mobile", mobile);
                payload.put("mask", mask);
                payload.put("order_id", orderId);
                payload.put("params", params);
                logger.info("[sendSms] Order-Ops : SendSms : For order id: {} payload: {}", orderId, payload);
                SmsRequest communicationRequest = new SmsRequest();
                communicationRequest.setHttpMethod(HttpMethod.POST);
                communicationRequest.setUrl("/api/sms");
                communicationRequest.setContentType("application/json");
                communicationRequest.setRequestPayload(payload);
                SmsResponse response;
                if(isBridgeEnabled) {
                    response = communicationBridgeSmsConnector.sendCommunication(communicationRequest, incrementId);
                } else {
                    response = communicationSmsConnector.sendPostRequest(communicationRequest, incrementId);
                }
                logger.info("[sendSms] Order-Ops : SendSms Response : {}", response);
            }
        } catch (Exception e) {
            logger.error("[sendSms] Send sms failed for increment id: " + incrementId, e);
            //throw new ApplicationException("Send sms failed for increment id: "+incrementId+" with exception: "+e.getMessage(),e);
        }
    }

    public String getShortUrl(String longUrl) throws ApplicationException {

        String shortUrl = null;
        logger.info("[getShortUrl] longUrl : "+longUrl);
        try{
            String encodedLongUrl = URLEncoder.encode(longUrl, "UTF-8");
            String newLongUrl = encodedLongUrl.replaceAll("%","~");
            String requestUrl = shortUrlServiceBaseUrl+newLongUrl;
            logger.info("[getShortUrl] requestUrl : "+requestUrl);
            ResponseEntity<String> responseEntity = new RestTemplate().getForEntity(requestUrl,String.class);
            if(null != responseEntity){
                if(responseEntity.getBody()!= null){
                    HashMap<String,String> psResponse = ObjectHelper.readValue(responseEntity.getBody(), HashMap.class);
                    shortUrl = psResponse.getOrDefault("short_url","");
                    logger.info("[getShortUrl] shortUrl : "+shortUrl);
                    shortUrl = shortUrl.replace("http://", "");
                    if(shortUrl.contains("https://")){
                        shortUrl = shortUrl.replace("https://", "");
                    }
                }
            }
            logger.info("[getShortUrl] short url : "+shortUrl);
        }catch(Exception e){
            logger.error("[getShortUrl] Exception occured when generating short url for a long url: {}, with exception: {} ",longUrl,e.getMessage());
            throw new ApplicationException("Exception occured when generating short url for a long url: "+longUrl,e);
        }
        return shortUrl;
    }
}
