package com.lenskart.oms.utils;

import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.KeyDeserializer;
import com.lenskart.oms.model.OrderItemOperations;

import java.io.IOException;

public class OmsTransitionsKeyDeserializer extends KeyDeserializer {

    @Override
    public OrderItemOperations deserializeKey(final String key, final DeserializationContext ctxt ) throws IOException {
        return ObjectHelper.getObjectMapper().readValue(key, OrderItemOperations.class);
    }
}