package com.lenskart.oms.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.configs.CallbackProperties;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.request.JunoAddress;
import com.lenskart.oms.request.JunoOrderAddress;
import com.lenskart.oms.request.JunoUpdateAddressRequest;
import com.lenskart.oms.dto.OrderAddressDto;
import com.lenskart.oms.request.OmsOrderEvent;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

@Component
@Setter(onMethod__ = {@Autowired})
public class OrderEventClientCallbackUtil {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    private CallbackProperties callbackProperties;

    public String getClientAddressUpdateRequest(Long incrementId, OmsOrderEvent orderRequest) throws JsonProcessingException {
        JunoUpdateAddressRequest updateAddressRequest = new JunoUpdateAddressRequest();
        JunoOrderAddress orderAddress = new JunoOrderAddress();
        orderAddress.setOrderId(String.valueOf(incrementId));
        orderAddress.setCallbackUrl("");
        JunoAddress addressUpdate = new JunoAddress();

        if(orderRequest.getShipmentDtoList().get(0).getShippingAddress() != null ){
            OrderAddressDto shippingAddressPayload = orderRequest.getShipmentDtoList().get(0).getShippingAddress();
            addressUpdate.setFirstName(shippingAddressPayload.getFirstName());
            addressUpdate.setLastName(shippingAddressPayload.getLastName());
            addressUpdate.setAddress1(shippingAddressPayload.getStreet());
            String phoneNo = shippingAddressPayload.getTelephone();
            if(StringUtils.isNotBlank(phoneNo)){
                phoneNo = phoneNo.replaceAll("\\s","");
            }
            addressUpdate.setPhone(phoneNo);
            addressUpdate.setCity(shippingAddressPayload.getCity());
            addressUpdate.setZip(shippingAddressPayload.getPostcode());
            addressUpdate.setState(shippingAddressPayload.getRegion());
            addressUpdate.setCountry(shippingAddressPayload.getCountry());
        }

        orderAddress.setJunoAddressUpdateDto(addressUpdate);
        updateAddressRequest.setJunoOrderAddressDto(orderAddress);
        String request = new ObjectMapper().writeValueAsString(updateAddressRequest);
        logger.info("Update address request for incrementId:{}, request:{}", incrementId, request);
        return request;
    }

    public void updateAddressCallback(String clientId, int orderId, String request) {
        logger.info("Giving address callback to client clientId:{}, orderId:{}, request:{}", new Object[]{clientId, orderId, request});
        if (StringUtils.isBlank(clientId)) {
            logger.error("client id is blank in callback to client so return");
        }

        try {
            String url = "";
            HttpHeaders headers = new HttpHeaders();
            Map jjConfig;
            if (ApplicationConstants.JUNO.equals(clientId)) {
                jjConfig = callbackProperties.getJuno();
                url = ((String)jjConfig.get("updateAddressUrl")).concat("/orders").concat(String.valueOf(orderId)).concat("/address");
                headers.add("X-Auth-Token", (String)jjConfig.get("accessToken"));
            } else if (ApplicationConstants.JJ.equals(clientId)) {
                jjConfig = callbackProperties.getJj();
                url = (String)jjConfig.get("url");
                headers.add("X-JJ-Access-Token", (String)jjConfig.get("accessToken"));
                headers.add("X-Action", "UPDATE_ADDRESS");
            }

            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> entity = new HttpEntity(request, headers);
            ResponseEntity<String> response = (new RestTemplate()).exchange(url, HttpMethod.POST, entity, String.class, new Object[0]);
            logger.info("response received for order id:{} is {}", orderId, response);
            if (response == null) {
                logger.error("[UpdateAddressCallback -> orderEventClientCallbackUtil] Unable to inform client about the order Status: ");
            }

            Integer responseCode = response.getStatusCode().value();
            if (responseCode == null || responseCode < 200 || responseCode > 226) {
                logger.error("[UpdateAddressCallback -> orderEventClientCallbackUtil] Unable to inform client about the order Status: ");
            }
        } catch (Exception var9) {
            logger.error("[UpdateAddressCallback -> orderEventClientCallbackUtil] Unable to inform client about the order address: Exception occured is: {}", var9.getMessage());
        }
    }
}
