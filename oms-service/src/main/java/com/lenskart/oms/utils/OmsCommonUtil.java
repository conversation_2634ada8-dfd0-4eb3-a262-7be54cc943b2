package com.lenskart.oms.utils;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.connector.OrderOpsConnector;
import com.lenskart.oms.dto.*;
import com.lenskart.oms.entity.DistributorOrders;
import com.lenskart.oms.entity.OrderItems;
import com.lenskart.oms.enums.*;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.model.UwOrder;
import com.lenskart.oms.producer.InterceptorAckProducer;
import com.lenskart.oms.request.distributor.JitDoItemStatusUpdateRequest;
import com.lenskart.oms.response.UwOrderResponse;
import com.lenskart.oms.service.DistributorOrdersService;
import com.lenskart.oms.service.OrderItemService;
import com.lenskart.oms.service.OrderService;
import com.lenskart.oms.service.ShipmentService;
import com.lenskart.order.interceptor.dto.OrderEventAcknowledgementDTO;
import com.lenskart.order.interceptor.dto.createorder.LineItemPayload;
import com.lenskart.order.interceptor.enums.OrderEventRequestType;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.apache.logging.log4j.core.appender.rolling.action.IfAll;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;

import static com.lenskart.oms.constants.ApplicationConstants.*;

@Component
@Setter(onMethod__ = {@Autowired})
public class OmsCommonUtil {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;
    private OrderItemService orderItemService;
    private OrderOpsConnector orderOpsConnector;
    private InterceptorAckProducer interceptorAckProducer;
    private ShipmentService shipmentService;
    private OrderService orderService;
    private DistributorOrdersService distributorOrdersService;

    @Setter(AccessLevel.NONE)
    @Value("#{'${d365.eligible.nav.channels:DODTC,COCOOTC,FOFOOTC,COCOB2B,WEBB2B,ODONLINEB2B,JJONLINEB2B,JJB2B,OJOSB2B}'.split(',')}")
    private List<String> d365EligibleNavChannels;

    public String getOrderMetaValueFromKey(List<OrderMetaDataDto> orderMetaDataList, String entityKey) {
        for (OrderMetaDataDto orderMetaData : orderMetaDataList) {
            if (entityKey.equalsIgnoreCase(orderMetaData.getEntityKey())) {
                return orderMetaData.getEntityValue();
            }
        }

        return null;
    }

    public String getShipmentMetaValueFromKey(List<ShipmentMetaDataDto> shipmentMetaDataDtoList, String entityKey) {
        for (ShipmentMetaDataDto shipmentMetaDataDto : shipmentMetaDataDtoList) {
            if (entityKey.equalsIgnoreCase(shipmentMetaDataDto.getEntityKey())) {
                return shipmentMetaDataDto.getEntityValue();
            }
        }

        return null;
    }

    public String getOrderItemMetaValueFromKey(List<OrderItemMetaDataDto> orderItems, String searchKey) throws ApplicationException {
        for(OrderItemMetaDataDto metaEntry: orderItems){
            if(metaEntry.getEntityKey().equalsIgnoreCase(searchKey)) {
                if (!Objects.isNull(metaEntry.getEntityValue()))
                    return metaEntry.getEntityValue();
                else
                    throw new ApplicationException("Null value present for the requested key in orders meta dto. requested key: "+searchKey);
            }
        }
        throw new ApplicationException("No entry found for the requested key in orders meta dto. requested key: "+searchKey);
    }

    public void sendEventAckToOrderInterceptor(String incrementId, String eventType, boolean scmSyncFlag, String errorMessage) throws ApplicationException {
        logger.info("[sendEventAckToOrderInterceptor] received for increment id: {}, event type: {}, scmSyncFlag: {}, error message: {}", incrementId, eventType, scmSyncFlag, errorMessage);
        if (isValidInterceptorEvent(eventType)) {
            OrderEventAcknowledgementDTO orderEventAcknowledgementDTO = omsOrderEventToInterceptorAckDto(incrementId, eventType, scmSyncFlag, errorMessage);
            logger.info("[sendEventAckToOrderInterceptor] created payload for orderEventAcknowledgementDTO is {}  for increment id: {} for event type: {}", orderEventAcknowledgementDTO, incrementId, eventType);
            interceptorAckProducer.sendMessage(orderEventAcknowledgementDTO);
        }
    }

    private OrderEventAcknowledgementDTO omsOrderEventToInterceptorAckDto(String incrementId, String eventType, boolean scmSyncFlag, String errorMessage) {
        OrderEventAcknowledgementDTO orderEventAcknowledgementDTO = new OrderEventAcknowledgementDTO();
        orderEventAcknowledgementDTO.setIncrementId(incrementId);
        orderEventAcknowledgementDTO.setRequestType(OrderEventRequestType.valueOf(eventType));
        orderEventAcknowledgementDTO.setScmSyncFlag(scmSyncFlag);
        orderEventAcknowledgementDTO.setErrorMessage(errorMessage);
        orderEventAcknowledgementDTO.setUpdatedBy(DEFAULT_OMS_USER);

        return orderEventAcknowledgementDTO;
    }

    private boolean isValidInterceptorEvent(String eventType) {
        boolean isValid = true;
        try {
            OrderEventRequestType.valueOf(eventType);
        } catch (Exception e) {
            isValid = false;
        }
        return isValid;
    }

    public String convertDateTimeToString(DateTime date) throws ApplicationException {
        String strDate = null;
        try {
            DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern("MM-dd-yyyy HH:mm:ss");
            strDate = dateTimeFormatter.print(date);
        } catch (Exception e) {
            logger.error("[OmsCommonUtil -> convertDateTimeToString] Exception Occurred for date {}", date, e);
            throw new ApplicationException("[convertDateTimeToString] Exception Occurred " + e);
        }
        return strDate;
    }

    public UwOrderResponse fetchOrderOpsUwItemIdAndUpdateInOms(Long incrementId, List<OrderItemDto> orderItemDtoList, String eventType,  Map<Long, String> shipmentTypeMap) throws ApplicationException {
        try {
            // TODO:: need to handle already populated case
            UwOrderResponse uwOrderResponse = orderOpsConnector.getUwOrderWithItemType(incrementId);
            if (uwOrderResponse == null || CollectionUtils.isEmpty(uwOrderResponse.getUwOrders())) {
                throw new ApplicationException("[fetchOrderOpsUwItemIdAndUpdateInOms] uwItems map is empty from order ops for incrementId " + incrementId, null);
            }

            Map<String, Long> magentoIdAndFittingIdMap = new HashMap<>();
            Map<String, List<String>> orderOpsDistinctUwItemIdMap = getOrderOpsUwItemIdMapAndUpdateFittingIdMap(uwOrderResponse, magentoIdAndFittingIdMap, eventType, orderItemDtoList);
            Map<String, OrderItemDto> omsDistinctUwItemIdMap = getOmsUwItemIdMap(orderItemDtoList);

            logger.info("[fetchOrderOpsUwItemIdAndUpdateInOms]  orderOpsDistinctUwItemIdMap -- {} and omsDistinctUwItemIdMap -- {}, magentoIdAndFittingIdMap {}", orderOpsDistinctUwItemIdMap, omsDistinctUwItemIdMap, magentoIdAndFittingIdMap);

            for (Map.Entry<String, List<String>> orderOpsDistinctUwItemIdEntry : orderOpsDistinctUwItemIdMap.entrySet()) {
                if (omsDistinctUwItemIdMap.containsKey(orderOpsDistinctUwItemIdEntry.getKey())) {
                    OrderItemDto omsOrderItem = omsDistinctUwItemIdMap.get(orderOpsDistinctUwItemIdEntry.getKey());
                    Long uwItemId = Long.valueOf(orderOpsDistinctUwItemIdEntry.getValue().get(0));
                    String unicomOrderCode = orderOpsDistinctUwItemIdEntry.getValue().get(1);
                    omsOrderItem.setUwItemId(uwItemId);
                    if (Objects.isNull(omsOrderItem.getB2bReferenceItemId()) &&
                            !ProcessingType.FR0.name().equals(shipmentTypeMap.get(omsOrderItem.getShipmentId())) &&
                            (ItemType.LEFT_LENS.equals(omsOrderItem.getItemType())
                                    || ItemType.RIGHT_LENS.equals(omsOrderItem.getItemType())
                                    || magentoIdAndFittingIdMap.values().contains(omsOrderItem.getUwItemId()))
                    ) {
                        omsOrderItem.setFittingId(magentoIdAndFittingIdMap.get(omsOrderItem.getMagentoItemId() + "_" + unicomOrderCode));
                    } else if (ItemType.LOYALTY_SERVICES.equals(omsOrderItem.getItemType())) {
                        omsOrderItem.setFittingId(0L);
                    }
                    OrderItemDto persistedDto = orderItemService.findById(omsOrderItem.getId());
                    persistedDto.setFittingId(omsOrderItem.getFittingId());
                    persistedDto.setUwItemId(uwItemId);

                    logger.info("[fetchOrderOpsUwItemIdAndUpdateInOms] update uwItemId {} for orderItemId {}", omsOrderItem.getUwItemId(), omsOrderItem.getId());
                    orderItemService.save(persistedDto);
                }
            }

            return uwOrderResponse;

        } catch (Exception exception) {
            logger.error("[fetchOrderOpsUwItemIdAndUpdateInOms] fetch and update uwItemsIds failed for incrementId {} with exception {} ", incrementId, exception.getMessage(), exception);
            throw new ApplicationException(exception.getMessage(), exception);
        }
    }

    private Map<String, OrderItemDto> getOmsUwItemIdMap(List<OrderItemDto> orderItemDtoList) {
        Map<String, OrderItemDto> omsDistinctUwItemIdMap = new HashMap<>();

        for (OrderItemDto orderItemDto : orderItemDtoList) {
            ShipmentDto shipmentDto = shipmentService.findById(orderItemDto.getShipmentId());
            String distinctItemKey = StringUtils.EMPTY;
            if(ItemType.NON_POWER_READING.equals(orderItemDto.getItemType())){
                distinctItemKey = StringUtils.EMPTY + orderItemDto.getMagentoItemId() + shipmentDto.getWmsOrderCode();
            }else{
                distinctItemKey = StringUtils.EMPTY + orderItemDto.getMagentoItemId() + orderItemDto.getItemType() + shipmentDto.getWmsOrderCode();
            }
            if (!omsDistinctUwItemIdMap.containsKey(distinctItemKey)) {
                logger.info("[getOmsUwItemIdMap] distinctItemKey {}", distinctItemKey);
                omsDistinctUwItemIdMap.put(distinctItemKey, orderItemDto);
            }
        }

        return omsDistinctUwItemIdMap;
    }

    private Map<String, List<String>> getOrderOpsUwItemIdMapAndUpdateFittingIdMap(UwOrderResponse uwOrderResponse, Map<String, Long> magentoIdAndFittingIdMap, String eventType, List<OrderItemDto> orderItemDtoList) throws ApplicationException {
        Map<String, List<String>> orderOpsDistinctUwItemIdMap = new HashMap<>();
        for (UwOrder uwItemResponse : uwOrderResponse.getUwOrders()) {
            validateUwResponseAndUpdateItemType(eventType, uwItemResponse);
            populateMagentoIdAndFittingIdMap(magentoIdAndFittingIdMap, uwItemResponse);
            String distinctItemKey = StringUtils.EMPTY;
            if(!ItemType.LEFT_LENS.name().equals(uwItemResponse.getItemType())
            && !ItemType.RIGHT_LENS.name().equals(uwItemResponse.getItemType())  &&  isNonPoweReadingItem(uwItemResponse.getMagentoItemId())){
                 distinctItemKey = uwItemResponse.getMagentoItemId() + uwItemResponse.getUnicomOrderCode();
            }else {
                 distinctItemKey = uwItemResponse.getMagentoItemId() + uwItemResponse.getItemType() + uwItemResponse.getUnicomOrderCode();
            }
            if (!orderOpsDistinctUwItemIdMap.containsKey(distinctItemKey)) {
                logger.info("[getOrderOpsUwItemIdMapAndUpdateFittingIdMap] key {}", distinctItemKey);
                orderOpsDistinctUwItemIdMap.put(distinctItemKey, Arrays.asList(String.valueOf(uwItemResponse.getUwItemId()),uwItemResponse.getUnicomOrderCode()));
            }
        }

        return orderOpsDistinctUwItemIdMap;
    }

    private boolean isNonPoweReadingItem(Long magentoItemId) {
      List<OrderItemDto> orderItemDtoList =  orderItemService.search("magentoItemId.eq:" + magentoItemId);
      Optional<OrderItemDto> orderItemDto =  orderItemDtoList.stream()
              .filter(oi -> oi.getParentMagentoItemId() == null)
              .filter(oi -> ItemType.NON_POWER_READING.equals(oi.getItemType()))
              .filter(oi -> oi.getB2bReferenceItemId() == null).findFirst();
      return orderItemDto.isPresent();
    }

    private void validateUwResponseAndUpdateItemType(String eventType, UwOrder uwItemResponse) throws ApplicationException {
        if (StringUtils.isEmpty(uwItemResponse.getItemType())) {
            if (UW_ITEM_ID_UPDATE_EVENT_TYPE_CANCELLATION.equalsIgnoreCase(eventType)) {
                logger.info("[validateUwResponseAndUpdateItemType] inside PID level compare for order-ops response {}", uwItemResponse);
                if (TEMP_RIGHT_LENS_PID.equals(uwItemResponse.getProductId())) {
                    logger.info("[validateUwResponseAndUpdateItemType] updating item type to RIGHT LENS for item {}", uwItemResponse.getUwItemId());
                    uwItemResponse.setItemType(ItemType.RIGHT_LENS.name());
                } else if (TEMP_LEFT_LENS_PID.equals(uwItemResponse.getProductId())) {
                    logger.info("[validateUwResponseAndUpdateItemType] updating item type to LEFT LENS for item {}", uwItemResponse.getUwItemId());
                    uwItemResponse.setItemType(ItemType.LEFT_LENS.name());
                }
            } else {
                throw new ApplicationException("Item type value is NULL for Item " + uwItemResponse.getUwItemId(), null);
            }
        }
    }

    private void populateMagentoIdAndFittingIdMap(Map<String, Long> magentoIdAndFittingIdMap, UwOrder uwItemResponse) {
        String key = uwItemResponse.getMagentoItemId() + "_" + uwItemResponse.getUnicomOrderCode();
        if (ItemType.getFrameItemTypes().contains(uwItemResponse.getItemType())
                && !magentoIdAndFittingIdMap.containsKey(key)
        ) {
            magentoIdAndFittingIdMap.put(key, Long.valueOf(uwItemResponse.getUwItemId()));
        }
    }

    public String getShipmentSubType(OrderDto orderDto, List<OrderItemDto> orderItemDtoList) {
        if (isDistributorSuperOrderBySubType(orderDto)) {
            return OrderSubType.DISTRIBUTOR_SUPER_ORDER.name();
        } else if(isDistributorJitOrder(orderDto)) {
            return OrderSubType.DISTRIBUTOR_JIT_ORDER.name();
        } else if (isDistributorOrder(orderDto)) {
            return OrderSubType.DISTRIBUTOR_ORDER.name();
        }
        String isBulkOrder = getOrderMetaValueFromKey(orderDto.getOrderMetaData(), ORDER_META_KEY_BULK_ORDER);
        if (!StringUtils.isEmpty(isBulkOrder) && Boolean.valueOf(isBulkOrder)) {
            return "BULK";
        }

        for (OrderItemDto orderItemDto : orderItemDtoList) {
            if (!ObjectUtils.isEmpty(orderItemDto.getItemPower()) && !ObjectUtils.isEmpty(orderItemDto.getItemPower().getPowerType()) && orderItemDto.getItemPower().getPowerType().contains("LENS_ONLY")) {
                return "LENS_ONLY";
            }
        }

        return "CUSTOMER";
    }

    public boolean isBulkOrder(OrderDto orderDto) {
        return isDistributorOrder(orderDto);
    }

    private boolean isDistributorSuperOrderBySubType(OrderDto orderDto) {
        return orderDto != null && isDistributorOrder(orderDto) && OrderSubType.SUPER_ORDER.equals(orderDto.getOrderSubType());
    }

    public boolean isDistributorOrder(OrderDto orderDto) {
        if(orderDto == null){
            return false;
        }
        if(OrderType.DISTRIBUTOR_ORDER.equals(orderDto.getOrderType())){
            return true;
        }
        return false;
    }

    public boolean isNonWarehouseProcessingOrder(LineItemPayload lineItemPayload){
        if(lineItemPayload == null)
            return false;

        logger.info("[isNexsStoreOrder] productDeliveryType :{} localfitting :{}" ,
                lineItemPayload.getProductDeliveryType(), lineItemPayload.getIsLocalFittingRequired());

        if (ProductDeliveryType.OTC.name().equalsIgnoreCase(lineItemPayload.getProductDeliveryType()) ||
                (ProductDeliveryType.DTC.name().equalsIgnoreCase(lineItemPayload.getProductDeliveryType()) &&
                        lineItemPayload.getIsLocalFittingRequired())) {
            return true;
        }

        return false;
    }

    public boolean isNonWarehouseProcessingOrder(OrderItemDto orderItemDto){
        if(orderItemDto == null)
            return false;
        Optional<OrderItemMetaDataDto> optionalOrderItemMetaDataDto =  orderItemDto.getOrderItemMetaData().stream().filter(oi -> OREDER_ITEM_META_KEY_CENTRAL_FACILITY_CODE.equals(oi.getEntityKey())).findFirst();

        if(optionalOrderItemMetaDataDto.isPresent() && !StringUtils.isEmpty(optionalOrderItemMetaDataDto.get().getEntityValue())) {
            return true;
        }

        return ProductDeliveryType.OTC.equals(orderItemDto.getProductDeliveryType()) ||
                (ProductDeliveryType.DTC.equals(orderItemDto.getProductDeliveryType()) &&
                        FulfillmentType.LOCAL_FITTING.equals(orderItemDto.getFulfillmentType())
                || (ProductDeliveryType.B2B.equals(orderItemDto.getProductDeliveryType())
                && orderItemDto.getB2bReferenceItemId() != null));
    }

    public boolean isNonWarehouseProcessingOrderExcludingB2b(OrderItemDto orderItemDto) {
        if(orderItemDto == null) return false;
        Optional<OrderItemMetaDataDto> optionalOrderItemMetaDataDto =  orderItemDto.getOrderItemMetaData().stream().filter(oi -> OREDER_ITEM_META_KEY_CENTRAL_FACILITY_CODE.equals(oi.getEntityKey())).findFirst();

        if(optionalOrderItemMetaDataDto.isPresent() && !StringUtils.isEmpty(optionalOrderItemMetaDataDto.get().getEntityValue())) {
            return true;
        }
        return ProductDeliveryType.OTC.equals(orderItemDto.getProductDeliveryType()) ||
                (ProductDeliveryType.DTC.equals(orderItemDto.getProductDeliveryType()) &&
                        FulfillmentType.LOCAL_FITTING.equals(orderItemDto.getFulfillmentType()));
    }

    public boolean isUaeOrder(OrderDto orderDto, ShipmentDto shipmentDto){
        if(orderDto == null){
            return false;
        }
        if("AE".equals(shipmentDto.getLegalOwnerCountry())){ // or we can get hubcode from uw_orders
            return true;
        }
        return false;
    }

    public boolean isB2bShipment(ShipmentDto shipmentDto){
        if(shipmentDto.getOrderItems().get(0).getB2bReferenceItemId() != null){
            return  true;
        }
        return false;
    }

    public boolean isNexsStoreOrder(OrderItemDto orderItemDto){
        if(orderItemDto == null)
            return false;
        return ProductDeliveryType.OTC.equals(orderItemDto.getProductDeliveryType()) ||
                (ProductDeliveryType.DTC.equals(orderItemDto.getProductDeliveryType()) &&
                        FulfillmentType.LOCAL_FITTING.equals(orderItemDto.getFulfillmentType()));
    }

    public boolean isReassignedShipment(ShipmentDto shipmentDto) {
        return ShipmentStatus.OMS_REASSIGNED == shipmentDto.getShipmentStatus();
    }

    public boolean isDistributorJitOrder(OrderDto orderDto) {
        if(orderDto == null){
            return false;
        }
        if(OrderSubType.DISTRIBUTOR_JIT_ORDER.equals(orderDto.getOrderSubType())){
            return true;
        }
        return false;
    }

    public boolean isEligibleForD365(ShipmentDto shipmentDto) {
        NavChannel navChannel = shipmentDto.getOrderItems().get(0).getNavChannel();
        if (navChannel != null && d365EligibleNavChannels.contains(navChannel.name())) {
            logger.info("[isEligibleForD365] NavChannel : {}, Shipment : {}, isEligibleForD365 : {}", navChannel, shipmentDto.getWmsShippingPackageId(), true);
            return true;
        }
        return false;
    }
    public void updateItemStatusAtInventory(List<OrderItemDto> orderItemDtoList, String eventName){
        OrderDto orderDto = orderService.findById(orderItemDtoList.get(0).getOrderId());
        DistributorOrders distributorOrders =  distributorOrdersService.findByIncrementId(String.valueOf(orderDto.getIncrementId()));
        JitDoItemStatusUpdateRequest jitDoItemStatusUpdateRequest = new JitDoItemStatusUpdateRequest();
        jitDoItemStatusUpdateRequest.setJitPoId(Long.valueOf(distributorOrders.getPoNumber()));
        jitDoItemStatusUpdateRequest.setReason(eventName.equalsIgnoreCase(OrderItemStatus.SKIPPED.toString()) ? "WMS SKIPPED" : "Status Update");
        jitDoItemStatusUpdateRequest.setEventName(eventName);
        jitDoItemStatusUpdateRequest.setDoItemStatus(eventName);
        jitDoItemStatusUpdateRequest.setFittingId(orderItemDtoList.get(0).getFittingId()-1);
        logger.info("[updateItemStatusAtInventory] calling order-ops to update skipped item status  request {}", jitDoItemStatusUpdateRequest);
        orderOpsConnector.markDOItemSkipped(jitDoItemStatusUpdateRequest);
    }

    public boolean isCentralFacilityCodePresent(OrderItemDto orderItemDto) {
        Optional<OrderItemMetaDataDto> centralFacilityCode = orderItemDto.getOrderItemMetaData().stream().filter(o -> o.getEntityKey().equals("CENTRAL_FACILITY_CODE")).findFirst();
        return centralFacilityCode.isPresent()
                && org.springframework.util.StringUtils.hasLength(centralFacilityCode.get().getEntityValue());
    }

}
