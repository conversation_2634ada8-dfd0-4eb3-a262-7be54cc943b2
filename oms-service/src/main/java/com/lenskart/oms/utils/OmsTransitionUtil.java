package com.lenskart.oms.utils;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.enums.OrderItemStatus;
import com.lenskart.oms.enums.OrderItemSubStatus;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.model.*;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.http.HttpStatus;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class OmsTransitionUtil {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    protected Logger logger;

    @Setter(onMethod__ = {@Autowired})
    private OmsEvents omsEvents;

    public Action getTransitionActionByOperationAndStatus(String operation, OrderItemStatus itemStatus) throws ApplicationException {
        OmsEvent omsEvent = omsEvents.getOmsEvents().get(operation);
        if (Objects.isNull(omsEvent)) {
            logger.error("[getTransitionActionByOperationAndStatus] No Transition available with operation name {} and state {}", operation, itemStatus.name());
            throw new ApplicationException(HttpStatus.SC_BAD_REQUEST, "No Transition available with operation name " + operation + " and state " + itemStatus.name(), null);
        }

        Action action = omsEvent.getTransition().get(new OrderItemOperations(itemStatus));
        if (action == null || action.getItemStatus() == null || action.getOrderStatus() == null || action.getShipmentStatus() == null) {
            logger.error("[getTransitionActionByOperationAndStatus] No Action available with operation name {} and state {}", operation, itemStatus.name());
            throw new ApplicationException(HttpStatus.SC_BAD_REQUEST, "No Action available with operation name " + operation + " and state " + itemStatus.name(), null);
        }

        return action;
    }
}
