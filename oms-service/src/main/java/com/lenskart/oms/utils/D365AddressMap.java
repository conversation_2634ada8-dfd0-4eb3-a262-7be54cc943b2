package com.lenskart.oms.utils;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Getter
public class D365AddressMap {

    public static final Map<String, String> StateMapping;
    static {
        Map<String, String> tempMap = new HashMap<>();
        tempMap.put("ANDAMANANDNICOBARISLANDS","AN");
        tempMap.put("ANDHRAPRADESH","AP");
        tempMap.put("ARUNACHALPRADESH","AR");
        tempMap.put("ASSAM","AS");
        tempMap.put("BIHAR","BH");
        tempMap.put("CHATTISGARH","CT");
        tempMap.put("CHANDIGARH","CH");
        tempMap.put("DAMANANDDIU","DD");
        tempMap.put("DELH<PERSON>","DL");
        tempMap.put("DAD<PERSON>AN<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>","DN");
        tempMap.put("GOA","GA");
        tempMap.put("GUJARAT","GJ");
        tempMap.put("HIMACHALPRADESH","HP");
        tempMap.put("HARYANA","HR");
        tempMap.put("JAMMUANDKASHMIR","JK");
        tempMap.put("JHARKHAND","JH");
        tempMap.put("KERALA","KL");
        tempMap.put("KARNATAKA","KA");
        tempMap.put("LAKSHADWEEP","LD");
        tempMap.put("MEGHALAYA","ME");
        tempMap.put("MAHARASHTRA","MH");
        tempMap.put("MANIPUR","MN");
        tempMap.put("MADHYA PRADESH","MP");
        tempMap.put("MIZORAM","MI");
        tempMap.put("NAGALAND","NL");
        tempMap.put("ORISSA","OR");
        tempMap.put("ODISHA","OR");
        tempMap.put("PUNJAB","PB");
        tempMap.put("PONDICHERRY","PY");
        tempMap.put("RAJASTHAN","RJ");
        tempMap.put("SIKKIM","SK");
        tempMap.put("TAMILNADU","TN");
        tempMap.put("TRIPURA","TR");
        tempMap.put("TELANGANA","TS");
        tempMap.put("UTTARAKHAND","UT");
        tempMap.put("UTTARPRADESH","UP");
        tempMap.put("WESTBENGAL","WB");
        StateMapping = Collections.unmodifiableMap(tempMap);
    }

    @PostConstruct
    public void init(){
        log.info("Initialized D365 State mapping");
    }
}
