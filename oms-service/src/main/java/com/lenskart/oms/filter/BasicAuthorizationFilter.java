package com.lenskart.oms.filter;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.apache.tomcat.util.codec.binary.Base64;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.util.StringUtils;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Set;
import java.util.StringTokenizer;

@Configuration
@Order()
public class BasicAuthorizationFilter implements Filter {

    private static final String X_CLIENT_KEY = "x-client-key";
    private static final String CLIENT = "client";
    private static final String USER_ID = "userId";
    private static final String USER_EMAIL = "userEmail";

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;


    @Value("#{'${service.allowed.clients}'.split(',')}")
    private Set<String> allowedClients;


    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain)
            throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) servletRequest;
        HttpServletResponse httpResponse = (HttpServletResponse) servletResponse;
        if (!httpRequest.getRequestURI().contains("oms")) {
            filterChain.doFilter(servletRequest, servletResponse);
            return;
        }
        String client = httpRequest.getHeader(X_CLIENT_KEY);
        if (StringUtils.hasLength(client)) {
            MDC.put(CLIENT, client);
        }
        if ((StringUtils.hasLength(client)
                && !allowedClients.contains(client.toLowerCase()))
                || !isValidUser(httpRequest)
        ) {
            logger.error("invalid request {}", httpRequest.getRequestURI());
            httpResponse.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            return;
        }
        filterChain.doFilter(servletRequest, servletResponse);
    }

    public boolean isValidUser(HttpServletRequest req) {
        String authHeader = req.getHeader("Authorization");
        if (authHeader == null) return false;
        StringTokenizer stringTokenizer = new StringTokenizer(authHeader);
        if (!stringTokenizer.hasMoreTokens()) return false;
        String basic = stringTokenizer.nextToken();
        if (basic.equalsIgnoreCase("Basic")) {
            try {
                if (!stringTokenizer.hasMoreTokens()) return false;
                String credentials = new String(Base64.decodeBase64(stringTokenizer.nextToken()), StandardCharsets.UTF_8);
                logger.debug("Credentials: {}", credentials);
                int p = credentials.indexOf(":");
                if (p != -1) {
                    String userDetails = credentials.substring(0, p).trim();
                    int userDetailsIndex = userDetails.indexOf("~");
                    if (userDetailsIndex == -1) return false;
                    String userId = userDetails.substring(0, userDetailsIndex).trim();
                    String userEmail = userDetails.substring(userDetailsIndex + 1).trim();
                    String password = credentials.substring(p + 1).trim();
                    MDC.put(USER_ID, userId);
                    MDC.put(USER_EMAIL, userEmail);
                    logger.debug("userId: {} userEmail: {} password: {}", userId, userEmail, password);
                } else {
                    logger.error("Invalid authentication token");
                    return false;
                }
            } catch (Exception e) {
                return false;
            }
        } else {
            return false;
        }
        return true;
    }
}
