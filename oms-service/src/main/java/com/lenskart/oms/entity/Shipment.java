package com.lenskart.oms.entity;

import com.lenskart.nexs.commons.entity.BaseEntity;
import com.lenskart.oms.enums.ShipmentSubStatus;
import com.lenskart.oms.enums.ShipmentStatus;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.envers.AuditOverride;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;

import javax.persistence.*;
import java.util.*;

@Getter
@Setter
@ToString(callSuper = true)
@Entity
@Audited
@AuditOverride(forClass = BaseEntity.class)
@Table(name = "shipment")
public class Shipment extends BaseEntity {

    @Column(name = "wms_order_code")
    private String wmsOrderCode;

    @Column(name = "wms_shipping_package_id")
    private String wmsShippingPackageId;

    @Enumerated(EnumType.STRING)
    @Column(name = "shipment_status")
    private ShipmentStatus shipmentStatus;

    @Enumerated(EnumType.STRING)
    @Column(name = "shipment_sub_status")
    private ShipmentSubStatus shipmentSubStatus;

    @Column(name = "facility")
    private String facility;

    @Column(name = "legal_owner")
    private String legalOwner;

    @Column(name = "legal_owner_country")
    private String legalOwnerCountry;

    @Column(name = "shipment_type")
    private String shipmentType;

    @Column(name = "shipment_sub_type")
    private String shipmentSubType;

    @Column(name = "courier_code")
    private String courierCode;

    @Column(name = "awb_number")
    private String awbNumber;

    @Column(name = "invoice_number")
    private String invoiceNumber;

    @Column(name = "manifest_number")
    private String manifestNumber;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "actual_ship_date")
    private Date actualShipDate;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "actual_delivery_date")
    private Date actualDeliveryDate;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "expected_ship_date")
    private Date expectedShipDate;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "expected_delivery_date")
    private Date expectedDeliveryDate;

    @ToString.Exclude
    @NotAudited
    @OneToOne(optional = false, cascade = CascadeType.ALL)
    @JoinColumn(name = "shipping_address_id", nullable = false, unique = true)
    private OrderAddress shippingAddress;

    @ToString.Exclude
    @NotAudited
    @OneToOne(optional = false, cascade = CascadeType.ALL)
    @JoinColumn(name = "billing_address_id", nullable = false, unique = true)
    private OrderAddress billingAddress;

    @ToString.Exclude
    @NotAudited
    @OneToMany(cascade = CascadeType.MERGE, fetch = FetchType.EAGER)
    @JoinColumn(name = "shipment_id", referencedColumnName = "id")
    private List<OrderItems> orderItems = new ArrayList<>();

    @ToString.Exclude
    @NotAudited
    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "shipment_id", referencedColumnName = "id")
    private List<ShipmentMetaData> shipmentMetaData = new ArrayList<>();
}
