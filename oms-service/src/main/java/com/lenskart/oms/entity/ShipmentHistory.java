package com.lenskart.oms.entity;

import com.lenskart.oms.enums.ShipmentStatus;
import com.lenskart.oms.enums.ShipmentSubStatus;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;

@Getter
@Setter
@ToString(callSuper = true)
@Entity
@Table(name = "shipment_history")
@IdClass(AuditCompositeKey.class)
public class ShipmentHistory {

    @Id
    @Column(name = "id", nullable = false)
    private Long id;

    @Id
    @Column(name = "rev", nullable = false)
    private Long rev;

    @Column(name = "rev_type")
    private boolean revType;

    @Column(name = "created_by", nullable = false, length = 100)
    private String createdBy;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "created_at", nullable = false, updatable = false)
    private Date createdAt;

    @Column(name = "updated_by", nullable = false, length = 100)
    private String updatedBy;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "updated_at", nullable = false)
    private Date updatedAt;

    @Column(name = "wms_order_code")
    private String wmsOrderCode;

    @Column(name = "wms_shipping_package_id")
    private String wmsShippingPackageId;

    @Enumerated(EnumType.STRING)
    @Column(name = "shipment_status")
    private ShipmentStatus shipmentStatus;

    @Enumerated(EnumType.STRING)
    @Column(name = "shipment_sub_status")
    private ShipmentSubStatus shipmentSubStatus;

    @Column(name = "facility")
    private String facility;

    @Column(name = "shipment_type")
    private String shipmentType;

    @Column(name = "shipment_sub_type")
    private String shipmentSubType;

    @Column(name = "courier_code")
    private String courierCode;

    @Column(name = "awb_number")
    private String awbNumber;

    @Column(name = "invoice_number")
    private String invoiceNumber;

    @Column(name = "manifest_number")
    private String manifestNumber;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "actual_ship_date")
    private Date actualShipDate;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "actual_delivery_date")
    private Date actualDeliveryDate;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "expected_ship_date")
    private Date expectedShipDate;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "expected_delivery_date")
    private Date expectedDeliveryDate;

    @Column(name = "shipping_address_id")
    private Long shippingAddressId;

    @Column(name = "billing_address_id")
    private Long billingAddressId;

}
