package com.lenskart.oms.entity;

import com.lenskart.nexs.commons.entity.BaseEntity;
import com.lenskart.oms.enums.OutboxEventAggregateType;
import com.lenskart.oms.enums.OutboxEventStatus;
import com.lenskart.oms.enums.OutboxEventType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.envers.NotAudited;

import javax.persistence.*;
import java.util.Date;

/**
 * Outbox Event Entity for implementing Transactional Outbox Pattern
 * This ensures reliable event publishing to Kafka topics
 */
@Getter
@Setter
@ToString(callSuper = true)
@Entity
@Table(name = "outbox_events", indexes = {
    @Index(name = "idx_outbox_status_created", columnList = "event_status, created_at"),
    @Index(name = "idx_outbox_aggregate_id", columnList = "aggregate_id"),
    @Index(name = "idx_outbox_event_type", columnList = "event_type")
})
public class OutboxEvent extends BaseEntity {

    @Column(name = "aggregate_id", nullable = false)
    private String aggregateId;

    @Enumerated(EnumType.STRING)
    @Column(name = "aggregate_type", nullable = false)
    private OutboxEventAggregateType aggregateType;

    @Enumerated(EnumType.STRING)
    @Column(name = "event_type", nullable = false)
    private OutboxEventType eventType;

    @Enumerated(EnumType.STRING)
    @Column(name = "event_status", nullable = false)
    private OutboxEventStatus eventStatus;

    @Column(name = "topic_name", nullable = false)
    private String topicName;

    @Column(name = "partition_key")
    private String partitionKey;

    @Lob
    @Column(name = "event_payload", nullable = false)
    private String eventPayload;

    @Column(name = "event_headers", columnDefinition = "TEXT")
    private String eventHeaders;

    @Column(name = "retry_count", nullable = false)
    private Integer retryCount = 0;

    @Column(name = "max_retry_count", nullable = false)
    private Integer maxRetryCount = 3;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "scheduled_at")
    private Date scheduledAt;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "processed_at")
    private Date processedAt;

    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    @Column(name = "idempotency_key")
    private String idempotencyKey;

    @Override
    @PrePersist
    protected void onCreate() {
        if (scheduledAt == null) {
            scheduledAt = new Date();
        }
        if (eventStatus == null) {
            eventStatus = OutboxEventStatus.PENDING;
        }
    }

    /**
     * Check if the event can be retried
     */
    public boolean canRetry() {
        return retryCount < maxRetryCount && 
               (eventStatus == OutboxEventStatus.PENDING || eventStatus == OutboxEventStatus.FAILED);
    }

    /**
     * Increment retry count
     */
    public void incrementRetryCount() {
        this.retryCount++;
    }

    /**
     * Mark event as processed successfully
     */
    public void markAsProcessed() {
        this.eventStatus = OutboxEventStatus.PROCESSED;
        this.processedAt = new Date();
        this.errorMessage = null;
    }

    /**
     * Mark event as failed
     */
    public void markAsFailed(String errorMessage) {
        this.eventStatus = OutboxEventStatus.FAILED;
        this.errorMessage = errorMessage;
        this.processedAt = new Date();
    }

    /**
     * Mark event as dead letter (max retries exceeded)
     */
    public void markAsDeadLetter(String errorMessage) {
        this.eventStatus = OutboxEventStatus.DEAD_LETTER;
        this.errorMessage = errorMessage;
        this.processedAt = new Date();
    }
}
