package com.lenskart.oms.entity;

import com.lenskart.nexs.commons.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;

@Getter
@Setter
@ToString(callSuper = true)
@Entity
@Table(name = "on_hold_master")
public class OnHoldMaster extends BaseEntity {

    @Column(name = "code")
    private String code;

    @Column(name = "description")
    private String description;
}
