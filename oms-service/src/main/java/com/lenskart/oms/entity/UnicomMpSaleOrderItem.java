package com.lenskart.oms.entity;

import com.lenskart.nexs.commons.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

@Getter
@Setter
@ToString(callSuper = true)
public class UnicomMpSaleOrderItem {
    private String barcode;
    private String productId;
    private String wmsOrderCode;
    private String itemId;
    private Date createdAt;

    public UnicomMpSaleOrderItem(String barcode, String productId, String wmsOrderCode, String itemId,Date createdAt) {
        this.barcode = barcode;
        this.productId = productId;
        this.wmsOrderCode = wmsOrderCode;
        this.itemId = itemId;
        this.createdAt = createdAt;
    }

}
