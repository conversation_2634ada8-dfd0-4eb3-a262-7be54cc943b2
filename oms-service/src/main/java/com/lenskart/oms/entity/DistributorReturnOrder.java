package com.lenskart.oms.entity;

import com.lenskart.nexs.commons.entity.BaseEntity;
import com.lenskart.oms.enums.DistributorReturnOrderStatus;
import com.lenskart.oms.enums.QcStatus;
import com.lenskart.oms.enums.ReturnType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;

@Getter
@Setter
@Entity
@ToString(callSuper = true)
@Table(name = "distributor_return_order")
public class DistributorReturnOrder extends BaseEntity {
    @Enumerated(EnumType.STRING)
    @Column(name = "return_type")
    private ReturnType returnType;
    @Column(name = "scanned_barcode")
    private String scannedBarcode;
    @Column(name = "itemId")
    private Long itemId;
    @Column(name = "wms_order_code")
    private String wmsOrderCode;
    @Enumerated(EnumType.STRING)
    @Column(name = "qc_status")
    private QcStatus qcStatus;
    @Column(name = "qc_fail_reason")
    private String qcFailReason;
    @Column(name = "return_reason")
    private String returnReason;
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private DistributorReturnOrderStatus status;
    @Column(name = "return_facility")
    private String returnFacility;
    @Column(name = "putaway_code")
    private String putawayCode;
}