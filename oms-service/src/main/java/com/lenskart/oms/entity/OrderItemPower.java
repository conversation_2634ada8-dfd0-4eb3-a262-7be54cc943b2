package com.lenskart.oms.entity;

import com.lenskart.nexs.commons.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;

@Getter
@Setter
@ToString(callSuper = true)
@Entity
@Table(name = "order_item_power")
public class OrderItemPower extends BaseEntity {

    @Column(name = "power_type")
    private String powerType;

    @Column(name = "product_id")
    private Long productId;

    @Column(name = "shell_id")
    private Long shellId;

    @Column(name = "sph")
    private String sph;

    @Column(name = "cyl")
    private String cyl;

    @Column(name = "axis")
    private String axis;

    @Column(name = "ap")
    private String ap;

    @Column(name = "pd")
    private String pd;

    @Column(name = "lens_height")
    private String lensHeight;

    @Column(name = "lens_width")
    private String lensWidth;

    @Column(name = "effective_dia")
    private String effectiveDia;

    @Column(name = "near_pd")
    private String nearPD;

    @Column(name = "edge_distance")
    private String edgeDistance;

    @Column(name = "top_distance")
    private String topDistance;

    @Column(name = "bottom_distance")
    private String bottomDistance;

    @Column(name = "coating_oid")
    private String coatingOid;

    @Column(name = "coating_name")
    private String coatingName;

    @Column(name = "lens_index")
    private String lensIndex;

    @Column(name = "lens_package_type")
    private String lensPackageType;

    @Column(name = "package_name")
    private String packageName;

    @Column(name = "lens_package")
    private String lensPackage;

    @Column(name = "web_package")
    private String webPackage;

    @Column(name = "package_price")
    private Double packagePrice;

    @Column(name = "patient_name")
    private String patientName;

    @Column(name = "patient_comments")
    private String patientComments;

    @Column(name = "prescription_url")
    private String prescriptionUrl;
}
