package com.lenskart.oms.entity;

import com.lenskart.nexs.commons.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;

@Getter
@Setter
@ToString(callSuper = true)
@Entity
@Table(name = "shipment_meta_data")
public class ShipmentMetaData extends BaseEntity {

    @Column(name = "entity_key")
    private String entityKey;

    @Column(name = "entity_value")
    private String entityValue;

    @Column(name = "shipment_id")
    private Long shipmentId;
}
