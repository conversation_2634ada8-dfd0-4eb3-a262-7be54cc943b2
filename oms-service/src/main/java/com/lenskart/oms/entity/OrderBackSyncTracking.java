package com.lenskart.oms.entity;

import com.lenskart.nexs.commons.entity.BaseEntity;
import com.lenskart.oms.enums.BackSyncEntityType;
import com.lenskart.oms.enums.BackSyncEventName;
import com.lenskart.oms.enums.BackSyncEventStatus;
import com.lenskart.oms.enums.BackSyncSystem;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;

@Getter
@Setter
@ToString(callSuper = true)
@Entity
@Table(name = "order_back_sync_tracking")
public class OrderBackSyncTracking extends BaseEntity {

    @Enumerated(EnumType.STRING)
    @Column(name = "event_name")
    private BackSyncEventName eventName;

    @Enumerated(EnumType.STRING)
    @Column(name = "entity_type")
    private BackSyncEntityType entityType;

    @Column(name = "entity_id")
    private String entityId;

    @Enumerated(EnumType.STRING)
    @Column(name = "event_status")
    private BackSyncEventStatus eventStatus;

    @Enumerated(EnumType.STRING)
    @Column(name = "back_sync_system")
    private BackSyncSystem backSyncSystem;

    @Column(name = "message")
    private String message;
}
