package com.lenskart.oms.entity;

import com.lenskart.oms.enums.OrderStatus;
import com.lenskart.oms.enums.OrderSubStatus;
import com.lenskart.oms.enums.OrderSubType;
import com.lenskart.oms.enums.OrderType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;

@Getter
@Setter
@ToString(callSuper = true)
@Entity
@Table(name = "orders_history")
@IdClass(AuditCompositeKey.class)
public class OrderHistory {

    @Id
    @Column(name = "id", nullable = false)
    private Long id;

    @Id
    @Column(name = "rev", nullable = false)
    private Long rev;

    @Column(name = "rev_type")
    private boolean revType;

    @Column(name = "created_by", nullable = false, length = 100)
    private String createdBy;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "created_at", nullable = false, updatable = false)
    private Date createdAt;

    @Column(name = "updated_by", nullable = false, length = 100)
    private String updatedBy;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "updated_at", nullable = false)
    private Date updatedAt;

    @Column(name = "increment_id")
    private Long incrementId;

    @Column(name = "juno_order_id")
    private Long junoOrderId;

    @Enumerated(EnumType.STRING)
    @Column(name = "order_status")
    private OrderStatus orderStatus;

    @Enumerated(EnumType.STRING)
    @Column(name = "order_sub_status")
    private OrderSubStatus orderSubStatus;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "order_date")
    private Date orderDate;

    @Column(name = "lk_country")
    private String lkCountry;

    @Column(name = "currency_code")
    private String currencyCode;

    @Column(name = "customer_id")
    private Long customerId;

    @Column(name = "merchant_id")
    private String merchantId;

    @Column(name = "order_source")
    private String orderSource;

    @Column(name = "store_id")
    private Long storeId;

    @Column(name = "exchange_flag")
    private Boolean exchangeFlag;

    @Column(name = "exchange_item_id")
    private Long exchangeItemId;

    @Column(name = "payment_captured")
    private Boolean paymentCaptured;

    @Column(name = "payment_method")
    private String paymentMethod;

    @Column(name = "is_onhold")
    private Boolean isOnHold;

    @Column(name = "onhold_reason_id")
    private Long onHoldReasonId;

    @Column(name = "legal_entity")
    private String legalEntity;

    @Column(name = "legal_owner_country")
    private String legalOwnerCountry;

}
