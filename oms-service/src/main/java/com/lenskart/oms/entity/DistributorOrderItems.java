package com.lenskart.oms.entity;

import com.lenskart.nexs.commons.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Getter
@Setter
@Entity
@ToString(callSuper = true)
@Table(name = "distributor_order_items")
public class DistributorOrderItems extends BaseEntity {

    @Column(name = "distributor_order_id")
    private Long orderId;

    @Column(name = "product_id")
    private Integer productId;

    @Column(name = "product_name")
    private String productName;

    @Column(name = "type")
    private String productType;

    @Column(name = "quantity")
    private Integer quantity;

    @Column(name = "price")
    private Double price;

}
