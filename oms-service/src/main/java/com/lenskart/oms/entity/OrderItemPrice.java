package com.lenskart.oms.entity;

import com.lenskart.nexs.commons.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;

@Getter
@Setter
@ToString(callSuper = true)
@Entity
@Table(name = "order_items_price")
public class OrderItemPrice extends BaseEntity {

    @Column(name = "coupon_discount")
    private Double couponDiscount;

    @Column(name = "exchange_discount")
    private Double exchangeDiscount;

    @Column(name = "fc_discount")
    private Double fcDiscount;

    @Column(name = "gv_discount")
    private Double gvDiscount;

    @Column(name = "implicit_discount")
    private Double implicitDiscount;

    @Column(name = "lenskart_plus_discount")
    private Double lenskartPlusDiscount;

    @Column(name = "lenskart_discount")
    private Double lenskartDiscount;

    @Column(name = "prepaid_discount")
    private Double prepaidDiscount;

    @Column(name = "sc_discount")
    private Double scDiscount;

    @Column(name = "wallet_discount")
    private Double walletDiscount;

    @Column(name = "wallet_plus_discount")
    private Double walletPlusDiscount;

    @Column(name = "item_total_after_discount")
    private Double itemTotalAfterDiscount;

    @Column(name = "item_grand_total")
    private Double itemGrandTotal;

    @Column(name = "shipping_charges")
    private Double shippingCharges;

    @Column(name = "tax_collected")
    private Double taxCollected;

    @Column(name = "gift_card_discount")
    private Double giftCardDiscount;

    @Column(name = "item_total")
    private Double itemTotal;

    @Column(name = "insurance_benefit_discount")
    private Double insuranceBenefitDiscount;


}
