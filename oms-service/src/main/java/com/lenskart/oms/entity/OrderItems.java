package com.lenskart.oms.entity;

import com.lenskart.nexs.commons.entity.BaseEntity;
import com.lenskart.oms.enums.*;
import lombok.*;
import org.hibernate.envers.AuditOverride;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@ToString(callSuper = true)
@Entity
@Audited
@AuditOverride(forClass = BaseEntity.class)
@Table(name = "order_items")
public class OrderItems extends BaseEntity {

    @Column(name = "magento_item_id")
    private Long magentoItemId;

    @Column(name = "parent_magento_item_id")
    private Long parentMagentoItemId;

    @Enumerated(EnumType.STRING)
    @Column(name = "item_status")
    private OrderItemStatus itemStatus;

    @Enumerated(EnumType.STRING)
    @Column(name = "item_sub_status")
    private OrderItemSubStatus itemSubStatus;

    @Column(name = "product_id")
    private Long productId;

    @Column(name = "item_barcode")
    private String itemBarcode;

    @Column(name = "fitting_id")
    private Long fittingId;

    @Enumerated(EnumType.STRING)
    @Column(name = "item_type")
    private ItemType itemType;

    @Enumerated(EnumType.STRING)
    @Column(name = "fitting_type")
    private FittingType fittingType;

    @Enumerated(EnumType.STRING)
    @Column(name = "fulfillment_type")
    private FulfillmentType fulfillmentType;

    @Column(name = "b2b_reference_item_id")
    private Long b2bReferenceItemId;

    @Enumerated(EnumType.STRING)
    @Column(name = "channel")
    private Channel channel;

    @Enumerated(EnumType.STRING)
    @Column(name = "nav_channel")
    private NavChannel navChannel;

    @Enumerated(EnumType.STRING)
    @Column(name = "product_delivery_type")
    private ProductDeliveryType productDeliveryType;

    @Column(name = "shipping_destination_type")
    private String shippingDestinationType;

    @Enumerated(EnumType.STRING)
    @Column(name = "delivery_priority")
    private DeliveryPriority deliveryPriority;

    @Column(name = "oma_status")
    private String omaStatus;

    @Column(name = "jit_status")
    private String jitStatus;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "promised_ship_date")
    private Date promisedShipDate;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "promised_delivery_date")
    private Date promisedDeliveryDate;

    @Column(name = "delivery_store_id")
    private String deliveryStoreId;

    @Column(name = "delivery_zip_code")
    private String deliveryZipCode;

    @Column(name = "delivery_country")
    private String deliveryCountry;

    @Column(name = "order_id")
    private Long orderId;

    @Column(name = "shipment_id")
    private Long shipmentId;

    @Column(name = "uw_item_id")
    private Long uwItemId;

    @ToString.Exclude
    @NotAudited
    @OneToOne(optional = false, cascade = CascadeType.ALL)
    @JoinColumn(name = "power_id", nullable = false, unique = true)
    private OrderItemPower itemPower;

    @ToString.Exclude
    @NotAudited
    @OneToOne(optional = false, cascade = CascadeType.ALL)
    @JoinColumn(name = "order_item_price_id")
    private OrderItemPrice orderItemPrice;

    @ToString.Exclude
    @NotAudited
    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "order_item_id", referencedColumnName = "id")
    private List<OrderItemMetaData> orderItemMetaData = new ArrayList<>();
}
