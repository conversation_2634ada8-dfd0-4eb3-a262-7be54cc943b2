package com.lenskart.oms.entity;

import com.lenskart.nexs.commons.entity.BaseEntity;
import com.lenskart.oms.enums.OrderStatus;
import com.lenskart.oms.enums.OrderSubStatus;
import com.lenskart.oms.enums.OrderSubType;
import com.lenskart.oms.enums.OrderType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.envers.AuditOverride;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;

import javax.persistence.*;
import java.util.*;

@Getter
@Setter
@ToString(callSuper = true)
@Entity
@Audited
@AuditOverride(forClass = BaseEntity.class)
@Table(name = "orders")
public class Order extends BaseEntity {

    @Column(name = "increment_id")
    private Long incrementId;

    @Column(name = "juno_order_id")
    private Long junoOrderId;

    @Enumerated(EnumType.STRING)
    @Column(name = "order_status")
    private OrderStatus orderStatus;

    @Enumerated(EnumType.STRING)
    @Column(name = "order_sub_status")
    private OrderSubStatus orderSubStatus;

    @Enumerated(EnumType.STRING)
    @Column(name = "order_type")
    @NotAudited
    private OrderType orderType;

    @Enumerated(EnumType.STRING)
    @Column(name = "order_sub_type")
    @NotAudited
    private OrderSubType orderSubType;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "order_date")
    private Date orderDate;

    @Column(name = "lk_country")
    private String lkCountry;

    @Column(name = "currency_code")
    private String currencyCode;

    @Column(name = "customer_id")
    private Long customerId;

    @Column(name = "merchant_id")
    private String merchantId;

    @Column(name = "order_source")
    private String orderSource;

    @Column(name = "store_id")
    private Long storeId;

    @Column(name = "exchange_flag")
    private Boolean exchangeFlag;

    @Column(name = "exchange_item_id")
    private Long exchangeItemId;

    @Column(name = "payment_captured")
    private Boolean paymentCaptured;

    @Column(name = "payment_method")
    private String paymentMethod;

    @Column(name = "is_onhold")
    private Boolean isOnHold;

    @Column(name = "onhold_reason_id")
    private Long onHoldReasonId;

    @ToString.Exclude
    @NotAudited
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    @JoinColumn(name = "order_id", referencedColumnName = "id")
    private List<OrderItems> orderItems = new ArrayList<>();

    @ToString.Exclude
    @NotAudited
    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "order_id", referencedColumnName = "id")
    private List<OrderMetaData> orderMetaData = new ArrayList<>();

}
