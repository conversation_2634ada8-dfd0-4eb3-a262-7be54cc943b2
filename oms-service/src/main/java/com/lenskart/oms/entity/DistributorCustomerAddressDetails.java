package com.lenskart.oms.entity;

import com.lenskart.nexs.commons.entity.BaseEntity;
import com.lenskart.oms.enums.AddressType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.envers.AuditOverride;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;

import javax.persistence.*;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Getter
@Setter
@ToString(callSuper = true)
@Entity
@Audited
@AuditOverride(forClass = BaseEntity.class)
@Table(name = "distributor_customer_address_details")
public class DistributorCustomerAddressDetails extends BaseEntity {

    @Column(name = "customer_details_id")
    private Long customerDetailsId;

    @Enumerated(EnumType.STRING)
    @Column(name = "address_type")
    private AddressType addressType;

    @NotBlank(message = "Address Line 1 field is mandatory")
    @Column(name = "address_line_1")
    private String addressLine1;

    @Column(name = "address_line_2")
    private String addressLine2;

    @NotBlank(message = "City field is mandatory")
    @Column(name = "city")
    private String city;

    @Column(name = "country")
    private String country;

    @Min(0)
    @Column(name = "pincode")
    private Integer pincode;

    @NotBlank(message = "State field is mandatory")
    @Column(name = "state")
    private String state;

    @NotBlank(message = "Phone field is mandatory")
    @Column(name = "phone")
    private String phone;
}
