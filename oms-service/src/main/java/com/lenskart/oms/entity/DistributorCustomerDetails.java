package com.lenskart.oms.entity;

import com.lenskart.nexs.commons.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.envers.AuditOverride;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Getter
@Setter
@ToString(callSuper = true)
@Entity
@Audited
@AuditOverride(forClass = BaseEntity.class)
@Table(name = "distributor_customer_details")
public class DistributorCustomerDetails extends BaseEntity {
    @NotBlank(message = "Name field is mandatory")
    @Column(name = "name")
    private String name;

    @NotBlank(message = "Code field is mandatory")
    @Column(name = "code")
    private String code;

    @Column(name = "email")
    private String email;

    @Column(name = "pan")
    private String pan;

    @Column(name = "tin")
    private String tin;

    @Column(name = "gstin")
    private String gstin;

    @Column(name = "mobile")
    private String mobile;

    @Column(name = "fax")
    private String fax;

    @Column(name = "website_url")
    private String websiteUrl;

    @Column(name = "central_sale_tax")
    private Double centralSaleTax;

    @Column(name = "service_tax")
    private Double serviceTax;

    @NotNull(message = "Tax Exempted field is mandatory")
    @Column(name = "tax_exempted")
    private boolean taxExempted;

    @Column(name = "customer_enabled")
    private boolean customerEnabled;

    @NotNull(message = "Registered Dealer field is mandatory")
    @Column(name = "registered_dealer")
    private boolean registeredDealer;

    @NotNull(message = "Dual Company Retail field is mandatory")
    @Column(name = "dual_company_retail")
    private boolean dualCompanyRetail;

    @NotNull(message = "Provides C Form field is mandatory")
    @Column(name = "provides_c_form")
    private boolean providesCForm;

    @ToString.Exclude
    @NotAudited
    @OneToMany(cascade = CascadeType.ALL)
    @JoinColumn(name = "customer_details_id", referencedColumnName = "id")
    private List<DistributorCustomerAddressDetails> distributorCustomerAddressDetails;
}
