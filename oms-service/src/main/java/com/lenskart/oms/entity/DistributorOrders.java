package com.lenskart.oms.entity;

import com.lenskart.nexs.commons.entity.BaseEntity;
import com.lenskart.oms.enums.DoStatus;
import com.lenskart.oms.enums.DoType;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import java.util.List;

@Getter
@Setter
@Entity
@ToString(callSuper = true)
@Table(name = "distributor_orders")
public class DistributorOrders extends BaseEntity {

    @Column(name = "increment_id")
    private String incrementId;

    @Column(name = "juno_order_id")
    private String junoOrderId;

    @Column(name = "po_number")
    private String poNumber;

    @Enumerated(EnumType.STRING)
    @Column(name = "do_type")
    private DoType doType;

    @Column(name = "facility")
    private String facility;

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private DoStatus status;

    @Column(name = "cancellation_reason")
    private String cancellationReason;

    @Column(name = "customer_id")
    private Long customerId;

    @ToString.Exclude
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "distributor_order_id", referencedColumnName = "id")
    private List<DistributorOrderItems> orderItems;


}
