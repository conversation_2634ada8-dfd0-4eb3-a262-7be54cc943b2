package com.lenskart.oms.entity;

import com.lenskart.nexs.commons.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;

@Getter
@Setter
@ToString(callSuper = true)
@Entity
@Table(name = "shipment_timeline")
public class ShipmentTimeline extends BaseEntity {

    @Column(name = "order_id")
    private Long orderId;

    @Column(name = "shipment_id")
    private Long shipmentId;

    @Column(name = "order_item_id")
    private Long orderItemId;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "fulfilled_time")
    private Date fulfilledTime;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "shipment_assigned_time")
    private Date assignedTime;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "picked_time")
    private Date pickedTime;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "qc_complete_time")
    private Date qcCompleteTime;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "qc_fail_time")
    private Date qcFailTime;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "packed_time")
    private Date packedTime;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "invoice_time")
    private Date invoiceTime;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "manifest_time")
    private Date manifestTime;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "dispatch_time")
    private Date dispatchTime;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "delivered_time")
    private Date deliveredTime;
}
