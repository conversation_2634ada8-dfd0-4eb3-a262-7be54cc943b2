package com.lenskart.oms.strategy.impl;

import com.lenskart.oms.connector.D365Connector;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.dto.*;
import com.lenskart.oms.enums.*;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.facade.D365Facade;
import com.lenskart.oms.model.Action;
import com.lenskart.oms.request.BackSyncRequest;
import com.lenskart.oms.request.D365PublishDispatchRequest;
import com.lenskart.oms.request.ShipmentItemUpdate;
import com.lenskart.oms.request.ShipmentUpdateEvent;
import com.lenskart.oms.response.D365PublishDispatchResponse;
import com.lenskart.oms.service.ShipmentMetaService;
import com.lenskart.oms.strategy.BaseShipmentEventStrategy;
import com.lenskart.oms.utils.LegalOwnerUtil;
import com.lenskart.oms.utils.OmsCommonUtil;
import com.lenskart.oms.validators.ShipmentEventValidator;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.lenskart.oms.constants.ApplicationConstants.JUNO_CALLBACK_CHECKPOINT;
import static com.lenskart.oms.constants.ApplicationConstants.ORDER_ITEM_META_KEY_HUB_CODE;

@Component
@Setter(onMethod__ = {@Autowired})
public class DispatchShipmentStrategy extends BaseShipmentEventStrategy {

    private ShipmentEventValidator shipmentEventValidator;
    private OmsCommonUtil omsCommonUtil;
    private D365Connector d365Connector;
    private ShipmentMetaService shipmentMetaService;
    private LegalOwnerUtil legalOwnerUtil;
    private D365Facade d365Facade;

    @Override
    protected ShipmentEvent supportedOrderEvents() {
        return ShipmentEvent.MARK_SHIPMENT_DISPATCH;
    }

    @Override
    protected void preExecute(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        shipmentEventValidator.validateDispatchEvent(shipmentUpdateEvent);
    }

    @Override
    protected void execute(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        if (CollectionUtils.isEmpty(shipmentUpdateEvent.getOrderItemList()))
            return;

        Action transitionAction = null;
        Long orderId = null;
        Set<Long> shipmentIds = new HashSet<>();
        List<Long> orderItemIds = shipmentUpdateEvent.getOrderItemList().stream().map(ShipmentItemUpdate::getOrderItemId).collect(Collectors.toList());
        List<OrderItemDto> orderItemDtoList = orderItemService.search("id.in:" + StringUtils.join(orderItemIds, ","));
        Map<Long, ShipmentItemUpdate> shipmentItemsMap = shipmentUpdateEvent.getOrderItemList().stream().collect(Collectors.toMap(ShipmentItemUpdate::getOrderItemId, Function.identity(), (x1, x2) ->  x1));
        for (OrderItemDto currentItemDto : orderItemDtoList) {
            ShipmentItemUpdate item = shipmentItemsMap.get(currentItemDto.getId());
            transitionAction = omsTransitionUtil.getTransitionActionByOperationAndStatus(
                    EventToOperationMap.valueOf(shipmentUpdateEvent.getShipmentEvent().name()).getOperation(), currentItemDto.getItemStatus()
            );
            orderId = currentItemDto.getOrderId();
            shipmentIds.add(currentItemDto.getShipmentId());
            if (currentItemDto != null) {
                logger.info("Updating order item status from {} to {} for orderId - {}", currentItemDto.getItemStatus(), transitionAction.getOrderStatus().getOrderStatus(), currentItemDto.getId());
                currentItemDto.setItemStatus(transitionAction.getItemStatus().getItemStatus());
                currentItemDto.setItemSubStatus(transitionAction.getItemStatus().getItemSubStatus());
                orderItemService.save(currentItemDto);
            }
            updateShipmentTimeline(item, currentItemDto);
            item.setOrderItemId(currentItemDto.getUwItemId());
        }
        String wmsOrderCode = fetchWmsOrderCodeAndUpdateShipmentData(transitionAction, shipmentIds);
        OrderDto orderDto = fetchAndUpdateOrderDataIfRequired(transitionAction, orderId);
        ShipmentDto shipmentDto = shipmentService.findBySearchTerms("id.eq:" + orderItemDtoList.get(0).getShipmentId(), new HashSet<>(Arrays.asList(ApplicationConstants.CHILD_ENTITIES.ORDER_ITEMS, ApplicationConstants.CHILD_ENTITIES.SHIPMENT_META_DATA)));
        if(OrderSubType.DISTRIBUTOR_JIT_ORDER.name().equals(shipmentDto.getShipmentSubType())) {
            logger.info("[DispatchShipmentStrategy] shipment {} is Jit DO", shipmentDto.getWmsOrderCode());
            omsCommonUtil.updateItemStatusAtInventory(orderItemDtoList, String.valueOf(transitionAction.getShipmentStatus().getShipmentStatus()));
        }
        if(!omsCommonUtil.isDistributorOrder(orderDto))
            processJunoCallBack(orderDto, shipmentUpdateEvent, BackSyncEventName.JUNO_BACKSYNC, wmsOrderCode);
        publishToD365IfEligible(orderDto, shipmentIds);
    }

    private void publishToD365IfEligible(OrderDto orderDto, Set<Long> shipmentIds) {
        List<ShipmentDto> shipmentDtoList = shipmentService.search("id.in:" + StringUtils.join(shipmentIds, ","));
        for (ShipmentDto shipmentDto : shipmentDtoList) {
            if (omsCommonUtil.isEligibleForD365(shipmentDto)) {
                d365Facade.publishDispatch(orderDto, shipmentDto);
            }
        }
    }

    private OrderDto fetchAndUpdateOrderDataIfRequired(Action transitionAction, Long orderId) {
        OrderDto orderDto = orderService.findById(orderId);
        if (isOrderStatusUpdateRequired(transitionAction, orderDto)) {
            logger.info("Updating order status from {} to {} for orderId - {}", orderDto.getOrderItems(), transitionAction.getOrderStatus().getOrderStatus(), orderDto.getId());
            orderDto.setOrderStatus(transitionAction.getOrderStatus().getOrderStatus());
            orderDto.setOrderSubStatus(transitionAction.getOrderStatus().getOrderSubStatus());
            orderService.save(orderDto);
        }
        return orderDto;
    }

    protected Boolean isOrderStatusUpdateRequired(Action transitionAction, OrderDto orderDto) {
        List<ShipmentDto> shipmentDtoList = getShipmentDtos(orderDto);

        for (ShipmentDto shipmentDto : shipmentDtoList) {
            if(omsCommonUtil.isReassignedShipment(shipmentDto)) continue;
            if (!shipmentDto.getShipmentStatus().name().equalsIgnoreCase(transitionAction.getOrderStatus().getOrderStatus().name()) && !shipmentDto.getShipmentStatus().name().equalsIgnoreCase(OrderStatus.CANCELLED.name())) {
                logger.info("[isOrderStatusUpdateRequired] shipment {} status {} is not matching with transactionAction orderStatus {}"
                        ,shipmentDto.getWmsOrderCode(), transitionAction.getOrderStatus().getOrderStatus().name());
                return false;
            }
        }
        return true;
    }

    private String fetchWmsOrderCodeAndUpdateShipmentData(Action transitionAction, Set<Long> shipmentIds) {
        String wmsOrderCode = null;
        List<ShipmentDto> shipmentDtoList = shipmentService.search("id.in:" + StringUtils.join(shipmentIds, ","));
        if (!CollectionUtils.isEmpty(shipmentDtoList)) {
            for (ShipmentDto shipmentDto : shipmentDtoList) {
                wmsOrderCode = shipmentDto.getWmsOrderCode();
                logger.info("Updating shipment status from {} to {} for ShipmentId - {}", shipmentDto.getShipmentStatus(), transitionAction.getOrderStatus().getOrderStatus(), shipmentDto.getId());
                shipmentDto.setShipmentStatus(transitionAction.getShipmentStatus().getShipmentStatus());
                shipmentDto.setShipmentSubStatus(transitionAction.getShipmentStatus().getShipmentSubStatus());
                shipmentService.save(shipmentDto);
            }
        }
        return wmsOrderCode;
    }

    private void updateShipmentTimeline(ShipmentItemUpdate item, OrderItemDto currentItemDto) {
        List<ShipmentTimelineDto> timelineDtoList = shipmentTimelineService.findAssociatedShipmentTimelines(currentItemDto);
        for (ShipmentTimelineDto timelineDto : timelineDtoList) {
            timelineDto.setDispatchTime(item.getEventTime() != null ? item.getEventTime() : new Date());
            shipmentTimelineService.save(timelineDto);
            logger.info("Updating Disptach Time - {} For OrderItemId - {} & ShipmentId - {}", timelineDto.getDispatchTime(), timelineDto.getOrderItemId(), timelineDto.getShipmentId());
        }
    }

    private void processJunoCallBack(OrderDto orderDto, ShipmentUpdateEvent shipmentUpdateEvent, BackSyncEventName eventName, String wmsOrderCode) throws ApplicationException {
        orderBackSyncTrackingService.persistRequestInTracking(eventName, wmsOrderCode, BackSyncSystem.ORDER_OPS);
        try {
            BackSyncRequest backSyncRequest = createBackSyncRequest(shipmentUpdateEvent);
            ResponseEntity<String> responseEntity = orderOpsConnector.processJunoBackSyncViaOO(orderDto.getIncrementId(), backSyncRequest);
            orderBackSyncTrackingService.persistResponseInTracking(responseEntity.getBody(), null, eventName, wmsOrderCode);
        } catch (Exception exception){
            String exceptionMessage = "processJunoCallBack For WmsOrderCode - " + wmsOrderCode + ", BackSyncEvent - " + eventName.name();
            logger.error(exceptionMessage, exception);
            throw new ApplicationException(exceptionMessage, exception);
        }
    }

    private BackSyncRequest createBackSyncRequest(ShipmentUpdateEvent shipmentUpdateEvent) {
        BackSyncRequest backSyncRequest = new BackSyncRequest();
        List<Integer> uwItemIdList = new ArrayList<>();
        for (ShipmentItemUpdate item : shipmentUpdateEvent.getOrderItemList())
            uwItemIdList.add(item.getOrderItemId().intValue());
        backSyncRequest.setCheckPoint(JUNO_CALLBACK_CHECKPOINT);
        backSyncRequest.setUwItemIdList(uwItemIdList);
        return backSyncRequest;
    }

}
