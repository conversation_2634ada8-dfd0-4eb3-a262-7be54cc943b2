package com.lenskart.oms.strategy.impl;

import com.lenskart.fds.enums.DocumentProvider;
import com.lenskart.fds.enums.DocumentSource;
import com.lenskart.fds.enums.DocumentType;
import com.lenskart.fds.request.CreateDocumentRequest;
import com.lenskart.nexs.ims.request.UpdateStocksRequestV2;
import com.lenskart.nexs.ims.response.ItemStockUpdateResponseV2;
import com.lenskart.nexs.ims.response.UpdateStocksResponseV2;
import com.lenskart.oms.connector.FdsConnector;
import com.lenskart.oms.connector.OrderOpsConnector;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.constants.KafkaConstants;
import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.dto.ShipmentTimelineDto;
import com.lenskart.oms.enums.*;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.exception.DuplicateEventException;
import com.lenskart.oms.model.Action;
import com.lenskart.oms.producer.CommonKafkaProducer;
import com.lenskart.oms.request.OmsOrderOpsLFBarcodeUpdate;
import com.lenskart.oms.request.OtcShipmentEvent;
import com.lenskart.oms.request.ShipmentItemUpdate;
import com.lenskart.oms.request.ShipmentUpdateEvent;
import com.lenskart.oms.service.OrderService;
import com.lenskart.oms.strategy.BaseOtcShipmentEventStrategy;
import com.lenskart.oms.utils.ObjectHelper;
import com.lenskart.oms.utils.OmsCommonUtil;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;

import static com.lenskart.oms.constants.KafkaConstants.MESSAGE_IDEMPOTENCY_KEY;

@Component
public class MarkOtcShipmentInvoicedEventStrategy extends BaseOtcShipmentEventStrategy {

    @Autowired
    private FdsConnector fdsConnector;

    @Autowired
    private CommonKafkaProducer commonKafkaProducer;

    @Autowired
    private OrderOpsConnector orderOpsConnector;

    @Autowired
    private OmsCommonUtil omsCommonUtil;

    @Autowired
    private OrderService orderService;

    @Value("${otc.dispatch.from.invoiced.flag}")
    private boolean dispatchFromInvoicedStateEnabled;

    @Override
    protected OtcShipmentEventType supportedOrderEvents() {
        return OtcShipmentEventType.INVOICED;
    }

    @Override
    protected void preExecute(OtcShipmentEvent otcShipmentEvent) throws ApplicationException {
        validateOtcEvent(otcShipmentEvent);
    }

    @Override
    protected void execute(OtcShipmentEvent otcShipmentEvent) throws Exception {
        ShipmentDto shipmentDto = shipmentService.findById(otcShipmentEvent.getShipmentId());
        CreateDocumentRequest createDocumentRequest = getCreateDocumentRequest(shipmentDto);
        String invoiceNo = fdsConnector.createDocument(createDocumentRequest);
        if (!StringUtils.hasLength(invoiceNo)) {
            logger.info("Invoice number not received from FDS for unicomOrder code {}", shipmentDto.getWmsOrderCode());
            throw new ApplicationException("GET INVOICE FAILED FOR OTC ORDER");
        }
        shipmentDto.setInvoiceNumber(invoiceNo);
        UpdateStocksRequestV2 stocksRequestV2 = getUpdateStocksRequestV2(shipmentDto, ApplicationConstants.IMS_OTC_INVOICED_OPERATION);
        if(!stocksRequestV2.getStockRequestV2List().isEmpty()) {
            UpdateStocksResponseV2 updateStocksResponseV2 = imsConnector.updateBarcodeStatus(stocksRequestV2);


            for (ItemStockUpdateResponseV2 responseV2 : updateStocksResponseV2.getItemStockUpdateResponseV2List()) {
                if (!responseV2.isSuccess()) {
                    logger.info("Update barcode failed for shipmentDto {} and barcode {}", shipmentDto, responseV2.getBarcode());
                    throw new ApplicationException("Update barcode failed for OTC Order");
                }
            }
        }
        Action transitionAction = omsTransitionUtil.getTransitionActionByOperationAndStatus(
                EventToOperationMap.CREATE_OTC_SHIPMENT_INVOICE.getOperation(), shipmentDto.getOrderItems().get(0).getItemStatus()
        );
        for (OrderItemDto orderItemDto : shipmentDto.getOrderItems()) {
            orderItemDto.setUwItemId(orderItemDto.getId());
            orderItemDto.setItemStatus(transitionAction.getItemStatus().getItemStatus());
            orderItemDto.setItemSubStatus(transitionAction.getItemStatus().getItemSubStatus());
            orderItemService.update(orderItemDto, orderItemDto.getId());
        }
        shipmentDto.setShipmentStatus(transitionAction.getShipmentStatus().getShipmentStatus());
        shipmentDto.setShipmentSubStatus(transitionAction.getShipmentStatus().getShipmentSubStatus());
        shipmentService.update(shipmentDto, shipmentDto.getId());

        List<ShipmentTimelineDto> shipmentTimelineDtoList = shipmentTimelineService.findByShipmentId(shipmentDto.getId());
        for (ShipmentTimelineDto shipmentTimelineDto : shipmentTimelineDtoList) {
            shipmentTimelineDto.setInvoiceTime(new Date());
            shipmentTimelineService.update(shipmentTimelineDto, shipmentTimelineDto.getId());
        }
        if (FulfillmentType.LOCAL_FITTING.equals(shipmentDto.getOrderItems().get(0).getFulfillmentType())) {
            callOrderOpsForBarcodeUpdate(shipmentDto.getOrderItems(), shipmentDto.getWmsOrderCode());
        }
    }


    private void callOrderOpsForBarcodeUpdate(List<OrderItemDto> orderItemsList, String wmsOrderCode) {
            OmsOrderOpsLFBarcodeUpdate omsOrderOpsLFBarcodeUpdate = new OmsOrderOpsLFBarcodeUpdate();
            omsOrderOpsLFBarcodeUpdate.setUnicomOrderCode(wmsOrderCode);
            for (OrderItemDto orderItems : orderItemsList) {
                if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(orderItems.getItemType().name(), ItemType.LEFT_LENS.name())) {
                    omsOrderOpsLFBarcodeUpdate.setLeftLensBarcode(orderItems.getItemBarcode());
                } else if (org.apache.commons.lang3.StringUtils.equalsIgnoreCase(orderItems.getItemType().name(), ItemType.RIGHT_LENS.name())) {
                    omsOrderOpsLFBarcodeUpdate.setRightLensBarcode(orderItems.getItemBarcode());
                } else if (ItemType.getFrameItemTypes().contains(orderItems.getItemType().name())) {
                    omsOrderOpsLFBarcodeUpdate.setFrameBarcode(orderItems.getItemBarcode());
                }
            }
            logger.info("[MarkOtcShipmentInvoicedEventStrategy][callOrderOpsForBarcodeUpdate] going to update barcode for :{}", wmsOrderCode);
            orderOpsConnector.updateOrderOpsForBarcode(Collections.singletonList(omsOrderOpsLFBarcodeUpdate));
    }


    private void updateOrderDataIfRequired(Long orderId, Action transitionAction) {
        OrderDto orderDto = orderService.findById(orderId);
        if (isOrderStatusUpdateRequired(transitionAction, orderDto)) {
            logger.info("Updating order status from {} to {} for orderId - {}", orderDto.getOrderItems(), transitionAction.getOrderStatus().getOrderStatus(), orderDto.getId());
            orderDto.setOrderStatus(OrderStatus.INVOICED);
            orderDto.setOrderSubStatus(OrderSubStatus.INVOICED);
            orderService.save(orderDto);
        }
    }

    private Boolean isOrderStatusUpdateRequired(Action transitionAction, OrderDto orderDto) {
        List<ShipmentDto> shipmentDtoList = getShipmentDtos(orderDto);
        logger.info("[MarkOtcShipmentInvoicedEventStrategy][isOrderStatusUpdateRequired] shipmentDtos :{} and transitionAction :{}",
                shipmentDtoList, transitionAction);
        for (ShipmentDto shipmentDto : shipmentDtoList) {
            logger.info("[MarkOtcShipmentInvoicedEventStrategy][isOrderStatusUpdateRequired] shipmentDto :{} ",
                    shipmentDto);
            logger.info("[MarkOtcShipmentInvoicedEventStrategy][isOrderStatusUpdateRequired] transactionAction :{}", transitionAction);
            if (!shipmentDto.getShipmentStatus().name().equalsIgnoreCase(transitionAction.getShipmentStatus().getShipmentStatus().name())) {
                return false;
            }
        }
        return true;
    }

    private List<ShipmentDto> getShipmentDtos(OrderDto orderDto) {
        List<ShipmentDto> shipmentDtoList = new ArrayList<>();
        Set<Long> shipmentIds = new HashSet<>();
        for(OrderItemDto orderItemDto : orderDto.getOrderItems()){
            if(omsCommonUtil.isNonWarehouseProcessingOrderExcludingB2b(orderItemDto) &&
                    !FulfillmentType.LOCAL_FITTING.equals(orderItemDto.getFulfillmentType())){
                continue;
            }
            shipmentIds.add(orderItemDto.getShipmentId());
        }

        for (Long shipmentId : shipmentIds) {
            shipmentDtoList.add(shipmentService.findById(shipmentId));
        }

        return shipmentDtoList;
    }

    @Override
    protected void postExecute(OtcShipmentEvent otcShipmentEvent) throws Exception {
        ShipmentDto shipmentDto = shipmentService.findById(otcShipmentEvent.getShipmentId());
        if (disableVsmCallback) {
            ShipmentUpdateEvent shipmentUpdateEvent = new ShipmentUpdateEvent();
            shipmentUpdateEvent.setShipmentEvent(ShipmentEvent.CREATE_SHIPMENT_INVOICE);
            shipmentUpdateEvent.setWmsOrderCode(shipmentDto.getWmsOrderCode());
            List<ShipmentItemUpdate> shipmentItemUpdates = new ArrayList<>();
            ShipmentItemUpdate shipmentItemUpdate = new ShipmentItemUpdate();
            shipmentItemUpdate.setUnicomShipmentStatus("PACKED");
            shipmentItemUpdate.setOrderOpsShipmentStatus("PACKED");
            shipmentItemUpdates.add(shipmentItemUpdate);
            shipmentUpdateEvent.setOrderItemList(shipmentItemUpdates);
            persistTrackingAndPublishToOms(shipmentUpdateEvent);
            Map<String, String> kafkaHeaders = new HashMap<>();
            kafkaHeaders.put(MESSAGE_IDEMPOTENCY_KEY, shipmentUpdateEvent.getShipmentEvent().name() + "_" + shipmentUpdateEvent.getWmsOrderCode());
            shipmentEventOmsProducer.sendMessage(shipmentUpdateEvent, omsShipmentBackSyncTopic, kafkaHeaders, shipmentUpdateEvent.getWmsOrderCode());
        }
        if (!FulfillmentType.LOCAL_FITTING.equals(shipmentDto.getOrderItems().get(0).getFulfillmentType()) || (dispatchFromInvoicedStateEnabled  && FulfillmentType.LOCAL_FITTING.equals(shipmentDto.getOrderItems().get(0).getFulfillmentType()))) {
            logger.info("[MarkOtcShipmentInvoicedEventStrategy] mark dispatched from invoiced state enabled, proceeding with dispatch kafka call for shipment : {}", shipmentDto.getWmsShippingPackageId());
            commonKafkaProducer.sendMessage(
                    KafkaConstants.OMS_OTC_ORDER_EVENTS_PROCESS_TOPIC,
                    String.valueOf(shipmentDto.getOrderItems().get(0).getOrderId()),
                    ObjectHelper.convertToString(new OtcShipmentEvent(OtcShipmentEventType.DISPATCHED, shipmentDto.getId())),
                    String.valueOf(shipmentDto.getId())
            );
        }
    }

    private void validateOtcEvent(OtcShipmentEvent otcShipmentEvent) {
        ShipmentDto shipmentDto = shipmentService.findById(otcShipmentEvent.getShipmentId());
        if (ShipmentStatus.INVOICED.equals(shipmentDto.getShipmentStatus())) {
            throw new DuplicateEventException("Skipping OTC Dispatched strategy execution");
        }
        if (!(shipmentDto.getShipmentStatus().equals(ShipmentStatus.CREATED) ||
                shipmentDto.getShipmentStatus().equals(ShipmentStatus.PENDING))) {
            throw new ApplicationException("Incorrect shipment state for shipment id :" + otcShipmentEvent.getShipmentId());
        }
    }

    private CreateDocumentRequest getCreateDocumentRequest(ShipmentDto shipmentDto) {
        CreateDocumentRequest createDocumentRequest = new CreateDocumentRequest();
        createDocumentRequest.setFacility(shipmentDto.getFacility());
        createDocumentRequest.setLenskartGeneratedUnicomOrderCode(shipmentDto.getWmsOrderCode());
        createDocumentRequest.setDocumentSource(DocumentSource.OTC_SALE_ORDER.name());
        createDocumentRequest.setDocumentType(DocumentType.INVOICE);
        createDocumentRequest.setDocumentProvider(DocumentProvider.FDS.name());
        createDocumentRequest.setDocumentSourceReferenceId(shipmentDto.getWmsShippingPackageId());
        return createDocumentRequest;
    }

}
