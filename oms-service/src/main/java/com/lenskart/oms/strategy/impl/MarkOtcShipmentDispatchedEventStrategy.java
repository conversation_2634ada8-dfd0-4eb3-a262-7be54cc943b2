package com.lenskart.oms.strategy.impl;

import com.lenskart.fds.dto.DocumentDetailsDto;
import com.lenskart.nexs.ims.request.UpdateStocksRequestV2;
import com.lenskart.nexs.ims.response.ItemStockUpdateResponseV2;
import com.lenskart.nexs.ims.response.UpdateStocksResponseV2;
import com.lenskart.oms.connector.FdsConnector;
import com.lenskart.oms.connector.OrderOpsConnector;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.dto.*;
import com.lenskart.oms.entity.Order;
import com.lenskart.oms.enums.*;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.exception.DuplicateEventException;
import com.lenskart.oms.exception.InvoiceDocumentNotFoundException;
import com.lenskart.oms.exception.OTCInvoicedStateException;
import com.lenskart.oms.facade.D365Facade;
import com.lenskart.oms.mapper.WmsOrderEventMapper;
import com.lenskart.oms.model.Action;
import com.lenskart.oms.model.UwOrder;
import com.lenskart.oms.producer.OrderEventWmsProducer;
import com.lenskart.oms.request.*;
import com.lenskart.oms.response.NonWareHouseOrderShipmentUpdateResponse;
import com.lenskart.oms.service.OrderService;
import com.lenskart.oms.strategy.BaseOtcShipmentEventStrategy;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.neo4j.cypher.internal.compiler.v2_1.ast.In;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.lenskart.oms.constants.ApplicationConstants.*;
import static com.lenskart.oms.constants.KafkaConstants.MESSAGE_IDEMPOTENCY_KEY;

@Component
@Setter(onMethod__ = {@Autowired})
public class MarkOtcShipmentDispatchedEventStrategy extends BaseOtcShipmentEventStrategy {

    @Value("${wm.markOrderComplete.otc.enabled:false}")
    @Setter(AccessLevel.NONE)
    private boolean markOrderCompleteEnabled;

    private OrderService orderService;
    private OrderEventWmsProducer orderEventWmsProducer;
    private WmsOrderEventMapper wmsOrderEventMapper;
    private OrderOpsConnector orderOpsConnector;
    private FdsConnector fdsConnector;
    private D365Facade d365Facade;


    private static ShipmentUpdateEvent getShipmentUpdateEvent(ShipmentDto shipmentDto) {
        ShipmentUpdateEvent shipmentUpdateEvent = new ShipmentUpdateEvent();
        shipmentUpdateEvent.setShipmentEvent(ShipmentEvent.MARK_SHIPMENT_DISPATCH);
        shipmentUpdateEvent.setWmsOrderCode(shipmentDto.getWmsOrderCode());
        List<ShipmentItemUpdate> shipmentItemUpdates = new ArrayList<>();
        ShipmentItemUpdate shipmentItemUpdate = new ShipmentItemUpdate();
        shipmentItemUpdate.setUnicomShipmentStatus("DISPATCHED");
        shipmentItemUpdate.setOrderOpsShipmentStatus("COMPLETE");
        shipmentItemUpdates.add(shipmentItemUpdate);

        shipmentUpdateEvent.setOrderItemList(shipmentItemUpdates);
        return shipmentUpdateEvent;
    }

    @Override
    protected OtcShipmentEventType supportedOrderEvents() {
        return OtcShipmentEventType.DISPATCHED;
    }

    @Override
    protected void preExecute(OtcShipmentEvent otcShipmentEvent) throws ApplicationException {
        validateOtcEvent(otcShipmentEvent);
    }

    @Override
    protected void execute(OtcShipmentEvent otcShipmentEvent) throws Exception {
        ShipmentDto shipmentDto = shipmentService.findById(otcShipmentEvent.getShipmentId());
        UpdateStocksRequestV2 stocksRequestV2 = getUpdateStocksRequestV2(shipmentDto, ApplicationConstants.IMS_OTC_DISPATCHED_OPERATION);

        if(!stocksRequestV2.getStockRequestV2List().isEmpty()) {
            UpdateStocksResponseV2 updateStocksResponseV2 = imsConnector.updateBarcodeStatus(stocksRequestV2);
            for (ItemStockUpdateResponseV2 responseV2 : updateStocksResponseV2.getItemStockUpdateResponseV2List()) {
                if (!responseV2.isSuccess()) {
                    logger.info("Update barcode failed for shipmentDto {} and barcode {}", shipmentDto, responseV2.getBarcode());
                    throw new OTCInvoicedStateException("Update barcode failed");
                }
            }
        }

        Action transitionAction = omsTransitionUtil.getTransitionActionByOperationAndStatus(
                EventToOperationMap.MARK_OTC_SHIPMENT_DISPATCH.getOperation(), shipmentDto.getOrderItems().get(0).getItemStatus()
        );

        for (OrderItemDto orderItemDto : shipmentDto.getOrderItems()) {
            orderItemDto.setItemStatus(transitionAction.getItemStatus().getItemStatus());
            orderItemDto.setItemSubStatus(transitionAction.getItemStatus().getItemSubStatus());
            if(isCentralFacilityCodePresent(orderItemDto)){
                orderItemDto.setItemBarcode("VOTC" + orderItemDto.getId());
            }
            if(StringUtils.isEmpty(orderItemDto.getItemBarcode()) && isValidNullBarcodeLoyaltyAndAccessoryItem(orderItemDto)){
                orderItemDto.setItemBarcode("DLP" + orderItemDto.getId());
            }
            if(StringUtils.isEmpty(orderItemDto.getItemBarcode()) && isStoreFittingLensOnlyItems(shipmentDto, orderItemDto)){
                orderItemDto.setItemBarcode("DLO" + orderItemDto.getId());
            }
            orderItemService.update(orderItemDto, orderItemDto.getId());
        }
        shipmentDto.setShipmentStatus(transitionAction.getShipmentStatus().getShipmentStatus());
        shipmentDto.setShipmentSubStatus(transitionAction.getShipmentStatus().getShipmentSubStatus());
        shipmentService.update(shipmentDto, shipmentDto.getId());
        if(isCentralFacilityCodePresent(shipmentDto.getOrderItems().get(0))) {
            logger.info("[MarkOtcShipmentDispatchedEventStrategy] shipment {} contains centrtal Facility code", shipmentDto.getWmsOrderCode());
            UpdateStocksRequestV2 stocksRequestV2Loyalty = getUpdateStocksRequestV2OTCLoyalty(shipmentDto, ApplicationConstants.IMS_OTC_LOYALTY_DISPATCHED_BARCODE_CREATION);
            logger.info("[MarkOtcShipmentDispatchedEventStrategy] shipment {} ims request size {} and condition {}", shipmentDto.getWmsOrderCode(), stocksRequestV2Loyalty.getStockRequestV2List().size(),stocksRequestV2.getStockRequestV2List().isEmpty());
            if(!stocksRequestV2Loyalty.getStockRequestV2List().isEmpty()) {
                UpdateStocksResponseV2 updateStocksResponseV2Loyalty = imsConnector.updateBarcodeStatus(stocksRequestV2Loyalty);
                for (ItemStockUpdateResponseV2 responseV2 : updateStocksResponseV2Loyalty.getItemStockUpdateResponseV2List()) {
                    if (!responseV2.isSuccess()) {
                        logger.info("Update barcode failed for shipmentDto {} and barcode {}", shipmentDto, responseV2.getBarcode());
                        throw new OTCInvoicedStateException("Barcode creation failed for OTC Loyalty");
                    }
                }
            }
        }

        List<ShipmentTimelineDto> shipmentTimelineDtoList = shipmentTimelineService.findByShipmentId(shipmentDto.getId());
        for (ShipmentTimelineDto shipmentTimelineDto : shipmentTimelineDtoList) {
            shipmentTimelineDto.setDispatchTime(new Date());
            shipmentTimelineService.update(shipmentTimelineDto, shipmentTimelineDto.getId());
        }
        Long orderId = shipmentDto.getOrderItems().get(0).getOrderId();
        OrderDto orderDto = orderService.findById(orderId);
        if (isOrderStatusUpdateRequired(transitionAction, orderDto)) {
            logger.info("[InvoiceState][OTC Order] orderId contains single shipment marking dispatched for order {}", orderDto);
            orderDto.setOrderStatus(transitionAction.getOrderStatus().getOrderStatus());
            orderDto.setOrderSubStatus(transitionAction.getOrderStatus().getOrderSubStatus());
            orderService.update(orderDto, orderDto.getId());
        }

    }

    @Override
    protected void postExecute(OtcShipmentEvent otcShipmentEvent) throws Exception {
        ShipmentDto shipmentDto = shipmentService.findById(otcShipmentEvent.getShipmentId());
        if(disableVsmCallback) {
            ShipmentUpdateEvent shipmentUpdateEvent = getShipmentUpdateEvent(shipmentDto);
            persistTrackingAndPublishToOms(shipmentUpdateEvent);
            Map<String, String> kafkaHeaders = new HashMap<>();
            kafkaHeaders.put(MESSAGE_IDEMPOTENCY_KEY, shipmentUpdateEvent.getShipmentEvent().name() + "_" + shipmentUpdateEvent.getWmsOrderCode());
            shipmentEventOmsProducer.sendMessage(shipmentUpdateEvent, omsShipmentBackSyncTopic, kafkaHeaders, shipmentUpdateEvent.getWmsOrderCode());
        }
        shipmentDto = updateShipmentLevelStatusAtOrderOps(shipmentDto);
        OrderDto orderDto = orderService.findById(shipmentDto.getOrderItems().get(0).getOrderId());
        WmsOrderEvent wmsOrderEvent = wmsOrderEventMapper.getWmsOrderEventFromShipmentDto(shipmentDto, orderDto, ShipmentEvent.CREATE_ORDER);
        logger.info("[Invoiced State OTC] sending wms order event {} for shipmentId {}", wmsOrderEvent, shipmentDto.getId());
        orderEventWmsProducer.sendMessage(wmsOrderEvent);
        if (markOrderCompleteEnabled &&  !SHIPPING_DESTINATION_CUSTOMER.equals(shipmentDto.getOrderItems().get(0).getShippingDestinationType())) {
            logger.info("proceeding with mark order complete on WM for {} and shipping destination type :{}", shipmentDto.getWmsShippingPackageId(), shipmentDto.getOrderItems().get(0).getShippingDestinationType());
            MarkOrderCompleteRequest markOrderCompleteRequest = getMarkOrderCompleteRequest(shipmentDto, orderDto);
            wmConnector.markOrderComplete(markOrderCompleteRequest, "OTCAWB" + shipmentDto.getId());
        }
        if(omsCommonUtil.isEligibleForD365(shipmentDto))
            d365Facade.publishDispatch(orderDto, shipmentDto);
    }

    void validateOtcEvent(OtcShipmentEvent otcShipmentEvent) {
        ShipmentDto shipmentDto = shipmentService.findById(otcShipmentEvent.getShipmentId());
        if (shipmentDto.getShipmentStatus().equals(ShipmentStatus.DISPATCHED)) {
            logger.info("Skipping OTC Dispatched strategy execution for shipment id {}", otcShipmentEvent.getShipmentId());
            throw new DuplicateEventException("Skipping OTC Dispatched strategy execution");
        }
        if (!shipmentDto.getShipmentStatus().equals(ShipmentStatus.INVOICED)) {
            throw new ApplicationException("Incorrect shipment state");
        }

    }

    protected Boolean isOrderStatusUpdateRequired(Action transitionAction, OrderDto orderDto) {
        List<ShipmentDto> shipmentDtoList = getShipmentDtos(orderDto);
        for (ShipmentDto shipmentDto : shipmentDtoList) {
            if(omsCommonUtil.isReassignedShipment(shipmentDto)) continue;
            if (!shipmentDto.getShipmentStatus().name().equalsIgnoreCase(transitionAction.getOrderStatus().getOrderStatus().name())) {
                logger.info("[isOrderStatusUpdateRequired] shipment {} status {} is not matching with transactionAction orderStatus {}"
                        ,shipmentDto.getWmsOrderCode(), transitionAction.getOrderStatus().getOrderStatus().name());
                return false;
            }
        }
        return true;
    }

    protected List<ShipmentDto> getShipmentDtos(OrderDto orderDto) {
        List<ShipmentDto> shipmentDtoList = new ArrayList<>();
        Set<Long> shipmentIds = orderDto.getOrderItems().stream()
                .map(OrderItemDto::getShipmentId)
                .collect(Collectors.toSet());

        for (Long shipmentId : shipmentIds) {
            shipmentDtoList.add(shipmentService.findById(shipmentId));
        }
        return shipmentDtoList;
    }

    private ShipmentDto updateShipmentLevelStatusAtOrderOps(ShipmentDto shipmentDto) {
        String fdsInvoiceUrl = getFdsInvoiceUrl(shipmentDto);
        if (fdsInvoiceUrl == null) {
            logger.error("[MarkOtcShipmentDispatchedEventStrategy][updateShipmentLevelStatusAtOrderOps] FDS Document Link is NULL for {}", shipmentDto.getWmsShippingPackageId());
            throw new InvoiceDocumentNotFoundException("FDS Document Link is NULL for " + shipmentDto.getWmsShippingPackageId());
        }
        NonWareHouseOrderShipmentUpdateRequest request = NonWareHouseOrderShipmentUpdateRequest.builder()
                .shipmentStatus(OrderStatus.COMPLETE_SHIPPED.name().toLowerCase())
                .shipmentState(OrderStatus.COMPLETE.name().toLowerCase())
                .shippingPackageId(shipmentDto.getWmsShippingPackageId())
                .facilityCode(shipmentDto.getFacility())
                .wmsOrderCode(shipmentDto.getWmsOrderCode())
                .unicomShipmentStatus(ShipmentStatus.DISPATCHED.name())
                .unicomSyncStatus("Yes")
                .invoiceUrl(fdsInvoiceUrl)
                .build();
        logger.info("[MarkDispatchedVirtualShipmentStrategy][updateShipmentLevelStatusAtOrderOps] NonWareHouseOrderShipmentUpdateRequest {}", request);
        NonWareHouseOrderShipmentUpdateResponse response = orderOpsConnector.updateShipmentLevelStatusForNonWarehouseOrder(request);
        if(!response.isSuccess()){
            logger.info("[MarkDispatchedVirtualShipmentStrategy][updateShipmentLevelStatusAtOrderOps] response {}", response);
            throw new ApplicationException("Update shipment status failed at Order ops for wmsOrdercode" + shipmentDto.getWmsOrderCode());
        }
        shipmentDto = updateUwItemId(response.getUwOrders(),shipmentDto);
        Optional<OrderItemMetaDataDto> optionalOrderItemMetaDataDto =  shipmentDto.getOrderItems().get(0).getOrderItemMetaData().stream()
                .filter(oi -> OREDER_ITEM_META_KEY_CENTRAL_FACILITY_CODE.equals(oi.getEntityKey()))
                .findFirst();

        logger.info("[MarkDispatchedVirtualShipmentStrategy][updateShipmentLevelStatusAtOrderOps] optionalOrderItemMetaDataDto isPresent {} for wmsOrderCode {}", optionalOrderItemMetaDataDto.isPresent(), shipmentDto.getWmsOrderCode());
        if(optionalOrderItemMetaDataDto.isPresent() && org.springframework.util.StringUtils.hasLength(optionalOrderItemMetaDataDto.get().getEntityValue())) {
            logger.info("[MarkDispatchedVirtualShipmentStrategy][updateShipmentLevelStatusAtOrderOps] again hitting order-ops to update loyalty barcode for shipment {}", shipmentDto.getWmsOrderCode());
            List<NonWareHouseOrderItem> items = shipmentDto.getOrderItems().stream()
                    .map(item -> new NonWareHouseOrderItem(item.getUwItemId(), item.getItemBarcode()))
                    .collect(Collectors.toList());
            NonWareHouseOrderShipmentUpdateRequest requestV2 = NonWareHouseOrderShipmentUpdateRequest.builder()
                    .shipmentStatus(OrderStatus.COMPLETE_SHIPPED.name().toLowerCase())
                    .shipmentState(OrderStatus.COMPLETE.name().toLowerCase())
                    .shippingPackageId(shipmentDto.getWmsShippingPackageId())
                    .facilityCode(shipmentDto.getFacility())
                    .wmsOrderCode(shipmentDto.getWmsOrderCode())
                    .unicomShipmentStatus(ShipmentStatus.DISPATCHED.name())
                    .unicomSyncStatus("Yes")
                    .items(items)
                    .build();

            NonWareHouseOrderShipmentUpdateResponse responseV2 = orderOpsConnector.updateShipmentLevelStatusForNonWarehouseOrder(requestV2);
            if(!response.isSuccess()){
                logger.info("[MarkDispatchedVirtualShipmentStrategy][updateShipmentLevelStatusAtOrderOps] response {}", response);
                throw new ApplicationException("Update shipment status failed at Order ops for wmsOrdercode" + shipmentDto.getWmsOrderCode());
            }

        }
        return shipmentDto;
    }

    private String getFdsInvoiceUrl(ShipmentDto shipmentDto) {
        List<DocumentDetailsDto> documentDetailsDtoList = fdsConnector.fetchInvoices(Collections.singletonList(shipmentDto.getWmsShippingPackageId()));
        logger.info("[getFdsInvoiceUrl] documentDetailsDtoList: {}", documentDetailsDtoList);
        String fdsInvoiceUrl = null;
        if (!CollectionUtils.isEmpty(documentDetailsDtoList) && !Objects.isNull(documentDetailsDtoList.get(0).getDocumentLink())) {
            logger.info("[getFdsInvoiceUrl] documentDetailsDtoList {} and documentLink {}", documentDetailsDtoList, documentDetailsDtoList.get(0).getDocumentLink());
            fdsInvoiceUrl = documentDetailsDtoList.get(0).getDocumentLink();
        }
        logger.info("[getFdsInvoiceUrl] fdsInvoiceUrl {}", fdsInvoiceUrl);
        return fdsInvoiceUrl;
    }


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    private ShipmentDto updateUwItemId(List<UwOrder> uwOrderList,ShipmentDto shipmentDto) throws ApplicationException{
        List<OrderItemDto> orderItemDtoList = shipmentDto.getOrderItems();
        Map<String, Long> uwBarcodeMap = new HashMap<>();
        Map<Long, Long> uwOrderMagentoIdMap = new HashMap<>();
        Long fitting_id = 0L;
        for(UwOrder uwOrder : uwOrderList) {
            if(!uwOrderMagentoIdMap.containsKey(uwOrder.getMagentoItemId())){
                uwOrderMagentoIdMap.put(uwOrder.getMagentoItemId(), Long.valueOf(uwOrder.getUwItemId()));
            }
        }
        for(UwOrder uwOrder : uwOrderList){
           if(StringUtils.isNotBlank(uwOrder.getBarcode()) && !uwBarcodeMap.containsKey(uwOrder.getBarcode())){
               uwBarcodeMap.put(uwOrder.getBarcode(),Long.valueOf(uwOrder.getUwItemId()));
           }
        }
        for(OrderItemDto orderItemDto : orderItemDtoList){
            if (isValidNullBarcodeLoyaltyAndAccessoryItem(orderItemDto) ||  isCentralFacilityCodePresent(orderItemDto) || isStoreFittingLensOnlyItems(shipmentDto, orderItemDto)){
                orderItemDto.setUwItemId(uwOrderMagentoIdMap.get(orderItemDto.getMagentoItemId()));
            } else if(StringUtils.isNotBlank(orderItemDto.getItemBarcode()) && uwBarcodeMap.containsKey(orderItemDto.getItemBarcode())){
                orderItemDto.setUwItemId(uwBarcodeMap.get(orderItemDto.getItemBarcode()));
            }else if(StringUtils.isNotBlank(orderItemDto.getItemBarcode()) && !uwBarcodeMap.containsKey(orderItemDto.getItemBarcode())){
                throw new ApplicationException("barcode not found in oms");
            }
            if(ItemType.getFrameItemTypes().contains(orderItemDto.getItemType().name())) {
                fitting_id = orderItemDto.getUwItemId();
            }
        }
        if(FulfillmentType.LOCAL_FITTING.equals(orderItemDtoList.get(0).getFulfillmentType())) {
            for (OrderItemDto orderItemDto : orderItemDtoList) {
                orderItemDto.setFittingId(fitting_id);
            }
        }
        shipmentService.save(shipmentDto);
        return shipmentDto;
    }
}
