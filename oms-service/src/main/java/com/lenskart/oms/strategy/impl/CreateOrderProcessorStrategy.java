package com.lenskart.oms.strategy.impl;

import com.lenskart.oms.connector.FmsConnector;
import com.lenskart.oms.connector.NexsImsConnector;
import com.lenskart.oms.connector.OptimaConnector;
import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.OrderItemMetaDataDto;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.enums.*;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.exception.DuplicateEventException;
import com.lenskart.oms.exception.OptimaFailureException;
import com.lenskart.oms.facade.OptimaFacade;
import com.lenskart.oms.facade.OrderBackSyncFacade;
import com.lenskart.oms.facade.ReassignOmsFacade;
import com.lenskart.oms.mapper.WmsOrderEventMapper;
import com.lenskart.oms.model.Action;
import com.lenskart.oms.request.OmsOrderEvent;
import com.lenskart.oms.request.WarehouseAssignmentResponse;
import com.lenskart.oms.request.WmsOrderEvent;
import com.lenskart.oms.strategy.BaseOrderEventStrategy;
import com.lenskart.oms.utils.ObjectHelper;
import com.lenskart.optima.enums.ShipmentType;
import com.lenskart.optima.request.AssignShipmentFullfillerRequest;
import com.lenskart.optima.request.ShipmentItem;
import com.lenskart.optima.response.AssignShipmentFullfillerResponse;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Metrics;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.lenskart.oms.constants.ApplicationConstants.UW_ITEM_ID_UPDATE_EVENT_TYPE_CREATE_ORDER;
import static com.lenskart.oms.enums.BackSyncEventName.FACILITY_CODE_UPDATE;

@Component
@Setter(onMethod__ = {@Autowired})
public class CreateOrderProcessorStrategy extends BaseOrderEventStrategy {

    private static final Counter RELEASE_INV_ERROR_COUNTER = Metrics.counter("release.inv", "result", "failure");

    @Value("#{'${nexs.order.criteria.facilities}'.split(',')}")
    @Setter(AccessLevel.NONE)
    private Set<String> nexsOrderCriteriaFacilities;
    @Value("${nexs.facility.new.facility.unicommordercode.regex}")
    @Setter(AccessLevel.NONE)
    private String unicommonOrderCodeRegex;
    @Value("${nexs.facility.optima.facility.unicommordercode.regex}")
    @Setter(AccessLevel.NONE)
    private String optimaUnicommonOrderCodeRegex;
    @Setter(AccessLevel.NONE)
    @Value("${enable.optima.facility.selection}")
    private boolean isOptimaFacilitySelectionEnabled;
    @Setter(AccessLevel.NONE)
    @Value("#{'${enable.optima.fr0.itemType}'.split(',')}")
    private Set<String> optimaEnableFr0ItemTypes;
    @Setter(AccessLevel.NONE)
    @Value("#{'${countries.eligible.for.optima}'.split(',')}")
    private List<String> countriesEligibleForOptima;
    @Setter(AccessLevel.NONE)
    private Pattern optimaEligiblityPattern;
    @Setter(AccessLevel.NONE)
    @Value("${already.blocked.facility.use.enabled:true}")
    private boolean alreadyBlockedFacilityUseEnabled;
    @Setter(AccessLevel.NONE)
    @Value("${item.type.eligible.for.split}")
    private List<String> itemTypeEligibleForSplit;

    private OptimaConnector optimaConnector;
    private OptimaFacade optimaFacade;
    private NexsImsConnector nexsImsConnector;
    private ReassignOmsFacade reassignOmsFacade;
    private OrderBackSyncFacade orderBackSyncFacade;
    private WmsOrderEventMapper wmsOrderEventMapper;
    private FmsConnector fmsConnector;

    @PostConstruct
    private void init() {
        optimaEligiblityPattern = Pattern.compile(optimaUnicommonOrderCodeRegex);
    }

    private StringRedisTemplate stringRedisTemplate;

    @Override
    protected OrderEventType supportedOrderEvents() {
        return OrderEventType.POST_CREATE_ORDER;
    }

    @Override
    protected void preExecute(OmsOrderEvent omsOrderEvent) throws ApplicationException {
        OrderDto persistedOrderDto = getOrderDto(omsOrderEvent.getOrderDto().getIncrementId());

        List<ShipmentDto> persistedShipmentDtoList = getShipmentDtos(persistedOrderDto);
        omsOrderEvent.setOrderDto(persistedOrderDto);
        omsOrderEvent.setShipmentDtoList(persistedShipmentDtoList);
        Map<Long, String> shipmentTypeMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(persistedShipmentDtoList)) {
            shipmentTypeMap = persistedShipmentDtoList.stream().filter(s -> !ShipmentStatus.OMS_REASSIGNED.equals(s.getShipmentStatus())).collect(Collectors.toMap(ShipmentDto::getId, ShipmentDto::getShipmentType));
        }
        for (ShipmentDto persistedShipmentDto : persistedShipmentDtoList) {
            if(persistedShipmentDto.getOrderItems().get(0).getB2bReferenceItemId() != null){
                logger.info("[CreateOrderProcessorStrategy][preExecute] b2b order {} skipping processsing", persistedShipmentDto.getWmsOrderCode());
                continue;
            }
            if (Boolean.TRUE.equals(isShipmentAlreadyProcessed(persistedShipmentDto))) {
                throw new DuplicateEventException("Order " + persistedOrderDto.getIncrementId() + " is already processed.", null);
            }
            if (!omsCommonUtil.isDistributorOrder(persistedOrderDto)) {
                omsCommonUtil.fetchOrderOpsUwItemIdAndUpdateInOms(omsOrderEvent.getOrderDto().getIncrementId(), omsOrderEvent.getOrderDto().getOrderItems(), UW_ITEM_ID_UPDATE_EVENT_TYPE_CREATE_ORDER, shipmentTypeMap);
            }
        }
    }

    @Override
    protected void execute(OmsOrderEvent omsOrderEvent) throws Exception {
        List<ShipmentDto> updateShipmentDtoList = new ArrayList<>();
        for (ShipmentDto shipmentDto : omsOrderEvent.getShipmentDtoList()) {
            ShipmentDto persistedShipmentDto = shipmentService.findById(shipmentDto.getId());
            WarehouseAssignmentResponse warehouseAssignmentResponse;
            boolean containsContactLens = persistedShipmentDto.getOrderItems().stream().allMatch(orderItemDto -> itemTypeEligibleForSplit.contains(orderItemDto.getItemType().name()));
            if(containsContactLens && !omsCommonUtil.isDistributorOrder(omsOrderEvent.getOrderDto())){
                logger.info("[CreateOrderProcessorStrategy][execute] containsContactLens reassiging to Order-adaptor order {}", shipmentDto.getWmsOrderCode());
                warehouseAssignmentResponse = new WarehouseAssignmentResponse();
                warehouseAssignmentResponse.setAssignedFacility(null);
                warehouseAssignmentResponse.setWarehouseAssigned(false);
                warehouseAssignmentResponse.setAssignmentFailedReason(OrderSubStatus.CL_ORDER.name());
                reassignOmsFacade.reassignOrderOms(shipmentDto.getWmsOrderCode(), warehouseAssignmentResponse.getAssignmentFailedReason(), omsOrderEvent);
                continue;
            }
            if(persistedShipmentDto.getOrderItems().get(0).getB2bReferenceItemId() != null){
                logger.info("[CreateOrderProcessorStrategy][execute] b2b order {} skipping processsing", shipmentDto.getWmsOrderCode());
                continue;
            }
            if (persistedShipmentDto.getShipmentStatus().ordinal() >= ShipmentStatus.PROCESSING.ordinal()) {
                logger.info("[CreateOrderProcessorStrategy] Shipment {} is already sync to WMS.", shipmentDto.getId());
                return;
            }
            if (alreadyBlockedFacilityUseEnabled && ShipmentSubStatus.FACILITY_ASSIGNED.equals(shipmentDto.getShipmentSubStatus())
                    && !StringUtils.isEmpty(shipmentDto.getFacility())) {
                logger.info("[CreateOrderProcessorStrategy] using facility already assigned to shipment {}", shipmentDto.getId());
                warehouseAssignmentResponse = new WarehouseAssignmentResponse();
                warehouseAssignmentResponse.setAssignedFacility(shipmentDto.getFacility());
                warehouseAssignmentResponse.setWarehouseAssigned(true);
            } else {
                warehouseAssignmentResponse = assignWarehouseAndBlockInventoryUsingOptima(persistedShipmentDto, omsOrderEvent);
                logger.info("warehouseAssignmentResponse from optima: {}", warehouseAssignmentResponse);
                if (warehouseAssignmentResponse.isUseDefaultOptimaLogic()) {
                    logger.info("defaulting to old behaviour for incrementId: {} and shipment {} ", omsOrderEvent.getIncrementId(), shipmentDto.getId());
                    if (!shipmentDto.getShippingAddress().getPostcode().matches("^-?\\d+$")) {
                        logger.info("[assignWarehouseAndBlockInventoryUsingOptima] post code is not numeric warehouse can't be finalzed {}", shipmentDto.getWmsOrderCode());
                        warehouseAssignmentResponse.setWarehouseAssigned(false);
                        warehouseAssignmentResponse.setAssignmentFailedReason(OrderSubStatus.SHIP_COUNTRY_CRITERIA_FAILED.name());
                    } else {
                        warehouseAssignmentResponse = optimaFacade.assignWarehouseAndBlockInventory(persistedShipmentDto, omsOrderEvent.getOrderDto());
                        logger.info("warehouseAssignmentResponse from optimaFacade: {}", warehouseAssignmentResponse);
                    }
                }
            }
            logger.info("final warehouseAssignmentResponse: {}", warehouseAssignmentResponse);
            persistedShipmentDto = shipmentService.findById(shipmentDto.getId());
            if (warehouseAssignmentResponse.isWarehouseAssigned()) {
                persistedShipmentDto.setFacility(warehouseAssignmentResponse.getAssignedFacility());
                updateShipmentDtoList.add(shipmentService.save(persistedShipmentDto));
                shipmentService.updateAssignedFacility(persistedShipmentDto.getId(), warehouseAssignmentResponse.getAssignedFacility());
            } else {
                if(omsCommonUtil.isDistributorOrder(omsOrderEvent.getOrderDto())){
                    logger.info("[CreateOrderProcessorStrategy] skipping oms reassignment for DO  {}", omsOrderEvent.getIncrementId());
                    return;
                }
                reassignOmsFacade.reassignOrderOms(shipmentDto.getWmsOrderCode(), warehouseAssignmentResponse.getAssignmentFailedReason(), omsOrderEvent);
                updateShipmentDtoList.add(persistedShipmentDto);
            }
        }
        omsOrderEvent.setShipmentDtoList(updateShipmentDtoList);
    }

    @Override
    protected void postExecute(OmsOrderEvent omsOrderEvent) throws ApplicationException {
        Set<Long> shipmentIds = omsOrderEvent.getShipmentDtoList()
                .stream()
                .map(ShipmentDto::getId)
                .collect(Collectors.toSet());
        omsOrderEvent.setOrderDto(orderService.findById(omsOrderEvent.getOrderDto().getId()));
        omsOrderEvent.setShipmentDtoList(shipmentIds.isEmpty() ? new ArrayList<>() : shipmentService.search("id.in:" + StringUtils.join( shipmentIds, ",")));

        logger.info("[CreateOrderProcessorStrategy][postExecute] updated omsOrder shipments {}", omsOrderEvent.getShipmentDtoList().stream().map(ShipmentDto::getWmsOrderCode).collect(Collectors.toList()));

        for (ShipmentDto shipmentDto : omsOrderEvent.getShipmentDtoList()) {
            if(omsCommonUtil.isB2bShipment(shipmentDto)){
                logger.info("[CreateOrderProcessorStrategy][postExecute] b2b order skipping processsing");
                continue;
            }
            if (ShipmentStatus.OMS_REASSIGNED.equals(shipmentDto.getShipmentStatus())) {
                logger.info("[CreateOrderProcessorStrategy] shipment {} is already reassigned to orderOps", shipmentDto.getId());
            }
        }
        omsOrderEvent.setShipmentDtoList(omsOrderEvent.getShipmentDtoList().stream()
                .filter(s -> !s.getShipmentStatus().equals(ShipmentStatus.OMS_REASSIGNED)).collect(Collectors.toList()));

        /** Update facility-code in order-ops */
        if(!omsCommonUtil.isDistributorOrder(omsOrderEvent.getOrderDto())) {
            orderBackSyncFacade.createBackSyncEntryAndUpdateOrderOpsSync(omsOrderEvent, FACILITY_CODE_UPDATE);
        }
        for (ShipmentDto shipmentDto : omsOrderEvent.getShipmentDtoList()) {
            if(omsCommonUtil.isNonWarehouseProcessingOrder(shipmentDto.getOrderItems().get(0))){
                continue;
            }
            OrderDto orderDto = new OrderDto();
            BeanUtils.copyProperties(omsOrderEvent.getOrderDto(), orderDto);

            syncShipmentToWms(shipmentDto, orderDto);
            updateOrderLimitCounter(shipmentDto.getFacility(),false);
        }
        updateOrderAndShipmentStatus(omsOrderEvent.getOrderDto());
    }
    private void updateOrderLimitCounter(String facilityCode, boolean fr0Order){
        String orderCountKey = getRedisKeyForOrderCount(fr0Order, facilityCode);
        long howMany = ObjectHelper.getMiliSecToMidNight();
        long totalOrder = 0;
        if (stringRedisTemplate.hasKey(orderCountKey)) {
            totalOrder = Long.parseLong(stringRedisTemplate.opsForValue().get(orderCountKey).toString());
            logger.info("[updateOrderLimitCounter] Total Order count in redis for key {} and count is {} and facility code is {}",orderCountKey, totalOrder, facilityCode);
        } else {
            logger.info("[updateOrderLimitCounter] Daily Order Count Redis key is not available for facility code {} and key {}", facilityCode,orderCountKey);
        }
        stringRedisTemplate.opsForValue().set(orderCountKey, String.valueOf(totalOrder+1), howMany, TimeUnit.MILLISECONDS);
        logger.info("[updateOrderLimitCounter] Total Updated Order count in redis for key {} and count is {} and facility code is {}",orderCountKey, totalOrder, facilityCode);
    }
    private String getRedisKeyForOrderCount(boolean fr0Order, String facility) {
        String stringDateFormatter = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        if (fr0Order) {
            return facility + "_fr0_total_order_" + stringDateFormatter;
        } else {
            return facility + "_fr1_fr2_total_order_" + stringDateFormatter;
        }
    }

    private Boolean isShipmentAlreadyProcessed(ShipmentDto shipmentDto) {
        if (!ShipmentStatus.CREATED.equals(shipmentDto.getShipmentStatus())) {
            logger.info("shipment {} status {} is not CREATED. Shipment is already processed", shipmentDto.getId(), shipmentDto.getShipmentStatus());
            return true;
        }
        return false;
    }

    private void syncShipmentToWms(ShipmentDto shipmentDto, OrderDto orderDto) throws ApplicationException {
        WmsOrderEvent wmsOrderEvent = wmsOrderEventMapper.getWmsOrderEventFromShipmentDto(shipmentDto, orderDto, ShipmentEvent.CREATE_ORDER);
        orderEventWmsProducer.sendMessage(wmsOrderEvent);
    }

    private void updateOrderAndShipmentStatus(OrderDto orderDto) throws ApplicationException {
        List<ShipmentDto> persistedShipmentDtoList = getShipmentDtosForReassignmentCheck(orderDto);
        logger.info("[updateOrderAndShipmentStatus] persistedShipmentDtoList {}", persistedShipmentDtoList.get(0).getWmsOrderCode());
        Optional<ShipmentDto> ifAnyNotReassignedShipment =  persistedShipmentDtoList.stream().filter(s -> ShipmentStatus.OMS_REASSIGNED != s.getShipmentStatus()).findFirst();

        logger.info("[updateOrderAndShipmentStatus] ifAnyNotReassignedShipment {}", ifAnyNotReassignedShipment.isPresent() ? ifAnyNotReassignedShipment.get() : "not present");

        if(!ifAnyNotReassignedShipment.isPresent()){
            logger.info("[updateOrderAndShipmentStatus] all shipments reassigned to Order ops {}", orderDto.getIncrementId());
            orderDto.setOrderStatus(OrderStatus.OMS_REASSIGNED);
            orderDto.setOrderSubStatus(OrderSubStatus.OMS_REASSIGNED);
            orderService.updateStatus(orderDto.getId(), OrderStatus.OMS_REASSIGNED,
                    OrderSubStatus.OMS_REASSIGNED);
            return;
        }
        Action transitionAction = omsTransitionUtil.getTransitionActionByOperationAndStatus(
                EventToOperationMap.READY_FOR_WH.getOperation(), ifAnyNotReassignedShipment.get().getOrderItems().get(0).getItemStatus()
        );
        for (ShipmentDto shipmentDto : persistedShipmentDtoList) {
            if(omsCommonUtil.isNonWarehouseProcessingOrder(shipmentDto.getOrderItems().get(0)) 
               || shipmentDto.getShipmentStatus().equals(ShipmentStatus.OMS_REASSIGNED)){
                logger.info("[CreateOrderProcessorStrategy][postExecute] lf order skipping processsing wmsOrdercode {}", shipmentDto.getWmsOrderCode());
                continue;
            }

            for (OrderItemDto orderItemDto : shipmentDto.getOrderItems()) {
                orderItemDto.setItemStatus(transitionAction.getItemStatus().getItemStatus());
                orderItemDto.setItemSubStatus(transitionAction.getItemStatus().getItemSubStatus());
            }
            shipmentDto.setShipmentStatus(transitionAction.getShipmentStatus().getShipmentStatus());
            shipmentDto.setShipmentSubStatus(transitionAction.getShipmentStatus().getShipmentSubStatus());
            shipmentService.update(shipmentDto, shipmentDto.getId());
        }
        boolean isOrderOnlyLeftWithNonWareHouseOrder = isOrderOnlyLeftWithNonWareHouseOrder(orderDto);
        if(isOrderOnlyLeftWithNonWareHouseOrder){
            orderService.updateStatus(orderDto.getId(), OrderStatus.PROCESSING, OrderSubStatus.PROCESSING);
        }else {
            orderService.updateStatus(orderDto.getId(), transitionAction.getOrderStatus().getOrderStatus(),
                    transitionAction.getOrderStatus().getOrderSubStatus());
        }
    }

    public AssignShipmentFullfillerRequest buildAssignmentFulfillerRequest(ShipmentDto shipmentDto) {
        AssignShipmentFullfillerRequest request = new AssignShipmentFullfillerRequest();
        request.setWmsOrderCode(shipmentDto.getWmsOrderCode());
        List<OrderItemMetaDataDto> orderItemMetaDataDtoList = shipmentDto.getOrderItems().get(0).getOrderItemMetaData();
        for(OrderItemMetaDataDto orderItemMetaDataDto : orderItemMetaDataDtoList){
            if(orderItemMetaDataDto.getEntityKey().equals("HUB_CODE")){
                request.setHubCode(orderItemMetaDataDto.getEntityValue());
                break;
            }
        }
        request.setPincode(Integer.valueOf(shipmentDto.getShippingAddress().getPostcode()));
        request.setShipmentType(ShipmentType.valueOf(shipmentDto.getShipmentType()));
        List<ShipmentItem> shipmentItemList = new ArrayList<>();

        for (OrderItemDto orderItemDto : shipmentDto.getOrderItems()) {
            ShipmentItem shipmentItem = new ShipmentItem();
            shipmentItem.setItemType(getItemType(orderItemDto.getItemType()));
            shipmentItem.setItemId(orderItemDto.getUwItemId());
            shipmentItem.setProductId(orderItemDto.getProductId());
            shipmentItem.setNavChannel(orderItemDto.getNavChannel().name());
            shipmentItem.setFittingId(orderItemDto.getFittingId());
            shipmentItemList.add(shipmentItem);
        }
        request.setShipmentItems(shipmentItemList);
        logger.info("[buildAssignmentFulfillerRequest]  {} for wmsOrderCode {}", request, shipmentDto.getWmsOrderCode());
        return request;
    }

    public WarehouseAssignmentResponse assignWarehouseAndBlockInventoryUsingOptima(ShipmentDto shipmentDto, OmsOrderEvent omsOrderEvent) {
        WarehouseAssignmentResponse warehouseAssignmentResponse = new WarehouseAssignmentResponse();
        String wmsOrderCode = shipmentDto.getWmsOrderCode();
        boolean isOptimaRegexPassed = optimaEligiblityPattern.matcher(wmsOrderCode).find();
        if (!isOptimaFacilitySelectionEnabled || !isOptimaRegexPassed || omsCommonUtil.isDistributorOrder(omsOrderEvent.getOrderDto())
                || isOrderCountryNotEligibleForOptima(shipmentDto.getId(), omsOrderEvent.getOrderDto().getLkCountry()) || isItemTypeNotAllowedForOptima(shipmentDto) || !shipmentDto.getShippingAddress().getPostcode().matches("^-?\\d+$")) {
            warehouseAssignmentResponse.setUseDefaultOptimaLogic(true);
            return warehouseAssignmentResponse;
        }
        logger.info("[assignWarehouseAndBlockInventoryUsingOptima] optimaEligiblityPattern : {}, wmsOrderCode: {}", isOptimaRegexPassed, wmsOrderCode);
        try {
            AssignShipmentFullfillerResponse fullfillerResponse = optimaConnector.getFulfillerFacility(buildAssignmentFulfillerRequest(shipmentDto));
            logger.info("[assignWarehouseAndBlockInventoryUsingOptima] fullfillerResponse: {}, wmsOrderCode: {}", fullfillerResponse, wmsOrderCode);
            if (Boolean.FALSE.equals(fullfillerResponse.getIsJit()) && nexsOrderCriteriaFacilities.contains(fullfillerResponse.getFacility())) {
                logger.info("[assignWarehouseAndBlockInventoryUsingOptima] trying block inventory using optima for: {}, wmsOrderCode: {}", fullfillerResponse, wmsOrderCode);
                warehouseAssignmentResponse.setAssignedFacility(fullfillerResponse.getFacility());
                warehouseAssignmentResponse.setWarehouseAssigned(false);
                warehouseAssignmentResponse = optimaFacade.blockInventoryAndFinalizeNexsFacility(
                        shipmentDto,
                        omsOrderEvent.getOrderDto().getJunoOrderId(),
                        warehouseAssignmentResponse
                );
            }
            return warehouseAssignmentResponse;
        } catch (OptimaFailureException e) {
            logger.error("Exception received while calling optima for request:{} , e: ", shipmentDto, e);
            warehouseAssignmentResponse.setUseDefaultOptimaLogic(true);
        }
        logger.info("[assignWarehouseAndBlockInventoryUsingOptima] optima warehouseAssignmentResponse: {}, wmsOrderCode: {}", warehouseAssignmentResponse, wmsOrderCode);
        return warehouseAssignmentResponse;
    }

    private String getItemType(com.lenskart.oms.enums.ItemType itemType) {
        if(com.lenskart.oms.enums.ItemType.LEFT_LENS.equals(itemType))
            return "LEFTLENS";
        else if(com.lenskart.oms.enums.ItemType.RIGHT_LENS.equals(itemType))
            return "RIGHTLENS";
        else if(com.lenskart.oms.enums.ItemType.CONTACT_LENS.equals(itemType))
            return "CONTACT_LENS";
        else if(com.lenskart.oms.enums.ItemType.LOYALTY_SERVICES.equals(itemType))
            return "LOYALTY";
        else if (com.lenskart.oms.enums.ItemType.CONTACT_LENS_SOLUTION.equals(itemType))
            return "CONTACT_LENS_SOLUTION";
        else if(com.lenskart.oms.enums.ItemType.EYEFRAME.equals(itemType))
            return "FRAME";
        else if(com.lenskart.oms.enums.ItemType.SUNGLASS.equals(itemType))
            return "SUNGLASS";
        else return null;
    }

    private boolean isOrderCountryNotEligibleForOptima(Long shipmentId, String lkCountry){
        logger.info("[isOrderCountryNotEligibleForOptima] shipment {} country {} , eligible countries {}",shipmentId, lkCountry, countriesEligibleForOptima);
        if(!countriesEligibleForOptima.contains(lkCountry)) {
            logger.info("[isOrderCountryNotEligibleForOptima] shipment id {} is not eligible ", shipmentId);
            return true;
        }
        return  false;
    }

    private boolean isItemTypeNotAllowedForOptima(ShipmentDto shipmentDto) {
        if(ShipmentType.FR1.name().equals(shipmentDto.getShipmentType())) return false;

        Set<ItemType> itemTypes = shipmentDto.getOrderItems().stream()
                .map(OrderItemDto::getItemType)
                .collect(Collectors.toSet());
        for(ItemType itemType : itemTypes) {
            return  !optimaEnableFr0ItemTypes.contains(itemType.name());
        }

        return false;
    }
    private List<ShipmentDto> getShipmentDtosForReassignmentCheck(OrderDto orderDto) {
        List<ShipmentDto> shipmentDtoList = new ArrayList<>();
        Set<Long> shipmentIds = orderDto.getOrderItems().stream()
                .filter(orderItemDto -> {
                    boolean isOtc = ProductDeliveryType.OTC.equals(orderItemDto.getProductDeliveryType());
                    boolean isDtcLocalFitting = ProductDeliveryType.DTC.equals(orderItemDto.getProductDeliveryType()) &&
                            FulfillmentType.LOCAL_FITTING.equals(orderItemDto.getFulfillmentType());
                    Optional<OrderItemMetaDataDto> centralFacilityCode = orderItemDto.getOrderItemMetaData().stream()
                            .filter(o -> o.getEntityKey().equals("CENTRAL_FACILITY_CODE"))
                            .findFirst();
                    boolean hasCentralFacilityCode = centralFacilityCode.isPresent()
                            && !StringUtils.isEmpty(centralFacilityCode.get().getEntityValue());
                    return !(isOtc || isDtcLocalFitting || hasCentralFacilityCode);
                })
                .map(OrderItemDto::getShipmentId)
                .collect(Collectors.toSet());

        for (Long shipmentId : shipmentIds) {
            logger.info("[getShipmentDtosForReassignmentCheck] order {} shipmentId {}", orderDto.getIncrementId(), shipmentId);
            shipmentDtoList.add(shipmentService.findById(shipmentId));
        }
        return shipmentDtoList;
    }

    private boolean isOrderOnlyLeftWithNonWareHouseOrder(OrderDto orderDto) {
        OrderDto persistedOrder = orderService.findById(orderDto.getId());
        for(OrderItemDto orderItemDto : persistedOrder.getOrderItems()) {
            if(!omsCommonUtil.isNonWarehouseProcessingOrder(orderItemDto)
                && !OrderItemStatus.OMS_REASSIGNED.equals(orderItemDto.getItemStatus())){
                logger.info("[isOrderOnlyLeftWithNonWareHouseOrder] order contains wareHouseItem orderItemId {}", orderItemDto.getId());
                return false;
            }
        }
        return true;
    }
}
