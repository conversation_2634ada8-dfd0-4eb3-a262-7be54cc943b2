package com.lenskart.oms.strategy;

import com.lenskart.oms.enums.ShipmentEvent;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.request.ShipmentUpdateEvent;
import lombok.AllArgsConstructor;
import org.apache.http.HttpStatus;

import java.util.Map;

@AllArgsConstructor
public class ShipmentEventStrategyExecutor {
    private final Map<ShipmentEvent, BaseShipmentEventStrategy> executors;

    public void doExecute(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        if (!executors.containsKey(shipmentUpdateEvent.getShipmentEvent())) {
            throw new ApplicationException(HttpStatus.SC_BAD_REQUEST,"Invalid event type: " + shipmentUpdateEvent.getShipmentEvent(), null);
        }
        executors.get(shipmentUpdateEvent.getShipmentEvent())
                .doExecute(shipmentUpdateEvent);
    }

    public void validator(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        if (!executors.containsKey(shipmentUpdateEvent.getShipmentEvent())) {
            throw new ApplicationException(HttpStatus.SC_BAD_REQUEST, "Invalid event type: " + shipmentUpdateEvent.getShipmentEvent(), null);
        }
        executors.get(shipmentUpdateEvent.getShipmentEvent()).preExecute(shipmentUpdateEvent);
    }
}
