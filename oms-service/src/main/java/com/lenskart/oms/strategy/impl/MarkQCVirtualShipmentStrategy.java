package com.lenskart.oms.strategy.impl;

import com.lenskart.oms.constants.KafkaConstants;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.enums.*;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.producer.CommonKafkaProducer;
import com.lenskart.oms.request.OtcShipmentEvent;
import com.lenskart.oms.request.ShipmentItemUpdate;
import com.lenskart.oms.request.ShipmentUpdateEvent;
import com.lenskart.oms.strategy.BaseOtcShipmentEventStrategy;
import com.lenskart.oms.utils.ObjectHelper;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.lenskart.oms.constants.KafkaConstants.MESSAGE_IDEMPOTENCY_KEY;

@Component
@Setter(onMethod__ = {@Autowired})
public class MarkQCVirtualShipmentStrategy extends BaseOtcShipmentEventStrategy {

    private CommonKafkaProducer commonKafkaProducer;
    @Override
    protected OtcShipmentEventType supportedOrderEvents() {
        return OtcShipmentEventType.VIRTUAL_QC_DONE;
    }

    @Override
    protected void preExecute(OtcShipmentEvent otcShipmentEvent) throws ApplicationException {
        validateVirtualShipment(otcShipmentEvent);
    }

    @Override
    protected void execute(OtcShipmentEvent otcShipmentEvent) throws Exception {
        logger.info("[MarkPickedVirtualShipmentStrategy] ShipmentEvent {}", otcShipmentEvent);
        ShipmentDto shipmentDto = shipmentService.findById(otcShipmentEvent.getShipmentId());
        OrderItemDto b2bOrderItemDto = shipmentDto.getOrderItems().get(0);
        b2bOrderItemDto.setItemStatus(OrderItemStatus.QC_DONE);
        b2bOrderItemDto.setItemSubStatus(OrderItemSubStatus.QC_DONE);
        orderItemService.update(b2bOrderItemDto, b2bOrderItemDto.getId());
    }

    @Override
    protected void postExecute(OtcShipmentEvent otcShipmentEvent) throws Exception {
        ShipmentDto shipmentDto = shipmentService.findById(otcShipmentEvent.getShipmentId());
        commonKafkaProducer.sendMessage(
                KafkaConstants.OMS_OTC_ORDER_EVENTS_PROCESS_TOPIC,
                String.valueOf(shipmentDto.getOrderItems().get(0).getOrderId()),
                ObjectHelper.convertToString(new OtcShipmentEvent(OtcShipmentEventType.VIRTUAL_INVOICED, shipmentDto.getId())),
                String.valueOf(shipmentDto.getId())
        );

    }

    private void validateVirtualShipment(OtcShipmentEvent otcShipmentEvent){
        if(ObjectUtils.isEmpty(otcShipmentEvent) || otcShipmentEvent.getShipmentId() == null) {
            throw new ApplicationException("virtual shipment payload is not correct");
        }

        ShipmentDto shipmentDto =  shipmentService.findById(otcShipmentEvent.getShipmentId());
        if(ShipmentStatus.PROCESSING != shipmentDto.getShipmentStatus()){
            throw new ApplicationException("virtual Shipment status is not correct");
        }

    }
}
