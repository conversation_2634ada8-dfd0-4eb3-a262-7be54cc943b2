package com.lenskart.oms.strategy.impl;

import com.lenskart.oms.constants.KafkaConstants;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.enums.OrderItemStatus;
import com.lenskart.oms.enums.OrderItemSubStatus;
import com.lenskart.oms.enums.OtcShipmentEventType;
import com.lenskart.oms.enums.ShipmentStatus;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.producer.CommonKafkaProducer;
import com.lenskart.oms.request.OtcShipmentEvent;
import com.lenskart.oms.strategy.BaseOtcShipmentEventStrategy;
import com.lenskart.oms.utils.ObjectHelper;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
@Component
@Setter(onMethod__ = {@Autowired})
public class MarkReadyToShipVirtualShipmentStrategy extends BaseOtcShipmentEventStrategy {
    private CommonKafkaProducer commonKafkaProducer;
    @Override
    protected OtcShipmentEventType supportedOrderEvents() {
        return OtcShipmentEventType.VIRTUAL_READY_TO_SHIP;
    }

    @Override
    protected void preExecute(OtcShipmentEvent otcShipmentEvent) throws ApplicationException {
        validateVirtualShipment(otcShipmentEvent);
    }

    @Override
    protected void execute(OtcShipmentEvent otcShipmentEvent) throws Exception {
        logger.info("[MarkReadyToShipVirtualShipmentStrategy] ShipmentEvent {}", otcShipmentEvent);
        ShipmentDto shipmentDto = shipmentService.findById(otcShipmentEvent.getShipmentId());
        OrderItemDto b2bOrderItemDto = shipmentDto.getOrderItems().get(0);
        b2bOrderItemDto.setItemStatus(OrderItemStatus.READY_TO_SHIP);
        b2bOrderItemDto.setItemSubStatus(OrderItemSubStatus.READY_TO_SHIP);
        orderItemService.update(b2bOrderItemDto, b2bOrderItemDto.getId());
    }

    @Override
    protected void postExecute(OtcShipmentEvent otcShipmentEvent) throws Exception {
        ShipmentDto shipmentDto = shipmentService.findById(otcShipmentEvent.getShipmentId());
        commonKafkaProducer.sendMessage(
                KafkaConstants.OMS_OTC_ORDER_EVENTS_PROCESS_TOPIC,
                String.valueOf(shipmentDto.getOrderItems().get(0).getOrderId()),
                ObjectHelper.convertToString(new OtcShipmentEvent(OtcShipmentEventType.VIRTUAL_DISPATCHED, shipmentDto.getId())),
                String.valueOf(shipmentDto.getId())
        );
    }

    private void validateVirtualShipment(OtcShipmentEvent otcShipmentEvent){
        if(ObjectUtils.isEmpty(otcShipmentEvent) || otcShipmentEvent.getShipmentId() == null) {
            throw new ApplicationException("vitual shipment payload is not correct");
        }
        ShipmentDto shipmentDto =  shipmentService.findById(otcShipmentEvent.getShipmentId());
        if(ShipmentStatus.MANIFESTED != shipmentDto.getShipmentStatus()){
            throw new ApplicationException("virtual Shipment status is not correct");
        }
    }
}
