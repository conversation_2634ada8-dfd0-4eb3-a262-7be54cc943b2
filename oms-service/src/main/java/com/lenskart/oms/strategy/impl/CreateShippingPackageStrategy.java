package com.lenskart.oms.strategy.impl;

import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.dto.ShipmentTimelineDto;
import com.lenskart.oms.enums.EventToOperationMap;
import com.lenskart.oms.enums.ShipmentEvent;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.model.Action;
import com.lenskart.oms.request.ShipmentItemUpdate;
import com.lenskart.oms.request.ShipmentUpdateEvent;
import com.lenskart.oms.strategy.BaseShipmentEventStrategy;
import com.lenskart.oms.utils.OmsCommonUtil;
import com.lenskart.oms.validators.ShipmentEventValidator;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Setter(onMethod__ = {@Autowired})
public class CreateShippingPackageStrategy extends BaseShipmentEventStrategy {

    private ShipmentEventValidator shipmentEventValidator;
    private OmsCommonUtil commonUtil;

    @Override
    protected ShipmentEvent supportedOrderEvents() {
        return ShipmentEvent.MARK_CREATE_SHIPMENT;
    }

    @Override
    protected void preExecute(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        shipmentEventValidator.validateCreateShippingPackageEvent(shipmentUpdateEvent);
    }

    @Override
    protected void execute(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        if (CollectionUtils.isEmpty(shipmentUpdateEvent.getOrderItemList()))
            return;

        Long orderId = null;
        Set<Long> shipmentIds = new HashSet<>();
        Action transitionAction = null;
        List<Long> orderItemIds = shipmentUpdateEvent.getOrderItemList().stream().map(ShipmentItemUpdate::getOrderItemId).collect(Collectors.toList());
        List<OrderItemDto> orderItemDtoList = orderItemService.search("id.in:" + StringUtils.join(orderItemIds, ","));
        Map<Long, ShipmentItemUpdate> shipmentItemsMap = shipmentUpdateEvent.getOrderItemList().stream().collect(Collectors.toMap(ShipmentItemUpdate::getOrderItemId, Function.identity(), (x1, x2) ->  x1));
        for (OrderItemDto currentItemDto : orderItemDtoList) {
            ShipmentItemUpdate item = shipmentItemsMap.get(currentItemDto.getId());
            transitionAction = omsTransitionUtil.getTransitionActionByOperationAndStatus(
                    EventToOperationMap.valueOf(shipmentUpdateEvent.getShipmentEvent().name()).getOperation(), currentItemDto.getItemStatus()
            );
            orderId = currentItemDto.getOrderId();
            shipmentIds.add(currentItemDto.getShipmentId());
            if (currentItemDto != null) {
                logger.info("Updating order item status from {} to {} for orderId - {}", currentItemDto.getItemStatus(), transitionAction.getOrderStatus().getOrderStatus(), currentItemDto.getId());
                currentItemDto.setItemStatus(transitionAction.getItemStatus().getItemStatus());
                currentItemDto.setItemSubStatus(transitionAction.getItemStatus().getItemSubStatus());
                orderItemService.save(currentItemDto);
            }
            updateShipmentTimeline(shipmentUpdateEvent, currentItemDto);
            item.setOrderItemId(currentItemDto.getUwItemId());
        }

        updateShipmentData(shipmentUpdateEvent, shipmentIds, transitionAction);
        updateOrderDataIfRequired(orderId, transitionAction);
    }

    private void updateOrderDataIfRequired(Long orderId, Action transitionAction) {
        OrderDto orderDto = orderService.findById(orderId);
        if (isOrderStatusUpdateRequired(transitionAction, orderDto)) {
            logger.info("Updating order status from {} to {} for orderId - {}", orderDto.getOrderItems(), transitionAction.getOrderStatus().getOrderStatus(), orderDto.getId());
            orderDto.setOrderStatus(transitionAction.getOrderStatus().getOrderStatus());
            orderService.save(orderDto);
        }
    }

    private void updateShipmentData(ShipmentUpdateEvent shipmentUpdateEvent, Set<Long> shipmentIds, Action transitionAction) {
        List<ShipmentDto> shipmentDtoList = shipmentService.search("id.in:" + StringUtils.join(shipmentIds, ","));
        if (!CollectionUtils.isEmpty(shipmentDtoList)) {
            for (ShipmentDto shipmentDto : shipmentDtoList) {
                logger.info("Updating shipment status from {} to {} for orderId - {}", shipmentDto.getShipmentStatus(), transitionAction.getShipmentStatus().getShipmentStatus(), shipmentDto.getId());
                shipmentDto.setShipmentStatus(transitionAction.getShipmentStatus().getShipmentStatus());
                shipmentDto.setWmsShippingPackageId(shipmentUpdateEvent.getEntityId());
                shipmentService.save(shipmentDto);
            }
        }
    }

    private void updateShipmentTimeline(ShipmentUpdateEvent shipmentUpdateEvent, OrderItemDto currentItemDto) {
        List<ShipmentTimelineDto> timelineDtoList = shipmentTimelineService.findAssociatedShipmentTimelines(currentItemDto);
        for (ShipmentTimelineDto timelineDto : timelineDtoList) {
            timelineDto.setAssignedTime(shipmentUpdateEvent.getEventTime() != null ? shipmentUpdateEvent.getEventTime() : new Date());
            shipmentTimelineService.save(timelineDto);
            logger.info("Updating Shipment Assigned Time - {} For OrderItemId - {} & ShipmentId - {}", timelineDto.getAssignedTime(), timelineDto.getOrderItemId(), timelineDto.getShipmentId());
        }
    }

    protected Boolean isOrderStatusUpdateRequired(Action transitionAction, OrderDto orderDto) {
        List<ShipmentDto> shipmentDtoList = getShipmentDtos(orderDto);
        for (ShipmentDto shipmentDto : shipmentDtoList) {
            if(commonUtil.isB2bShipment(shipmentDto)) continue;
            if (!shipmentDto.getShipmentStatus().name().equalsIgnoreCase(transitionAction.getOrderStatus().getOrderStatus().name())) {
                return false;
            }
        }
        return true;
    }
}
