package com.lenskart.oms.strategy;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.connector.OrderOpsConnector;
import com.lenskart.oms.dto.*;
import com.lenskart.oms.enums.*;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.exception.DuplicateEventException;
import com.lenskart.oms.exception.TerminatedStateException;
import com.lenskart.oms.model.Action;
import com.lenskart.oms.model.OmsEvents;
import com.lenskart.oms.model.UwOrder;
import com.lenskart.oms.producer.ProcessOrderEventOmsProducer;
import com.lenskart.oms.producer.OrderEventWmsProducer;
import com.lenskart.oms.request.OmsOrderEvent;
import com.lenskart.oms.response.UwOrderResponse;
import com.lenskart.oms.service.*;
import com.lenskart.oms.utils.OmsCommonUtil;
import com.lenskart.oms.utils.OmsTransitionUtil;
import com.lenskart.oms.validators.OrderSyncValidator;
import com.lenskart.order.interceptor.enums.Client;
import com.newrelic.api.agent.Trace;
import io.micrometer.core.annotation.Timed;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.lenskart.oms.constants.ApplicationConstants.ON_HOLD_REASON_FRAUD_ORDER;
import static com.lenskart.oms.constants.ApplicationConstants.ORDER_OPS_STATUS_PROCESSING;

@Setter(onMethod__ = {@Autowired})
public abstract class BaseOrderEventStrategy {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    protected Logger logger;

    protected OmsEvents events;
    protected OrderService orderService;
    protected OmsCommonUtil omsCommonUtil;
    protected ShipmentService shipmentService;
    protected OrderItemService orderItemService;
    private OrderOpsConnector orderOpsConnector;
    protected OmsTransitionUtil omsTransitionUtil;
    private OrderSyncValidator orderSyncValidator;
    private OnHoldMasterService onHoldMasterService;
    protected OrderEventWmsProducer orderEventWmsProducer;
    protected ShipmentTimelineService shipmentTimelineService;
    protected OrderBackSyncTrackingService orderBackSyncTrackingService;
    protected ProcessOrderEventOmsProducer processOrderEventOmsProducer;

    @Setter(AccessLevel.NONE)
    @Value("#{'${nexs.oms.optima.eligible.events}'.split(',')}")
    private Set<String> optimaEligibleEvents;

    protected abstract OrderEventType supportedOrderEvents();

    protected abstract void preExecute(OmsOrderEvent orderRequest) throws Exception;
    protected abstract void execute(OmsOrderEvent orderRequest) throws Exception;
    protected abstract void postExecute(OmsOrderEvent orderRequest) throws Exception;

    @Trace
    @Timed
    @Transactional(rollbackFor = Exception.class)
    public void doExecute(OmsOrderEvent orderRequest) throws Exception {
        logger.info("[{} -> doExecute] inside {} strategy for request {}", this.getClass().getName(), supportedOrderEvents(), orderRequest);
        try {
            preExecute(orderRequest);
            execute(orderRequest);
            postExecute(orderRequest);
        } catch (DuplicateEventException | TerminatedStateException e) {
            String errorMessage = MessageFormat.format("[InterceptorOrderEventsConsumer -> doExecute] error while processing order create / update event for {0}", orderRequest);
            logger.error(errorMessage, e);
        }
        if(!(orderRequest.getClientId()!= null && orderRequest.getClientId().equals(Client.OS.getUsername()) || omsCommonUtil.isDistributorOrder(orderRequest.getOrderDto()))) {
            omsCommonUtil.sendEventAckToOrderInterceptor(String.valueOf(orderRequest.getIncrementId()), orderRequest.getEventType().name(), true, null);
        }
    }

    public boolean validateAndSyncOrder(OmsOrderEvent omsOrderEvent) throws ApplicationException {
        boolean pushedToWms = checkAndPushUpdateWms(omsOrderEvent);
        if (!pushedToWms) {
            return validateAndPushToPostProcessor(omsOrderEvent);
        }
        return true;
    }

    protected void pushToPostOrderCreateProcessing(OmsOrderEvent omsOrderEvent) throws ApplicationException {

    }

    protected void pushUpdateToWmsProcessing(OmsOrderEvent omsOrderEvent) throws ApplicationException {

    }

    protected OrderDto getOrderDto(Long incrementId) throws ApplicationException {
        OrderDto orderDto = orderService.findByIncrementId(incrementId);
        if (Objects.isNull(orderDto)) {
            throw new ApplicationException("invalid order for incrementId:" + incrementId, null);
        }
        return orderDto;
    }

    protected List<ShipmentDto> getShipmentDtos(OrderDto orderDto) {
        List<ShipmentDto> shipmentDtoList = new ArrayList<>();
        Set<Long> shipmentIds = orderDto.getOrderItems().stream()
                .filter(orderItemDto -> !omsCommonUtil.isNonWarehouseProcessingOrder(orderItemDto))
                .map(OrderItemDto::getShipmentId)
                .collect(Collectors.toSet());

        for (Long shipmentId : shipmentIds) {
            shipmentDtoList.add(shipmentService.findById(shipmentId));
        }
        return shipmentDtoList;
    }

    private Boolean checkAndPushUpdateWms(OmsOrderEvent omsOrderEvent) throws ApplicationException {
        boolean pushedToWms = false;
        if(!CollectionUtils.isEmpty(omsOrderEvent.getShipmentDtoList())){
            for (ShipmentDto shipmentDto : omsOrderEvent.getShipmentDtoList()) {
                if (isShipmentSyncedToWMS(shipmentDto)) {
                    pushUpdateToWmsProcessing(omsOrderEvent);
                    pushedToWms = true;
                }
            }
        }
        return pushedToWms;
    }

    protected boolean isShipmentSyncedToWMS(ShipmentDto shipmentDto) {
        List<ShipmentTimelineDto> shipmentTimelineDtoList = shipmentTimelineService.findByShipmentId(shipmentDto.getId());
        if (CollectionUtils.isEmpty(shipmentTimelineDtoList)) {
            logger.info("[ValidateOrderSyncStrategy -> isShipmentSyncedToWMS] Shipment {} is not yet synced to WMS.", shipmentDto.getId());
            return false;
        }

        for (ShipmentTimelineDto shipmentTimelineDto : shipmentTimelineDtoList) {
            if (shipmentTimelineDto.getFulfilledTime() != null) {
                logger.info("[ValidateOrderSyncStrategy -> isShipmentSyncedToWMS] Shipment {} is already synced to WMS.", shipmentDto.getId());
                return true;
            }
        }

        logger.info("[ValidateOrderSyncStrategy -> isShipmentSyncedToWMS] Shipment {} is not yet synced to WMS.", shipmentDto.getId());
        return false;
    }

    private boolean validateAndPushToPostProcessor(OmsOrderEvent omsOrderEvent) throws ApplicationException {
        if (optimaEligibleEvents.contains(omsOrderEvent.getEventType().name())
                && isValidShipment(omsOrderEvent.getShipmentDtoList().get(0), omsOrderEvent.getOrderDto())
        ) {
            logger.info("[ValidateOrderSyncStrategy] All the validations passed for shipment {}", omsOrderEvent.getShipmentDtoList().get(0).getId());
            pushToPostOrderCreateProcessing(omsOrderEvent);
            return true;
        }

        return false;
    }

    private boolean isValidShipment(ShipmentDto shipmentDto, OrderDto orderDto) throws ApplicationException {
        if (orderSyncValidator.isFraudOrder(shipmentDto, orderDto)) {
            logger.info("[isValidShipment -> isFraudOrder] Order {} is fraud order. Putting on Hold", orderDto.getIncrementId());
            orderDto.setIsOnHold(true);
            orderDto.setOnHoldReasonId(getOnHoldMasterDto());
            orderService.update(orderDto, orderDto.getId());

            return false;
        }

        if (!orderSyncValidator.isPaymentCaptured(orderDto)) {
            logger.info("[isValidShipment -> isPaymentCaptured] Payment is not captured for order {}.", orderDto.getIncrementId());
            return false;
        }

        if (OrderStatus.PENDING.equals(orderDto.getOrderStatus())) {
            logger.info("[isValidShipment -> orderStatusCheck] order {} is in NEW status.", orderDto.getIncrementId());
            return false;
        }

        if (Boolean.TRUE.equals(orderDto.getIsOnHold())) {
            logger.info("[isValidShipment -> isOrderOnHold] Order {} is on Hold.", orderDto.getIncrementId());
            return false;
        }

        return orderSyncValidator.isPowerWiseProductIdsAssigned(orderDto.getIncrementId())
                && (omsCommonUtil.isDistributorOrder(orderDto) || isOrderSyncedToOrderOps(orderDto.getIncrementId()));
    }

    private Long getOnHoldMasterDto() throws ApplicationException {
        OnHoldMasterDto onHoldMasterDto = onHoldMasterService.findBySearchTerms("code.eq:" + ON_HOLD_REASON_FRAUD_ORDER);
        if (Objects.isNull(onHoldMasterDto)) {
            throw new ApplicationException("invalid hold reason code passed", null);
        }
        return onHoldMasterDto.getId();
    }

    private boolean isOrderSyncedToOrderOps(Long incrementId) throws ApplicationException {
        StringBuilder searchTerm = new StringBuilder("entityId.eq:")
                .append(incrementId)
                .append("___")
                .append("eventName.eq:")
                .append(BackSyncEventName.CREATE_ORDER.name());
        OrderBackSyncTrackingDto orderBackSyncTrackingDto = orderBackSyncTrackingService.findBySearchTerms(searchTerm.toString());

        if (orderBackSyncTrackingDto != null) {
            if (BackSyncEventStatus.SUCCESS.equals(orderBackSyncTrackingDto.getEventStatus())) {
                logger.info("[ValidateOrderSyncStrategy -> isOrderSyncedToOrderOps] Order {} is already synced to OrderOps.", incrementId);
                return true;
            } else {
                return updateCreateOrderTrackingIfRequired(incrementId, orderBackSyncTrackingDto);
            }
        }

        logger.info("[ValidateOrderSyncStrategy -> isOrderSyncedToOrderOps] Order {} is not yet synced to OrderOps.", incrementId);
        return false;
    }

    private boolean updateCreateOrderTrackingIfRequired(Long incrementId, OrderBackSyncTrackingDto orderBackSyncTrackingDto) throws ApplicationException {
        UwOrderResponse uwOrderResponse = orderOpsConnector.getUwOrderDetails(incrementId);
        if (Objects.isNull(uwOrderResponse) || CollectionUtils.isEmpty(uwOrderResponse.getUwOrders())) {
            logger.info("[ValidateOrderSyncStrategy -> isOrderSyncedToOrderOps] Order {} is not yet synced to OrderOps.", incrementId);
            return false;
        } else {
            for (UwOrder uwOrder : uwOrderResponse.getUwOrders()) {
                if (!ORDER_OPS_STATUS_PROCESSING.equalsIgnoreCase(uwOrder.getUnicomShipmentStatus())) {
                    logger.info("[ValidateOrderSyncStrategy -> isOrderSyncedToOrderOps] Order {} is not yet synced to OrderOps.", incrementId);
                    return false;
                }
            }
        }

        orderBackSyncTrackingDto.setEventStatus(BackSyncEventStatus.SUCCESS);
        orderBackSyncTrackingService.save(orderBackSyncTrackingDto);
        return true;
    }

    protected Boolean isOrderStatusUpdateRequired(Action transitionAction, OrderDto orderDto) {
        List<ShipmentDto> shipmentDtoList = getShipmentDtos(orderDto);
        for (ShipmentDto shipmentDto : shipmentDtoList) {
            if (!shipmentDto.getShipmentStatus().name().equalsIgnoreCase(transitionAction.getOrderStatus().getOrderStatus().name())) {
                return false;
            }
        }
        return true;
    }
}
