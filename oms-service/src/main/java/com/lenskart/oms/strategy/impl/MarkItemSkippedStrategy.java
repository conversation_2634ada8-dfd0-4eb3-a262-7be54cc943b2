package com.lenskart.oms.strategy.impl;

import com.lenskart.inventoryadapter.request.GetMarkFoundRequest;
import com.lenskart.oms.connector.InventoryAdaptorConnector;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.entity.DistributorOrders;
import com.lenskart.oms.enums.*;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.model.Action;
import com.lenskart.oms.model.MarkFoundAsyncPayload;
import com.lenskart.oms.producer.MarkOrderFoundProducer;
import com.lenskart.oms.request.ShipmentItemUpdate;
import com.lenskart.oms.request.ShipmentUpdateEvent;
import com.lenskart.oms.request.distributor.JitDoItemStatusUpdateRequest;
import com.lenskart.oms.service.DistributorOrdersService;
import com.lenskart.oms.strategy.BaseShipmentEventStrategy;
import com.lenskart.oms.validators.ShipmentEventValidator;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.lenskart.oms.constants.ApplicationConstants.DEFAULT_SHELF;

@Component
@Setter(onMethod__ = {@Autowired})
public class MarkItemSkippedStrategy extends BaseShipmentEventStrategy {

    private ShipmentEventValidator shipmentEventValidator;
    private InventoryAdaptorConnector inventoryAdaptorConnector;
    private DistributorOrdersService distributorOrdersService;

    @Override
    protected ShipmentEvent supportedOrderEvents() {
        return ShipmentEvent.MARK_ITEM_SKIPPED;
    }

    @Override
    protected void preExecute(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        shipmentEventValidator.validateItemSkipEvent(shipmentUpdateEvent);
    }

    @Value("#{${so.unicomFacilityMapping}}")
    @Setter(AccessLevel.NONE)
    private Map<String, String> unicomFacilityMapping;

    @Value("${mark.found.async.enabled:true}")
    @Setter(AccessLevel.NONE)
    private boolean markFoundAsyncEnabled;

    private MarkOrderFoundProducer markOrderFoundProducer;

    @Override
    protected void execute(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        List<OrderItemDto> orderItemDtoList = orderItemService.search("id.in:" + StringUtils.join(shipmentUpdateEvent.getOrderItemList().stream().map(ShipmentItemUpdate::getOrderItemId).collect(Collectors.toList()), ","), new HashSet<>(Arrays.asList(ApplicationConstants.CHILD_ENTITIES.ITEM_POWER, ApplicationConstants.CHILD_ENTITIES.ORDER_ITEM_PRICE, ApplicationConstants.CHILD_ENTITIES.ORDER_ITEM_META_DATA)));
        List<Long> orderItemIds = orderItemDtoList.stream().map(OrderItemDto::getId).collect(Collectors.toList());
        orderItemService.updateSkippedItems(orderItemIds, OrderItemStatus.SKIPPED.toString(), OrderItemSubStatus.SKIPPED.toString(), null, null);

        ShipmentDto shipmentDto = shipmentService.findBySearchTerms("id.eq:" + orderItemDtoList.get(0).getShipmentId(), new HashSet<>(Arrays.asList(ApplicationConstants.CHILD_ENTITIES.ORDER_ITEMS, ApplicationConstants.CHILD_ENTITIES.SHIPMENT_META_DATA)));
        if (shipmentDto != null && OrderSubType.DISTRIBUTOR_SUPER_ORDER.name().equalsIgnoreCase(shipmentDto.getShipmentSubType())) {
            Map<Long, Long> pidQtyMap = orderItemDtoList.stream().collect(Collectors.groupingBy(OrderItemDto::getProductId, Collectors.counting()));
            if (markFoundAsyncEnabled) {
                markOrderFoundProducer.sendMessage(new MarkFoundAsyncPayload(pidQtyMap, unicomFacilityMapping.get(shipmentDto.getFacility())), orderItemDtoList.get(0).getUwItemId());
            } else {
                handleMarkFoundForDoAsSo(pidQtyMap, unicomFacilityMapping.get(shipmentDto.getFacility()));
            }
        }

        if(OrderSubType.DISTRIBUTOR_JIT_ORDER.name().equals(shipmentDto.getShipmentSubType())) {
            logger.info("[MarkItemSkippedStrategy] shipment {} is Jit DO", shipmentDto.getWmsOrderCode());
            omsCommonUtil.updateItemStatusAtInventory(orderItemDtoList, "SKIPPED");
        }
    }

    public void handleMarkFoundForDoAsSo(Map<Long, Long> pidQtyMap, String facilityCode) {
        if (!pidQtyMap.isEmpty()) {
            for (Map.Entry<Long, Long> entry : pidQtyMap.entrySet()) {
                GetMarkFoundRequest markFoundRequest = new GetMarkFoundRequest();
                markFoundRequest.setFacilityCode(facilityCode);
                markFoundRequest.setQuantityNotFound(entry.getValue().intValue());
                markFoundRequest.setSkuId(entry.getKey().toString());
                markFoundRequest.setShelf(DEFAULT_SHELF);
                try {
                    logger.info("MarkItemSkippedStrategy.handleMarkFoundForDoAsSo Request - " + markFoundRequest);
                    inventoryAdaptorConnector.triggerInventoryMarkFound(markFoundRequest);
                    logger.info("MarkItemSkippedStrategy.handleMarkFoundForDoAsSo triggerInventoryMarkFound Executed For Request - " + markFoundRequest);
                } catch (Exception exception) {
                    logger.error("MarkItemSkippedStrategy.execute.triggerInventoryMarkFound For Payload - " + markFoundRequest, exception);
                }
            }
        }
    }
}
