package com.lenskart.oms.strategy.impl;

import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.enums.*;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.mapper.PaymentUpdateMapper;
import com.lenskart.oms.model.Action;
import com.lenskart.oms.request.OmsOrderEvent;
import com.lenskart.oms.request.WmsOrderEvent;
import com.lenskart.oms.strategy.BaseOrderEventStrategy;
import com.lenskart.oms.validators.UpdatePaymentEventValidator;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.List;

@Component
@Setter(onMethod__ = {@Autowired})
public class UpdatePaymentCaptureStrategy  extends BaseOrderEventStrategy {

    private UpdatePaymentEventValidator updatePaymentEventValidator;

    private PaymentUpdateMapper paymentUpdateMapper;

    @Override
    protected OrderEventType supportedOrderEvents() {
        return OrderEventType.UPDATE_PAYMENT_CAPTURE;
    }

    @Override
    protected void preExecute(OmsOrderEvent orderRequest) throws ApplicationException {
        updatePaymentEventValidator.validateUpdatePaymentEvent(orderRequest);
    }

    @Override
    protected void execute(OmsOrderEvent omsOrderEvent) throws ApplicationException {
        OrderDto persistedOrderDto = orderService.findBySearchTerms("incrementId.eq:" + omsOrderEvent.getOrderDto().getIncrementId());
        List<ShipmentDto> shipmentDtoList = getShipmentDtos(persistedOrderDto);

        if (Boolean.FALSE.equals(persistedOrderDto.getPaymentCaptured()) && Boolean.TRUE.equals(omsOrderEvent.getOrderDto().getPaymentCaptured())) {
            persistedOrderDto.setPaymentCaptured(omsOrderEvent.getOrderDto().getPaymentCaptured());
            persistedOrderDto.setUpdatedBy(ApplicationConstants.DEFAULT_OMS_USER);
            omsOrderEvent.setOrderDto(orderService.save(persistedOrderDto));
        } else {
            omsOrderEvent.setOrderDto(persistedOrderDto);
        }

        omsOrderEvent.setShipmentDtoList(shipmentDtoList);
    }

    @Override
    protected void postExecute(OmsOrderEvent orderRequest) throws ApplicationException {
        validateAndSyncOrder(orderRequest);
    }

    @Override
    protected void pushUpdateToWmsProcessing(OmsOrderEvent omsOrderEvent) throws ApplicationException {
        OrderDto order = omsOrderEvent.getOrderDto();
        for (ShipmentDto shipment : omsOrderEvent.getShipmentDtoList()) {
            if(!shipment.getShipmentStatus().equals(ShipmentStatus.OMS_REASSIGNED)) {
                WmsOrderEvent wmsOrderEvent = paymentUpdateMapper.getWmsOrderEventForPaymentUpdate(order, ShipmentEvent.UPDATE_PAYMENT_CAPTURE, shipment);
                orderEventWmsProducer.sendMessage(wmsOrderEvent);
            }
        }
    }
}
