package com.lenskart.oms.strategy.impl;

import com.lenskart.core.model.Product;
import com.lenskart.oms.exception.DuplicateEventException;
import com.lenskart.oms.exception.TerminatedStateException;
import com.lenskart.oms.mapper.CreateOrderMapper;
import com.lenskart.oms.mapper.UpdatePowerMapper;
import com.lenskart.oms.response.AssignPowerDetails;
import com.lenskart.oms.service.OnHoldMasterService;
import com.lenskart.oms.utils.CommunicationUtil;
import com.lenskart.oms.connector.CatalogOpsConnector;
import com.lenskart.oms.connector.POSConnector;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.dto.*;
import com.lenskart.oms.enums.*;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.request.*;
import com.lenskart.oms.response.AssignPowerResponse;
import com.lenskart.oms.response.AssignPowerResponseWrapper;
import com.lenskart.oms.service.OrderItemPowerService;
import com.lenskart.oms.strategy.BaseOrderEventStrategy;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import java.util.*;
import java.util.stream.Collectors;

import static com.lenskart.oms.constants.ApplicationConstants.*;

@Component
@Setter(onMethod__ = {@Autowired})
public class UpdatePowerStrategy extends BaseOrderEventStrategy {

    private OnHoldMasterService onHoldMasterService;

    private CatalogOpsConnector catalogOpsConnector;

    private POSConnector posConnector;

    private OrderItemPowerService orderItemPowerService;

    private CommunicationUtil communicationUtil;

    private UpdatePowerMapper updatePowerMapper;

    private CreateOrderMapper createOrderMapper;

    @Setter(AccessLevel.NONE)
    @Value("${nexs.oms.enable.cl.inventory.flag}")
    private Boolean enableClInventoryFlag;

    private final String HEC_ORDER = "HEC";

    private final String POWER_UPDATE = "POWER_UPDATE";

    @Override
    protected OrderEventType supportedOrderEvents() {
        return OrderEventType.UPDATE_POWER;
    }

    @Override
    protected void preExecute(OmsOrderEvent omsOrderEvent) throws ApplicationException, TerminatedStateException {
        Map<Long, List<LineItemPayloadEvent>> lineItemIdToLineItemPayloadListMap = new HashMap<>();
        for (LineItemPayloadEvent lineItemPayloadEvent: omsOrderEvent.getLineItemPayloadEventList()) {
            Long lineItemId = lineItemPayloadEvent.getParentLineItemId() == null ? lineItemPayloadEvent.getLineItemId() : lineItemPayloadEvent.getParentLineItemId();
            if(lineItemIdToLineItemPayloadListMap.containsKey(lineItemId)) {
                lineItemIdToLineItemPayloadListMap.get(lineItemId).add(lineItemPayloadEvent);
            } else {
                List<LineItemPayloadEvent> lineItemPayloadEventList = new ArrayList<>();
                lineItemPayloadEventList.add(lineItemPayloadEvent);
                lineItemIdToLineItemPayloadListMap.put(lineItemId, lineItemPayloadEventList);
            }
        }
        List<LineItemPayloadEvent> filteredLineItemPayloadList = new ArrayList<>();
        for (Map.Entry<Long, List<LineItemPayloadEvent>> lineItemPayloadList : lineItemIdToLineItemPayloadListMap.entrySet()) {
            boolean isPureOtcOrder = lineItemPayloadList.getValue().stream()
                    .anyMatch(e -> ProductDeliveryType.OTC.name().equals(e.getProductDeliveryType()) && Boolean.FALSE.equals(e.getIsLocalFittingRequired()));
            boolean isContactLensItem =  lineItemPayloadList.getValue().stream()
                    .anyMatch(e -> ItemType.CONTACT_LENS.name().equals(e.getItemType().name()));
            if(!isPureOtcOrder && !isContactLensItem) {
                filteredLineItemPayloadList.addAll(lineItemPayloadList.getValue());
            }
        }
        omsOrderEvent.setLineItemPayloadEventList(filteredLineItemPayloadList);
        if(CollectionUtils.isEmpty(filteredLineItemPayloadList)){
            logger.info("[UpdatePowerStrategy][preExecute] order is pure OTC skipping update power order {}", omsOrderEvent.getIncrementId());
            return;
        }

        validatePowerUpdateRequest(omsOrderEvent);
        performPrePowerUpdateOperation(omsOrderEvent);
    }

    @Override
    protected void execute(OmsOrderEvent omsOrderEvent) throws ApplicationException {
        if(CollectionUtils.isEmpty(omsOrderEvent.getLineItemPayloadEventList())){
            logger.info("[UpdatePowerStrategy][preExecute] order is pure OTC skipping update power order {}", omsOrderEvent.getIncrementId());
            return;
        }
        updateProductIdsForLineItems(omsOrderEvent);
    }

    @Override
    protected void postExecute(OmsOrderEvent omsOrderEvent) throws ApplicationException {
        logger.debug("[postExecute] status of omsOrderEvent object: {}", omsOrderEvent);
        if(CollectionUtils.isEmpty(omsOrderEvent.getLineItemPayloadEventList())){
            logger.info("[UpdatePowerStrategy][preExecute] order is pure OTC skipping update power order {}", omsOrderEvent.getIncrementId());
            return;
        }
        boolean anyItemPowerFollowUp = false;
        Set<Long> shipmentIds = new HashSet<>();
        for (LineItemPayloadEvent lineItem : omsOrderEvent.getLineItemPayloadEventList()) {
            List<OrderItemDto> orderItems = orderItemService.search("magentoItemId.eq:" + lineItem.getLineItemId());
            shipmentIds.addAll(orderItems.stream().map(OrderItemDto::getShipmentId).collect(Collectors.toSet()));

            if (lineItem.isPowerFollowUpOrder()) {
                logger.info("[postExecute] fetch of orderItems success, resultset size: {}", orderItems.size());
                Set<Long> productIds = orderItems.stream().map(OrderItemDto::getProductId).collect(Collectors.toSet());
                List<Product> productDetails = catalogOpsConnector.getProductDetailsByPIdsAndClassification(productIds, 0, HEC_ORDER);
                if (CollectionUtils.isEmpty(productDetails) && !omsOrderEvent.getEventType().equals(OrderEventType.CREATE_ORDER)) {
                    if (Objects.nonNull(omsOrderEvent.getOrderDto()) && Objects.nonNull(lineItem.getItemType())) {
                        logger.info("[postExecute] sending power follow up sms for order: {}, lineItem: {} and product ids list: {}",omsOrderEvent.getOrderDto(), lineItem, productIds);
                        if (CollectionUtils.isEmpty(omsOrderEvent.getShipmentDtoList())) {
                            ShipmentDto shipmentDto = shipmentService.findById(omsOrderEvent.getOrderDto().getOrderItems().get(0).getShipmentId());
                            communicationUtil. sendPowerFollowUpSMS(omsOrderEvent.getOrderDto(), lineItem, productIds, shipmentDto.getShippingAddress());
                        } else {
                            communicationUtil.sendPowerFollowUpSMS(omsOrderEvent.getOrderDto(), lineItem, productIds, omsOrderEvent.getShipmentDtoList().get(0).getShippingAddress());
                        }
                    }
                }
                anyItemPowerFollowUp = true;
            }
        }
        logger.info("[postExecute] value of anyItemPowerFollowUp flag after checking for all line items: {}", anyItemPowerFollowUp);
        if (!anyItemPowerFollowUp) {
            List<ShipmentDto> shipmentDtoList = new ArrayList<>();
            for (Long shipmentId : shipmentIds) {
                shipmentDtoList.add(shipmentService.findById(shipmentId));
            }
            omsOrderEvent.setShipmentDtoList(shipmentDtoList);

            validateAndSyncOrder(omsOrderEvent);
        }
    }

    @Override
    protected void pushToPostOrderCreateProcessing(OmsOrderEvent omsOrderEvent) throws ApplicationException {
        OrderDto orderDto = new OrderDto();
        orderDto.setId(omsOrderEvent.getOrderDto().getId());
        orderDto.setIncrementId(omsOrderEvent.getOrderDto().getIncrementId());
        omsOrderEvent.setOrderDto(orderDto);
        omsOrderEvent.setIncrementId(omsOrderEvent.getOrderDto().getIncrementId());
        omsOrderEvent.setShipmentDtoList(Collections.EMPTY_LIST);
        omsOrderEvent.setEventType(OrderEventType.POST_CREATE_ORDER);

        processOrderEventOmsProducer.sendMessage(omsOrderEvent);
    }

    private void validatePowerUpdateRequest(OmsOrderEvent omsOrderEvent) throws ApplicationException {
        logger.debug("[validatePowerUpdateRequest] status of omsOrderEvent object: {}",omsOrderEvent);
        if(Objects.isNull(omsOrderEvent)) {
            logger.error("[validatePowerUpdateRequest] Bad request - omsOrderEvent is empty/null");
            throw new ApplicationException("Bad request - omsOrderEvent is empty/null");
        }

        if(Objects.isNull(omsOrderEvent.getOrderDto())) {
            logger.error("[validatePowerUpdateRequest] Bad request - omsOrderEvent.getOrderDto() is empty/null for increment id: {}",omsOrderEvent.getIncrementId());
            throw new ApplicationException("Bad request - omsOrderEvent.getOrderDto() is empty/null");
        }

        if(CollectionUtils.isEmpty(omsOrderEvent.getLineItemPayloadEventList())) {
            logger.error("[validatePowerUpdateRequest] Bad request - LineItemPayloadList is empty/null for increment id: {}",omsOrderEvent.getIncrementId());
            throw new ApplicationException("Bad request - LineItemPayloadList is empty/null for increment id: " + omsOrderEvent.getIncrementId());
        }
    }

    private void performPrePowerUpdateOperation(OmsOrderEvent omsOrderEvent) throws ApplicationException, TerminatedStateException {
        List<LineItemPayloadEvent> lineItemPayloadList = checkAnyItemIsPower(omsOrderEvent);
        Long incrementId = omsOrderEvent.getIncrementId();

        if(CollectionUtils.isEmpty(lineItemPayloadList)) {
            logger.error("[performPrePowerUpdateOperation] Line Item Payload is empty for increment id: {}",incrementId);
            throw new ApplicationException("Line Item Payload is empty for increment id: " + incrementId);
        }

        OrderDto orderDto = orderService.findByIncrementId(incrementId);

        if(Objects.isNull(orderDto)) {
            logger.error("[performPrePowerUpdateOperation] order not found in orderDto for increment id: {}",incrementId);
            throw new ApplicationException("order not found in orderDto for increment id: " + incrementId);
        }

        checkForDuplicatePowerUpdate(omsOrderEvent, orderDto);

        String prescriptionType = findPrescriptionType(omsOrderEvent);
        logger.info("[performPrePowerUpdateOperation] prescription type: {} for increment id: {}", prescriptionType, incrementId);
        if(POWER_TYPE_NORMAL.equalsIgnoreCase(omsOrderEvent.getPrescriptionType()) || POWER_TYPE_PROGRESSIVE.equalsIgnoreCase(prescriptionType)) {
            omsOrderEvent.setPrescriptionType(prescriptionType);
        }
        if(!OrderStatus.getPreProcessingAndProcessingStatus().contains(orderDto.getOrderStatus().name())) {
            logger.error("[performPrePowerUpdateOperation] Invalid order state for increment id: {}, status is neither NEW/CREATED nor PROCESSING and nor in READY_FOR_WH but is in: {}",incrementId, orderDto.getOrderStatus());
            throw new TerminatedStateException("Invalid Order state : " + orderDto.getOrderStatus() + " for increment id " + incrementId + ". Order Status is neither NEW/CREATED nor PROCESSING and nor in READY_FOR_WH");
        }

        omsOrderEvent.setOrderDto(orderDto);
        Set<ItemType> supportedItemTypeForPowerUpdateList = getSupportedItemTypeForPowerUpdate();
        for(LineItemPayloadEvent lineItem:lineItemPayloadList){
            List<OrderItemDto> orderItems = orderDto.getOrderItems();
            if(supportedItemTypeForPowerUpdateList.contains(lineItem.getItemType())){
                if(CollectionUtils.isEmpty(orderItems) || Objects.isNull(orderItems.get(0).getOrderId())) {
                    continue;
                }

                if(Objects.isNull(lineItem.getItemType())) {
                    logger.error("[performPrePowerUpdateOperation] Line item type is missing for incrementId: {}",incrementId);
                    throw new ApplicationException("Line item type is missing for incrementId: " + incrementId);
                }
            }

            if(!orderCountAndPOSValidation(orderItems, orderDto)){
                logger.error("[performPrePowerUpdateOperation] Order Count and pos validation failed for the increment id: {}",incrementId);
                throw new ApplicationException("Order Count and pos validation failed for the increment id: "+incrementId);
            }
        }
    }

    private void checkForDuplicatePowerUpdate(OmsOrderEvent omsOrderEvent, OrderDto orderDto) throws DuplicateEventException {
        List<OrderItemDto> orderItemDtoList = orderDto.getOrderItems();
        Map<String, OrderItemDto> itemTypeOrderItemDtoMap = new HashMap<>();

        for(OrderItemDto orderItem: orderItemDtoList){
            itemTypeOrderItemDtoMap.put(orderItem.getMagentoItemId()+"___"+orderItem.getItemType(),orderItem);
        }

        if(!CollectionUtils.isEmpty(omsOrderEvent.getLineItemPayloadEventList())){
            for(LineItemPayloadEvent payload:omsOrderEvent.getLineItemPayloadEventList()){
                if(payload.getIsPower() == 1 && !payload.isLineItemPropertiesNull() && ItemType.getFrameItemTypes().contains(payload.getItemType().name())) {
                    OrderItemPowerDto existingLeftPower = itemTypeOrderItemDtoMap.get(payload.getLineItemId()+"___"+ItemType.LEFT_LENS.name()).getItemPower();
                    OrderItemPowerDto requestedLeftPower = payload.getLeft();
                    validateIfPowersAreSame(omsOrderEvent, orderDto, payload, existingLeftPower, requestedLeftPower);

                    OrderItemPowerDto existingRightPower = itemTypeOrderItemDtoMap.get(payload.getLineItemId()+"___"+ItemType.RIGHT_LENS.name()).getItemPower();
                    OrderItemPowerDto requestedRightPower = payload.getRight();
                    validateIfPowersAreSame(omsOrderEvent, orderDto, payload, existingRightPower, requestedRightPower);
                }
            }
        }
        logger.info("[checkForDuplicatePowerUpdate] Requested powers in the payload is NOT same as the already existing powers, hence continuing the power update flow for incrementId: {}", omsOrderEvent.getIncrementId());
    }

    private void validateIfPowersAreSame(OmsOrderEvent omsOrderEvent, OrderDto orderDto, LineItemPayloadEvent payload, OrderItemPowerDto existingPower, OrderItemPowerDto requestedPower) {
        if(Objects.nonNull(existingPower) && Objects.nonNull(requestedPower) && checkIfSame(existingPower, requestedPower)) {
            logger.error("[validateIfPowersAreSame] Requested powers in the payload is same as the already existing powers for omsOrderEvent: {}, orderDto: {}", omsOrderEvent, orderDto);
            throw new DuplicateEventException("[201][PowerUpdate] Requested powers in the payload is same as the already existing powers for increment id: "+ omsOrderEvent.getIncrementId()+" Line item Id: "+ payload.getLineItemId());
        }
    }

    public boolean checkIfSame(OrderItemPowerDto existingPower, OrderItemPowerDto requestedPower) {
        if(!checkValuesAreSame(existingPower.getPowerType(), requestedPower.getPowerType()) || !checkValuesAreSame(existingPower.getSph(), requestedPower.getSph()))
            return false;
        if(!checkValuesAreSame(existingPower.getCyl(), requestedPower.getCyl()) || !checkValuesAreSame(existingPower.getAxis(), requestedPower.getAxis()))
            return false;
        if(!checkValuesAreSame(existingPower.getAp(), requestedPower.getAp()) || !checkValuesAreSame(existingPower.getPd(), requestedPower.getPd()))
            return false;
        if(!checkValuesAreSame(existingPower.getNearPD(), requestedPower.getNearPD()) || !checkValuesAreSame(existingPower.getLensHeight(), requestedPower.getLensHeight()))
            return false;
        if(!checkValuesAreSame(existingPower.getLensWidth(), requestedPower.getLensWidth()) || !checkValuesAreSame(existingPower.getEffectiveDia(), requestedPower.getEffectiveDia()))
            return false;
        if(!checkValuesAreSame(existingPower.getEdgeDistance(), requestedPower.getEdgeDistance()) || !checkValuesAreSame(existingPower.getTopDistance(), requestedPower.getTopDistance()))
            return false;
        if(!checkValuesAreSame(existingPower.getBottomDistance(), requestedPower.getBottomDistance()) || !checkValuesAreSame(existingPower.getPrescriptionUrl(), requestedPower.getPrescriptionUrl()))
            return false;
        return true;
    }

    public boolean checkValuesAreSame(String existingPower,String requestedPower){
        try {
            if (StringUtils.isEmpty(existingPower)) {
                if (!StringUtils.isEmpty(requestedPower) && !Float.valueOf(requestedPower).equals(0F)) {
                    logger.info("[checkValuesAreSame][existing empty] existing value: [{}] is not equal to requested value: [{}]", existingPower, requestedPower);
                    return false;
                }
            } else if (StringUtils.isEmpty(requestedPower)) {
                if (!StringUtils.isEmpty(existingPower) && !Float.valueOf(existingPower).equals(0F)) {
                    logger.info("[checkValuesAreSame][existing empty] existing value: [{}] is not equal to requested value: [{}]", existingPower, requestedPower);
                    return false;
                }
            } else if (!Float.valueOf(existingPower).equals(Float.valueOf(requestedPower))){
                logger.info("[checkValuesAreSame][existing not empty] existing value: [{}] is not equal to requested value: [{}]", existingPower, requestedPower);
                return false;
            }
        } catch (NumberFormatException e){
            logger.info("[checkValuesAreSame] inside catch block with message: {}",e.getMessage());
            if(existingPower.equals(requestedPower)) {
                logger.info("[checkValuesAreSame] existing value: [{}] is equal to requested value: [{}]", existingPower, requestedPower);
                return true;
            }
            logger.info("[checkValuesAreSame][catch] existing value: [{}] is not equal to requested value: [{}]", existingPower, requestedPower);
            return false;
        }
        logger.info("[checkValuesAreSame] existing value: [{}] is equal to requested value: [{}]", existingPower, requestedPower);
        return true;
    }

    private List<LineItemPayloadEvent> checkAnyItemIsPower(OmsOrderEvent omsOrderEvent) {
        boolean isPowerRequired = false;
        List<LineItemPayloadEvent> lineItemPayloadList = new ArrayList<>();
        for(LineItemPayloadEvent lineItemPayloadEvent: omsOrderEvent.getLineItemPayloadEventList()){
            if(Objects.nonNull(lineItemPayloadEvent.getIsPower()) && lineItemPayloadEvent.getIsPower()==1) {
                isPowerRequired = true;
            }
        }
        logger.info("[checkAnyItemIsPower] isPowerRequired: {} for increment id: {}", isPowerRequired, omsOrderEvent.getIncrementId());
        if(isPowerRequired)
            lineItemPayloadList = omsOrderEvent.getLineItemPayloadEventList();
        omsOrderEvent.setLineItemPayloadEventList(lineItemPayloadList);
        return lineItemPayloadList;
    }

    private String findPrescriptionType(OmsOrderEvent omsOrderEvent) {
        String prescriptionType = POWER_TYPE_NORMAL;
        for(LineItemPayloadEvent lineItemPayloadEvent: omsOrderEvent.getLineItemPayloadEventList()){
            logger.info("[findPrescriptionType] checking prescription type for lineItem object: {}",lineItemPayloadEvent);
            String powerType = lineItemPayloadEvent.getPowerType();
            if(StringUtils.isNotBlank(powerType) && powerType.equalsIgnoreCase(POWER_TYPE_PROGRESSIVE)){
                prescriptionType = POWER_TYPE_PROGRESSIVE;
                break;
            } else if(StringUtils.isNotBlank(powerType) && powerType.equalsIgnoreCase(POWER_TYPE_BIFOCAL)){
                prescriptionType = POWER_TYPE_BIFOCAL;
            }
        }
        logger.info("[findPrescriptionType] prescription type: {} for increment id: {}",prescriptionType,omsOrderEvent.getIncrementId());
        return prescriptionType;
    }

    private boolean orderCountAndPOSValidation(List<OrderItemDto> orderItemList, OrderDto orderDto) throws ApplicationException {
        //TODO: move this check on shipment level(need to consider contact lens case
//        if (orderItemList.size() < 3) {
//            logger.error("[orderCountAndPOSValidation] orderItemList.size() < 3 for increment id: {}",orderDto.getIncrementId());
//            return false;
//        }

        if(!findValueFromOrdersMetaUsingKey(orderDto, ApplicationConstants.ORDER_META_KEY_FACILITY_CODE).equals("0")){
            Boolean powerStatus;
            try {
                logger.info("[orderCountAndPOSValidation] calling pos to getPowerStatusFromPOS for increment id: {}",orderDto.getIncrementId());
                powerStatus = posConnector.getPowerStatusFromPOS(Math.toIntExact(orderDto.getIncrementId()));
                powerStatus = !Objects.isNull(powerStatus) && powerStatus;
            }catch(Exception e){
                logger.error("[orderCountAndPOSValidation] Fetch power status form POS failed for increment id : {}, with exception: {}",orderDto.getIncrementId(), e.getMessage());
                throw new ApplicationException("Fetch power status form POS failed with exception: "+e.getMessage(), e);
            }
            if(!powerStatus) { //No Entry in pos.kafka_queue_status with power status 1 for magentoItemId
                return false;
            }
        }
        return true;
    }

    private String findValueFromOrdersMetaUsingKey(OrderDto orderDto, String searchKey) throws ApplicationException {
        for(OrderMetaDataDto metaEntry: orderDto.getOrderMetaData()){
            if(metaEntry.getEntityKey().equalsIgnoreCase(searchKey))
                return metaEntry.getEntityValue();
        }
        logger.error("[findValueFromOrdersMetaUsingKey] No entry found for the requested key in orders meta dto. requested key: {}",searchKey);
        throw new ApplicationException("No entry found for the requested key in orders meta dto. requested key: "+searchKey);
    }

    private void updateProductIdsForLineItems(OmsOrderEvent omsOrderEvent) throws ApplicationException {
        logger.debug("[updateProductIdsForLineItems] status of omsOrderEvent object: {}", omsOrderEvent);
        OrderDto orderDto = omsOrderEvent.getOrderDto();
        Long incrementId = omsOrderEvent.getIncrementId();

        /** Updating DB since we assigned the Prescription type in our preExecute() method */
        orderService.save(orderDto);
        logger.debug("[updateProductIdsForLineItems] updated orderDto successfully after populating prescription type, orderDto object: {}", orderDto);

        CatalogOpsPowerWiseIdRequest catalogOpsPowerWiseIdRequest = populatePowerWiseIdRequest(omsOrderEvent, orderDto);
        logger.info("[updateProductIdsForLineItems] catalogOpsPowerWiseIdRequest: {}",catalogOpsPowerWiseIdRequest);

        try {
            AssignPowerResponseWrapper assignPowerResponseWrapper = catalogOpsConnector.fetchPowerWisePidsFromCatalogOps(catalogOpsPowerWiseIdRequest);
            if(Objects.isNull(assignPowerResponseWrapper.getData())) {
                logger.error("[updateProductIdsForLineItems] Catalog response object has null/empty for assignPowerResponseWrapper.getData() for increment id: {}",incrementId);
                throw new Exception("Catalog response object has null/empty for assignPowerResponseWrapper.getData() for increment id: " + incrementId);
            }
            AssignPowerResponse powerResponse = assignPowerResponseWrapper.getData();
            List<AssignPowerDetails> lineItemWisePids = powerResponse.getPowerWiseIdDetails();
            Map<Long, AssignPowerDetails> catalogResponseAndMagentoIdMap = createCatalogResponseAndMagentoIdMap(lineItemWisePids);
            logger.info("[updateProductIdsForLineItems] catalogResponseAndMagentoIdMap: {}", catalogResponseAndMagentoIdMap);

            for(LineItemPayloadEvent lineItem: omsOrderEvent.getLineItemPayloadEventList()){
                if (enableClInventoryFlag != null && enableClInventoryFlag && lineItem.getContactLensId() != null && ItemType.CONTACT_LENS.equals(lineItem.getItemType())
                        && lineItem.getContactLensId() >= 90000000) {
                    logger.error("[updateProductIdsForLineItems] contact lens orders are not being processed by OMS at this moment for increment id: {},  line item: {}",incrementId,lineItem);
                    throw new ApplicationException("This "+incrementId+" contains contact lens but contact lens orders are not being processed by OMS at this moment");
                    /*List<OrderItemDto> orderItems = orderItemService.search("magentoItemId.eq:"+Long.valueOf(lineItem.getLineItemId()));
                    populateAndSavePidsInEntities(Long.valueOf(lineItem.getContactLensId()), orderItems);

                    if(firstOrderItem.getB2bReferenceItemId() != null){
                        List<OrderItemDto> b2BOrderItems = orderItemService.search("magentoItemId.eq:"+firstOrderItem.getB2bReferenceItemId());
                        populateAndSavePidsInEntities(Long.valueOf(lineItem.getContactLensId()), b2BOrderItems);
                    }*/
                }

                Long magentoItemId = Long.valueOf(lineItem.getLineItemId());
                AssignPowerDetails powerWisePid = catalogResponseAndMagentoIdMap.getOrDefault(magentoItemId,null);
                if (Objects.isNull(powerWisePid)) {
                    continue;
                }

                logger.info("[updateProductIdsForLineItems] powerWisePid catlaog response: {} for line item id: {}", powerWisePid, lineItem.getLineItemId());
                if (powerWisePid.getIsPowerFollowUpRequired()) {
                    updatePowerFollowUpDetailsInEntities(powerWisePid.getPowerFollowUpType(), orderDto);
                    lineItem.setPowerFollowUpOrder(true);
                    logger.info("[updateProductIdsForLineItems] updated entities successfully for power follow up request, tLineItemId: {}", lineItem.getLineItemId());
                } else {
                    updateProductIdsInEntities(lineItem, powerWisePid);
                    logger.info("[updateProductIdsForLineItems] updated entities successfully for power wise pid, tLineItemId: {}", lineItem.getLineItemId());
                    markOrderSubStatusPendingIfRequired(orderDto);
                }
            }
        } catch (Exception e) {
            logger.error("[updateProductIdsForLineItems] Fetch Power wise PId's from catalog failed for increment id: {} with exception: {}",incrementId, e.getMessage());
            throw new ApplicationException("Fetch Power wise PId's from catalog failed for increment id: "+incrementId+" with message: "+e.getMessage(), e);
        }
    }

    private void markOrderSubStatusPendingIfRequired(OrderDto orderDto) {
        List<OrderItemDto> powerFollowUpItems = getPowerFollowUpItems(orderDto);
        if (OrderStatus.getNonProcessingStatusEnum().contains(orderDto.getOrderStatus())
                && powerFollowUpItems.size() == 0
        ) {
            OrderDto updatedOrderDto = orderService.findById(orderDto.getId());
            updatedOrderDto.setOrderSubStatus(OrderSubStatus.PROCESSING_PENDING);
            orderService.update(updatedOrderDto, updatedOrderDto.getId());
        }
    }

    private List<OrderItemDto> getPowerFollowUpItems(OrderDto orderDto) {
        return orderDto.getOrderItems()
                .stream()
                .filter(orderItemDto ->
                        orderItemDto.getProductId().equals(Long.valueOf(TEMP_LEFT_LENS_PID))
                        || orderItemDto.getProductId().equals(Long.valueOf(TEMP_RIGHT_LENS_PID)))
                .collect(Collectors.toList());
    }


    private void populateAndSavePidsInEntities(AssignPowerDetails powerWisePid, List<OrderItemDto> orderItems, OrderItemPowerDto orderItemPowerDto, String powerType) {
        OrderItemPowerDto existingOrderItemPower;

        for (OrderItemDto orderItem : orderItems) {
            if (ItemType.LEFT_LENS.equals(orderItem.getItemType())) {
                orderItem.setProductId(powerWisePid.getLeftPowerWisePID());
                existingOrderItemPower = updatePowerMapper.populateExistingPowerFromRequested(powerWisePid, orderItemPowerDto, orderItem, powerType);
            } else if (ItemType.RIGHT_LENS.equals(orderItem.getItemType())) {
                orderItem.setProductId(powerWisePid.getRightPowerWisePID());
                existingOrderItemPower = updatePowerMapper.populateExistingPowerFromRequested(powerWisePid, orderItemPowerDto, orderItem, powerType);
            } else {
                existingOrderItemPower = orderItem.getItemPower();
                existingOrderItemPower.setPackageName(powerWisePid.getPackageName());
                existingOrderItemPower.setCoatingOid(powerWisePid.getCoatingOid());
            }

            if (existingOrderItemPower != null) {
                OrderDto orderDto = orderService.findById(orderItem.getOrderId());
                ProcessingType processingType = createOrderMapper.getProcessingType(orderItem, orderDto);
                logger.info("[populateAndSavePidsInEntities] item type: {}, power type: {}, processingType: {} for order item id: {}, order id: {}", orderItem.getItemType().name(), powerType, processingType, orderItem.getId(), orderItem.getOrderId());
                existingOrderItemPower.setLensPackageType(createOrderMapper.getLensPackageType(orderItem.getItemType().name(), powerType, processingType));
                orderItemPowerService.update(existingOrderItemPower, existingOrderItemPower.getId());
                orderItemService.save(orderItem);
            }
            logger.info("[populateAndSavePidsInEntities] Completed for order item id: {}, order id: {}", orderItem.getId(), orderItem.getOrderId());
        }
    }

    private CatalogOpsPowerWiseIdRequest populatePowerWiseIdRequest(OmsOrderEvent omsOrderEvent, OrderDto order) throws ApplicationException {
        CatalogOpsPowerWiseIdRequest catalogOpsPowerWiseIdRequest = new CatalogOpsPowerWiseIdRequest();
        catalogOpsPowerWiseIdRequest.setIncrementId(omsOrderEvent.getOrderDto().getIncrementId());
        catalogOpsPowerWiseIdRequest.setOrderId(omsOrderEvent.getOrderDto().getJunoOrderId());
        catalogOpsPowerWiseIdRequest.setPowerRequestType(omsOrderEvent.getPrescriptionType());
        catalogOpsPowerWiseIdRequest.setOrderStatus("new");
        catalogOpsPowerWiseIdRequest.setFacilityCode(findValueFromOrdersMetaUsingKey(order, ApplicationConstants.ORDER_META_KEY_FACILITY_CODE));
        List<PowerWisePidLineItemRequest> powerWisePidLineItemRequests = populateLineItems(omsOrderEvent);
        catalogOpsPowerWiseIdRequest.setPowerWisePidRequestLineItem(powerWisePidLineItemRequests);

        return catalogOpsPowerWiseIdRequest;
    }

    private void updatePowerFollowUpDetailsInEntities(String followUpType, OrderDto existingOrderDto) throws ApplicationException {
        OrderDto orderDto = orderService.findById(existingOrderDto.getId());
        if (StringUtils.isEmpty(followUpType)) {
            orderDto.setOrderSubStatus(OrderSubStatus.PROCESSING_POWER_FOLLOWUP);
            orderService.update(orderDto, orderDto.getId());
        } else if (orderDto.getOrderStatus().allowedSubStatusList.contains(followUpType.toUpperCase(Locale.ROOT))) {
            orderDto.setOrderSubStatus(OrderSubStatus.valueOf(followUpType.toUpperCase(Locale.ROOT)));
            orderService.update(orderDto, orderDto.getId());
        } else {
            logger.info("[updatePowerFollowUpDetailsInEntities] orderSubStatus {} is not allowed for orderStatus {} for increment id {}", followUpType.toUpperCase(Locale.ROOT), orderDto.getOrderStatus(), orderDto.getIncrementId());
            throw new ApplicationException("orderSubStatus " + followUpType.toUpperCase(Locale.ROOT) + " is not allowed for orderStatus " + orderDto.getOrderStatus() + " for increment id " + orderDto.getIncrementId());
        }
    }

    private Map<Long, AssignPowerDetails> createCatalogResponseAndMagentoIdMap(List<AssignPowerDetails> lineItemWisePids) {
        Map<Long, AssignPowerDetails> catalogResponseAndMagentoIdMap = new HashMap<>();
        for(AssignPowerDetails lineItemWisePid: lineItemWisePids){
            catalogResponseAndMagentoIdMap.put(lineItemWisePid.getMagentoItemId(),lineItemWisePid);
        }
        return catalogResponseAndMagentoIdMap;
    }

    private void updateProductIdsInEntities(LineItemPayloadEvent lineItem, AssignPowerDetails powerWisePid) throws ApplicationException{
        Long magentoItemId = Long.valueOf(lineItem.getLineItemId());

        List<OrderItemDto> orderItems = orderItemService.search("magentoItemId.eq:" + magentoItemId + "___itemType.eq:" + ItemType.LEFT_LENS);
        populateAndSavePidsInEntities(powerWisePid, orderItems, lineItem.getLeft(), lineItem.getPowerType());

        orderItems = orderItemService.search("magentoItemId.eq:" + magentoItemId + "___itemType.eq:" + ItemType.RIGHT_LENS);
        populateAndSavePidsInEntities(powerWisePid, orderItems, lineItem.getRight(), lineItem.getPowerType());

        orderItems = orderItemService.search("magentoItemId.eq:" + magentoItemId + "___itemType.eq:" + ItemType.EYEFRAME);
        if (CollectionUtils.isEmpty(orderItems)) {
            orderItems = orderItemService.search("magentoItemId.eq:" + magentoItemId + "___itemType.eq:" + ItemType.SUNGLASS);
        }
        populateAndSavePidsInEntities(powerWisePid, orderItems, null, lineItem.getPowerType());
    }

    protected void pushUpdateToWmsProcessing(OmsOrderEvent omsOrderEvent) throws ApplicationException {
        for (ShipmentDto shipmentDto : omsOrderEvent.getShipmentDtoList()) {
            if(!shipmentDto.getShipmentStatus().equals(ShipmentStatus.OMS_REASSIGNED)) {
                WmsOrderEvent wmsOrderEvent = updatePowerMapper.
                        getWmsOrderEventForPaymentUpdate(omsOrderEvent.getOrderDto(), ShipmentEvent.UPDATE_POWER, shipmentDto);
                logger.info("[pushUpdateToWmsProcessing] pushing to orderEventWmsProducer with object: {}", wmsOrderEvent);
                orderEventWmsProducer.sendMessage(wmsOrderEvent);
            }
        }
    }

    private List<PowerWisePidLineItemRequest> populateLineItems(OmsOrderEvent omsOrderEvent) {
        List<PowerWisePidLineItemRequest> powerWisePidLineItemRequests = new ArrayList<>();

        for(LineItemPayloadEvent payload:omsOrderEvent.getLineItemPayloadEventList()){
            PowerWisePidLineItemRequest lineItem = new PowerWisePidLineItemRequest();
            lineItem.setItemType(payload.getItemType().name());
            lineItem.setMagentoItemId(payload.getLineItemId());
            lineItem.setParentLineItemId(payload.getParentLineItemId());
            lineItem.setCoatingOId(payload.getOid());
            lineItem.setQuantity(payload.getQuantity());
            lineItem.setPowerType(payload.getPowerType());
            lineItem.setType(payload.getType());
            lineItem.setPatientName(payload.getPatientName());
            lineItem.setNotes(payload.getNotes());
            lineItem.setIsPower(payload.getIsPower());
            lineItem.setPrescriptionUrl(payload.getPrescriptionUrl());
            lineItem.setIsLocalFittingOrder(payload.getIsLocalFittingRequired());
            lineItem.setProductId(Long.valueOf(payload.getSku()));
            lineItem.setPackageName(payload.getProductName());
            if(!payload.isLineItemPropertiesNull())
                populateLineItemProperties(lineItem,payload);

            powerWisePidLineItemRequests.add(lineItem);
        }
        return powerWisePidLineItemRequests;
    }

    private void populateLineItemProperties(PowerWisePidLineItemRequest lineItem, LineItemPayloadEvent payload) {
        LineItemEyePowerRequest leftEyePower = Objects.isNull(payload.getLeft()) ? null : populatePowerDetails(payload.getLeft());
        LineItemEyePowerRequest rightEyePower = Objects.isNull(payload.getRight())? null : populatePowerDetails(payload.getRight());

        lineItem.setLeftEyePower(leftEyePower);
        lineItem.setRightEyePower(rightEyePower);

        LineItemRelationShipRequest relationship = payload.isRelationShipNull() ? null : populateRelationShipDetails(payload);
        lineItem.setRelationship(relationship);
    }

    private LineItemRelationShipRequest populateRelationShipDetails(LineItemPayloadEvent payload) {
        LineItemRelationShipRequest relationShip = new LineItemRelationShipRequest();
        relationShip.setType(payload.getRelationType());
        relationShip.setName(payload.getRelationName());
        relationShip.setTelephone(payload.getRelationTelephone());
        relationShip.setPhoneCode(payload.getRelationPhoneCode());
        relationShip.setGender(payload.getRelationGender());
        relationShip.setYearOfBirth(payload.getRelationYearOfBirth());

        return relationShip;
    }

    private LineItemEyePowerRequest populatePowerDetails(OrderItemPowerDto payload) {
        LineItemEyePowerRequest lineItemEyePowerRequest = new LineItemEyePowerRequest();
        lineItemEyePowerRequest.setSku(payload.getProductId() != null ? payload.getProductId().toString() : null);
        lineItemEyePowerRequest.setSph(StringUtils.isNotBlank(payload.getSph()) ? payload.getSph() : StringUtils.EMPTY);
        lineItemEyePowerRequest.setCyl(StringUtils.isNotBlank(payload.getCyl()) ? payload.getCyl() : StringUtils.EMPTY);
        lineItemEyePowerRequest.setAxis(StringUtils.isNotBlank(payload.getAxis()) ? payload.getAxis() : null);
        lineItemEyePowerRequest.setPd(StringUtils.isNotBlank(payload.getPd()) ? payload.getPd() : StringUtils.EMPTY);
        lineItemEyePowerRequest.setEffectiveDia(StringUtils.isNotBlank(payload.getEffectiveDia()) ? payload.getEffectiveDia() : StringUtils.EMPTY);
        lineItemEyePowerRequest.setNearPD(StringUtils.isNotBlank(payload.getNearPD()) ? payload.getNearPD() : StringUtils.EMPTY);
        lineItemEyePowerRequest.setTopDistance(StringUtils.isNotBlank(payload.getTopDistance()) ? payload.getTopDistance() : StringUtils.EMPTY);
        lineItemEyePowerRequest.setBottomDistance(StringUtils.isNotBlank(payload.getBottomDistance()) ? payload.getBottomDistance() : StringUtils.EMPTY);
        lineItemEyePowerRequest.setAp(payload.getAp());
        lineItemEyePowerRequest.setLensHeight(payload.getLensHeight());
        lineItemEyePowerRequest.setLensWidth(payload.getLensWidth());
        lineItemEyePowerRequest.setEdgeDistance(payload.getEdgeDistance());
        lineItemEyePowerRequest.setTint(payload.getTint());
        lineItemEyePowerRequest.setColor(payload.getColor());
        lineItemEyePowerRequest.setBaseCurve(payload.getBaseCurve());
        lineItemEyePowerRequest.setBoxQty(payload.getBoxQty());
        lineItemEyePowerRequest.setSpvd(payload.getSpvd());

        return lineItemEyePowerRequest;
    }

    private Set<ItemType> getSupportedItemTypeForPowerUpdate() {
        return new HashSet<>(
                Arrays.asList(
                    ItemType.READING_EYEGLASS,
                    ItemType.LENS_ONLY,
                    ItemType.EYEFRAME,
                    ItemType.SUNGLASS,
                    ItemType.CONTACT_LENS,
                    ItemType.SUNGLASSES,
                    ItemType.PRESCRIPTION_LENS,
                    ItemType.SMART_GLASSES
            )
        );
    }

}
