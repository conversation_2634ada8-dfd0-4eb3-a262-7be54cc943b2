package com.lenskart.oms.strategy.impl;

import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.dto.OrderAddressDto;
import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.enums.AddressType;
import com.lenskart.oms.enums.OrderEventType;
import com.lenskart.oms.enums.ShipmentEvent;
import com.lenskart.oms.enums.ShipmentStatus;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.mapper.OmsOrderEventMapper;
import com.lenskart.oms.mapper.UpdateAddressTransformer;
import com.lenskart.oms.request.OmsOrderEvent;
import com.lenskart.oms.request.WmsOrderEvent;
import com.lenskart.oms.service.OrderAddressService;
import com.lenskart.oms.strategy.BaseOrderEventStrategy;
import com.lenskart.oms.utils.OrderEventClientCallbackUtil;
import com.lenskart.oms.validators.UpdateAddressEventValidator;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.lenskart.oms.constants.ApplicationConstants.JUNO_STORE_ID;

@Component
@Setter(onMethod__ = {@Autowired})
public class UpdateAddressStrategy extends BaseOrderEventStrategy {

    private OrderAddressService orderAddressService;
    private OmsOrderEventMapper omsOrderEventMapper;
    private UpdateAddressTransformer updateAddressTransformer;
    private OrderEventClientCallbackUtil orderEventClientCallbackUtil;
    private UpdateAddressEventValidator updateAddressEventValidator;

    @Override
    protected OrderEventType supportedOrderEvents() {
        return OrderEventType.UPDATE_ADDRESS;
    }

    @Override
    protected void preExecute(OmsOrderEvent orderRequest) throws ApplicationException {
        updateAddressEventValidator.validateUpdateAddressEvent(orderRequest);
    }

    @Override
    protected void execute(OmsOrderEvent orderRequest) throws ApplicationException {
        if (orderRequest.getShipmentDtoList().get(0).getBillingAddress() != null && orderRequest.getShipmentDtoList().get(0).getBillingAddress().getAddressType() != null)
            processUpdateAddressEvent(orderRequest.getShipmentDtoList().get(0).getBillingAddress(), orderRequest.getIncrementId());

        if (orderRequest.getShipmentDtoList().get(0).getShippingAddress() != null && orderRequest.getShipmentDtoList().get(0).getShippingAddress().getAddressType() != null)
            processUpdateAddressEvent(orderRequest.getShipmentDtoList().get(0).getShippingAddress(), orderRequest.getIncrementId());
    }

    @Override
    protected void postExecute(OmsOrderEvent orderRequest) throws ApplicationException {
        OrderDto orderDto = orderService.findByIncrementId(orderRequest.getIncrementId());
        ShipmentDto shipmentDto = shipmentService.findById(orderDto.getOrderItems().get(0).getShipmentId());
        checkValidShipmentAndPublishToWmsEventProcessor(shipmentDto, orderDto, orderRequest.getEventType());
        triggerAddressCallbacks(orderRequest, orderDto);
    }

    @Override
    protected void pushUpdateToWmsProcessing(OmsOrderEvent omsOrderEvent) throws ApplicationException {
        for (ShipmentDto shipmentDto: omsOrderEvent.getShipmentDtoList()) {
            if(!shipmentDto.getShipmentStatus().equals(ShipmentStatus.OMS_REASSIGNED)) {
                WmsOrderEvent wmsOrderEvent = updateAddressTransformer.getWmsOrderEventFromShipmentDto(shipmentDto, omsOrderEvent.getOrderDto(), ShipmentEvent.UPDATE_ADDRESS);
                orderEventWmsProducer.sendMessage(wmsOrderEvent);
            }
        }
    }

    private void checkValidShipmentAndPublishToWmsEventProcessor(ShipmentDto shipmentDto, OrderDto orderDto, OrderEventType orderEventType) throws ApplicationException {
        OmsOrderEvent omsOrderEvent = omsOrderEventMapper.getOmsOrderEventFromShipmentDto(shipmentDto, orderDto, orderEventType);
        validateAndSyncOrder(omsOrderEvent);
    }


    private void triggerAddressCallbacks(OmsOrderEvent orderRequest, OrderDto orderDto) throws ApplicationException {
        try {
            logger.info("Calling order address update API of client incrementId: {}",orderDto.getIncrementId());
            if (orderDto.getStoreId() != null && orderDto.getStoreId().intValue() == JUNO_STORE_ID) {
                orderEventClientCallbackUtil.updateAddressCallback(ApplicationConstants.JJ,
                        Math.toIntExact(orderRequest.getIncrementId()),
                        orderEventClientCallbackUtil.getClientAddressUpdateRequest(orderRequest.getIncrementId(), orderRequest)
                );
            }
        } catch (Exception e) {
            logger.error("Update address callbacks failed due to {}",e.getMessage());
            throw new ApplicationException("Update address callbacks failed due to " + e.getMessage(), null);
        }
    }

    private void processUpdateAddressEvent(OrderAddressDto orderAddressDto, Long incrementId) throws ApplicationException {
        try{
            OrderDto order = orderService.findByIncrementId(incrementId);
            for(OrderItemDto orderItem : order.getOrderItems()){
                ShipmentDto shipment = shipmentService.findById(orderItem.getShipmentId());
                if(orderAddressDto.getAddressType() == AddressType.SHIPPING) shipment.setShippingAddress(orderAddressDto);
                else if(orderAddressDto.getAddressType() == AddressType.BILLING) shipment.setBillingAddress(orderAddressDto);
                if(shipment.getShipmentStatus().ordinal() < ShipmentStatus.COMPLETE_SHIPPED.ordinal()) {
                    shipmentService.update(shipment, shipment.getId());
                }
            }
            orderAddressService.save(orderAddressDto);
        }catch (Exception e){
            logger.error("[UpdateAddressStrategy -> processUpdateAddressEvent} Update Address order event failed for incrementId with exception {}", e.getMessage());
            throw new ApplicationException("Update Address order event while persisting it", null);
        }
    }
}
