package com.lenskart.oms.strategy.impl;

import com.lenskart.nexs.ims.request.UpdateStocksRequestV2;
import com.lenskart.nexs.ims.response.ItemStockUpdateResponseV2;
import com.lenskart.nexs.ims.response.UpdateStocksResponseV2;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.constants.KafkaConstants;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.entity.Shipment;
import com.lenskart.oms.enums.*;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.producer.CommonKafkaProducer;
import com.lenskart.oms.request.OtcShipmentEvent;
import com.lenskart.oms.request.ShipmentItemUpdate;
import com.lenskart.oms.request.ShipmentUpdateEvent;
import com.lenskart.oms.strategy.BaseOtcShipmentEventStrategy;
import com.lenskart.oms.utils.ObjectHelper;
import lombok.AccessLevel;
import lombok.Setter;
import org.aspectj.weaver.ast.Or;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.lenskart.oms.constants.KafkaConstants.MESSAGE_IDEMPOTENCY_KEY;

@Component
@Setter(onMethod__ = {@Autowired})
public class MarkPickedVirtualShipmentStrategy extends BaseOtcShipmentEventStrategy {

    private CommonKafkaProducer commonKafkaProducer;

    @Override
    protected OtcShipmentEventType supportedOrderEvents() {
        return OtcShipmentEventType.VIRTUAL_PICKED;
    }

    @Override
    protected void preExecute(OtcShipmentEvent otcShipmentEvent) throws ApplicationException {
        validateVirtualShipment(otcShipmentEvent);
    }

    @Override
    protected void execute(OtcShipmentEvent otcShipmentEvent) throws Exception {
        logger.info("[MarkPickedVirtualShipmentStrategy] ShipmentEvent {}", otcShipmentEvent);
        ShipmentDto shipmentDto = shipmentService.findById(otcShipmentEvent.getShipmentId());
        List<OrderItemDto> b2bOrderItemList  = shipmentDto.getOrderItems();
        for(OrderItemDto orderItemDto : b2bOrderItemList) {
            OrderItemDto persistedB2bOrderItemDto = orderItemService.findById(orderItemDto.getId());
            orderItemDto.setItemBarcode(persistedB2bOrderItemDto.getItemBarcode());
        }

        UpdateStocksRequestV2 stocksRequestV2 = getUpdateStocksRequestV2(shipmentDto, ApplicationConstants.IMS_OTC_PICKED_OPERATION);
        UpdateStocksResponseV2 updateStocksResponseV2 = imsConnector.updateBarcodeStatus(stocksRequestV2);

        for (ItemStockUpdateResponseV2 responseV2 : updateStocksResponseV2.getItemStockUpdateResponseV2List()) {
            if (!responseV2.isSuccess()) {
                logger.info("Update barcode failed for shipmentDto {} and barcode {}", shipmentDto, responseV2.getBarcode());
                throw new ApplicationException("Update barcode failed for OTC Order");
            }
        }

        for(OrderItemDto orderItemDto : b2bOrderItemList) {
            orderItemDto.setItemStatus(OrderItemStatus.PICKED);
            orderItemDto.setItemSubStatus(OrderItemSubStatus.PICKED);
            orderItemService.update(orderItemDto, orderItemDto.getId());
        }

        shipmentDto.setWmsShippingPackageId("SH" + shipmentDto.getFacility() + shipmentDto.getId());
        shipmentDto.setShipmentStatus(ShipmentStatus.PROCESSING);
        shipmentDto.setShipmentSubStatus(ShipmentSubStatus.PROCESSING);
        shipmentService.update(shipmentDto,shipmentDto.getId());
    }

    @Override
    protected void postExecute(OtcShipmentEvent otcShipmentEvent) throws Exception {
        ShipmentDto shipmentDto = shipmentService.findById(otcShipmentEvent.getShipmentId());
//        ShipmentUpdateEvent shipmentUpdateEvent = new ShipmentUpdateEvent();
//        shipmentUpdateEvent.setShipmentEvent(ShipmentEvent.MARK_SHIPMENT_PICKED);
//        shipmentUpdateEvent.setWmsOrderCode(shipmentDto.getWmsOrderCode());
//        List<ShipmentItemUpdate> shipmentItemUpdates = new ArrayList<>();
//        ShipmentItemUpdate shipmentItemUpdate = new ShipmentItemUpdate();
//        shipmentItemUpdate.setUnicomShipmentStatus("PICKED");
//        shipmentItemUpdate.setOrderOpsShipmentStatus("PICKED");
//        shipmentItemUpdates.add(shipmentItemUpdate);
//        shipmentUpdateEvent.setOrderItemList(shipmentItemUpdates);
        //persistTrackingAndPublishToOms(shipmentUpdateEvent);
//        Map<String, String> kafkaHeaders = new HashMap<>();
        //kafkaHeaders.put(MESSAGE_IDEMPOTENCY_KEY, shipmentUpdateEvent.getShipmentEvent().name() + "_" + shipmentUpdateEvent.getWmsOrderCode());
        //shipmentEventOmsProducer.sendMessage(shipmentUpdateEvent, omsShipmentBackSyncTopic, kafkaHeaders, shipmentUpdateEvent.getWmsOrderCode());
        commonKafkaProducer.sendMessage(
                KafkaConstants.OMS_OTC_ORDER_EVENTS_PROCESS_TOPIC,
                String.valueOf(shipmentDto.getOrderItems().get(0).getOrderId()),
                ObjectHelper.convertToString(new OtcShipmentEvent(OtcShipmentEventType.VIRTUAL_INVOICED, shipmentDto.getId())),
                String.valueOf(shipmentDto.getId())
        );
    }

    private void validateVirtualShipment(OtcShipmentEvent otcShipmentEvent){
        if(ObjectUtils.isEmpty(otcShipmentEvent) || otcShipmentEvent.getShipmentId() == null) {
            throw new ApplicationException("virtual shipment payload is not correct");
        }
        ShipmentDto shipmentDto = checkIfInPending(otcShipmentEvent);
        if(ShipmentStatus.CREATED != shipmentDto.getShipmentStatus()){
            throw new ApplicationException("virtual Shipment status is not correct");
        }

    }

    private ShipmentDto checkIfInPending(OtcShipmentEvent otcShipmentEvent) {
        ShipmentDto shipmentDto =  shipmentService.findById(otcShipmentEvent.getShipmentId());
        if(ShipmentStatus.PENDING == shipmentDto.getShipmentStatus()){
            shipmentDto.setShipmentStatus(ShipmentStatus.CREATED);
            shipmentService.update(shipmentDto,shipmentDto.getId());
        }

        return shipmentDto;
    }
}
