package com.lenskart.oms.strategy.impl;

import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.dto.ShipmentTimelineDto;
import com.lenskart.oms.enums.EventToOperationMap;
import com.lenskart.oms.enums.OrderItemSubStatus;
import com.lenskart.oms.enums.ShipmentEvent;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.model.Action;
import com.lenskart.oms.request.ShipmentItemUpdate;
import com.lenskart.oms.request.ShipmentUpdateEvent;
import com.lenskart.oms.strategy.BaseShipmentEventStrategy;
import com.lenskart.oms.validators.ShipmentEventValidator;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Setter(onMethod__ = {@Autowired})
public class CreateShipmentAwbStrategy extends BaseShipmentEventStrategy {
    private ShipmentEventValidator shipmentEventValidator;

    @Override
    protected ShipmentEvent supportedOrderEvents() {
        return ShipmentEvent.CREATE_SHIPMENT_AWB;
    }

    @Override
    protected void preExecute(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        shipmentEventValidator.validateShipmentAwbEvent(shipmentUpdateEvent);
    }

    @Override
    protected void execute(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        if (CollectionUtils.isEmpty(shipmentUpdateEvent.getOrderItemList()))
            return;

        Set<Long> shipmentIds = new HashSet<>();
        Action transitionAction = null;
        List<Long> orderItemIds = shipmentUpdateEvent.getOrderItemList().stream().map(ShipmentItemUpdate::getOrderItemId).collect(Collectors.toList());
        List<OrderItemDto> orderItemDtoList = orderItemService.search("id.in:" + StringUtils.join(orderItemIds, ","));
        Map<Long, ShipmentItemUpdate> shipmentItemsMap = shipmentUpdateEvent.getOrderItemList().stream().collect(Collectors.toMap(ShipmentItemUpdate::getOrderItemId, Function.identity(), (x1, x2) ->  x1));
        for (OrderItemDto currentItemDto : orderItemDtoList) {
            ShipmentItemUpdate item = shipmentItemsMap.get(currentItemDto.getId());
            transitionAction = omsTransitionUtil.getTransitionActionByOperationAndStatus(
                    EventToOperationMap.valueOf(shipmentUpdateEvent.getShipmentEvent().name()).getOperation(), currentItemDto.getItemStatus()
            );
            shipmentIds.add(currentItemDto.getShipmentId());
            if (currentItemDto != null) {
                logger.info("Updating order item status from {} to {} for orderId - {}", currentItemDto.getItemStatus(), transitionAction.getOrderStatus().getOrderStatus(), currentItemDto.getId());
                currentItemDto.setItemStatus(transitionAction.getItemStatus().getItemStatus());
                currentItemDto.setItemSubStatus(transitionAction.getItemStatus().getItemSubStatus());
                orderItemService.save(currentItemDto);
            }
            List<ShipmentTimelineDto> timelineDtoList = shipmentTimelineService.findAssociatedShipmentTimelines(currentItemDto);
            for (ShipmentTimelineDto timelineDto : timelineDtoList) {
                timelineDto.setPackedTime(item.getEventTime() != null ? item.getEventTime() : new Date());
                shipmentTimelineService.save(timelineDto);
                logger.info("Updating PackedTime - {} For OrderItemId - {} & ShipmentId - {}", timelineDto.getFulfilledTime(), timelineDto.getOrderItemId(), timelineDto.getShipmentId());
            }
            item.setOrderItemId(currentItemDto.getUwItemId());
        }
        List<ShipmentDto> shipmentDtoList = shipmentService.search("id.in:" + StringUtils.join(shipmentIds, ","));
        if (!CollectionUtils.isEmpty(shipmentDtoList)) {
            for (ShipmentDto shipmentDto : shipmentDtoList) {
                logger.info("Updating shipment status from {} to {} for orderId - {}", shipmentDto.getShipmentStatus(), transitionAction.getOrderStatus().getOrderStatus(), shipmentDto.getId());
                shipmentDto.setAwbNumber(shipmentUpdateEvent.getEntityId());
                shipmentDto.setCourierCode(shipmentUpdateEvent.getCourierCode());
                shipmentDto.setShipmentStatus(transitionAction.getShipmentStatus().getShipmentStatus());
                shipmentService.save(shipmentDto);
            }
        }
    }
}
