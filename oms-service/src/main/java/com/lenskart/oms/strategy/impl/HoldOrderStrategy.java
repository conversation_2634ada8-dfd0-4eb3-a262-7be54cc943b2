package com.lenskart.oms.strategy.impl;

import com.lenskart.oms.dto.OnHoldMasterDto;
import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.enums.OrderEventType;
import com.lenskart.oms.enums.ShipmentEvent;
import com.lenskart.oms.enums.ShipmentStatus;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.request.OmsOrderEvent;
import com.lenskart.oms.request.WmsOrderEvent;
import com.lenskart.oms.service.OnHoldMasterService;
import com.lenskart.oms.strategy.BaseOrderEventStrategy;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Setter(onMethod__ = {@Autowired})
public class HoldOrderStrategy extends BaseOrderEventStrategy {

    private OnHoldMasterService onHoldMasterService;

    @Override
    protected OrderEventType supportedOrderEvents() {
        return OrderEventType.HOLD_ORDER;
    }

    @Override
    protected void preExecute(OmsOrderEvent orderRequest) throws ApplicationException {
        if (Objects.isNull(orderRequest.getOrderDto())) {
            throw new ApplicationException("OrderDto object can not be null  for incrementId: " + orderRequest.getIncrementId());
        }
        if (Boolean.FALSE.equals(orderRequest.getOrderDto().getIsOnHold())) {
            throw new ApplicationException("IsOnHold can not be false  for incrementId: " + orderRequest.getIncrementId());
        }
        if (orderRequest.getOrderDto().getOnHoldReasonId() == null) {
            throw new ApplicationException("OnHoldReasonId can not be null for incrementId: " + orderRequest.getIncrementId());
        }
    }

    @Override
    protected void execute(OmsOrderEvent orderRequest) throws ApplicationException {

        OrderDto orderDto = getOrderDto(orderRequest.getIncrementId());
        List<ShipmentDto> shipmentDtoList = getShipmentDtos(orderDto);

        orderDto.setIsOnHold(orderRequest.getOrderDto().getIsOnHold());
        orderDto.setOnHoldReasonId(orderRequest.getOrderDto().getOnHoldReasonId());
        orderService.update(orderDto, orderDto.getId());

        orderRequest.setShipmentDtoList(shipmentDtoList);
        orderRequest.setOrderDto(orderDto);
    }

    @Override
    protected void postExecute(OmsOrderEvent orderRequest) throws ApplicationException {
        validateAndSyncOrder(orderRequest);
    }

    @Override
    protected void pushUpdateToWmsProcessing(OmsOrderEvent omsOrderEvent) throws ApplicationException {
        List<ShipmentDto> shipmentDtoList = omsOrderEvent.getShipmentDtoList();
        for (ShipmentDto shipmentDto : shipmentDtoList) {
            if(!shipmentDto.getShipmentStatus().equals(ShipmentStatus.OMS_REASSIGNED)) {
                WmsOrderEvent wmsOrderEvent = new WmsOrderEvent();
                wmsOrderEvent.setShipmentEvent(ShipmentEvent.HOLD);
                wmsOrderEvent.setOrderDto(omsOrderEvent.getOrderDto());
                wmsOrderEvent.setShipmentDto(shipmentDto);
                orderEventWmsProducer.sendMessage(wmsOrderEvent);
            }
        }
    }
}
