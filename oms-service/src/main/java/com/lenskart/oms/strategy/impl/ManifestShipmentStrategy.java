package com.lenskart.oms.strategy.impl;

import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.dto.ShipmentTimelineDto;
import com.lenskart.oms.enums.*;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.model.Action;
import com.lenskart.oms.request.ShipmentItemUpdate;
import com.lenskart.oms.request.ShipmentUpdateEvent;
import com.lenskart.oms.strategy.BaseShipmentEventStrategy;
import com.lenskart.oms.validators.ShipmentEventValidator;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Setter(onMethod__ = {@Autowired})
public class ManifestShipmentStrategy extends BaseShipmentEventStrategy {

    private ShipmentEventValidator shipmentEventValidator;

    @Override
    protected ShipmentEvent supportedOrderEvents() {
        return ShipmentEvent.MARK_SHIPMENT_MANIFEST;
    }

    @Override
    protected void preExecute(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        shipmentEventValidator.validateManifestEvent(shipmentUpdateEvent);
    }

    @Override
    protected void execute(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        if (CollectionUtils.isEmpty(shipmentUpdateEvent.getOrderItemList()))
            return;

        Long orderId = null;
        Action transitionAction = null;
        Set<Long> shipmentIds = new HashSet<>();
        List<Long> orderItemIds = shipmentUpdateEvent.getOrderItemList().stream().map(ShipmentItemUpdate::getOrderItemId).collect(Collectors.toList());
        List<OrderItemDto> orderItemDtoList = orderItemService.search("id.in:" + StringUtils.join(orderItemIds, ","));
        Map<Long, ShipmentItemUpdate> shipmentItemsMap = shipmentUpdateEvent.getOrderItemList().stream().collect(Collectors.toMap(ShipmentItemUpdate::getOrderItemId, Function.identity(), (x1, x2) ->  x1));
        for (OrderItemDto currentItemDto : orderItemDtoList) {
            ShipmentItemUpdate item = shipmentItemsMap.get(currentItemDto.getId());
            transitionAction = omsTransitionUtil.getTransitionActionByOperationAndStatus(
                    EventToOperationMap.valueOf(shipmentUpdateEvent.getShipmentEvent().name()).getOperation(), currentItemDto.getItemStatus()
            );
            orderId = currentItemDto.getOrderId();
            shipmentIds.add(currentItemDto.getShipmentId());
            if (currentItemDto != null) {
                logger.info("Updating order item status from {} to {} for orderId - {}", currentItemDto.getItemStatus(), transitionAction.getOrderStatus().getOrderStatus(), currentItemDto.getId());
                currentItemDto.setItemStatus(transitionAction.getItemStatus().getItemStatus());
                currentItemDto.setItemSubStatus(transitionAction.getItemStatus().getItemSubStatus());
                orderItemService.save(currentItemDto);
            }
            updateShipmentTimeline(item, currentItemDto);
            item.setOrderItemId(currentItemDto.getUwItemId());
        }

        UpdateShipmentData(shipmentUpdateEvent, transitionAction, shipmentIds);
        updateOrderDataIfRequired(orderId, transitionAction);
    }

    private void updateOrderDataIfRequired(Long orderId, Action transitionAction) {
        OrderDto orderDto = orderService.findById(orderId);
        if (isOrderStatusUpdateRequired(transitionAction, orderDto)) {
            logger.info("Updating order status from {} to {} for orderId - {}", orderDto.getOrderItems(), transitionAction.getOrderStatus().getOrderStatus(), orderDto.getId());
            orderDto.setOrderStatus(transitionAction.getOrderStatus().getOrderStatus());
            orderDto.setOrderSubStatus(transitionAction.getOrderStatus().getOrderSubStatus());
            orderService.save(orderDto);
        }
    }

    private void UpdateShipmentData(ShipmentUpdateEvent shipmentUpdateEvent, Action transitionAction, Set<Long> shipmentIds) {
        List<ShipmentDto> shipmentDtoList = shipmentService.search("id.in:" + StringUtils.join(shipmentIds, ","));
        if (!CollectionUtils.isEmpty(shipmentDtoList)) {
            for (ShipmentDto shipmentDto : shipmentDtoList) {
                logger.info("Updating Manifest Number - {}, Shipment status from {} to {} for ShipmentId - {}", shipmentUpdateEvent.getEntityId(), shipmentDto.getShipmentStatus(), transitionAction.getOrderStatus().getOrderStatus(), shipmentDto.getId());
                shipmentDto.setManifestNumber(shipmentUpdateEvent.getEntityId());
                shipmentDto.setShipmentStatus(transitionAction.getShipmentStatus().getShipmentStatus());
                shipmentDto.setShipmentSubStatus(transitionAction.getShipmentStatus().getShipmentSubStatus());
                shipmentService.save(shipmentDto);
            }
        }
    }

    private void updateShipmentTimeline(ShipmentItemUpdate item, OrderItemDto currentItemDto) {
        List<ShipmentTimelineDto> timelineDtoList = shipmentTimelineService.findAssociatedShipmentTimelines(currentItemDto);
        for (ShipmentTimelineDto timelineDto : timelineDtoList) {
            timelineDto.setManifestTime(item.getEventTime() != null ? item.getEventTime() : new Date());
            shipmentTimelineService.save(timelineDto);
            logger.info("Updating Manifest Time - {} For OrderItemId - {} & ShipmentId - {}", timelineDto.getManifestTime(), timelineDto.getOrderItemId(), timelineDto.getShipmentId());
        }
    }
}
