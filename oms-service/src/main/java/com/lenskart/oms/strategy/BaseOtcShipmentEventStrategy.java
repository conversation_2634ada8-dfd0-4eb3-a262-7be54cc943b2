package com.lenskart.oms.strategy;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.ims.request.StockRequestV2;
import com.lenskart.nexs.ims.request.UpdateStocksRequestV2;
import com.lenskart.oms.connector.ImsConnector;
import com.lenskart.oms.connector.WmConnector;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.OrderItemMetaDataDto;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.enums.*;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.exception.DuplicateEventException;
import com.lenskart.oms.mapper.MailContentMapper;
import com.lenskart.oms.producer.ShipmentEventOmsProducer;
import com.lenskart.oms.request.MarkOrderCompleteRequest;
import com.lenskart.oms.request.OtcShipmentEvent;
import com.lenskart.oms.request.ShipmentUpdateEvent;
import com.lenskart.oms.service.*;
import com.lenskart.oms.utils.OmsCommonUtil;
import com.lenskart.oms.utils.OmsTransitionUtil;
import com.newrelic.api.agent.Trace;
import io.micrometer.core.annotation.Timed;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.aspectj.weaver.ast.Or;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.*;

import static com.lenskart.oms.constants.KafkaConstants.MESSAGE_IDEMPOTENCY_KEY;

@Setter(onMethod__ = {@Autowired})
public abstract class BaseOtcShipmentEventStrategy {

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");
    private static final SimpleDateFormat TIME_FORMAT = new SimpleDateFormat("HH:mm:ss");

    @CustomLogger
    @Setter(AccessLevel.NONE)
    protected Logger logger;

    protected OrderBackSyncTrackingService orderBackSyncTrackingService;
    protected ShipmentEventOmsProducer shipmentEventOmsProducer;
    protected ShipmentService shipmentService;
    protected ImsConnector imsConnector;
    protected OmsTransitionUtil omsTransitionUtil;
    protected ShipmentTimelineService shipmentTimelineService;
    protected OrderItemService orderItemService;
    protected MailContentMapper mailContentMapper;
    protected WmConnector wmConnector;
    protected OmsCommonUtil omsCommonUtil;
    protected OrderItemMetaService orderItemMetaService;

    @Setter(AccessLevel.NONE)
    @Value("${oms.shipment.backsync.topic}")
    protected String omsShipmentBackSyncTopic;
    @Setter(AccessLevel.NONE)
    @Value("${disable.vsm.callback:false}")
    protected boolean disableVsmCallback;
    @Setter(AccessLevel.NONE)
    @Value("#{'${carryBag.pids}'.split(',')}")
    protected Set<Long> carryBagPids;
    @Setter(AccessLevel.NONE)
    @Value("#{'${store.fitting.facilities:LKST1322,LKST299}'.split(',')}")
    protected Set<String> storeFittingFacilities;
    @Setter(AccessLevel.NONE)
    @Value("#{'${store.fitting.pids:96506,103509}'.split(',')}")
    protected Set<Long> storeFittingPids;

    protected abstract OtcShipmentEventType supportedOrderEvents();

    protected abstract void preExecute(OtcShipmentEvent otcShipmentEvent) throws Exception;

    protected abstract void execute(OtcShipmentEvent otcShipmentEvent) throws Exception;

    protected abstract void postExecute(OtcShipmentEvent otcShipmentEvent) throws Exception;

    @Transactional(rollbackFor = Exception.class)
    @Timed
    @Trace
    public void doExecute(OtcShipmentEvent otcShipmentEvent) throws Exception {
        logger.info("[BaseOtcShipmentEventStrategy -> doExecute] inside {} strategy for request {}", supportedOrderEvents(), otcShipmentEvent);
        try {
            preExecute(otcShipmentEvent);
            execute(otcShipmentEvent);
            postExecute(otcShipmentEvent);
        } catch (DuplicateEventException e) {
            logger.error("Duplicate event exception for shipmentId {} error {}", otcShipmentEvent.getShipmentId(), e);
        }

    }

    protected void persistTrackingAndPublishToOms(ShipmentUpdateEvent shipmentUpdateEvent) {
        orderBackSyncTrackingService.persistRequestInTracking(BackSyncEventName.valueOf(shipmentUpdateEvent.getShipmentEvent().name()),
                shipmentUpdateEvent.getWmsOrderCode(), BackSyncSystem.VSM);
        Map<String, String> kafkaHeaders = new HashMap<>();
        kafkaHeaders.put(MESSAGE_IDEMPOTENCY_KEY, shipmentUpdateEvent.getShipmentEvent().name() + "_" + shipmentUpdateEvent.getWmsOrderCode());
        shipmentEventOmsProducer.sendMessage(shipmentUpdateEvent, omsShipmentBackSyncTopic, kafkaHeaders, shipmentUpdateEvent.getWmsOrderCode());
    }

    protected UpdateStocksRequestV2 getUpdateStocksRequestV2(ShipmentDto shipmentDto, String operation) {
        UpdateStocksRequestV2 stocksRequestV2 = new UpdateStocksRequestV2();
        List<StockRequestV2> stockRequestV2List = new ArrayList<>();
        for (OrderItemDto orderItemDto : shipmentDto.getOrderItems()) {
            if((!StringUtils.hasLength(orderItemDto.getItemBarcode())
                    && isValidNullBarcodeLoyaltyAndAccessoryItem(orderItemDto)) ||
                    (!StringUtils.hasLength(orderItemDto.getItemBarcode())
                            && isStoreFittingLensOnlyItems(shipmentDto, orderItemDto))
            || isCentralFacilityCodePresent(orderItemDto)) {
                logger.info("skipping ims update stock for wmsOrderCode {} and item {}", shipmentDto.getWmsOrderCode(), orderItemDto.getId());
                continue;
            }
            StockRequestV2 requestV2 = new StockRequestV2();
            requestV2.setPid(Math.toIntExact(orderItemDto.getProductId()));
            requestV2.setFacility(shipmentDto.getFacility());
            requestV2.setBarcode(orderItemDto.getItemBarcode());
            requestV2.setUpdatedBy(ApplicationConstants.DEFAULT_OMS_USER);
            requestV2.setLegalOwner(shipmentDto.getLegalOwner());
            stockRequestV2List.add(requestV2);
        }
        stocksRequestV2.setStockRequestV2List(stockRequestV2List);
        stocksRequestV2.setOperation(operation);
        return stocksRequestV2;
    }

    protected UpdateStocksRequestV2 getUpdateStocksRequestV2OTCLoyalty(ShipmentDto shipmentDto, String operation) {
        UpdateStocksRequestV2 stocksRequestV2 = new UpdateStocksRequestV2();
        List<StockRequestV2> stockRequestV2List = new ArrayList<>();
        for (OrderItemDto orderItemDto : shipmentDto.getOrderItems()) {
            StockRequestV2 requestV2 = new StockRequestV2();
            requestV2.setPid(Math.toIntExact(orderItemDto.getProductId()));
            requestV2.setFacility(shipmentDto.getFacility());
            requestV2.setBarcode(orderItemDto.getItemBarcode());
            requestV2.setUpdatedBy(ApplicationConstants.DEFAULT_OMS_USER);
            requestV2.setLegalOwner(shipmentDto.getLegalOwner());
            stockRequestV2List.add(requestV2);
        }
        stocksRequestV2.setStockRequestV2List(stockRequestV2List);
        stocksRequestV2.setOperation(operation);
        return stocksRequestV2;
    }

    protected boolean isCentralFacilityCodePresent(OrderItemDto orderItemDto) {
        Optional<OrderItemMetaDataDto> centralFacilityCode = orderItemDto.getOrderItemMetaData().stream().filter(o -> o.getEntityKey().equals("CENTRAL_FACILITY_CODE")).findFirst();
        return centralFacilityCode.isPresent()
                && StringUtils.hasLength(centralFacilityCode.get().getEntityValue());
    }

    protected boolean isStoreFittingLensOnlyItems(ShipmentDto shipmentDto, OrderItemDto orderItemDto) {
        return ProductDeliveryType.OTC.equals(orderItemDto.getProductDeliveryType())
                && storeFittingFacilities.contains(shipmentDto.getFacility())
                && storeFittingPids.contains(orderItemDto.getProductId());
    }

    protected MarkOrderCompleteRequest getMarkOrderCompleteRequest(ShipmentDto shipmentDto, OrderDto orderDto) {
        MarkOrderCompleteRequest markOrderCompleteRequest = new MarkOrderCompleteRequest();
        markOrderCompleteRequest.setIncrementId(String.valueOf(orderDto.getIncrementId()));
        markOrderCompleteRequest.setOrderId(shipmentDto.getWmsOrderCode());
        markOrderCompleteRequest.setShippingPackageId(shipmentDto.getWmsShippingPackageId());
        markOrderCompleteRequest.setCourier("LK_STORE");
        markOrderCompleteRequest.setDocketNumber(shipmentDto.getWmsOrderCode());
        markOrderCompleteRequest.setInvDate(DATE_FORMAT.format(shipmentDto.getUpdatedAt()));
        markOrderCompleteRequest.setInvTime(TIME_FORMAT.format(shipmentDto.getUpdatedAt()));
        markOrderCompleteRequest.setManifestDate(DATE_FORMAT.format(shipmentDto.getUpdatedAt()));
        markOrderCompleteRequest.setManifestTime(TIME_FORMAT.format(shipmentDto.getUpdatedAt()));
        markOrderCompleteRequest.setManifestNumber(shipmentDto.getWmsShippingPackageId() != null ? shipmentDto.getWmsShippingPackageId() : "OTC");
        return markOrderCompleteRequest;
    }

    protected boolean isValidNullBarcodeLoyaltyAndAccessoryItem(OrderItemDto orderItemDto) {
        return  ((ProductDeliveryType.OTC.equals(orderItemDto.getProductDeliveryType())
                && ItemType.LOYALTY_SERVICES.equals(orderItemDto.getItemType()))
                || (ProductDeliveryType.OTC.equals(orderItemDto.getProductDeliveryType()))
        && ItemType.ACCESSORIES.equals(orderItemDto.getItemType()) && carryBagPids.contains(orderItemDto.getProductId()));


    }

}
