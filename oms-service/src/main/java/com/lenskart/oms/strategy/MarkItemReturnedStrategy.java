package com.lenskart.oms.strategy;

import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.enums.EventToOperationMap;
import com.lenskart.oms.enums.OrderItemStatus;
import com.lenskart.oms.enums.ShipmentEvent;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.model.Action;
import com.lenskart.oms.request.ShipmentItemUpdate;
import com.lenskart.oms.request.ShipmentUpdateEvent;
import com.lenskart.oms.validators.ShipmentEventValidator;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
@Setter(onMethod__ = {@Autowired})
public class MarkItemReturnedStrategy extends BaseShipmentEventStrategy {
    private ShipmentEventValidator shipmentEventValidator;

    @Override
    protected ShipmentEvent supportedOrderEvents() {
        return ShipmentEvent.MARK_ITEM_RETURNED;
    }

    @Override
    protected void preExecute(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        shipmentEventValidator.validateItemReturnEvent(shipmentUpdateEvent);
    }

    @Override
    protected void execute(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        Action transitionAction;
        List<OrderItemDto> orderItemDtoList = orderItemService.search("id.in:" + StringUtils.join(shipmentUpdateEvent.getOrderItemList().stream().map(ShipmentItemUpdate::getOrderItemId).collect(Collectors.toList()), ","));
        for (OrderItemDto currentItemDto : orderItemDtoList) {
            transitionAction = omsTransitionUtil.getTransitionActionByOperationAndStatus(EventToOperationMap.valueOf(shipmentUpdateEvent.getShipmentEvent().name()).getOperation(), currentItemDto.getItemStatus());
            logger.info("Updating order item status from {} to {} for ItemId - {}", currentItemDto.getItemStatus(), transitionAction.getItemStatus().getItemStatus(), currentItemDto.getId());
            currentItemDto.setItemStatus(transitionAction.getItemStatus().getItemStatus());
            currentItemDto.setItemSubStatus(transitionAction.getItemStatus().getItemSubStatus());
            orderItemService.save(currentItemDto);
        }
    }
}
