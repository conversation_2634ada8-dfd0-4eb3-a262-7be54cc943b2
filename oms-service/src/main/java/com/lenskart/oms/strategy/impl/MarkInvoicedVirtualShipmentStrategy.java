package com.lenskart.oms.strategy.impl;

import com.lenskart.communication.client.model.Email;
import com.lenskart.core.model.ItemWisePriceDetails;
import com.lenskart.core.model.Order;
import com.lenskart.core.model.Product;
import com.lenskart.fds.enums.DocumentProvider;
import com.lenskart.fds.enums.DocumentSource;
import com.lenskart.fds.enums.DocumentType;
import com.lenskart.fds.request.CreateDocumentRequest;
import com.lenskart.nexs.commonMailer.connector.CommunicationConnector;
import com.lenskart.nexs.ims.request.UpdateStocksRequestV2;
import com.lenskart.nexs.ims.response.ItemStockUpdateResponseV2;
import com.lenskart.nexs.ims.response.UpdateStocksResponseV2;
import com.lenskart.nexs.wms.response.orderops.OrderAddressUpdate;
import com.lenskart.oms.configs.InvoiceMailConfig;
import com.lenskart.oms.connector.CatalogOpsConnector;
import com.lenskart.oms.connector.FdsConnector;
import com.lenskart.oms.connector.OrderOpsConnector;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.constants.KafkaConstants;
import com.lenskart.oms.dto.*;
import com.lenskart.oms.entity.OrderAddress;
import com.lenskart.oms.enums.*;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.model.Action;
import com.lenskart.oms.producer.CommonKafkaProducer;
import com.lenskart.oms.request.*;
import com.lenskart.oms.service.OrderService;
import com.lenskart.oms.strategy.BaseOtcShipmentEventStrategy;
import com.lenskart.oms.utils.ObjectHelper;
import com.sendgrid.SendGrid;
import com.lenskart.communication.client.service.MailSender;

import lombok.AccessLevel;
import lombok.Setter;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.lenskart.fds.enums.DocumentProvider.NEXS;
import static com.lenskart.oms.constants.KafkaConstants.MESSAGE_IDEMPOTENCY_KEY;
import static com.lenskart.oms.enums.FDSConstants.FDS;
import static com.lenskart.oms.enums.FDSConstants.SALE_ORDER;
import static org.apache.commons.io.FileUtils.getFile;

@Component
@Setter(onMethod__ = {@Autowired})
public class MarkInvoicedVirtualShipmentStrategy extends BaseOtcShipmentEventStrategy {

    private FdsConnector fdsConnector;
    private OrderService orderService;
    private CommonKafkaProducer commonKafkaProducer;
    private CommunicationConnector communicationConnector;
    private CatalogOpsConnector catalogOpsConnector;
    private TemplateEngine templateEngine;
    private InvoiceMailConfig invoiceMailConfig;

    @Value("${attachmentFileName}")
    @Setter(AccessLevel.NONE)
    private String attachmentFileName;

    @Value("${from}")
    @Setter(AccessLevel.NONE)
    private String from;

    @Value("${fromJohnJacobs}")
    @Setter(AccessLevel.NONE)
    private String fromJohnJacobs;

    @Value("${fromOwndays}")
    @Setter(AccessLevel.NONE)
    private String fromOwndays;

    @Value("${subject}")
    @Setter(AccessLevel.NONE)
    private String subject;

    @Value("${subjectOwndays}")
    @Setter(AccessLevel.NONE)
    private String subjectOwndays;

    @Value("${invoiceSenderMailTemplate}")
    @Setter(AccessLevel.NONE)
    private String invoiceSenderMailTemplate;

    @Value("${johnJacobsSupportMail}")
    @Setter(AccessLevel.NONE)
    private String johnJacobsSupportMail;

    @Value("${lenskartSupportMail}")
    @Setter(AccessLevel.NONE)
    private String lenskartSupportMail;

    @Value("${mediaServerHostname}")
    @Setter(AccessLevel.NONE)
    private String mediaServerHostname;

    @Value("${owndays.support.email:<EMAIL>}")
    @Setter(AccessLevel.NONE)
    private String ownDaysSupportEmail;

    @Value("${enable.email.trigger:true}")
    @Setter(AccessLevel.NONE)
    private boolean enableEmailTrigger;

    private static final String ODONLINE = "ODONLINE";
    private static final String DEFAULT = "DEFAULT";

    @Override
    protected OtcShipmentEventType supportedOrderEvents() {
        return OtcShipmentEventType.VIRTUAL_INVOICED;
    }

    @Override
    protected void preExecute(OtcShipmentEvent otcShipmentEvent) throws ApplicationException {
        validateEvent(otcShipmentEvent);
    }

    @Override
    protected void execute(OtcShipmentEvent otcShipmentEvent) throws Exception {
        ShipmentDto shipmentDto = shipmentService.findById(otcShipmentEvent.getShipmentId());
        OrderDto orderDto = orderService.findById(shipmentDto.getOrderItems().get(0).getOrderId());
        CreateDocumentRequest createDocumentRequest = getCreateDocumentRequest(shipmentDto,orderDto);
       String invoiceNo = fdsConnector.createDocument(createDocumentRequest);
        if (!StringUtils.hasLength(invoiceNo)) {
            logger.info("Invoice number not received from FDS for unicomOrder code {}", shipmentDto.getWmsOrderCode());
            throw new ApplicationException("GET INVOICE FAILED FOR OTC ORDER");
        }
        shipmentDto.setInvoiceNumber(invoiceNo);

        UpdateStocksRequestV2 stocksRequestV2 = getUpdateStocksRequestV2(shipmentDto, ApplicationConstants.IMS_OTC_INVOICED_OPERATION);
        UpdateStocksResponseV2 updateStocksResponseV2 = imsConnector.updateBarcodeStatus(stocksRequestV2);

        for (ItemStockUpdateResponseV2 responseV2 : updateStocksResponseV2.getItemStockUpdateResponseV2List()) {
            if (!responseV2.isSuccess()) {
                logger.info("Update barcode failed for shipmentDto {} and barcode {}", shipmentDto, responseV2.getBarcode());
                throw new ApplicationException("Update barcode failed for OTC Order");
            }
        }

        Long b2bRefrenceId = null;
        for (OrderItemDto orderItemDto : shipmentDto.getOrderItems()) {
            orderItemDto.setItemStatus(OrderItemStatus.INVOICED);
            orderItemDto.setItemSubStatus(OrderItemSubStatus.INVOICED);
            orderItemService.update(orderItemDto, orderItemDto.getId());
            if(!Objects.isNull(orderItemDto.getB2bReferenceItemId())) {
                b2bRefrenceId = orderItemDto.getB2bReferenceItemId();
            }
        }
        OrderItemDto orderItemDto = orderItemService.findById(b2bRefrenceId);
        ShipmentDto warehouseShipment = shipmentService.findById(orderItemDto.getShipmentId());
        shipmentDto.setAwbNumber(warehouseShipment.getAwbNumber());
        shipmentDto.setCourierCode(warehouseShipment.getCourierCode());
        shipmentDto.setManifestNumber(warehouseShipment.getManifestNumber());
        shipmentDto.setShipmentStatus(ShipmentStatus.INVOICED);
        shipmentDto.setShipmentSubStatus(ShipmentSubStatus.INVOICED);
        shipmentService.update(shipmentDto, shipmentDto.getId());

        List<ShipmentTimelineDto> shipmentTimelineDtoList = shipmentTimelineService.findByShipmentId(shipmentDto.getId());
        for (ShipmentTimelineDto shipmentTimelineDto : shipmentTimelineDtoList) {
            shipmentTimelineDto.setInvoiceTime(new Date());
            shipmentTimelineService.update(shipmentTimelineDto, shipmentTimelineDto.getId());
        }
    }

    @Override
    protected void postExecute(OtcShipmentEvent otcShipmentEvent) throws Exception {
        ShipmentDto shipmentDto = shipmentService.findById(otcShipmentEvent.getShipmentId());

        if (disableVsmCallback) {
            ShipmentUpdateEvent shipmentUpdateEvent = new ShipmentUpdateEvent();
            shipmentUpdateEvent.setShipmentEvent(ShipmentEvent.CREATE_SHIPMENT_INVOICE);
            shipmentUpdateEvent.setWmsOrderCode(shipmentDto.getWmsOrderCode());
            List<ShipmentItemUpdate> shipmentItemUpdates = new ArrayList<>();
            ShipmentItemUpdate shipmentItemUpdate = new ShipmentItemUpdate();
            shipmentItemUpdate.setUnicomShipmentStatus("PACKED");
            shipmentItemUpdate.setOrderOpsShipmentStatus("PACKED");
            shipmentItemUpdates.add(shipmentItemUpdate);
            shipmentUpdateEvent.setOrderItemList(shipmentItemUpdates);
            persistTrackingAndPublishToOms(shipmentUpdateEvent);
            Map<String, String> kafkaHeaders = new HashMap<>();
            kafkaHeaders.put(MESSAGE_IDEMPOTENCY_KEY, shipmentUpdateEvent.getShipmentEvent().name() + "_" + shipmentUpdateEvent.getWmsOrderCode());
            shipmentEventOmsProducer.sendMessage(shipmentUpdateEvent, omsShipmentBackSyncTopic, kafkaHeaders, shipmentUpdateEvent.getWmsOrderCode());
        }
        commonKafkaProducer.sendMessage(
                KafkaConstants.OMS_OTC_ORDER_EVENTS_PROCESS_TOPIC,
                String.valueOf(shipmentDto.getOrderItems().get(0).getOrderId()),
                ObjectHelper.convertToString(new OtcShipmentEvent(OtcShipmentEventType.VIRTUAL_DISPATCHED, shipmentDto.getId())),
                String.valueOf(shipmentDto.getId())
        );
    }

    public void triggerInvoiceEmail(ShipmentDto shipmentDto, String toEmail) throws Exception {
        OrderDto orderDto =  orderService.findById(shipmentDto.getOrderItems().get(0).getOrderId());
        String documentSource = FDSConstants.SENSEI_SALE_ORDER.name();
        String templateType = FDSConstants.INVOICE.name();
        if (isAlmadallahInsuranceOrder(orderDto)) {
            documentSource = FDSConstants.SALE_ORDER.name();
            templateType = ApplicationConstants.ALMADALLAH_INVOICE;
        }
        GetDocumentRequest getDocumentRequest = new GetDocumentRequest(documentSource, FDSConstants.INVOICE.name(), NEXS.name(), shipmentDto.getWmsShippingPackageId() , shipmentDto.getFacility(), templateType);

        InputStream inputStream =  fdsConnector.getDocument(getDocumentRequest);;
        Map<String, InputStream> map = new HashMap<>();
        String fileName = "invoice" + "-" +  shipmentDto.getWmsShippingPackageId() + ".pdf";
        map.put("invoice" + "-" +  shipmentDto.getWmsShippingPackageId() + ".pdf", inputStream);
        String emailHtmlBody =  getEmailBody(shipmentDto, inputStream, orderDto);

        Channel itemChannel = shipmentDto.getOrderItems().get(0).getChannel();
        String fromEmail = getInvoiceItem(orderDto.getLkCountry(), from, InvoiceMailItem::getSupportUrl);
        if (Channel.ODONLINE.equals(itemChannel))
            fromEmail = ownDaysSupportEmail;

        if(enableEmailTrigger)
            communicationConnector.sendMail(emailHtmlBody, fromEmail, new String[]{toEmail}, subject + " " + orderDto.getIncrementId(), map, fileName);
    }

    private String getEmailBody(ShipmentDto shipmentDto, InputStream inputStream, OrderDto orderDto) throws Exception {
        Channel itemChannel = shipmentDto.getOrderItems().get(0).getChannel();
        String countryCode = orderDto.getLkCountry();
        Context emailMap = new Context();
        emailMap.setVariable("incrementId", String.valueOf(orderDto.getIncrementId()));
        emailMap.setVariable("storeId", String.valueOf(orderDto.getStoreId()));
        emailMap.setVariable("channel", itemChannel.name());
        emailMap.setVariable("orderCurrency", orderDto.getCurrencyCode());
        if (PaymentMethod.CASHONDELIVERY.name().equalsIgnoreCase(orderDto.getPaymentMethod())){
            emailMap.setVariable("paymentMethod", "Cash On Delivery");
        }else{
            emailMap.setVariable("paymentMethod", "Prepaid");
        }

        emailMap.setVariable("orderDate", orderDto.getCreatedAt());
        emailMap.setVariable("shippingAddress", shipmentDto.getShippingAddress());
        emailMap.setVariable("customerEmail", shipmentDto.getShippingAddress());
        emailMap.setVariable("billingAddress", shipmentDto.getBillingAddress());
        emailMap.setVariable("expectedDeliveryDate", shipmentDto.getExpectedDeliveryDate() != null ? shipmentDto.getExpectedDeliveryDate() : new Date());

        List<ProductItemDetailDTO> productDetails = new ArrayList<>();
        double subTotalForShipment = 0;
        for(OrderItemDto orderItemDto : shipmentDto.getOrderItems()) {
            ProductItemDetailDTO productItemDetailDTO = null;
            Product product = catalogOpsConnector.findProductDetailsByProductId(orderItemDto.getProductId());
            if(ItemType.CONTACT_LENS.equals(orderItemDto.getItemType())) {
                Map<String, Object> powerDetails = new HashMap<>();
                powerDetails.put("rightSph", orderItemDto.getItemPower().getSph());
                powerDetails.put("rightCyl", orderItemDto.getItemPower().getCyl());
                powerDetails.put("rightAxis", orderItemDto.getItemPower().getAxis());
                powerDetails.put("leftSph", orderItemDto.getItemPower().getSph());
                powerDetails.put("leftCyl", orderItemDto.getItemPower().getSph());
                powerDetails.put("leftAxis", orderItemDto.getItemPower().getSph());
                String productUrl = (orderDto.getStoreId() != 4) ? "https://www.lenskart.com/" + product.getProductUrl() : "https://www.johnjacobseyewear.com/";
                emailMap.setVariable("productUrl", productUrl);
                productItemDetailDTO = new ProductItemDetailDTO(product, powerDetails, 0);
            }else{
                productItemDetailDTO = new ProductItemDetailDTO(product, null, 0);
            }
            double itemPrice = orderItemDto.getOrderItemPrice().getItemTotal();
            itemPrice = Double.parseDouble(String.format("%.2f", itemPrice));
            productItemDetailDTO.setItemPrice(itemPrice);

            subTotalForShipment += itemPrice;

            productDetails.add(productItemDetailDTO);
        }

        List<OrderItemPricesDto> itemPriceDtoList = shipmentDto.getOrderItems().stream()
                .map(OrderItemDto::getOrderItemPrice).collect(Collectors.toList());
        shipmentWisePriceCalculation(itemPriceDtoList, emailMap);
        String encodedIncrementId = new String(Base64.getEncoder().encode(orderDto.getIncrementId().toString().getBytes()));
        String encodedEmail = DigestUtils.sha1Hex(shipmentDto.getShippingAddress().getEmail());

        emailMap.setVariable("productDetails", productDetails);
        emailMap.setVariable("mediaServerHostname", mediaServerHostname);

        logger.info("getEmailBody - StoreId - {}, IncrementId - {}", orderDto.getStoreId(), orderDto.getIncrementId());

        if (orderDto.getStoreId() == 4) {
            emailMap.setVariable("supportMail", "mailto:" + johnJacobsSupportMail);
            emailMap.setVariable("trackYourOrder", "http://www.john-jacobs.com/sales/guest/view/?incrementId=" + encodedIncrementId + "&email=" + encodedEmail);
            emailMap.setVariable("checkEmOut", "https://www.johnjacobseyewear.com/collections/sunglasses");
            emailMap.setVariable("instagramUrl", "http://instagram.com/johnjacobseyewear/");
            emailMap.setVariable("twitterUrl", "https://twitter.com/LoveJohnJacobs");
            emailMap.setVariable("facebookUrl", "https://www.facebook.com/JohnJacobsEyewear");
            emailMap.setVariable("registeredTo", "John Jacobs");
            emailMap.setVariable("unsubscribe", "http://vsm.dkrt.in/unsubscribeUserEmail/" + encodedEmail);
            emailMap.setVariable("callUs", "(+91)9999899998");
        } else if (Channel.ODONLINE.equals(itemChannel)) {
            emailMap.setVariable("supportMail", "mailto:" + ownDaysSupportEmail);
            emailMap.setVariable("trackYourOrder", "https://www.lenskart.com/sales/guest/view/?incrementId=" + encodedIncrementId + "&email=" + encodedEmail);
            emailMap.setVariable("checkEmOut", "https://www.owndays.com/sg/en");
            emailMap.setVariable("instagramUrl", "https://www.instagram.com/owndays/");
            emailMap.setVariable("twitterUrl", "https://twitter.com/owndays");
            emailMap.setVariable("facebookUrl", "https://www.facebook.com/Lenskartindia");
            emailMap.setVariable("registeredTo", "Owndays Singapore Pte Ltd");
            emailMap.setVariable("unsubscribe", "http://vsm.dkrt.in/unsubscribeUserEmail/" + encodedEmail);
            emailMap.setVariable("callUs", "(+91)9999899998");
        } else {
            emailMap.setVariable("supportMail", "mailto:" + getInvoiceItem(orderDto.getLkCountry(), lenskartSupportMail, InvoiceMailItem::getSupportUrl));
            emailMap.setVariable("trackYourOrder", "https://www.lenskart.com/sales/guest/view/?incrementId=" + encodedIncrementId + "&email=" + encodedEmail);
            emailMap.setVariable("checkEmOut", getInvoiceItem(countryCode, "https://www.lenskart.com/sunglasses.html", InvoiceMailItem::getSunglassesUrl));
            emailMap.setVariable("instagramUrl", getInvoiceItem(countryCode, "https://www.instagram.com/lenskart/", InvoiceMailItem::getInstaUrl));
            emailMap.setVariable("twitterUrl", getInvoiceItem(countryCode, "https://twitter.com/lenskart_com", InvoiceMailItem::getTwitterUrl));
            emailMap.setVariable("facebookUrl", getInvoiceItem(countryCode, "https://www.facebook.com/Lenskartindia", InvoiceMailItem::getFacebookUrl));
            emailMap.setVariable("callUs", getInvoiceItem(countryCode, "(+91)9999899998", InvoiceMailItem::getCallUs));
            emailMap.setVariable("visitNearByUrl", getInvoiceItem(countryCode, "https://www.lenskart.com/stores", InvoiceMailItem::getVisitNearByUrl));
            emailMap.setVariable("registeredTo", "Lenskart");
            emailMap.setVariable("unsubscribe", "http://vsm.dkrt.in/unsubscribeUserEmail/" + encodedEmail);
        }

        return templateEngine.process("invoiceMail", emailMap);
    }

    @FunctionalInterface
    public interface InvoiceItemGetter {
        String getItem(InvoiceMailItem countryConfig);
    }

    private String getInvoiceItem(String countryCode, String defaultValue, InvoiceItemGetter urlGetter) {
        return Optional.ofNullable(invoiceMailConfig.getCountry())
                .map(countries -> countries.get(countryCode))
                .map(urlGetter::getItem)
                .orElse(defaultValue);
    }

    private void validateEvent(OtcShipmentEvent otcShipmentEvent){
        if(ObjectUtils.isEmpty(otcShipmentEvent) || otcShipmentEvent.getShipmentId() == null){
            throw new ApplicationException("Invalid Virtual Shipment Event" + otcShipmentEvent);
        }
    }


    private CreateDocumentRequest getCreateDocumentRequest(ShipmentDto shipmentDto, OrderDto orderDto) {
        CreateDocumentRequest createDocumentRequest = new CreateDocumentRequest();
        createDocumentRequest.setFacility(shipmentDto.getFacility());
        createDocumentRequest.setLenskartGeneratedUnicomOrderCode(shipmentDto.getWmsOrderCode());
        createDocumentRequest.setDocumentSource(FDSConstants.SENSEI_SALE_ORDER.name());
        if (isAlmadallahInsuranceOrder(orderDto)) {
            createDocumentRequest.setDocumentSource(FDSConstants.SALE_ORDER.name());
        }
        createDocumentRequest.setDocumentType(DocumentType.INVOICE);
        createDocumentRequest.setDocumentProvider(DocumentProvider.FDS.name());
        createDocumentRequest.setDocumentSourceReferenceId(shipmentDto.getWmsShippingPackageId());
        return createDocumentRequest;
    }

    private boolean isAlmadallahInsuranceOrder(OrderDto orderDto) {
        if (orderDto != null && CollectionUtils.isNotEmpty(orderDto.getOrderMetaData())) {
            for (OrderMetaDataDto metaData : orderDto.getOrderMetaData()) {
                if (ApplicationConstants.INSURANCE_PROVIDER.equalsIgnoreCase(metaData.getEntityKey()) && StringUtils.hasText(metaData.getEntityValue()) && "ALMADALLAH".equalsIgnoreCase(metaData.getEntityValue())) {
                    return true;
                }
            }
        }
        return false;
    }

    private void shipmentWisePriceCalculation(List<OrderItemPricesDto> itemPriceDtoList, Context model){
        double shipmentDiscount = 0;
        double taxCollectedForShipment = 0;
        double shipmentChargesForShipment = 0;
        double grandTotalForShipment = 0;
        int qty = 1;
        for (OrderItemPricesDto detail : itemPriceDtoList) {
            double itemWiseDiscount =
                    Optional.ofNullable(detail.getCouponDiscount()).orElse(0.0) +
                            Optional.ofNullable(detail.getFcDiscount()).orElse(0.0) +
                            Optional.ofNullable(detail.getGiftCardDiscount()).orElse(0.0) +
                            Optional.ofNullable(detail.getImplicitDiscount()).orElse(0.0) +
                            Optional.ofNullable(detail.getLenskartDiscount()).orElse(0.0) +
                            Optional.ofNullable(detail.getLenskartPlusDiscount()).orElse(0.0) +
                            Optional.ofNullable(detail.getScDiscount()).orElse(0.0) +
                            Optional.ofNullable(detail.getPrepaidDiscount()).orElse(0.0);
            shipmentDiscount += itemWiseDiscount;

            taxCollectedForShipment += Optional.ofNullable(detail.getTaxCollected()).orElse(0.0) / qty;
            shipmentChargesForShipment += Optional.ofNullable(detail.getShippingCharges()).orElse(0.0) / qty;
            grandTotalForShipment += Optional.ofNullable(detail.getItemTotalAfterDiscount()).orElse(0.0)/ qty;

        }

        // Round value upto two decimal places.
        model.setVariable("discount",String.format("%.2f", shipmentDiscount));
        model.setVariable("taxes",String.format("%.2f", taxCollectedForShipment));
        model.setVariable("shipping",String.format("%.2f", shipmentChargesForShipment));
        model.setVariable("orderTotal",String.format("%.2f", grandTotalForShipment));
    }

}
