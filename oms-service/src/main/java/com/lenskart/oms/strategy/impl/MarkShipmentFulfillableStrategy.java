package com.lenskart.oms.strategy.impl;


import com.lenskart.oms.connector.NexsWmsConnector;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.dto.*;
import com.lenskart.oms.enums.*;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.model.Action;
import com.lenskart.oms.request.OrderBackSyncRequest;
import com.lenskart.oms.request.OrderOpsOrderEvent;
import com.lenskart.oms.request.ShipmentItemUpdate;
import com.lenskart.oms.request.ShipmentUpdateEvent;
import com.lenskart.oms.strategy.BaseShipmentEventStrategy;
import com.lenskart.oms.utils.OmsCommonUtil;
import com.lenskart.oms.validators.ShipmentEventValidator;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Setter(onMethod__ = {@Autowired})
public class MarkShipmentFulfillableStrategy extends BaseShipmentEventStrategy {

    private ShipmentEventValidator shipmentEventValidator;
    private OmsCommonUtil commonUtil;
    private NexsWmsConnector nexsWmsConnector;

    @Override
    protected ShipmentEvent supportedOrderEvents() {
        return ShipmentEvent.MARK_ITEM_FULFILLABLE;
    }

    @Override
    protected void preExecute(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        shipmentEventValidator.validateMarkItemFulfillable(shipmentUpdateEvent);
    }

    @Override
    protected void execute(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        if (CollectionUtils.isEmpty(shipmentUpdateEvent.getOrderItemList()))
            return;

        if(!nexsWmsConnector.orderExistsInWms(shipmentUpdateEvent.getWmsOrderCode())) {
            logger.error("[MarkShipmentFulfillableStrategy] Order NOT Created in WMS for wmsOrderCode: {}. Cancel/Retry MARK_ITEM_FULFILABLE event", shipmentUpdateEvent.getWmsOrderCode());
            throw new ApplicationException("[MarkShipmentFulfillableStrategy] Order with wmsOrderCode: " + shipmentUpdateEvent.getWmsOrderCode() + " not persisted in WMS System yet");
        }

        Long orderId = null;
        Set<Long> shipmentIds = new HashSet<>();
        Action transitionAction = null;
        List<Long> uwItemIds = new ArrayList<>();
        List<Long> orderItemIds = shipmentUpdateEvent.getOrderItemList().stream().map(ShipmentItemUpdate::getOrderItemId).collect(Collectors.toList());
        List<OrderItemDto> orderItemDtoList = orderItemService.search("id.in:" + StringUtils.join(orderItemIds, ","));
        Map<Long, ShipmentItemUpdate> shipmentItemsMap = shipmentUpdateEvent.getOrderItemList().stream().collect(Collectors.toMap(ShipmentItemUpdate::getOrderItemId, Function.identity(), (x1, x2) ->  x1));
        for (OrderItemDto currentItemDto : orderItemDtoList) {
            ShipmentItemUpdate item = shipmentItemsMap.get(currentItemDto.getId());
            if(null == currentItemDto) {
                logger.info("[{}, execute] Unable to find the order item dto for the order item id {} ",this.getClass().getSimpleName(), item.getOrderItemId());
            }
            transitionAction = omsTransitionUtil.getTransitionActionByOperationAndStatus(
                    EventToOperationMap.valueOf(shipmentUpdateEvent.getShipmentEvent().name()).getOperation(), currentItemDto.getItemStatus()
            );
            orderId = currentItemDto.getOrderId();
            shipmentIds.add(currentItemDto.getShipmentId());
            logger.info("Updating OrderItemId - {}", item.getOrderItemId());
            currentItemDto.setItemStatus(transitionAction.getItemStatus().getItemStatus());
            currentItemDto.setItemSubStatus(transitionAction.getItemStatus().getItemSubStatus());
            createOrUpdateOrderItemMetaDataForFullFillType(item, currentItemDto);


            orderItemService.save(currentItemDto);

            updateShipmentTimeline(item, currentItemDto);
            if(null == item.getEntityId())
               item.setEntityId(Integer.toString(1));
            item.setOrderItemId(currentItemDto.getUwItemId());
            uwItemIds.add(currentItemDto.getUwItemId());
        }


        String wmsOrderCode = fetchWmsOrderCodeAndUpdateShipmentData(shipmentIds, transitionAction);
        OrderDto orderDto = fetchAndUpdateOrderDataIfRequired(orderId, transitionAction);
        if(!commonUtil.isDistributorOrder(orderDto)) {
            publishStatusChangesToOrderOps(orderDto.getIncrementId(), uwItemIds, BackSyncEventName.OO_FULFILLABLE_SYNC, wmsOrderCode);
        }
    }

    private void createOrUpdateOrderItemMetaDataForFullFillType(ShipmentItemUpdate item, OrderItemDto currentItemDto) {
        OrderItemMetaDataDto orderItemMetaDataDto = currentItemDto.getOrderItemMetaData().stream().filter(itemMetaData -> ApplicationConstants.IS_FULLFILLABLE.equalsIgnoreCase(itemMetaData.getEntityKey())).findFirst().orElse(null);
        logger.info("[{}, createOrUpdateOrderItemMetaDataForFullFillType] The item is {}",this.getClass().getSimpleName(),
                item);
        if(null == orderItemMetaDataDto) {
            orderItemMetaDataDto = new OrderItemMetaDataDto();
            orderItemMetaDataDto.setOrderItemId(item.getOrderItemId());
            orderItemMetaDataDto.setEntityKey(ApplicationConstants.IS_FULLFILLABLE);
            orderItemMetaDataDto.setEntityValue(String.valueOf("1".equalsIgnoreCase(item.getEntityId())));
            orderItemMetaDataDto.setCreatedBy(ApplicationConstants.OMS_ORDER_SYSTEM);
            orderItemMetaDataDto.setUpdatedBy(ApplicationConstants.OMS_ORDER_SYSTEM);
            orderItemMetaService.save(orderItemMetaDataDto);
        }
        else {
            orderItemMetaDataDto.setEntityValue(String.valueOf("1".equalsIgnoreCase(item.getEntityId())));
            orderItemMetaDataDto.setUpdatedBy(ApplicationConstants.OMS_ORDER_SYSTEM);
            orderItemMetaService.update(orderItemMetaDataDto,orderItemMetaDataDto.getId());
        }
    }

    private void updateShipmentTimeline(ShipmentItemUpdate item, OrderItemDto currentItemDto) {
        List<ShipmentTimelineDto> timelineDtoList = shipmentTimelineService.findAssociatedShipmentTimelines(currentItemDto);
        for (ShipmentTimelineDto timelineDto : timelineDtoList) {
            timelineDto.setFulfilledTime(item.getEventTime() != null ? item.getEventTime() : new Date());
            shipmentTimelineService.save(timelineDto);
            logger.info("Updating FulfillableTime - {} For OrderItemId - {} & ShipmentId - {}", timelineDto.getFulfilledTime(), timelineDto.getOrderItemId(), timelineDto.getShipmentId());
        }
    }

    private String fetchWmsOrderCodeAndUpdateShipmentData(Set<Long> shipmentIds, Action transitionAction) {
        String wmsOrderCode = null;
        List<ShipmentDto> shipmentDtoList = shipmentService.search("id.in:" + StringUtils.join(shipmentIds, ","));
        if (!CollectionUtils.isEmpty(shipmentDtoList)) {
            wmsOrderCode = shipmentDtoList.get(0).getWmsOrderCode();
            for (ShipmentDto shipmentDto : shipmentDtoList) {
                logger.info("Updating ShipmentId - {}", shipmentDto.getId());
                shipmentDto.setShipmentStatus(transitionAction.getShipmentStatus().getShipmentStatus());
                shipmentDto.setShipmentSubStatus(transitionAction.getShipmentStatus().getShipmentSubStatus());
                shipmentService.save(shipmentDto);
            }
        }
        return wmsOrderCode;
    }

    private OrderDto fetchAndUpdateOrderDataIfRequired(Long orderId, Action transitionAction) {
        OrderDto orderDto = orderService.findById(orderId);
        if (isOrderStatusUpdateRequired(transitionAction, orderDto)) {
            logger.info("Updating OrderId - {}", orderDto.getId());
            orderDto.setOrderStatus(transitionAction.getOrderStatus().getOrderStatus());
            orderDto.setOrderSubStatus(transitionAction.getOrderStatus().getOrderSubStatus());
            orderService.save(orderDto);
        }
        return orderDto;
    }

    private void publishStatusChangesToOrderOps(Long incrementId, List<Long> uwItemList, BackSyncEventName eventName, String wmsOrderCode) throws ApplicationException {
        try {
            orderBackSyncTrackingService.persistRequestInTracking(BackSyncEventName.OO_FULFILLABLE_SYNC, wmsOrderCode, BackSyncSystem.ORDER_OPS);
            OrderDto orderDto = new OrderDto();
            orderDto.setIncrementId(incrementId);
            for(Long uwItemId : uwItemList){
                OrderItemDto orderItemDto = new OrderItemDto();
                orderItemDto.setUwItemId(uwItemId);
                orderDto.getOrderItems().add(orderItemDto);
            }
            OrderBackSyncRequest orderBackSyncRequest = new OrderBackSyncRequest();
            orderBackSyncRequest.setOrderDto(orderDto);
            OrderOpsOrderEvent orderOpsOrderEvent = new OrderOpsOrderEvent();
            orderOpsOrderEvent.setEventName(eventName != null ? eventName : BackSyncEventName.valueOf(supportedOrderEvents().name()));
            orderOpsOrderEvent.setOrderId(orderDto.getIncrementId());
            orderOpsOrderEvent.setOrderBackSyncRequest(orderBackSyncRequest);
            ResponseEntity<String> responseEntity = orderOpsConnector.updateOmsOrder(orderOpsOrderEvent);
            orderBackSyncTrackingService.persistResponseInTracking(responseEntity.getBody(), null, eventName, wmsOrderCode);
        } catch (Exception exception){
            String exceptionMessage = "publishStatusChangesToOrderOps For WmsOrderCode - " + wmsOrderCode + ", BackSyncEvent - " + eventName.name();
            logger.error(exceptionMessage, exception);
            throw new ApplicationException(exceptionMessage, exception);
        }
    }


    protected Boolean isOrderStatusUpdateRequired(Action transitionAction, OrderDto orderDto) {
        List<ShipmentDto> shipmentDtoList = getShipmentDtos(orderDto);
        for (ShipmentDto shipmentDto : shipmentDtoList) {
            if(commonUtil.isB2bShipment(shipmentDto) || commonUtil.isReassignedShipment(shipmentDto)) continue;
            if (!shipmentDto.getShipmentStatus().name().equalsIgnoreCase(transitionAction.getOrderStatus().getOrderStatus().name())) {
                logger.info("[isOrderStatusUpdateRequired] shipment {} status {} is not matching with transactionAction orderStatus {}"
                        ,shipmentDto.getWmsOrderCode(), transitionAction.getOrderStatus().getOrderStatus().name());
                return false;
            }
        }
        return true;
    }

}
