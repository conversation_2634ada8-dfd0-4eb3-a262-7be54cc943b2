package com.lenskart.oms.strategy.impl;

import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.ShipmentTimelineDto;
import com.lenskart.oms.enums.EventToOperationMap;
import com.lenskart.oms.enums.OrderItemStatus;
import com.lenskart.oms.enums.OrderItemSubStatus;
import com.lenskart.oms.enums.ShipmentEvent;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.model.Action;
import com.lenskart.oms.request.ShipmentItemUpdate;
import com.lenskart.oms.request.ShipmentUpdateEvent;
import com.lenskart.oms.strategy.BaseShipmentEventStrategy;
import com.lenskart.oms.validators.ShipmentEventValidator;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

@Component
@Setter(onMethod__ = {@Autowired})
public class MarkShipmentQCStrategy extends BaseShipmentEventStrategy {

    private ShipmentEventValidator shipmentEventValidator;

    @Override
    protected ShipmentEvent supportedOrderEvents() {
        return ShipmentEvent.MARK_SHIPMENT_QC;
    }

    @Override
    protected void preExecute(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        shipmentEventValidator.validateQcEvent(shipmentUpdateEvent);
    }

    @Override
    protected void execute(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        if (CollectionUtils.isEmpty(shipmentUpdateEvent.getOrderItemList()))
            return;

        Action transitionAction = null;
        for (ShipmentItemUpdate item : shipmentUpdateEvent.getOrderItemList()) {
            OrderItemDto currentItemDto = orderItemService.findById(item.getOrderItemId());
            transitionAction = omsTransitionUtil.getTransitionActionByOperationAndStatus(
                    EventToOperationMap.valueOf(shipmentUpdateEvent.getShipmentEvent().name()).getOperation(), currentItemDto.getItemStatus()
            );
            if (currentItemDto != null) {
                logger.info("Updating order item status from {} to {} for orderId - {}", currentItemDto.getItemStatus(), transitionAction.getOrderStatus().getOrderStatus(), currentItemDto.getId());
                currentItemDto.setItemStatus(transitionAction.getItemStatus().getItemStatus());
                currentItemDto.setItemSubStatus(transitionAction.getItemStatus().getItemSubStatus());
                orderItemService.save(currentItemDto);
            }
            List<ShipmentTimelineDto> timelineDtoList = shipmentTimelineService.findAssociatedShipmentTimelines(currentItemDto);
            for (ShipmentTimelineDto timelineDto : timelineDtoList) {
                if (StringUtils.isNotBlank(item.getUnicomShipmentStatus()) && "CUSTOMIZATION_COMPLETE".equalsIgnoreCase(item.getUnicomShipmentStatus()))
                    timelineDto.setQcCompleteTime(item.getEventTime() != null ? item.getEventTime() : new Date());
                else
                    timelineDto.setQcFailTime(item.getEventTime() != null ? item.getEventTime() : new Date());
                shipmentTimelineService.save(timelineDto);
                logger.info("Updating QC Time - {} For OrderItemId - {} & ShipmentId - {}", timelineDto.getQcCompleteTime(), timelineDto.getOrderItemId(), timelineDto.getShipmentId());
            }
            item.setOrderItemId(currentItemDto.getUwItemId());
        }
    }
}
