package com.lenskart.oms.strategy.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lenskart.core.model.Product;
import com.lenskart.oms.connector.CatalogOpsConnector;
import com.lenskart.oms.constants.KafkaConstants;
import com.lenskart.oms.dto.*;
import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.dto.ShipmentTimelineDto;
import com.lenskart.oms.enums.*;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.exception.DuplicateEventException;
import com.lenskart.oms.mapper.CreateOrderMapper;
import com.lenskart.oms.mapper.OmsOrderEventMapper;
import com.lenskart.oms.mapper.UpdatePowerMapper;
import com.lenskart.oms.producer.CommonKafkaProducer;
import com.lenskart.oms.producer.OrderEventOmsProducer;
import com.lenskart.oms.producer.OutboxKafkaProducer;
import com.lenskart.oms.request.LineItemPayloadEvent;
import com.lenskart.oms.request.OtcShipmentEvent;
import com.lenskart.oms.service.OrderItemMetaService;
import com.lenskart.oms.enums.OutboxEventType;
import com.lenskart.oms.request.*;
import com.lenskart.oms.service.OrderMetaService;
import com.lenskart.oms.utils.LegalOwnerUtil;
import com.lenskart.order.interceptor.request.UpdatePowerRequest;
import com.lenskart.oms.strategy.BaseOrderEventStrategy;
import com.lenskart.oms.utils.ObjectHelper;
import com.lenskart.oms.validators.OrderCreateEventValidator;
import lombok.AccessLevel;
import lombok.Setter;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.lenskart.oms.constants.ApplicationConstants.*;
import static com.lenskart.oms.constants.KafkaConstants.MESSAGE_IDEMPOTENCY_KEY;

@Component
@Setter(onMethod__ = {@Autowired})
public class CreateOrderStrategy extends BaseOrderEventStrategy {

    @Value("${outbox.event.pattern.enabled}")
    @Setter(AccessLevel.NONE)
    private boolean outboxEventPatternEnabled;

    @Value("${enable.pure.loyalty.handling.in.nexs}")
    @Setter(AccessLevel.NONE)
    private Boolean enablePureLoyaltyHandlingInNexs;

    @Value("${oms.orders.event.topic}")
    @Setter(AccessLevel.NONE)
    private String omsOrdersEventTopic;

    @Value("${oms.orders.event.process.topic}")
    @Setter(AccessLevel.NONE)
    private String processOrderEventTopic;

    private OmsOrderEventMapper omsOrderEventMapper;
    private CatalogOpsConnector catalogOpsConnector;
    private OrderCreateEventValidator orderCreateEventValidator;
    private UpdatePowerMapper updatePowerMapper;
    private CreateOrderMapper createOrderMapper;
    private OrderMetaService orderMetaService;
    private OrderItemMetaService orderItemMetaService;
    private LegalOwnerUtil legalOwnerUtil;
    private OutboxKafkaProducer outboxKafkaProducer;
    private CommonKafkaProducer commonKafkaProducer;
    private OrderEventOmsProducer orderEventOmsProducer;

    @Override
    protected OrderEventType supportedOrderEvents() {
        return OrderEventType.CREATE_ORDER;
    }

    @Override
    protected void preExecute(OmsOrderEvent omsOrderEvent) throws ApplicationException {
        orderCreateEventValidator.validateCreateOrderEvent(omsOrderEvent);
        logger.info("[validateCreateOrderEvent] create order event request validation successful for {}", omsOrderEvent.getOrderDto().getIncrementId());
    }

    @Override
    protected void execute(OmsOrderEvent omsOrderEvent) throws ApplicationException {
        processCreateOrderEvent(omsOrderEvent);
    }

    @Override
    protected void postExecute(OmsOrderEvent omsOrderEvent) throws Exception {
        triggerPowerUpdateIfRequired(omsOrderEvent);
        for (ShipmentDto shipmentDto : omsOrderEvent.getShipmentDtoList()) {
            if (omsCommonUtil.isNonWarehouseProcessingOrderExcludingB2b(shipmentDto.getOrderItems().get(0))) {
                if (!FulfillmentType.LOCAL_FITTING.equals(shipmentDto.getOrderItems().get(0).getFulfillmentType())
                        && !OrderItemStatus.PENDING.equals(shipmentDto.getOrderItems().get(0).getItemStatus())) {
                    logger.info("isLocalFitting is false and item status is not pending so we will create invoice right now for shipment {}", shipmentDto.getId());
                    if (!outboxEventPatternEnabled) {
                        commonKafkaProducer.sendMessage(
                                KafkaConstants.OMS_OTC_ORDER_EVENTS_PROCESS_TOPIC,
                                String.valueOf(shipmentDto.getOrderItems().get(0).getOrderId()),
                                ObjectHelper.convertToString(new OtcShipmentEvent(OtcShipmentEventType.INVOICED, shipmentDto.getId())),
                                String.valueOf(shipmentDto.getId())
                        );
                    } else {
                        publishOutboxEventForOtcOrder(omsOrderEvent, shipmentDto);
                    }
                }
            } else {
                logger.info("[CreateOrderStrategy][postExecute] not UAE OTC order pushing shipmentDto {}", shipmentDto);
                pushToPostOrderCreateProcessingIfEligible(shipmentDto, omsOrderEvent.getOrderDto(), omsOrderEvent.getEventType());
            }
        }
    }

    private void publishOutboxEventForOtcOrder(OmsOrderEvent omsOrderEvent, ShipmentDto shipmentDto) {
        Map<String, String> headers = new HashMap<>();
        headers.put(MESSAGE_IDEMPOTENCY_KEY, String.valueOf(shipmentDto.getId()));
        String eventPayload = ObjectHelper.convertToString(new OtcShipmentEvent(OtcShipmentEventType.INVOICED, shipmentDto.getId()));
        outboxKafkaProducer.publishOrderEvent(
                String.valueOf(omsOrderEvent.getIncrementId()),
                OutboxEventAggregateType.ORDER_ID,
                KafkaConstants.OMS_OTC_ORDER_EVENTS_PROCESS_TOPIC,
                eventPayload,
                OutboxEventType.OTC_SHIPMENT_EVENT,
                headers,
                new Date()
        );
    }

    private void triggerPowerUpdateIfRequired(OmsOrderEvent omsOrderEvent) throws ApplicationException {
        OrderDto order = omsOrderEvent.getOrderDto();
        if (isPowerFollowUpRequired(omsOrderEvent.getLineItemPayloadEventList())) {
            pushOrderToPowerReceivedQueue(Math.toIntExact(order.getJunoOrderId()), order.getIncrementId(), OrderEventType.UPDATE_POWER.name(), omsOrderEvent.getLineItemPayloadEventList());
            logger.info("[CreateOrderStrategy -> triggerPowerUpdateIfRequired] Order {} pushed for Power Follow-Up", order.getIncrementId());
        }
        logger.info("[CreateOrderStrategy -> triggerPowerUpdateIfRequired] Power Follow-Up check passed for order {}", order.getIncrementId());
    }

    private void pushOrderToPowerReceivedQueue(Integer orderId, Long incrementId, String requestType, List<LineItemPayloadEvent> lineItemPayloadEventList) throws ApplicationException {
        try {
            UpdatePowerRequest updatePowerRequest = getUpdatePowerRequest(orderId, lineItemPayloadEventList);
            String jsonRequest = ObjectHelper.getObjectMapper().writeValueAsString(updatePowerRequest);
            PowerReceivedEvent powerReceivedEvent = getPowerReceivedEventMessage(String.valueOf(incrementId), null, jsonRequest, requestType);

            logger.info("[CreateOrderStrategy -> pushOrderToPowerReceivedQueue] Going to push for Power Follow-Up for order {} with payload {}", incrementId, powerReceivedEvent);
            if (!outboxEventPatternEnabled) {
                orderEventOmsProducer.sendMessage(ObjectHelper.getObjectMapper().writeValueAsString(powerReceivedEvent), incrementId, powerReceivedEvent.getRequestType());
            } else {
                publishOutboxPowerUpdateEvent(incrementId, powerReceivedEvent);
            }
            logger.info("[CreateOrderStrategy -> pushOrderToPowerReceivedQueue] Successfully queued power update event for order: {}", incrementId);
        } catch (Exception e) {
            logger.error("[CreateOrderStrategy -> pushOrderToPowerReceivedQueue] Push to Kafka Queue Failed for power follow up with order id {}", orderId, e);
            throw new ApplicationException("[CreateOrderStrategy -> pushOrderToPowerReceivedQueue] Push to Kafka Queue Failed for power follow up with order id: "+orderId, e);
        }
    }

    private void publishOutboxPowerUpdateEvent(Long incrementId, PowerReceivedEvent powerReceivedEvent) throws JsonProcessingException {
        Map<String, String> headers = new HashMap<>();
        headers.put(MESSAGE_IDEMPOTENCY_KEY, powerReceivedEvent.getRequestType() + "_" + incrementId);
        String eventPayload = ObjectHelper.getObjectMapper().writeValueAsString(powerReceivedEvent);
        outboxKafkaProducer.publishOrderEvent(
                String.valueOf(incrementId),
                OutboxEventAggregateType.ORDER_ID,
                omsOrdersEventTopic,
                eventPayload,
                OutboxEventType.POWER_UPDATE,
                headers,
                new Date()
        );
    }

    private UpdatePowerRequest getUpdatePowerRequest(Integer orderId, List<LineItemPayloadEvent> lineItemPayloadEventList){
        UpdatePowerRequest updatePowerRequest = new UpdatePowerRequest();

        updatePowerRequest.setLineItems(updatePowerMapper.mapOmsLineItemObjectToInterceptorLineItemPayload(lineItemPayloadEventList));
        updatePowerRequest.setOrderId(orderId);

        return updatePowerRequest;
    }

    private PowerReceivedEvent getPowerReceivedEventMessage(String incrementId, String clientId, String requestPayload, String requestType) throws ParseException, ParseException {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String onlyTimeStr = formatter.format(new Date());
        return new PowerReceivedEvent(incrementId, clientId, requestPayload, requestType, formatter.parse(onlyTimeStr));
    }

    private boolean isPowerFollowUpRequired(List<LineItemPayloadEvent> lineItemPayloadEventList) {
        for (LineItemPayloadEvent orderItem : lineItemPayloadEventList) {
            if (!orderItem.isLineItemPropertiesNull() && orderItem.getIsPower() == 1) {
                return true;
            }
        }

        return false;
    }

    private void pushToPostOrderCreateProcessingIfEligible(ShipmentDto shipmentDto, OrderDto orderDto, OrderEventType orderEventType) throws ApplicationException {
        OmsOrderEvent omsOrderEvent = omsOrderEventMapper.getOmsOrderEventFromShipmentDto(shipmentDto, orderDto, orderEventType);
        validateAndSyncOrder(omsOrderEvent);
    }

    @Override
    protected void pushToPostOrderCreateProcessing(OmsOrderEvent omsOrderEvent) throws ApplicationException {
        OrderDto orderDto = new OrderDto();
        orderDto.setId(omsOrderEvent.getOrderDto().getId());
        orderDto.setIncrementId(omsOrderEvent.getOrderDto().getIncrementId());
        omsOrderEvent.setOrderDto(orderDto);
        omsOrderEvent.setIncrementId(omsOrderEvent.getOrderDto().getIncrementId());
        omsOrderEvent.setShipmentDtoList(Collections.emptyList());
        omsOrderEvent.setEventType(OrderEventType.POST_CREATE_ORDER);
        if (!outboxEventPatternEnabled) {
            processOrderEventOmsProducer.sendMessage(omsOrderEvent);
        } else {
            publishOutboxPostOrderCreateEvent(omsOrderEvent);
        }
        logger.info("[pushToPostOrderCreateProcessing] Successfully queued post-create order event for order: {}",
                omsOrderEvent.getIncrementId());
    }

    private void publishOutboxPostOrderCreateEvent(OmsOrderEvent omsOrderEvent) {
        Map<String, String> headers = new HashMap<>();
        headers.put(MESSAGE_IDEMPOTENCY_KEY, omsOrderEvent.getEventType().name() + "_" + omsOrderEvent.getOrderDto().getIncrementId());
        String eventPayload = ObjectHelper.writeValue(omsOrderEvent);
        outboxKafkaProducer.publishOrderEvent(
                String.valueOf(omsOrderEvent.getIncrementId()),
                OutboxEventAggregateType.ORDER_ID,
                processOrderEventTopic,
                eventPayload,
                OutboxEventType.POST_ORDER_CREATE,
                headers,
                new Date()
        );
    }

    private void processCreateOrderEvent(OmsOrderEvent omsOrderEvent) throws ApplicationException {
        OrderDto orderDto = orderService.findByIncrementId(omsOrderEvent.getOrderDto().getIncrementId());
        if (!ObjectUtils.isEmpty(orderDto)) {
            logger.error("[CreateOrderStrategy -> processCreateOrderEvent] Order with incrementId {} already exists.", omsOrderEvent.getOrderDto().getIncrementId());
            throw new DuplicateEventException("Order with incrementId " + omsOrderEvent.getOrderDto().getIncrementId() + " already exists.");
        }

        try {
            Map<String, ShipmentDto> shipmentMap = new HashMap<>();
            persistOrderLevelData(omsOrderEvent);


            List<ShipmentDto> shipments = businessSplitAndReturnShipments(omsOrderEvent, shipmentMap);
            List<ShipmentDto> updatedShipmentDtoList = persistShipmentLevelData(shipments, omsOrderEvent.getOrderDto());
            omsOrderEvent.setShipmentDtoList(updatedShipmentDtoList);
            addEntryInShipmentTimeline(omsOrderEvent.getOrderDto().getId(), omsOrderEvent.getShipmentDtoList());
            logger.info("[CreateOrderStrategy -> processCreateOrderEvent] shipment list for the order {} after the business split logic {}", omsOrderEvent.getOrderDto().getIncrementId(), updatedShipmentDtoList);
        } catch (Exception e) {
            logger.error("[CreateOrderStrategy -> processCreateOrderEvent} Create order event failed for incrementId {} with exception", omsOrderEvent.getOrderDto().getIncrementId(), e);
            throw new ApplicationException("Create order event failed with message " + e.getMessage(), null);
        }
    }

    private void enrichOtcShipmentData(ShipmentDto shipmentDto) throws ApplicationException {
        if (omsCommonUtil.isNonWarehouseProcessingOrderExcludingB2b(shipmentDto.getOrderItems().get(0))) {
            String facilityCode = "";
            if(FulfillmentType.LOCAL_FITTING.equals(shipmentDto.getOrderItems().get(0).getFulfillmentType())) {
                List<OrderItemMetaDataDto> orderItemMetaDataDtos = orderItemMetaService.search("orderItemId.eq:" + shipmentDto.getOrderItems().get(0).getId());
                logger.info("[enrichOtcShipmentData] local fitting orderItemMetaDataDtos :{}", orderItemMetaDataDtos);
                Optional<OrderItemMetaDataDto> storeFacilityCode = orderItemMetaDataDtos.stream().filter(o -> o.getEntityKey().equals("LOCAL_FITTING_FACILITY")).findFirst();
                logger.info("[enrichOtcShipmentData] local fitting facility :{}", storeFacilityCode);
                if (!(storeFacilityCode.isPresent() && StringUtils.hasLength(storeFacilityCode.get().getEntityValue()))) {
                    throw new ApplicationException("Local Facility code is not present for OTC shipment :" + shipmentDto.getId());
                }
                facilityCode = storeFacilityCode.get().getEntityValue();
            }else {

                Optional<OrderItemMetaDataDto> centralFacilityCode = shipmentDto.getOrderItems().get(0).getOrderItemMetaData().stream().filter(o -> o.getEntityKey().equals("CENTRAL_FACILITY_CODE")).findFirst();
                if(centralFacilityCode.isPresent() && StringUtils.hasLength(centralFacilityCode.get().getEntityValue())){
                    facilityCode = centralFacilityCode.get().getEntityValue();
                    shipmentDto.setFacility(facilityCode);
                    shipmentDto.setWmsShippingPackageId(String.format("S%s%s", facilityCode, shipmentDto.getWmsOrderCode()));
                    return;
                }
                List<OrderMetaDataDto> orderMetaDataDtos = orderMetaService.search("orderId.eq:" + shipmentDto.getOrderItems().get(0).getOrderId());
                Optional<OrderMetaDataDto> storeFacilityCode = orderMetaDataDtos.stream().filter(o -> o.getEntityKey().equals("FACILITY_CODE")).findFirst();
                if (!(storeFacilityCode.isPresent() && StringUtils.hasLength(storeFacilityCode.get().getEntityValue()))) {
                    throw new ApplicationException("Facility code is not present for OTC shipment :" + shipmentDto.getId());
                }
                facilityCode = storeFacilityCode.get().getEntityValue();
            }

            shipmentDto.setFacility(facilityCode);
            shipmentDto.setWmsShippingPackageId(String.format("S%s%s", facilityCode, shipmentDto.getWmsOrderCode()));
        }
    }

    private void addEntryInShipmentTimeline(Long orderId, List<ShipmentDto> shipmentDtoList) {
        for (ShipmentDto shipmentDto : shipmentDtoList) {
            for (OrderItemDto orderItemDto : shipmentDto.getOrderItems()) {
                ShipmentTimelineDto shipmentTimelineDto = new ShipmentTimelineDto();

                shipmentTimelineDto.setOrderId(orderId);
                shipmentTimelineDto.setShipmentId(shipmentDto.getId());
                shipmentTimelineDto.setOrderItemId(orderItemDto.getId());
                shipmentTimelineDto.setCreatedBy(DEFAULT_OMS_USER);
                shipmentTimelineDto.setUpdatedBy(DEFAULT_OMS_USER);

                shipmentTimelineService.save(shipmentTimelineDto);
            }
        }
    }

    private List<ShipmentDto> businessSplitAndReturnShipments(OmsOrderEvent omsOrderEvent, Map<String, ShipmentDto> shipmentMap) throws Exception {
        HashMap<String, String> splitDetails = new HashMap<>();
        for (OrderItemDto orderItem : omsOrderEvent.getOrderDto().getOrderItems()) {
            logger.info("[businessSplitAndReturnShipments] orderItem {}", orderItem);
            if (!(orderItem.getProductId().equals(Long.valueOf(TEMP_LEFT_LENS_PID))
                    || orderItem.getProductId().equals(Long.valueOf(TEMP_RIGHT_LENS_PID)))
            ) {
                logger.info("[businessSplitAndReturnShipments] orerItem {} is allowd for split logic", orderItem);
                executeBusinessSplitAndUpdateShipmentMap(orderItem, omsOrderEvent, splitDetails, shipmentMap);
            }
        }

        return new ArrayList<>(shipmentMap.values());
    }

    private List<ShipmentDto> persistShipmentLevelData(List<ShipmentDto> shipmentDtoList, OrderDto orderDto){
        List<ShipmentDto> updatedShipmentDtoList = new ArrayList<>();
        Map<Long, List<OrderItemDto>> magentoItemToOrderItemsMap = orderDto.getOrderItems().stream()
                .filter(oi -> ProductDeliveryType.B2B.equals(oi.getProductDeliveryType()))
                .filter(oi -> !ItemType.LEFT_LENS.equals(oi.getItemType()))
                .filter(oi -> !ItemType.RIGHT_LENS.equals(oi.getItemType()))
                .collect(Collectors.groupingBy(OrderItemDto::getMagentoItemId));
        logger.info("[persistShipmentLevelData] magentoItemToOrderItemsMap {}", magentoItemToOrderItemsMap);
        for(Map.Entry<Long, List<OrderItemDto>> entry : magentoItemToOrderItemsMap.entrySet()) {
            List<OrderItemDto> itemDtoList = entry.getValue();
            logger.info("[persistShipmentLevelData] updating data  for B2B orders for order {} itemDtoList size {}", orderDto.getIncrementId(), itemDtoList.size());
            if(itemDtoList.size() == 2) {
                orderItemService.updateB2BRefId(itemDtoList.get(1).getId(), itemDtoList.get(0).getId());
            }
        }

        for (ShipmentDto shipmentDto : shipmentDtoList) {
            updateWmsCodeForLensItemsIfRequired(shipmentDto, orderDto);
            enrichOtcShipmentData(shipmentDto);
            enrichB2BShipmentDataIfApplicable(shipmentDto, orderDto);
            updatedShipmentDtoList.add(shipmentService.save(shipmentDto));
        }
        return updatedShipmentDtoList;
    }

    private void updateWmsCodeForLensItemsIfRequired(ShipmentDto shipmentDto, OrderDto orderDto) {
        Long frameMagentoItemId = Long.valueOf(0);

        shipmentDto.setShipmentType(String.valueOf(createOrderMapper.getShipmentType(shipmentDto)));
        for (OrderItemDto orderItemDto : shipmentDto.getOrderItems()) {
            if (((ItemType.EYEFRAME.equals(orderItemDto.getItemType())
                    || ItemType.SMART_GLASSES.equals(orderItemDto.getItemType())
                    || ItemType.SUNGLASS.equals(orderItemDto.getItemType())
                    || ItemType.SUNGLASSES.equals(orderItemDto.getItemType()))
                    || ItemType.CONTACT_LENS.equals(orderItemDto.getItemType()))
                    || (ItemType.CONTACT_LENS_SOLUTION.equals(orderItemDto.getItemType())
                    && isPureContactLensShipment(shipmentDto))
                    || (ItemType.ACCESSORIES_REVENUE.equals(orderItemDto.getItemType())
                    || ItemType.ACCESSORIES.equals(orderItemDto.getItemType())
                    || ItemType.NON_POWER_READING.equals(orderItemDto.getItemType()))
                    || (ItemType.LOYALTY_SERVICES.equals(orderItemDto.getItemType())
                    && FittingType.NOT_REQD.equals(orderItemDto.getFittingType()) &&  isPureLoyaltyShipment(shipmentDto))
            ){
                logger.info("[updateWmsCodeForLensItemsIfRequired] setting shipment type for orderItem {}", orderItemDto.getId());
                shipmentDto.setShipmentSubType(omsCommonUtil.getShipmentSubType(orderDto, shipmentDto.getOrderItems()));
                frameMagentoItemId = orderItemDto.getMagentoItemId();
            }
        }

        if(!Objects.isNull(shipmentDto.getOrderItems().get(0).getB2bReferenceItemId())) {
            logger.info("skip order item attach for virtual order {}", shipmentDto.getId());
            return;
        }
        logger.info("[updateWmsCodeForLensItemsIfRequired] frameMagentoId :{}", frameMagentoItemId);
        List<OrderItemDto> existingOrderItems = new ArrayList<>(shipmentDto.getOrderItems());
        for (OrderItemDto orderItemDto : orderDto.getOrderItems()) {
            logger.info("[updateWmsCodeForLensItemsIfRequired] item magento id :{}", orderItemDto.getParentMagentoItemId());
            if ((ItemType.RIGHT_LENS.equals(orderItemDto.getItemType())
                    || ItemType.LEFT_LENS.equals(orderItemDto.getItemType()))
                    && FittingType.NOT_REQD.equals(orderItemDto.getFittingType())
                    && orderItemDto.getParentMagentoItemId().equals(frameMagentoItemId)
            ) {
                orderItemDto.setShipmentId(shipmentDto.getId());
                existingOrderItems.add(orderItemDto);
                logger.info("[updateWmsCodeForLensItemsIfRequired] updating  existingOrderItems  for orderItem {}", orderItemDto.getId());
            }
        }

        shipmentDto.setOrderItems(existingOrderItems);
        logger.info("[updateWmsCodeForLensItemsIfRequired] shipmentDto {}", shipmentDto);
    }

    private void executeBusinessSplitAndUpdateShipmentMap(OrderItemDto orderItem, OmsOrderEvent omsOrderEvent, HashMap<String, String> splitDetails, Map<String, ShipmentDto> shipmentMap) throws Exception {
        ShipmentDto shipmentDto = new ShipmentDto();
        BeanUtils.copyProperties(omsOrderEvent.getShipmentDtoList().get(0), shipmentDto);
        if(orderItem.getNavChannel() == NavChannel.FOFOOTC) {
            Optional<OrderMetaDataDto> orderMetaDataDtoOpt =  omsOrderEvent.getOrderDto().getOrderMetaData().stream().filter(om -> ORDER_META_KEY_FACILITY_CODE.equals(om.getEntityKey())).findFirst();
            if(orderMetaDataDtoOpt.isPresent()){
                shipmentDto.setLegalOwner(orderMetaDataDtoOpt.get().getEntityValue());
            }else {
                orderMetaDataDtoOpt.ifPresent(orderMetaDataDto -> shipmentDto.setLegalOwner(orderMetaDataDto.getEntityValue()));
            }//remove this harcode legal owner
        } else if (omsOrderEvent.getShipmentLegalOwnerMap().containsKey(orderItem.getMagentoItemId())) {
            shipmentDto.setLegalOwner(omsOrderEvent.getShipmentLegalOwnerMap().get(orderItem.getMagentoItemId()));
        } else {
            shipmentDto.setLegalOwner(omsOrderEvent.getShipmentLegalOwnerMap().get(orderItem.getParentMagentoItemId()));
        }

        String key = getGeneratedKey(orderItem, shipmentDto, omsOrderEvent.getOrderDto().getIncrementId());
        logger.info("[executeBusinessSplittingLogic] generated key for order {} and orderItem id {} is {}", omsOrderEvent.getOrderDto().getIncrementId(), orderItem.getId() ,key);

        Product product = new Product();
        if (LENS_PACKAGE_PID.equals(orderItem.getProductId())
                || LENS_COATING_PID.equals(orderItem.getProductId())
        ) {
            product = new Product();
        } else if(ItemType.CONTACT_LENS.equals(orderItem.getItemType())) {
            Integer pid = getRequestedProductIdForContactLensItem(orderItem, omsOrderEvent);
            product = catalogOpsConnector.findProductDetailsByProductId(Long.valueOf(pid));
        }
        else {
            product = catalogOpsConnector.findProductDetailsByProductId(orderItem.getProductId());
        }

        if(Boolean.TRUE.equals(enablePureLoyaltyHandlingInNexs) && product.getClassification() == LOYALTY_CLASSIFICATION) {
            key = key + LOYALTY;
        }

        Map<String, String> orderEligibilityStatus = isOrderEligibleForSplit(omsOrderEvent, orderItem, product, key);
        String isOrderEligibleForSplit = orderEligibilityStatus.get("isOrderEligibleForSplit");
        logger.info("[executeBusinessSplittingLogic] orderItem {} isOrderEligibleForSplit - {}", orderItem.getId() , isOrderEligibleForSplit);

        if (NavChannel.MPDTC.equals(orderItem.getNavChannel())) {
            updateSplitShipmentMap(orderItem, shipmentMap, shipmentDto);
            return;
        }

        key = orderEligibilityStatus.get("key");
        if (isOrderEligibleForSplit.equalsIgnoreCase("true")) {
            updateShipmentForSplitEligibleOrder(shipmentDto, omsOrderEvent.getOrderDto(), splitDetails, key);
            logger.info("[executeBusinessSplittingLogic] order {} and orderItem {} is eligible for split. new wmsOrderCode is {}", omsOrderEvent.getOrderDto().getIncrementId(), orderItem.getId() ,shipmentDto.getWmsOrderCode());
        } else {
            key = updateShipmentForNonSplitEligibleOrder(shipmentDto, omsOrderEvent.getOrderDto(), splitDetails, orderItem, key, product);
            logger.info("[executeBusinessSplittingLogic] order {} and orderItem {} is not eligible for split. wmsOrderCode is {}", omsOrderEvent.getOrderDto().getIncrementId(), orderItem.getId() ,shipmentDto.getWmsOrderCode());
        }

        //if orderItem is OTC  then set facility enrichOTCFacilityIfApplicable
        splitDetails.put(key, shipmentDto.getWmsOrderCode());
        updateSplitShipmentMap(orderItem, shipmentMap, shipmentDto);
    }

    private  Integer getRequestedProductIdForContactLensItem(OrderItemDto orderItem, OmsOrderEvent omsOrderEvent) {
        Long magentoItemId = orderItem.getMagentoItemId();
        Optional<LineItemPayloadEvent> lineItemPayloadEventOptional =  omsOrderEvent.getLineItemPayloadEventList().stream().filter(o -> Objects.equals(magentoItemId, o.getLineItemId())).findFirst();
        if(lineItemPayloadEventOptional.isPresent()) {
            Integer pid = lineItemPayloadEventOptional.get().getSku();
            return pid;
        }
        logger.info("[getRequestedProductIdForContactLensItem] Pid not found for magentoItemd {}", magentoItemId);
        return  null;
    }

    private void updateSplitShipmentMap(OrderItemDto orderItem, Map<String, ShipmentDto> shipmentMap, ShipmentDto shipmentDto) {
        if (shipmentMap.containsKey(shipmentDto.getWmsOrderCode())) {
            ShipmentDto existingShipment = shipmentMap.get(shipmentDto.getWmsOrderCode());
            List<OrderItemDto> existingOrderItems = new ArrayList<>(existingShipment.getOrderItems());
            if (CollectionUtils.isEmpty(existingOrderItems)) {
                existingOrderItems = new ArrayList<>();
            }
            existingOrderItems.add(orderItem);

            shipmentDto.setOrderItems(existingOrderItems);
        } else {
            shipmentDto.setOrderItems(Collections.singletonList(orderItem));
        }

        shipmentMap.put(shipmentDto.getWmsOrderCode(), shipmentDto);
    }

    private void updateShipmentForSplitEligibleOrder(ShipmentDto shipmentDto, OrderDto orderDto, HashMap<String, String> splitDetails, String key) {
        if (splitDetails.get(orderDto.getIncrementId() + "_MAX") != null) {
            if (splitDetails.get(key.concat(ACCESSORIES)) == null
                    || splitDetails.get(key.concat(ACCESSORIES)).equalsIgnoreCase("0")
            ) {
                Integer maxUnicomOrderCode = Integer.valueOf(splitDetails.get(orderDto.getIncrementId() + "_MAX")) + 1;
                splitDetails.replace(orderDto.getIncrementId() + "_MAX", maxUnicomOrderCode.toString());
                shipmentDto.setWmsOrderCode(orderDto.getJunoOrderId() + "-" + maxUnicomOrderCode);
            } else {
                shipmentDto.setWmsOrderCode(splitDetails.get(key.concat(ACCESSORIES)));
                splitDetails.replace(key.concat(ACCESSORIES), "0");
            }
        } else {
            splitDetails.put(orderDto.getIncrementId() + "_MAX", String.valueOf(0));
            shipmentDto.setWmsOrderCode(String.valueOf(orderDto.getJunoOrderId()));
        }
    }

    private String updateShipmentForNonSplitEligibleOrder(ShipmentDto shipmentDto, OrderDto orderDto, HashMap<String, String> splitDetails, OrderItemDto orderItem, String key, Product product) {
        if (splitDetails.get(key) != null) {
            shipmentDto.setWmsOrderCode(splitDetails.get(key));
        } else if (SHIPPING_ACCESSORIES_CLASSIFICATIONS.contains(String.valueOf(product.getClassification()))
                && splitDetails.get(key.substring(0, key.length() - 1).concat("1")) != null
        ) {
            key = key.substring(0, key.length() - 1).concat("1");
            shipmentDto.setWmsOrderCode(splitDetails.get(key));
        } else if (splitDetails.get(orderDto.getIncrementId() + "_MAX") != null) {
            Integer maxUnicomOrderCode= Integer.valueOf(splitDetails.get(orderDto.getIncrementId()+"_MAX")) + 1;
            splitDetails.replace(orderDto.getIncrementId() + "_MAX", maxUnicomOrderCode.toString());
            shipmentDto.setWmsOrderCode(orderDto.getJunoOrderId() + "-" + maxUnicomOrderCode);
        } else {
            splitDetails.put(orderDto.getIncrementId() + "_MAX", String.valueOf(0));
            shipmentDto.setWmsOrderCode(String.valueOf(orderDto.getJunoOrderId()));
        }

        if (enablePureLoyaltyHandlingInNexs && splitDetails.get(key) == null && SHIPPING_ACCESSORIES_CLASSIFICATIONS.contains(String.valueOf(product.getClassification()))
                && product.getClassification() != LOYALTY_CLASSIFICATION) {
            if (Long.valueOf(GOLD_MEMBERSHIP_PRODUCT_ID).equals(orderItem.getProductId())
                    || ProductDeliveryType.DTC.equals(orderItem.getProductDeliveryType())
            ) {
                key = key.substring(0, key.length() - 1).concat("1");
            }
            splitDetails.put(key.concat(ACCESSORIES), shipmentDto.getWmsOrderCode());
        } else if(splitDetails.get(key) == null && SHIPPING_ACCESSORIES_CLASSIFICATIONS.contains(String.valueOf(product.getClassification()))) {
            if (Long.valueOf(GOLD_MEMBERSHIP_PRODUCT_ID).equals(orderItem.getProductId())
                    || ProductDeliveryType.DTC.equals(orderItem.getProductDeliveryType())
            ) {
                key = key.substring(0, key.length() - 1).concat("1");
            }
            splitDetails.put(key.concat(ACCESSORIES), shipmentDto.getWmsOrderCode());
        }
        return key;
    }

    public Map<String, String> isOrderEligibleForSplit(OmsOrderEvent omsOrderEvent, OrderItemDto orderItem, Product product, String key) {
        boolean isOrderEligibleForSplit = true;
        Map<String, String> orderEligibilityStatus = new HashMap<>();

        logger.info("[isOrderEligibleForSplit] bulk and otc -- {}  contact-lens-solution and shipping-accessories -- {} contact-lens and sunglss -- {} cod and sunglass -- {} for  orderItem Id {}",
                Channel.FRANCHISEBULK.equals(orderItem.getChannel())
                        || (orderItem.getProductDeliveryType() != null
                        && (orderItem.getProductDeliveryType().equals(ProductDeliveryType.OTC) && (ObjectUtils.isEmpty(orderItem.getFulfillmentType())  || !orderItem.getFulfillmentType().equals(FulfillmentType.LOCAL_FITTING)))),
                ((!SHIPPING_DESTINATION_STORE.equalsIgnoreCase(orderItem.getShippingDestinationType())
                        && product.getClassification() == CONTACT_LENS_SOLUTION_CLASSIFICATION_ID)
                        || SHIPPING_ACCESSORIES_CLASSIFICATIONS.contains(String.valueOf(product.getClassification()))),
                (product.getClassification() == CONTACT_LENS_CLASSIFICATION_ID
                        || (!SUNGLASS_AND_SOLUTION_CLASSIFICATIONS.contains(String.valueOf(product.getClassification()))
                        && FITTING_NOT_REQUIRED.equalsIgnoreCase(orderItem.getFittingType().name()))),
                (PaymentMethod.CASHONDELIVERY.getValue().equalsIgnoreCase(omsOrderEvent.getOrderDto().getPaymentMethod())
                        && SUNGLASS_CLASSIFICATIONS.contains(String.valueOf(product.getClassification()))
                        && FITTING_NOT_REQUIRED.equalsIgnoreCase(orderItem.getFittingType().name())),
                orderItem.getId());


        if ((Channel.FRANCHISEBULK.equals(orderItem.getChannel())
                || (orderItem.getProductDeliveryType() != null
                && (orderItem.getProductDeliveryType().equals(ProductDeliveryType.OTC) && (ObjectUtils.isEmpty(orderItem.getFulfillmentType())  || !orderItem.getFulfillmentType().equals(FulfillmentType.LOCAL_FITTING)))))

            || ((!SHIPPING_DESTINATION_STORE.equalsIgnoreCase(orderItem.getShippingDestinationType())
                && product.getClassification() == CONTACT_LENS_SOLUTION_CLASSIFICATION_ID)
                || SHIPPING_ACCESSORIES_CLASSIFICATIONS.contains(String.valueOf(product.getClassification())))

            || (product.getClassification() == CONTACT_LENS_CLASSIFICATION_ID
                || (!SUNGLASS_AND_SOLUTION_CLASSIFICATIONS.contains(String.valueOf(product.getClassification()))
                && FITTING_NOT_REQUIRED.equalsIgnoreCase(orderItem.getFittingType().name())))

            || (PaymentMethod.CASHONDELIVERY.getValue().equalsIgnoreCase(omsOrderEvent.getOrderDto().getPaymentMethod())
                && SUNGLASS_CLASSIFICATIONS.contains(String.valueOf(product.getClassification()))
                && FITTING_NOT_REQUIRED.equalsIgnoreCase(orderItem.getFittingType().name()))
        ) {
            logger.info("[isOrderEligibleForSplit] orderItem id {} not eligible for split", orderItem.getId());
            isOrderEligibleForSplit = false;
            key = key.concat("0");
        }

        if (isOrderEligibleForSplit) {
            key = key.concat("1");
        }
        orderEligibilityStatus.put("isOrderEligibleForSplit", String.valueOf(isOrderEligibleForSplit));
        orderEligibilityStatus.put("key", key);
        logger.info("[isOrderEligibleForSplit] split {} key {}", orderEligibilityStatus.get("isOrderEligibleForSplit"),  orderEligibilityStatus.get("key"));
        return orderEligibilityStatus;
    }

    public String getGeneratedKey(OrderItemDto orderItem, ShipmentDto shipmentDto, Long incrementId){
        StringBuilder stringBuilder = new StringBuilder();
        String facilityCode = null;
        Optional<OrderItemMetaDataDto> optionalOrderItemMetaDataDto =  orderItem.getOrderItemMetaData().stream().filter(oi -> OREDER_ITEM_META_KEY_CENTRAL_FACILITY_CODE.equals(oi.getEntityKey())).findFirst();
        if(!ObjectUtils.isEmpty(orderItem.getFulfillmentType())
                && FulfillmentType.LOCAL_FITTING.equals(orderItem.getFulfillmentType())){

            Optional<OrderItemMetaDataDto> facilityCodeOptional = orderItem.getOrderItemMetaData().stream()
                    .filter(oim -> oim.getEntityKey().equals(ORDER_ITEM_META_KEY_LOCAL_FITTING_FACILITY))
                    .findFirst();
            if(facilityCodeOptional.isPresent()){
                facilityCode = facilityCodeOptional.get().getEntityValue();
            }else{
                facilityCode = FulfillmentType.LOCAL_FITTING.name();
            }

        }else if(!ObjectUtils.isEmpty(orderItem.getB2bReferenceItemId())){
            facilityCode = "VIRTUAL_FACILITY";
        }
        else if(optionalOrderItemMetaDataDto.isPresent() && StringUtils.hasLength(optionalOrderItemMetaDataDto.get().getEntityValue())) {
            facilityCode = optionalOrderItemMetaDataDto.get().getEntityValue();
        }

        String hubCode = "";
        String searchTerm = "orderItemId.eq:" +
                orderItem.getId() +
                "___" +
                "entityKey.eq:" +
                ORDER_ITEM_META_KEY_HUB_CODE;
        List<OrderItemMetaDataDto> orderItemMetaDataDtoList = orderItemMetaService.search(searchTerm);
        for(OrderItemMetaDataDto orderItemMetaDataDto : orderItemMetaDataDtoList){
            if(orderItemMetaDataDto.getEntityKey().equals("HUB_CODE")){
                hubCode = orderItemMetaDataDto.getEntityValue();
                break;
            }
        }
        String key = "";
        if(orderItem.getProductDeliveryType() != null){
            key = stringBuilder.append(incrementId)
                    .append(orderItem.getProductDeliveryType())
                    .append(facilityCode)
                    .append(hubCode)
                    .append(SHIPPING_DESTINATION_STORE.equalsIgnoreCase(orderItem.getShippingDestinationType()))
                    .toString();
        }

        return key;
    }

    private void persistOrderLevelData(OmsOrderEvent omsOrderEvent) {
        List<OrderItemDto> orderItemDtoList = new ArrayList<>();
        for (OrderItemDto orderItemDto : omsOrderEvent.getOrderDto().getOrderItems()) {
            if (ItemType.getLensTypesToSkip().contains(orderItemDto.getItemType().name())) {
                continue;
            }
            orderItemDtoList.add(orderItemDto);
        }

        omsOrderEvent.getOrderDto().setOrderItems(orderItemDtoList);
        omsOrderEvent.setOrderDto(orderService.save(omsOrderEvent.getOrderDto()));
    }

    private boolean isPureLoyaltyShipment(ShipmentDto shipmentDto){
        Set<ItemType> itemTypeSet =  shipmentDto.getOrderItems().stream()
                .map(OrderItemDto::getItemType)
                        .collect(Collectors.toSet());
        return itemTypeSet.size() == 1 && itemTypeSet.contains(ItemType.LOYALTY_SERVICES);
    }

    private void enrichB2BShipmentDataIfApplicable(ShipmentDto shipmentDto, OrderDto orderDto) {
        logger.info("[enrichB2BShipmentDataIfApplicable] b2bId {} for shipmentId: {}", shipmentDto.getOrderItems().get(0).getB2bReferenceItemId(), shipmentDto.getId());
        if(shipmentDto.getOrderItems().get(0).getB2bReferenceItemId() == null){
            return;
        }
        shipmentDto.setLegalOwner(NavChannel.FOFOB2B == shipmentDto.getOrderItems().get(0).getNavChannel() ? shipmentDto.getFacility() : shipmentDto.getLegalOwner());
        Optional<OrderMetaDataDto> orderMetaDataDto =  orderDto.getOrderMetaData().stream().filter(o -> o.getEntityKey().equals(ORDER_META_KEY_FACILITY_CODE)).findFirst();
        if(orderMetaDataDto.isPresent() && orderMetaDataDto.get().getEntityValue().equals("0")){
            OrderItemDto orderItemDto =  shipmentDto.getOrderItems()
                    .stream()
                    .filter(oi -> oi.getParentMagentoItemId() == null)
                    .findFirst()
                    .orElse(null);
            if (orderItemDto != null) {
                String facility= null;
                String hubCode = null;
                for(OrderItemMetaDataDto orderItemMetaDataDto : orderItemDto.getOrderItemMetaData()) {
                    if(orderItemMetaDataDto.getEntityKey().equals(ORDER_ITEM_META_KEY_VIRTUAL_FACILITY_CODE)) {
                        facility = orderItemMetaDataDto.getEntityValue();
                    } else if(orderItemMetaDataDto.getEntityKey().equals(ORDER_ITEM_META_KEY_HUB_CODE)) {
                        hubCode = orderItemMetaDataDto.getEntityValue();
                    }

                }
                shipmentDto.setFacility(facility);
                shipmentDto.setLegalOwner(legalOwnerUtil.getLegalOwner(facility, hubCode, shipmentDto.getLegalOwnerCountry()));
            }
        } else{
            OrderMetaDataDto orderMetaDataDto1 = orderDto.getOrderMetaData()
                    .stream()
                    .filter(o -> o.getEntityKey().equals(ORDER_META_KEY_FACILITY_CODE))
                    .findFirst()
                    .orElse(null);
            OrderItemMetaDataDto orderItemMetaDataDto = shipmentDto.getOrderItems().get(0).getOrderItemMetaData().stream()
                    .filter(oimd -> oimd.getEntityKey().equals(ORDER_ITEM_META_KEY_HUB_CODE))
                    .findAny()
                    .orElse(null);
            String facility = orderMetaDataDto1 != null ? orderMetaDataDto1.getEntityValue() : null;
            String hubCode = orderItemMetaDataDto != null ? orderItemMetaDataDto.getEntityValue() : null;
            logger.info("hubcode {}, facility {}", hubCode, facility);
            shipmentDto.setFacility(facility);
            shipmentDto.setLegalOwner(legalOwnerUtil.getLegalOwner(facility, hubCode, shipmentDto.getLegalOwnerCountry()));
        }

    }

    private boolean  isPureContactLensShipment(ShipmentDto shipmentDto) {
        Set<ItemType> itemTypeSet =  shipmentDto.getOrderItems().stream()
                .map(OrderItemDto::getItemType)
                .collect(Collectors.toSet());
        return itemTypeSet.size() == 1 && itemTypeSet.contains(ItemType.CONTACT_LENS_SOLUTION);
    }




}
