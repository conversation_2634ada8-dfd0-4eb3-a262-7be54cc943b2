package com.lenskart.oms.strategy.impl;

import com.lenskart.oms.connector.NexsWmsConnector;
import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.enums.*;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.mapper.WmsCancelOrderRequestMapper;
import com.lenskart.oms.model.Action;
import com.lenskart.oms.request.OmsOrderEvent;
import com.lenskart.oms.request.WmsOrderActionRequest;
import com.lenskart.oms.strategy.BaseOrderEventStrategy;
import com.lenskart.oms.validators.CancelOrderEventValidator;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.lenskart.oms.constants.ApplicationConstants.VSM_PARTIAL;

@Component
@Setter(onMethod__ = {@Autowired})
public class CancelOrderStrategy extends BaseOrderEventStrategy {

    private WmsCancelOrderRequestMapper wmsCancelOrderRequestMapper;
    private NexsWmsConnector nexsWmsConnector;

    private CancelOrderEventValidator cancelOrderEventValidator;

    @Override
    protected OrderEventType supportedOrderEvents() {
        return OrderEventType.CANCEL_ORDER;
    }

    @Override
    protected void preExecute(OmsOrderEvent orderRequest) throws ApplicationException {
        cancelOrderEventValidator.validateCancelOrderEvent(orderRequest);
    }

    @Override
    protected void execute(OmsOrderEvent orderRequest) throws ApplicationException {
        processCancelOrder(orderRequest);
    }

    private void processCancelOrder(OmsOrderEvent omsOrderEvent) throws ApplicationException {
        try {
            OrderDto orderDto;
            Map<Long, OrderItemDto> orderItemDtoMap;
            if (CollectionUtils.isEmpty(omsOrderEvent.getCancelOrderRequest().getOrderItems())) {
                orderDto = orderService.findByIncrementId(omsOrderEvent.getIncrementId());
                orderItemDtoMap = orderDto.getOrderItems().stream().collect(Collectors.toMap(OrderItemDto :: getId, Function.identity()));
            } else {
                List<OrderItemDto> orderItemDtoList = omsOrderEvent.getCancelOrderRequest().getOrderItems();
                orderDto = orderService.findById(orderItemDtoList.get(0).getOrderId());

                Set<Long> orderItemIdSet = orderItemDtoList.stream().map(OrderItemDto :: getId).collect(Collectors.toSet());
                orderItemDtoMap = orderDto.getOrderItems().stream().filter(orderItemDto ->
                    orderItemIdSet.contains(orderItemDto.getId())
                ).collect(Collectors.toMap(OrderItemDto :: getId, Function.identity()));
            }

            Action transitionAction = omsTransitionUtil.getTransitionActionByOperationAndStatus(
                    EventToOperationMap.CANCELLED.getOperation(), orderItemDtoMap.values().iterator().next().getItemStatus()
            );

            Set<Long> shipmentIds = new HashSet<>();
            boolean isCancellationSubTypeNull = !StringUtils.hasLength(omsOrderEvent.getCancelOrderRequest().getCancellationSubType());
            logger.info("[processCancelOrder] isCancellationSubTypeNull: {}, CancellationSubType: {}, increment id: {}", isCancellationSubTypeNull, omsOrderEvent.getCancelOrderRequest().getCancellationSubType(), omsOrderEvent.getIncrementId());
            String cancellationSubType = isCancellationSubTypeNull ? null : omsOrderEvent.getCancelOrderRequest().getCancellationSubType().toUpperCase();
            for (OrderItemDto orderItemDto : orderItemDtoMap.values()) {
                shipmentIds.add(orderItemDto.getShipmentId());

                orderItemDto.setItemStatus(transitionAction.getItemStatus().getItemStatus());
                orderItemDto.setItemSubStatus(!isCancellationSubTypeNull ? getOrderItemSubStatus(cancellationSubType) : transitionAction.getItemStatus().getItemSubStatus());
                orderItemService.save(orderItemDto);
                logger.info("[processCancelOrder] updated successfully in orderItems table for increment id: {} with cancellationSubType: {}", omsOrderEvent.getIncrementId(), orderItemDto.getItemSubStatus());
            }

            List<ShipmentDto> shipmentDtoList = new ArrayList<>();
            for (Long shipmentId : shipmentIds) {
                shipmentDtoList.add(shipmentService.findById(shipmentId));
            }

            for (ShipmentDto shipmentDto : shipmentDtoList) {
                if(!shipmentDto.getShipmentStatus().equals(ShipmentStatus.OMS_REASSIGNED)) {
                    shipmentDto.setShipmentStatus(transitionAction.getShipmentStatus().getShipmentStatus());
                    shipmentDto.setShipmentSubStatus(!isCancellationSubTypeNull ? getShipmentSubStatus(cancellationSubType) : transitionAction.getShipmentStatus().getShipmentSubStatus());

                    shipmentService.save(shipmentDto);
                    logger.info("[processCancelOrder] updated successfully in shipment table for increment id: {} with cancellationSubType: {}", omsOrderEvent.getIncrementId(), shipmentDto.getShipmentSubStatus());
                }
            }

            if (isOrderStatusUpdateRequired(transitionAction, orderDto)) {
                orderDto.setOrderStatus(transitionAction.getOrderStatus().getOrderStatus());
                orderDto.setOrderSubStatus(!isCancellationSubTypeNull ? getOrderSubStatus(cancellationSubType) : transitionAction.getOrderStatus().getOrderSubStatus());

                orderService.save(orderDto);
                logger.info("[processCancelOrder] updated successfully in orders table for increment id: {} with cancellationSubType: {}", omsOrderEvent.getIncrementId(), orderDto.getOrderSubStatus());
            }
            omsOrderEvent.setOrderDto(orderDto);
        } catch (Exception e) {
            logger.error("[CancelOrderStrategyOrderStrategy -> processCancelOrder} Cancel order persistence failed for for incrementId - " + omsOrderEvent.getIncrementId() + ", errorMessage - " + e.getMessage(), e);
            throw new ApplicationException("[CancelOrderStrategyOrderStrategy -> processCancelOrder} Cancel order persistence failed with exception" + e, null);
        }
    }

    private static OrderItemSubStatus getOrderItemSubStatus(String cancellationSubType) {
        try {
            return OrderItemSubStatus.valueOf(cancellationSubType);
        }  catch (Exception e){
            return OrderItemSubStatus.CANCELLED;
        }
    }

    private static ShipmentSubStatus getShipmentSubStatus(String cancellationSubType) {
        try{
        return ShipmentSubStatus.valueOf(cancellationSubType);
        } catch (Exception e){
            return ShipmentSubStatus.CANCELLED;
        }
    }

    private static OrderSubStatus getOrderSubStatus(String cancellationSubType) {
        try{
            return OrderSubStatus.valueOf(cancellationSubType);
        } catch (Exception e){
            return OrderSubStatus.CANCELLED;
        }
    }

    @Override
    protected void postExecute(OmsOrderEvent orderRequest) throws ApplicationException {
        try {
            logger.info("CancelOrderStrategy.postExecute - OmsOrderEvent : {}", orderRequest);
            if (CancellationType.PARTIAL_CANCELLATION.equals(orderRequest.getCancelOrderRequest().getCancellationType()) ||
                    (OrderType.DISTRIBUTOR_ORDER.equals(orderRequest.getOrderDto().getOrderType()) && CancellationType.FULL_CANCELLATION.equals(orderRequest.getCancelOrderRequest().getCancellationType()))) {
                WmsOrderActionRequest orderActionRequest = wmsCancelOrderRequestMapper.orderRequestToWmsCancelOrderRequest(orderRequest);
                if (!CollectionUtils.isEmpty(orderActionRequest.getOrderItems())) {
                    ShipmentDto shipmentDto = shipmentService.findBySearchTerms("wmsOrderCode.eq:" + orderActionRequest.getOrderItems().get(0).getOrderCode());
                    if (shipmentDto != null && isShipmentSyncedToWMS(shipmentDto)) {
                        nexsWmsConnector.triggerCancelOrderInNexsWms(orderActionRequest, orderRequest.getOrderDto().getOrderType());
                    }
                }
            }
        } catch (Exception e) {
            logger.error("[CancelOrderStrategyOrderStrategy -> processCancelOrder} Cancel order wms call failed for incrementId - " + orderRequest.getIncrementId() + ", errorMessage - " + e.getMessage(), e);
            throw new ApplicationException("[CancelOrderStrategyOrderStrategy -> processCancelOrder} Cancel order wms call failed with exception " + e.getMessage(), e);
        }
    }
}
