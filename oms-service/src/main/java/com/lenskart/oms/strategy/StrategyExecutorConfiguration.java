package com.lenskart.oms.strategy;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

@Configuration
public class StrategyExecutorConfiguration {

    @Bean
    public OrderEventStrategyExecutor orderEventStrategyExecutor(List<BaseOrderEventStrategy> updateStrategies) {
        return new OrderEventStrategyExecutor(
                updateStrategies.stream()
                        .collect(Collectors.toMap(BaseOrderEventStrategy::supportedOrderEvents, Function.identity()))
        );
    }

    @Bean
    public ShipmentEventStrategyExecutor shipmentEventStrategyExecutor(List<BaseShipmentEventStrategy> updateStrategies) {
        return new ShipmentEventStrategyExecutor(
                updateStrategies.stream()
                        .collect(Collectors.toMap(BaseShipmentEventStrategy::supportedOrderEvents, Function.identity()))
        );
    }

    @Bean
    public OtcShipmentEventStrategyExecutor otcShipmentEventStrategyExecutor(List<BaseOtcShipmentEventStrategy> updateStrategies){
        return new OtcShipmentEventStrategyExecutor(
                updateStrategies.stream()
                                .collect(Collectors.toMap(BaseOtcShipmentEventStrategy::supportedOrderEvents,Function.identity())));
    }


}
