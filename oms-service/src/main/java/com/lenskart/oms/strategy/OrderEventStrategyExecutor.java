package com.lenskart.oms.strategy;

import com.lenskart.oms.enums.OrderEventType;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.request.OmsOrderEvent;
import lombok.AllArgsConstructor;

import java.util.Map;
import java.util.Objects;

@AllArgsConstructor
public class OrderEventStrategyExecutor {
    private final Map<OrderEventType, BaseOrderEventStrategy> executors;

    public void doExecute(OmsOrderEvent orderRequest) throws Exception {
        if (Objects.isNull(orderRequest)) {
            throw new ApplicationException("OmsOrderEvent can not be null", null);
        }
        if (!executors.containsKey(orderRequest.getEventType())) {
            throw new ApplicationException("invalid event type: " + orderRequest.getEventType(), null);
        }
        executors.get(orderRequest.getEventType())
                .doExecute(orderRequest);
    }
}
