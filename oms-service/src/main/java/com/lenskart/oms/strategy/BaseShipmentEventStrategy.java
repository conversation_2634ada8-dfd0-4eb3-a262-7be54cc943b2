package com.lenskart.oms.strategy;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.connector.OrderOpsConnector;
import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.enums.*;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.model.Action;
import com.lenskart.oms.producer.ShipmentEventOmsProducer;
import com.lenskart.oms.request.ShipmentUpdateEvent;
import com.lenskart.oms.service.*;
import com.lenskart.oms.utils.OmsCommonUtil;
import com.lenskart.oms.utils.OmsTransitionUtil;
import com.newrelic.api.agent.Trace;
import io.micrometer.core.annotation.Timed;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.lenskart.oms.constants.KafkaConstants.MESSAGE_IDEMPOTENCY_KEY;

@Setter(onMethod__ = {@Autowired})
public abstract class BaseShipmentEventStrategy {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    protected Logger logger;

    @Setter(AccessLevel.NONE)
    @Value("${oms.shipment.backsync.topic}")
    private String omsShipmentBackSyncTopic;

    private static final List<String> DO_SHIPMENTS = Arrays.asList(OrderSubType.DISTRIBUTOR_ORDER.name(), OrderSubType.DISTRIBUTOR_SUPER_ORDER.name(), OrderSubType.DISTRIBUTOR_JIT_ORDER.name());

    protected OrderService orderService;
    protected OrderItemService orderItemService;
    protected ShipmentService shipmentService;
    protected OmsTransitionUtil omsTransitionUtil;
    protected OrderItemMetaService orderItemMetaService;
    protected ShipmentEventOmsProducer shipmentEventOmsProducer;
    protected ShipmentTimelineService shipmentTimelineService;
    protected OrderBackSyncTrackingService orderBackSyncTrackingService;
    protected OrderOpsConnector orderOpsConnector;
    protected OmsCommonUtil omsCommonUtil;

    protected abstract ShipmentEvent supportedOrderEvents();

    protected abstract void preExecute(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException;

    protected abstract void execute(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException;

    @Trace
    @Timed
    @Transactional(rollbackFor = Exception.class)
    public void doExecute(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        logger.info("[BaseShipmentEventStrategy -> doExecute] inside {} strategy for request {}", supportedOrderEvents(), shipmentUpdateEvent);
        preExecute(shipmentUpdateEvent);
        execute(shipmentUpdateEvent);
        ShipmentDto shipmentDto = shipmentService.findBySearchTerms("wmsOrderCode.eq:" + shipmentUpdateEvent.getWmsOrderCode());
        if (!DO_SHIPMENTS.contains(shipmentDto.getShipmentSubType())) {
            persistTrackingAndPublishToOms(shipmentUpdateEvent);
        }
    }

    private void persistTrackingAndPublishToOms(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        orderBackSyncTrackingService.persistRequestInTracking(BackSyncEventName.valueOf(shipmentUpdateEvent.getShipmentEvent().name()),
                shipmentUpdateEvent.getWmsOrderCode(), BackSyncSystem.VSM);
        Map<String, String> kafkaHeaders = new HashMap<>();
        kafkaHeaders.put(MESSAGE_IDEMPOTENCY_KEY, shipmentUpdateEvent.getShipmentEvent().name() + "_" + shipmentUpdateEvent.getWmsOrderCode());
        shipmentEventOmsProducer.sendMessage(shipmentUpdateEvent, omsShipmentBackSyncTopic, kafkaHeaders, shipmentUpdateEvent.getWmsOrderCode());
    }

    protected List<ShipmentDto> getShipmentDtos(OrderDto orderDto) {
        List<ShipmentDto> shipmentDtoList = new ArrayList<>();
        Set<Long> shipmentIds = orderDto.getOrderItems().stream()
                // Exclude OTC (Over The Counter) delivery type items
                .filter(orderItemDto ->
                        !ProductDeliveryType.OTC.equals(orderItemDto.getProductDeliveryType())
                )
                // Exclude DTC (Direct To Customer) with LOCAL_FITTING fulfillment type
                .filter(orderItemDto ->
                        !(ProductDeliveryType.DTC.equals(orderItemDto.getProductDeliveryType()) &&
                                FulfillmentType.LOCAL_FITTING.equals(orderItemDto.getFulfillmentType()))
                )
                // Exclude items that have a B2B reference item ID
                .filter(orderItemDto ->
                        orderItemDto.getB2bReferenceItemId() == null
                )
                // Exclude items that have a non-empty CENTRAL_FACILITY_CODE in their metadata
                .filter(orderItemDto ->
                        orderItemDto.getOrderItemMetaData().stream()
                                .noneMatch(meta ->
                                        "CENTRAL_FACILITY_CODE".equals(meta.getEntityKey()) &&
                                                meta.getEntityValue() != null &&
                                                !meta.getEntityValue().isEmpty()
                                )
                )

                .map(OrderItemDto::getShipmentId)
                .collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(shipmentIds)) return shipmentDtoList;

        for (Long shipmentId : shipmentIds) {
            shipmentDtoList.add(shipmentService.findById(shipmentId));
        }
        return shipmentDtoList;
    }

    protected Boolean isOrderStatusUpdateRequired(Action transitionAction, OrderDto orderDto) {
        List<ShipmentDto> shipmentDtoList = getShipmentDtos(orderDto);
        for (ShipmentDto shipmentDto : shipmentDtoList) {
            if (!shipmentDto.getShipmentStatus().name().equalsIgnoreCase(transitionAction.getOrderStatus().getOrderStatus().name())) {
                return false;
            }
        }
        return true;
    }

    private boolean isEligibleOrderItem(OrderItemDto item) {
        // Filter out OTC products
        if (ProductDeliveryType.OTC.equals(item.getProductDeliveryType())) return false;

        // Filter out DTC with LOCAL_FITTING
        if (ProductDeliveryType.DTC.equals(item.getProductDeliveryType()) &&
                FulfillmentType.LOCAL_FITTING.equals(item.getFulfillmentType())) return false;

        // Exclude items with non-null B2B reference ID
        if (item.getB2bReferenceItemId() != null) return false;

        // Exclude if CENTRAL_FACILITY_CODE meta key exists and is non-empty
        return item.getOrderItemMetaData().stream()
                .noneMatch(meta -> "CENTRAL_FACILITY_CODE".equals(meta.getEntityKey()) &&
                        meta.getEntityValue() != null &&
                        !meta.getEntityValue().isEmpty());
    }
}
