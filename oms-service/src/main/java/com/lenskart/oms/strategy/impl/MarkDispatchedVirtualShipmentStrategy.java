package com.lenskart.oms.strategy.impl;

import com.lenskart.nexs.ims.request.UpdateStocksRequestV2;
import com.lenskart.nexs.ims.response.ItemStockUpdateResponseV2;
import com.lenskart.nexs.ims.response.UpdateStocksResponseV2;
import com.lenskart.oms.connector.OrderOpsConnector;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.enums.*;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.mapper.WmsOrderEventMapper;
import com.lenskart.oms.model.Action;
import com.lenskart.oms.producer.CommonKafkaProducer;
import com.lenskart.oms.producer.OrderEventWmsProducer;
import com.lenskart.oms.request.*;
import com.lenskart.oms.response.NonWareHouseOrderShipmentUpdateResponse;
import com.lenskart.oms.service.OrderService;
import com.lenskart.oms.strategy.BaseOtcShipmentEventStrategy;
import lombok.AccessLevel;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.lenskart.oms.constants.KafkaConstants.MESSAGE_IDEMPOTENCY_KEY;

@Component
@Setter(onMethod__ = {@Autowired})
public class MarkDispatchedVirtualShipmentStrategy extends BaseOtcShipmentEventStrategy {

    @Value("${wm.markOrderComplete.virtual.enabled:false}")
    @Setter(AccessLevel.NONE)
    private boolean markOrderCompleteEnabled;

    private OrderService orderService;
    private OrderEventWmsProducer orderEventWmsProducer;
    private WmsOrderEventMapper wmsOrderEventMapper;
    private OrderOpsConnector orderOpsConnector;

    @Override
    protected OtcShipmentEventType supportedOrderEvents() {
        return OtcShipmentEventType.VIRTUAL_DISPATCHED;
    }

    @Override
    protected void preExecute(OtcShipmentEvent otcShipmentEvent) throws ApplicationException {
        validateVirtualShipment(otcShipmentEvent);
    }

    @Override
    protected void execute(OtcShipmentEvent otcShipmentEvent) throws Exception {
        logger.info("[MarkDispatchedVirtualShipmentStrategy] ShipmentEvent {}", otcShipmentEvent);
        ShipmentDto shipmentDto = shipmentService.findById(otcShipmentEvent.getShipmentId());

        UpdateStocksRequestV2 stocksRequestV2 = getUpdateStocksRequestV2(shipmentDto, ApplicationConstants.IMS_OTC_DISPATCHED_OPERATION);
        UpdateStocksResponseV2 updateStocksResponseV2 = imsConnector.updateBarcodeStatus(stocksRequestV2);

        for (ItemStockUpdateResponseV2 responseV2 : updateStocksResponseV2.getItemStockUpdateResponseV2List()) {
            if (!responseV2.isSuccess()) {
                logger.info("Update barcode failed for shipmentDto {} and barcode {}", shipmentDto, responseV2.getBarcode());
                throw new ApplicationException("Update barcode failed for OTC Order");
            }
        }
        for(OrderItemDto b2bOrderItemDto : shipmentDto.getOrderItems() ){
            b2bOrderItemDto.setItemStatus(OrderItemStatus.DISPATCHED);
            b2bOrderItemDto.setItemSubStatus(OrderItemSubStatus.DISPATCHED);
            orderItemService.update(b2bOrderItemDto, b2bOrderItemDto.getId());
        }
        shipmentDto.setShipmentSubStatus(ShipmentSubStatus.DISPATCHED);
        shipmentDto.setShipmentStatus(ShipmentStatus.DISPATCHED);
        shipmentService.update(shipmentDto, shipmentDto.getId());
        OrderDto orderDto = orderService.findById(shipmentDto.getOrderItems().get(0).getOrderId());
        if (isOrderStatusUpdateRequired(orderDto)) {
            logger.info("[InvoiceState][OTC Order] orderId contains single shipment marking dispatched for order {}", orderDto);
            orderDto.setOrderStatus(OrderStatus.DISPATCHED);
            orderDto.setOrderSubStatus(OrderSubStatus.DISPATCHED);
            orderService.update(orderDto, orderDto.getId());
        }
    }

    @Override
    protected void postExecute(OtcShipmentEvent otcShipmentEvent) {
        ShipmentDto shipmentDto = shipmentService.findById(otcShipmentEvent.getShipmentId());
        if(disableVsmCallback) {
            ShipmentUpdateEvent shipmentUpdateEvent = getShipmentUpdateEvent(shipmentDto);
            persistTrackingAndPublishToOms(shipmentUpdateEvent);
            Map<String, String> kafkaHeaders = new HashMap<>();
            kafkaHeaders.put(MESSAGE_IDEMPOTENCY_KEY, shipmentUpdateEvent.getShipmentEvent().name() + "_" + shipmentUpdateEvent.getWmsOrderCode());
            shipmentEventOmsProducer.sendMessage(shipmentUpdateEvent, omsShipmentBackSyncTopic, kafkaHeaders, shipmentUpdateEvent.getWmsOrderCode());
        }

        OrderDto orderDto = orderService.findById(shipmentDto.getOrderItems().get(0).getOrderId());

        WmsOrderEvent wmsOrderEvent = wmsOrderEventMapper.getWmsOrderEventFromShipmentDto(shipmentDto, orderDto, ShipmentEvent.CREATE_ORDER);
        logger.info("[Invoiced State OTC] sending wms order event {} for shipmentId {}", wmsOrderEvent, shipmentDto.getId());
        orderEventWmsProducer.sendMessage(wmsOrderEvent);
        updateShipmentLevelStatusAtOrderOps(shipmentDto);
        if (markOrderCompleteEnabled) {
            MarkOrderCompleteRequest markOrderCompleteRequest = getMarkOrderCompleteRequest(shipmentDto, orderDto);
            wmConnector.markOrderComplete(markOrderCompleteRequest, "OTCAWB" + shipmentDto.getId());
        }
    }

    private void validateVirtualShipment(OtcShipmentEvent otcShipmentEvent){
        if(ObjectUtils.isEmpty(otcShipmentEvent) || otcShipmentEvent.getShipmentId() == null) {
            throw new ApplicationException("vitual shipment payload is not correct");
        }
        ShipmentDto shipmentDto =  shipmentService.findById(otcShipmentEvent.getShipmentId());
        if(ShipmentStatus.INVOICED != shipmentDto.getShipmentStatus()){
            throw new ApplicationException("virtual Shipment status is not correct");
        }
    }
    private static ShipmentUpdateEvent getShipmentUpdateEvent(ShipmentDto shipmentDto) {
        ShipmentUpdateEvent shipmentUpdateEvent = new ShipmentUpdateEvent();
        shipmentUpdateEvent.setShipmentEvent(ShipmentEvent.MARK_SHIPMENT_DISPATCH);
        shipmentUpdateEvent.setWmsOrderCode(shipmentDto.getWmsOrderCode());
        List<ShipmentItemUpdate> shipmentItemUpdates = new ArrayList<>();
        ShipmentItemUpdate shipmentItemUpdate = new ShipmentItemUpdate();
        shipmentItemUpdate.setUnicomShipmentStatus("DISPATCHED");
        shipmentItemUpdate.setOrderOpsShipmentStatus("COMPLETE");
        shipmentItemUpdates.add(shipmentItemUpdate);

        shipmentUpdateEvent.setOrderItemList(shipmentItemUpdates);
        return shipmentUpdateEvent;
    }

    protected boolean isOrderStatusUpdateRequired(OrderDto orderDto) {
        List<ShipmentDto> shipmentDtoList = getShipmentDtos(orderDto);
        for (ShipmentDto shipmentDto : shipmentDtoList) {
            if (!ShipmentStatus.OMS_REASSIGNED.equals(shipmentDto.getShipmentStatus()) && !shipmentDto.getShipmentStatus().name().equalsIgnoreCase(ShipmentStatus.DISPATCHED.name())) {
                return false;
            }
        }
        return true;
    }

    protected List<ShipmentDto> getShipmentDtos(OrderDto orderDto) {
        List<ShipmentDto> shipmentDtoList = new ArrayList<>();
        Set<Long> shipmentIds = orderDto.getOrderItems().stream()
                .map(OrderItemDto::getShipmentId)
                .collect(Collectors.toSet());

        for (Long shipmentId : shipmentIds) {
            shipmentDtoList.add(shipmentService.findById(shipmentId));
        }
        return shipmentDtoList;
    }

    private void updateShipmentLevelStatusAtOrderOps(ShipmentDto shipmentDto) {
        List<NonWareHouseOrderItem> items = shipmentDto.getOrderItems().stream()
                .map(item -> new NonWareHouseOrderItem(item.getUwItemId(), item.getItemBarcode()))
                .collect(Collectors.toList());
        NonWareHouseOrderShipmentUpdateRequest request = NonWareHouseOrderShipmentUpdateRequest.builder()
                .shipmentStatus(OrderStatus.COMPLETE_SHIPPED.name().toLowerCase())
                .shipmentState(OrderStatus.COMPLETE.name().toLowerCase())
                .shippingPackageId(shipmentDto.getWmsShippingPackageId())
                .facilityCode(shipmentDto.getFacility())
                .items(items)
                .wmsOrderCode(shipmentDto.getWmsOrderCode())
                .unicomShipmentStatus(ShipmentStatus.DISPATCHED.name())
                .unicomSyncStatus("Yes")
                .build();
        logger.info("[MarkDispatchedVirtualShipmentStrategy][updateShipmentLevelStatusAtOrderOps] NonWareHouseOrderShipmentUpdateRequest {}", request);
        NonWareHouseOrderShipmentUpdateResponse response = orderOpsConnector.updateShipmentLevelStatusForNonWarehouseOrder(request);
        logger.info("[MarkDispatchedVirtualShipmentStrategy][updateShipmentLevelStatusAtOrderOps] NonWareHouseOrderShipmentUpdateRequest {}, Response {}", request, response);
        if(!response.isSuccess()){
            logger.info("[MarkDispatchedVirtualShipmentStrategy][updateShipmentLevelStatusAtOrderOps] response {}", response);
            throw new ApplicationException("Update shipment status failed at Order ops for wmsOrdercode" + shipmentDto.getWmsOrderCode());
        }
    }
}
