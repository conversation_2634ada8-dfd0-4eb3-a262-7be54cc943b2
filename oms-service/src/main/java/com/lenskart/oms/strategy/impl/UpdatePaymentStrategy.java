package com.lenskart.oms.strategy.impl;


import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.OrderMetaDataDto;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.enums.OrderEventType;
import com.lenskart.oms.enums.ShipmentEvent;
import com.lenskart.oms.enums.ShipmentStatus;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.request.OmsOrderEvent;
import com.lenskart.oms.strategy.BaseOrderEventStrategy;
import com.lenskart.oms.validators.UpdatePaymentEventValidator;
import com.lenskart.oms.request.WmsOrderEvent;
import com.lenskart.oms.mapper.PaymentUpdateMapper;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Setter(onMethod__ = {@Autowired})
public class UpdatePaymentStrategy extends BaseOrderEventStrategy {

    private UpdatePaymentEventValidator updatePaymentEventValidator;

    private PaymentUpdateMapper paymentUpdateMapper;

    @Override
    protected OrderEventType supportedOrderEvents() {
        return OrderEventType.UPDATE_PAYMENT;
    }

    @Override
    protected void preExecute(OmsOrderEvent orderRequest) throws ApplicationException {
        updatePaymentEventValidator.validateUpdatePaymentEvent(orderRequest);
    }

    @Override
    protected void execute(OmsOrderEvent omsOrderEvent) throws ApplicationException {

        OrderDto persistedOrderDto = orderService.findBySearchTerms("incrementId.eq:" + omsOrderEvent.getOrderDto().getIncrementId());
        switch (omsOrderEvent.getOrderDto().getPaymentMethod().toLowerCase()) {
            case ApplicationConstants.PAYMENT_METHOD_JUSPAY:
                processJuspayPayment(omsOrderEvent, persistedOrderDto);
                break;
            case ApplicationConstants.PAYMENT_METHOD_PRIMER:
                processPrimerPayment(omsOrderEvent, persistedOrderDto);
                break;
            default:
                processOtherPayment(omsOrderEvent, persistedOrderDto);

        }
        persistedOrderDto.setUpdatedBy(ApplicationConstants.DEFAULT_OMS_USER);
        omsOrderEvent.setOrderDto(
                orderService.save(persistedOrderDto)
        );
        omsOrderEvent.setShipmentDtoList(
                getShipmentDtos(omsOrderEvent.getOrderDto())
        );
    }

    @Override
    protected void postExecute(OmsOrderEvent orderRequest) throws ApplicationException {
        validateAndSyncOrder(orderRequest);
    }

    @Override
    protected void pushUpdateToWmsProcessing(OmsOrderEvent omsOrderEvent) throws ApplicationException {
        OrderDto order = omsOrderEvent.getOrderDto();
        for (ShipmentDto shipment : omsOrderEvent.getShipmentDtoList()) {
            if(!shipment.getShipmentStatus().equals(ShipmentStatus.OMS_REASSIGNED)) {
                WmsOrderEvent wmsOrderEvent = paymentUpdateMapper.getWmsOrderEventForPaymentUpdate(order, ShipmentEvent.UPDATE_PAYMENT, shipment);
                orderEventWmsProducer.sendMessage(wmsOrderEvent);
            }
        }
    }

    private void processJuspayPayment(OmsOrderEvent paymentUpdateOmsOrderEvent, OrderDto persistedOrderDto) throws ApplicationException {

        if (persistedOrderDto == null || persistedOrderDto.getOrderMetaData() == null) {
            throw new ApplicationException("[UpdatePaymentStrategy] processJuspayPayment failed, persistedOrderDto is null ", null);
        }

        OrderDto paymentUpdateOrderEvent = paymentUpdateOmsOrderEvent.getOrderDto();

        Map<String, String> requestOrderMetaMap = paymentUpdateOrderEvent.getOrderMetaData()
                .stream()
                .filter(e -> StringUtils.isNotBlank(e.getEntityValue()))
                .collect(Collectors.toMap(OrderMetaDataDto::getEntityKey, OrderMetaDataDto::getEntityValue));

        Map<String, String> persistedOrderMetaMap = persistedOrderDto.getOrderMetaData()
                .stream()
                .filter(e -> StringUtils.isNotBlank(e.getEntityValue()))
                .collect(Collectors.toMap(OrderMetaDataDto::getEntityKey, OrderMetaDataDto::getEntityValue));

        if (paymentUpdateOrderEvent.getPaymentMethod().equalsIgnoreCase(ApplicationConstants.PAY_LATER)) {
            persistedOrderDto.setPaymentMethod(ApplicationConstants.PAY_LATER);
        }

        if(StringUtils.isNotBlank(requestOrderMetaMap.get(ApplicationConstants.PAYMENT_TRANSACTION_ID)) && !requestOrderMetaMap.get(ApplicationConstants.PAYMENT_TRANSACTION_ID).equalsIgnoreCase(persistedOrderMetaMap.get(ApplicationConstants.PAYMENT_TRANSACTION_ID))){
            persistedOrderMetaMap.put(ApplicationConstants.PAYMENT_TRANSACTION_ID,requestOrderMetaMap.get(ApplicationConstants.PAYMENT_TRANSACTION_ID));
        }

        if(StringUtils.isNotBlank(requestOrderMetaMap.get(ApplicationConstants.PAYMENT_PAY_U_ID)) && !requestOrderMetaMap.get(ApplicationConstants.PAYMENT_PAY_U_ID).equalsIgnoreCase(persistedOrderMetaMap.get(ApplicationConstants.PAYMENT_PAY_U_ID))){
            persistedOrderMetaMap.put(ApplicationConstants.PAYMENT_PAY_U_ID,requestOrderMetaMap.get(ApplicationConstants.PAYMENT_PAY_U_ID));
        }

        persistedOrderDto.setPaymentCaptured(paymentUpdateOrderEvent.getPaymentCaptured() == Boolean.TRUE ? Boolean.TRUE : Boolean.FALSE);

        List<OrderMetaDataDto> orderMetaDataDtoList = persistedOrderMetaMap.entrySet().stream()
                .map(entry -> {
                    OrderMetaDataDto orderMetaDataDto = new OrderMetaDataDto();
                    orderMetaDataDto.setEntityKey(entry.getKey());
                    orderMetaDataDto.setEntityValue(entry.getValue());
                    orderMetaDataDto.setCreatedBy(ApplicationConstants.DEFAULT_OMS_USER);
                    orderMetaDataDto.setUpdatedBy(ApplicationConstants.DEFAULT_OMS_USER);
                    orderMetaDataDto.setUpdatedAt(new Date());
                    return orderMetaDataDto;
                })
                .collect(Collectors.toList());
        persistedOrderDto.setOrderMetaData(orderMetaDataDtoList);
    }

    private void processPrimerPayment(OmsOrderEvent paymentUpdateOmsOrderEvent, OrderDto persistedOrderDto) throws ApplicationException {

        if (persistedOrderDto == null || persistedOrderDto.getOrderMetaData() == null) {
            throw new ApplicationException("[UpdatePaymentStrategy] processPrimerPayment failed,persistedOrderDto is null ", null);
        }

        OrderDto paymentUpdateOrderEvent = paymentUpdateOmsOrderEvent.getOrderDto();

        Map<String, String> requestOrderMetaMap = paymentUpdateOrderEvent.getOrderMetaData()
                .stream()
                .filter(e -> StringUtils.isNotBlank(e.getEntityValue()))
                .collect(Collectors.toMap(OrderMetaDataDto::getEntityKey, OrderMetaDataDto::getEntityValue));

        Map<String, String> persistedOrderMetaMap = persistedOrderDto.getOrderMetaData()
                .stream()
                .filter(e -> StringUtils.isNotBlank(e.getEntityValue()))
                .collect(Collectors.toMap(OrderMetaDataDto::getEntityKey, OrderMetaDataDto::getEntityValue));


        if(StringUtils.isNotBlank(requestOrderMetaMap.get(ApplicationConstants.PAYMENT_TRANSACTION_ID)) && !requestOrderMetaMap.get(ApplicationConstants.PAYMENT_TRANSACTION_ID).equalsIgnoreCase(persistedOrderMetaMap.get(ApplicationConstants.PAYMENT_TRANSACTION_ID))){
            persistedOrderMetaMap.put(ApplicationConstants.PAYMENT_TRANSACTION_ID,requestOrderMetaMap.get(ApplicationConstants.PAYMENT_TRANSACTION_ID));
        }

        persistedOrderDto.setPaymentCaptured(paymentUpdateOrderEvent.getPaymentCaptured() == Boolean.TRUE ? Boolean.TRUE : Boolean.FALSE);

        List<OrderMetaDataDto> orderMetaDataDtoList = persistedOrderMetaMap.entrySet().stream()
                .map(entry -> {
                    OrderMetaDataDto orderMetaDataDto = new OrderMetaDataDto();
                    orderMetaDataDto.setEntityKey(entry.getKey());
                    orderMetaDataDto.setEntityValue(entry.getValue());
                    orderMetaDataDto.setCreatedBy(ApplicationConstants.DEFAULT_OMS_USER);
                    orderMetaDataDto.setUpdatedBy(ApplicationConstants.DEFAULT_OMS_USER);
                    orderMetaDataDto.setUpdatedAt(new Date());
                    return orderMetaDataDto;
                })
                .collect(Collectors.toList());
        persistedOrderDto.setOrderMetaData(orderMetaDataDtoList);
    }

    private void processOtherPayment(OmsOrderEvent paymentUpdateOmsOrderEvent, OrderDto persistedOrderDto) throws ApplicationException {

        if (persistedOrderDto == null || persistedOrderDto.getOrderMetaData() == null) {
            throw new ApplicationException("[UpdatePaymentStrategy] processOtherPayment failed,persistedOrderDto is null ", null);
        }

        OrderDto paymentUpdateOrderEvent = paymentUpdateOmsOrderEvent.getOrderDto();

        Map<String, String> requestOrderMetaMap = paymentUpdateOrderEvent.getOrderMetaData()
                .stream()
                .filter(e -> StringUtils.isNotBlank(e.getEntityValue()))
                .collect(Collectors.toMap(OrderMetaDataDto::getEntityKey, OrderMetaDataDto::getEntityValue));

        Map<String, String> persistedOrderMetaMap = persistedOrderDto.getOrderMetaData()
                .stream()
                .filter(e -> StringUtils.isNotBlank(e.getEntityValue()))
                .collect(Collectors.toMap(OrderMetaDataDto::getEntityKey, OrderMetaDataDto::getEntityValue));

        if (ApplicationConstants.STORE_TYPE_HEC.equalsIgnoreCase(requestOrderMetaMap.get(ApplicationConstants.ORDER_META_KEY_STORE_TYPE))
                || "0".equals(requestOrderMetaMap.get(ApplicationConstants.ORDER_META_KEY_FACILITY_CODE))) {
            if (persistedOrderDto.getPaymentMethod().equalsIgnoreCase(ApplicationConstants.PAYMENT_METHOD_CASHONDELIVERY)) {
                persistedOrderDto.setPaymentMethod(paymentUpdateOrderEvent.getPaymentMethod());

                if (requestOrderMetaMap.get(ApplicationConstants.PAYMENT_PAY_U_ID) != null) {
                    persistedOrderMetaMap.put(ApplicationConstants.PAYMENT_PAY_U_ID,requestOrderMetaMap.get(ApplicationConstants.PAYMENT_PAY_U_ID));
                }
            }
        } else {
            persistedOrderDto.setPaymentMethod(paymentUpdateOrderEvent.getPaymentMethod());
            persistedOrderDto.setPaymentCaptured(Boolean.TRUE);
        }
        List<OrderMetaDataDto> orderMetaDataDtoList = persistedOrderMetaMap.entrySet().stream()
                .map(entry -> {
                    OrderMetaDataDto orderMetaDataDto = new OrderMetaDataDto();
                    orderMetaDataDto.setEntityKey(entry.getKey());
                    orderMetaDataDto.setEntityValue(entry.getValue());
                    orderMetaDataDto.setCreatedBy(ApplicationConstants.DEFAULT_OMS_USER);
                    orderMetaDataDto.setUpdatedBy(ApplicationConstants.DEFAULT_OMS_USER);
                    orderMetaDataDto.setUpdatedAt(new Date());
                    return orderMetaDataDto;
                })
                .collect(Collectors.toList());
        persistedOrderDto.setOrderMetaData(orderMetaDataDtoList);
    }

}
