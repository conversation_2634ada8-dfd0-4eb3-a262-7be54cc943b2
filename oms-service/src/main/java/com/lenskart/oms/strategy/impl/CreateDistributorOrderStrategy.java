package com.lenskart.oms.strategy.impl;

import com.lenskart.core.model.Product;
import com.lenskart.oms.connector.CatalogOpsConnector;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.dto.*;
import com.lenskart.oms.entity.DistributorOrderItems;
import com.lenskart.oms.entity.DistributorOrders;
import com.lenskart.oms.enums.*;
import com.lenskart.oms.enums.ItemType;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.facade.OrderBackSyncFacade;
import com.lenskart.oms.mapper.CreateOrderMapper;
import com.lenskart.oms.mapper.OmsOrderEventMapper;
import com.lenskart.oms.request.OmsOrderEvent;
import com.lenskart.oms.service.DistributorCustomerDetailsService;
import com.lenskart.oms.service.DistributorOrdersService;
import com.lenskart.oms.strategy.BaseOrderEventStrategy;
import com.lenskart.oms.validators.OrderCreateEventValidator;
import com.lenskart.order.interceptor.dto.createorder.*;
import com.lenskart.order.interceptor.enums.createorder.*;
import com.lenskart.order.interceptor.enums.createorder.LKCountry;
import com.lenskart.order.interceptor.request.CreateOrderRequest;
import lombok.AccessLevel;
import lombok.Setter;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.regex.Pattern;

import static com.lenskart.oms.constants.ApplicationConstants.*;
import static com.lenskart.oms.constants.ApplicationConstants.DEFAULT_OMS_USER;

@Component
@Setter(onMethod__ = {@Autowired})
public class CreateDistributorOrderStrategy extends BaseOrderEventStrategy {

    @Value("${enable.pure.loyalty.handling.in.nexs}")
    @Setter(AccessLevel.NONE)
    private Boolean enablePureLoyaltyHandlingInNexs;

    @Value("${so.default.facilities}")
    @Setter(AccessLevel.NONE)
    private Pattern soFacilities;

    @Value("#{${so.nexsFacilityMapping}}")
    @Setter(AccessLevel.NONE)
    private Map<String, String> nexsFacilityMapping;

    @Value("#{${so.nexsFacilityCountryMapping}}")
    @Setter(AccessLevel.NONE)
    private Map<String, String> nexsFacilityCountryMapping;

    @Value("#{${so.nexsFacilityCurrencyMapping}}")
    @Setter(AccessLevel.NONE)
    private Map<String, String> nexsFacilityCurrencyMapping;

    private OrderCreateEventValidator orderCreateEventValidator;
    private OmsOrderEventMapper omsOrderEventMapper;
    private CatalogOpsConnector catalogOpsConnector;
    private CreateOrderMapper createOrderMapper;
    private DistributorOrdersService distributorOrdersService;
    private DistributorCustomerDetailsService distributorCustomerDetailsService;
    private OrderBackSyncFacade orderBackSyncFacade;

    @Override
    protected OrderEventType supportedOrderEvents() {
        return OrderEventType.CREATE_DISTRIBUTOR_ORDER;
    }

    @Override
    protected void preExecute(OmsOrderEvent omsOrderEvent) throws ApplicationException {
    }

    @Override
    protected void execute(OmsOrderEvent omsOrderEvent) throws Exception {
        try {
            CreateOrderRequest createOrderRequest = createOrderRequest(Math.toIntExact(omsOrderEvent.getIncrementId()));
            omsOrderEvent = createOrderMapper.createOrderRequestToOmsOrderEvent(createOrderRequest, omsOrderEvent.getEventType().name(), String.valueOf(omsOrderEvent.getIncrementId()), omsOrderEvent);
            orderCreateEventValidator.validateCreateOrderEvent(omsOrderEvent);
            omsOrderEvent.setOrderDto(orderService.save(omsOrderEvent.getOrderDto()));
            List<OrderItemDto> lineItems = updateUwItemId(omsOrderEvent.getOrderDto().getOrderItems());
            omsOrderEvent.getOrderDto().setOrderItems(lineItems);
            omsOrderEvent.setOrderDto(orderService.save(omsOrderEvent.getOrderDto()));
            Map<String, ShipmentDto> shipmentMap = new HashMap<>();
            List<ShipmentDto> shipments = businessSplitAndReturnShipments(omsOrderEvent, shipmentMap);
            List<ShipmentDto> updatedShipmentDtoList = persistShipmentLevelData(shipments, omsOrderEvent.getOrderDto());
            omsOrderEvent.setShipmentDtoList(updatedShipmentDtoList);
            addEntryInShipmentTimeline(omsOrderEvent.getOrderDto().getId(), omsOrderEvent.getShipmentDtoList());
            if(omsOrderEvent.getOrderDto().getOrderSubType().equals(OrderSubType.DISTRIBUTOR_JIT_ORDER)) {
                approveDoJitOrder(omsOrderEvent);
            }
            logger.info("[CreateOrderStrategy -> processCreateOrderEvent] shipment list for the order {} after the business split logic {}", omsOrderEvent.getOrderDto().getIncrementId(), updatedShipmentDtoList);
        } catch (Exception e){
            logger.error("[CreateDistributorOrderStrategy][execute] error "+e.getMessage(),e);
            throw e;
        }
    }

    private void addEntryInShipmentTimeline(Long orderId, List<ShipmentDto> shipmentDtoList) {
        for (ShipmentDto shipmentDto : shipmentDtoList) {
            for (OrderItemDto orderItemDto : shipmentDto.getOrderItems()) {
                ShipmentTimelineDto shipmentTimelineDto = new ShipmentTimelineDto();

                shipmentTimelineDto.setOrderId(orderId);
                shipmentTimelineDto.setShipmentId(shipmentDto.getId());
                shipmentTimelineDto.setOrderItemId(orderItemDto.getId());
                shipmentTimelineDto.setCreatedBy(DEFAULT_OMS_USER);
                shipmentTimelineDto.setUpdatedBy(DEFAULT_OMS_USER);

                shipmentTimelineService.save(shipmentTimelineDto);
            }
        }
    }

    private List<ShipmentDto> persistShipmentLevelData(List<ShipmentDto> shipmentDtoList, OrderDto orderDto) {
        List<ShipmentDto> updatedShipmentDtoList = new ArrayList<>();
        for (ShipmentDto shipmentDto : shipmentDtoList) {
            updateWmsCodeForLensItemsIfRequired(shipmentDto, orderDto);
            updatedShipmentDtoList.add(shipmentService.save(shipmentDto));
        }

        return updatedShipmentDtoList;
    }

    private List<ShipmentDto> businessSplitAndReturnShipments(OmsOrderEvent omsOrderEvent, Map<String, ShipmentDto> shipmentMap) throws Exception {
        HashMap<String, String> splitDetails = new HashMap<>();
        for (OrderItemDto orderItem : omsOrderEvent.getOrderDto().getOrderItems()) {
            if (!(orderItem.getProductId().equals(Long.valueOf(TEMP_LEFT_LENS_PID))
                    || orderItem.getProductId().equals(Long.valueOf(TEMP_RIGHT_LENS_PID)))
            ) {
                executeBusinessSplitAndUpdateShipmentMap(orderItem, omsOrderEvent, splitDetails, shipmentMap);
            }
        }

        return new ArrayList<>(shipmentMap.values());
    }

    public String getGeneratedKey(OrderItemDto orderItem, ShipmentDto shipmentDto, Long incrementId){
        StringBuilder stringBuilder = new StringBuilder();

        String key = "";
        if(orderItem.getProductDeliveryType() != null){
            key = stringBuilder.append(incrementId)
                    .append(orderItem.getProductDeliveryType())
                    .append(shipmentDto.getFacility())
                    .append(SHIPPING_DESTINATION_STORE.equalsIgnoreCase(orderItem.getShippingDestinationType()))
                    .toString();
        }

        return key;
    }

    private void executeBusinessSplitAndUpdateShipmentMap(OrderItemDto orderItem, OmsOrderEvent omsOrderEvent, HashMap<String, String> splitDetails, Map<String, ShipmentDto> shipmentMap) throws Exception {
        ShipmentDto shipmentDto = new ShipmentDto();
        BeanUtils.copyProperties(omsOrderEvent.getShipmentDtoList().get(0), shipmentDto);
        if (omsOrderEvent.getShipmentLegalOwnerMap().containsKey(orderItem.getMagentoItemId())) {
            shipmentDto.setLegalOwner(omsOrderEvent.getShipmentLegalOwnerMap().get(orderItem.getMagentoItemId()));
        } else {
            shipmentDto.setLegalOwner(omsOrderEvent.getShipmentLegalOwnerMap().get(orderItem.getParentMagentoItemId()));
        }

        String key = getGeneratedKey(orderItem, shipmentDto, omsOrderEvent.getOrderDto().getIncrementId());
        logger.debug("[executeBusinessSplittingLogic] generated key for order {} is {}", omsOrderEvent.getOrderDto().getIncrementId(), key);

        Product product;
        if (LENS_PACKAGE_PID.equals(orderItem.getProductId())
                || LENS_COATING_PID.equals(orderItem.getProductId())
        ) {
            product = new Product();
        } else {
            product = catalogOpsConnector.findProductDetailsByProductId(orderItem.getProductId());
        }

        if(enablePureLoyaltyHandlingInNexs && product.getClassification() == LOYALTY_CLASSIFICATION) {
            key = key + LOYALTY;
        }

        Map<String, String> orderEligibilityStatus = isOrderEligibleForSplit(omsOrderEvent, orderItem, product, key);
        String isOrderEligibleForSplit = orderEligibilityStatus.get("isOrderEligibleForSplit");
        logger.debug("[executeBusinessSplittingLogic] order {} isOrderEligibleForSplit - {}", omsOrderEvent.getOrderDto().getIncrementId(), isOrderEligibleForSplit);

        if (NavChannel.MPDTC.equals(orderItem.getNavChannel())) {
            updateSplitShipmentMap(orderItem, omsOrderEvent, shipmentMap, shipmentDto);
            return;
        }

        key = orderEligibilityStatus.get("key");
        if (isOrderEligibleForSplit.equalsIgnoreCase("true")) {
            updateShipmentForSplitEligibleOrder(shipmentDto, omsOrderEvent.getOrderDto(), splitDetails, key);
            logger.info("[executeBusinessSplittingLogic] order {} is eligible for split. new wmsOrderCode is {}", omsOrderEvent.getOrderDto().getIncrementId(), shipmentDto.getWmsOrderCode());
        } else {
            key = updateShipmentForNonSplitEligibleOrder(shipmentDto, omsOrderEvent.getOrderDto(), splitDetails, orderItem, key, product);
            logger.info("[executeBusinessSplittingLogic] order {} is not eligible for split. wmsOrderCode is {}", omsOrderEvent.getOrderDto().getIncrementId(), shipmentDto.getWmsOrderCode());
        }

        splitDetails.put(key, shipmentDto.getWmsOrderCode());
        updateSplitShipmentMap(orderItem, omsOrderEvent, shipmentMap, shipmentDto);
    }

    private void updateShipmentForSplitEligibleOrder(ShipmentDto shipmentDto, OrderDto orderDto, HashMap<String, String> splitDetails, String key) {
        if (splitDetails.get(orderDto.getIncrementId() + "_MAX") != null) {
            if (splitDetails.get(key.concat(ACCESSORIES)) == null
                    || splitDetails.get(key.concat(ACCESSORIES)).equalsIgnoreCase("0")
            ) {
                Integer maxUnicomOrderCode = Integer.valueOf(splitDetails.get(orderDto.getIncrementId() + "_MAX")) + 1;
                splitDetails.replace(orderDto.getIncrementId() + "_MAX", maxUnicomOrderCode.toString());
                shipmentDto.setWmsOrderCode(orderDto.getJunoOrderId() + "-" + maxUnicomOrderCode);
            } else {
                shipmentDto.setWmsOrderCode(splitDetails.get(key.concat(ACCESSORIES)));
                splitDetails.replace(key.concat(ACCESSORIES), "0");
            }
        } else {
            splitDetails.put(orderDto.getIncrementId() + "_MAX", String.valueOf(0));
            shipmentDto.setWmsOrderCode(String.valueOf(orderDto.getJunoOrderId()));
        }
    }

    public Map<String, String> isOrderEligibleForSplit(OmsOrderEvent omsOrderEvent, OrderItemDto orderItem, Product product, String key) {
        Boolean isOrderEligibleForSplit = true;
        Map<String, String> orderEligibilityStatus = new HashMap<>();

        if ((Channel.FRANCHISEBULK.equals(orderItem.getChannel())
                || (orderItem.getProductDeliveryType() != null
                && orderItem.getProductDeliveryType().equals(ProductDeliveryType.OTC)))
                || omsCommonUtil.isDistributorOrder(omsOrderEvent.getOrderDto())
                || ((!SHIPPING_DESTINATION_STORE.equalsIgnoreCase(orderItem.getShippingDestinationType())
                && product.getClassification() == CONTACT_LENS_SOLUTION_CLASSIFICATION_ID)
                || SHIPPING_ACCESSORIES_CLASSIFICATIONS.contains(String.valueOf(product.getClassification())))

                || (product.getClassification() == CONTACT_LENS_CLASSIFICATION_ID
                || (!SUNGLASS_AND_SOLUTION_CLASSIFICATIONS.contains(String.valueOf(product.getClassification()))
                && FITTING_NOT_REQUIRED.equalsIgnoreCase(orderItem.getFittingType().name())))

                || (PaymentMethod.CASHONDELIVERY.getValue().equalsIgnoreCase(omsOrderEvent.getOrderDto().getPaymentMethod())
                && SUNGLASS_CLASSIFICATIONS.contains(String.valueOf(product.getClassification()))
                && FITTING_NOT_REQUIRED.equalsIgnoreCase(orderItem.getFittingType().name()))
        ) {
            isOrderEligibleForSplit = false;
            key = key.concat("0");
        }

        if (isOrderEligibleForSplit) {
            key = key.concat("1");
        }

        orderEligibilityStatus.put("isOrderEligibleForSplit", String.valueOf(isOrderEligibleForSplit));
        orderEligibilityStatus.put("key", key);
        return orderEligibilityStatus;
    }

    private String updateShipmentForNonSplitEligibleOrder(ShipmentDto shipmentDto, OrderDto orderDto, HashMap<String, String> splitDetails, OrderItemDto orderItem, String key, Product product) {
        if (splitDetails.get(key) != null) {
            shipmentDto.setWmsOrderCode(splitDetails.get(key));
        } else if (SHIPPING_ACCESSORIES_CLASSIFICATIONS.contains(String.valueOf(product.getClassification()))
                && splitDetails.get(key.substring(0, key.length() - 1).concat("1")) != null
        ) {
            key = key.substring(0, key.length() - 1).concat("1");
            shipmentDto.setWmsOrderCode(splitDetails.get(key));
        } else if (splitDetails.get(orderDto.getIncrementId() + "_MAX") != null) {
            Integer maxUnicomOrderCode= Integer.valueOf(splitDetails.get(orderDto.getIncrementId()+"_MAX")) + 1;
            splitDetails.replace(orderDto.getIncrementId() + "_MAX", maxUnicomOrderCode.toString());
            shipmentDto.setWmsOrderCode(orderDto.getJunoOrderId() + "-" + maxUnicomOrderCode);
        } else {
            splitDetails.put(orderDto.getIncrementId() + "_MAX", String.valueOf(0));
            shipmentDto.setWmsOrderCode(String.valueOf(orderDto.getJunoOrderId()));
        }

        if (enablePureLoyaltyHandlingInNexs && splitDetails.get(key) == null && SHIPPING_ACCESSORIES_CLASSIFICATIONS.contains(String.valueOf(product.getClassification()))
                && product.getClassification() != LOYALTY_CLASSIFICATION) {
            if (Long.valueOf(GOLD_MEMBERSHIP_PRODUCT_ID).equals(orderItem.getProductId())
                    || ProductDeliveryType.DTC.equals(orderItem.getProductDeliveryType())
            ) {
                key = key.substring(0, key.length() - 1).concat("1");
            }
            splitDetails.put(key.concat(ACCESSORIES), shipmentDto.getWmsOrderCode());
        } else if(splitDetails.get(key) == null && SHIPPING_ACCESSORIES_CLASSIFICATIONS.contains(String.valueOf(product.getClassification()))) {
            if (Long.valueOf(GOLD_MEMBERSHIP_PRODUCT_ID).equals(orderItem.getProductId())
                    || ProductDeliveryType.DTC.equals(orderItem.getProductDeliveryType())
            ) {
                key = key.substring(0, key.length() - 1).concat("1");
            }
            splitDetails.put(key.concat(ACCESSORIES), shipmentDto.getWmsOrderCode());
        }
        return key;
    }

    private void updateSplitShipmentMap(OrderItemDto orderItem, OmsOrderEvent omsOrderEvent, Map<String, ShipmentDto> shipmentMap, ShipmentDto shipmentDto) {
        if (shipmentMap.containsKey(shipmentDto.getWmsOrderCode())) {
            ShipmentDto existingShipment = shipmentMap.get(shipmentDto.getWmsOrderCode());
            List<OrderItemDto> existingOrderItems = new ArrayList<>(existingShipment.getOrderItems());
            if (CollectionUtils.isEmpty(existingOrderItems)) {
                existingOrderItems = new ArrayList<>();
            }
            existingOrderItems.add(orderItem);

            shipmentDto.setOrderItems(existingOrderItems);
        } else {
            shipmentDto.setOrderItems(Collections.singletonList(orderItem));
        }

        shipmentMap.put(shipmentDto.getWmsOrderCode(), shipmentDto);
    }

    private void updateWmsCodeForLensItemsIfRequired(ShipmentDto shipmentDto, OrderDto orderDto) {
        shipmentDto.setShipmentType(String.valueOf(createOrderMapper.getShipmentType(shipmentDto)));
        for (OrderItemDto orderItemDto : shipmentDto.getOrderItems()) {
            if (((ItemType.EYEFRAME.equals(orderItemDto.getItemType())
                    || ItemType.SUNGLASS.equals(orderItemDto.getItemType()))
                    && FittingType.REQD.equals(orderItemDto.getFittingType())) ||(ItemType.LOYALTY_SERVICES.equals(orderItemDto.getItemType())
                    && FittingType.NOT_REQD.equals(orderItemDto.getFittingType()))  || (omsCommonUtil.isDistributorOrder(orderDto))
            )
            {
                shipmentDto.setShipmentSubType(omsCommonUtil.getShipmentSubType(orderDto, shipmentDto.getOrderItems()));
            }
        }
    }

    @Override
    protected void postExecute(OmsOrderEvent omsOrderEvent) throws ApplicationException {
        try {
            for (ShipmentDto shipmentDto : omsOrderEvent.getShipmentDtoList()) {
                pushToPostOrderCreateProcessingIfEligible(shipmentDto, omsOrderEvent.getOrderDto(), omsOrderEvent.getEventType());
            }
        }  catch (Exception e){
            logger.error("postExecute error "+e.getMessage(),e);
        }
    }

    private void pushToPostOrderCreateProcessingIfEligible(ShipmentDto shipmentDto, OrderDto orderDto, OrderEventType orderEventType) throws ApplicationException {
        OmsOrderEvent omsOrderEvent = omsOrderEventMapper.getOmsOrderEventFromShipmentDto(shipmentDto, orderDto, orderEventType);
        validateAndSyncOrder(omsOrderEvent);
    }

    @Override
    protected void pushToPostOrderCreateProcessing(OmsOrderEvent omsOrderEvent) throws ApplicationException {
        OrderDto orderDto = new OrderDto();
        orderDto.setId(omsOrderEvent.getOrderDto().getId());
        orderDto.setIncrementId(omsOrderEvent.getOrderDto().getIncrementId());
        omsOrderEvent.setOrderDto(orderDto);
        omsOrderEvent.setIncrementId(omsOrderEvent.getOrderDto().getIncrementId());
        omsOrderEvent.setShipmentDtoList(Collections.EMPTY_LIST);
        omsOrderEvent.setEventType(OrderEventType.POST_CREATE_ORDER);
        processOrderEventOmsProducer.sendMessage(omsOrderEvent);
    }

    private CreateOrderRequest createOrderRequest(Integer incrementId) throws Exception {
        DistributorOrders distributorOrder = distributorOrdersService.findByIncrementId(String.valueOf(incrementId));
        DistributorCustomerDetailsDto distributorCustomerDetailsDto = distributorCustomerDetailsService.findById(distributorOrder.getCustomerId());
        CreateOrderRequest request = new CreateOrderRequest();
        request.setOrderId(Integer.valueOf(distributorOrder.getJunoOrderId()));
        request.setLineItems(createLineItems(distributorOrder));
        request.setBillingAddress(Collections.singletonList(createDistributorOrderBillingAddressPayload(distributorCustomerDetailsDto)));
        request.setShippingAddress(Collections.singletonList(createDistributorOrderShippingAddressPayload(distributorCustomerDetailsDto)));
        request.setStatus(Status.PENDING);
        request.setStoreId(1);
        request.setDeliveryStoreId(1);
        request.setDeliveryType(DeliveryType.STANDARD);
        request.setState(State.NEW);
        request.setDispatchDate(getDispatchDate());
        request.setDeliveryDate(getDeliveryDate());
        request.setDeliveryStoreId(1);
        request.setOffer3orfree("00000000");
        request.setCustomerPayload(createCustomerPayload(distributorCustomerDetailsDto));
        request.setExchangeItemId(null);
        request.setExchangeFlag(0);
        request.setDiscounts(Collections.singletonList(createDiscountPayload()));
        request.setTotalDiscount(0.0);
        request.setTaxLines(null);
        request.setTotalTax(0.0);
        request.setShippingCharge(0.0);
        Double total = getTotal(distributorOrder);
        request.setTotalTax(total);
        request.setGrandTotal(total);
        request.setSubTotal(total);
        request.setPayments(createPaymentsPayload(distributorOrder));
        request.setGiftMessage(null);
        request.setGiftMessageId(null);
        request.setNoteAttributes(null);
        request.setStoreType(null);
        request.setSourceName(ApplicationConstants.DISTRIBUTOR_ORDER_SOURCE_NAME);
        if (soFacilities.matcher(distributorOrder.getFacility()).matches()) {
            logger.info("Found SO Order - {}, Current Facility - {}, Updated Facility - {}", request.getOrderId(), distributorOrder.getFacility(), nexsFacilityMapping.get(distributorOrder.getFacility()));
            request.setFacilityCode(nexsFacilityMapping.get(distributorOrder.getFacility()));
        } else
            request.setFacilityCode(distributorOrder.getFacility());
        request.setIsDualCompanyEnabled(false);
        request.setIsBulkOrder(false);
        request.setTransactionId("");
        request.setIsPaymentCaptured(true);
        request.setLkCountry(LKCountry.valueOf(nexsFacilityCountryMapping.getOrDefault(distributorOrder.getFacility(), "IN")));
        request.setCurrencyCode(CurrencyCode.valueOf(nexsFacilityCurrencyMapping.getOrDefault(distributorOrder.getFacility(), "IN")));
        request.setLegalOwnerCountry(LKCountry.valueOf(nexsFacilityCountryMapping.getOrDefault(distributorOrder.getFacility(), "IN")));
        request.setClientOrg(null);
        request.setDeliveryStoreCode(null);
        request.setCreatedAt(new Date());
        return request;
    }


    private Double getTotal(DistributorOrders distributorOrder){
        List<DistributorOrderItems> orderItemsList = distributorOrder.getOrderItems();
        Double total = 0.0;
        for(DistributorOrderItems item : orderItemsList){
            total = total+(item.getPrice()*item.getQuantity());
        }
        return total;
    }

    private Date getDispatchDate(){
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_YEAR, 5);
        return cal.getTime();
    }

    private Date getDeliveryDate(){
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_YEAR, 5);
        return cal.getTime();
    }
    private ShippingAddressPayload createDistributorOrderShippingAddressPayload(DistributorCustomerDetailsDto distributorCustomerDetailsDto){
        ShippingAddressPayload shippingAddressPayload = new ShippingAddressPayload();
        List<DistributorCustomerAddressDetailsDto> customerAddressDetailsDtoList = distributorCustomerDetailsDto.getCustomerAddressDetails();
        DistributorCustomerAddressDetailsDto shippingAddress = customerAddressDetailsDtoList.get(1).getAddressType().equals(AddressType.SHIPPING) ? customerAddressDetailsDtoList.get(1) : customerAddressDetailsDtoList.get(0);
        shippingAddressPayload.setCity(shippingAddress.getCity());
        shippingAddressPayload.setCountry(shippingAddress.getCountry());
        shippingAddressPayload.setPhone(distributorCustomerDetailsDto.getMobile());
        shippingAddressPayload.setEmail(distributorCustomerDetailsDto.getEmail());
        shippingAddressPayload.setFirstName(distributorCustomerDetailsDto.getName());
        shippingAddressPayload.setLastName("");
        shippingAddressPayload.setState(shippingAddress.getState());
        shippingAddressPayload.setStreet1(shippingAddress.getAddressLine1());
        shippingAddressPayload.setStreet2(shippingAddress.getAddressLine2());
        shippingAddressPayload.setCountryCode(shippingAddress.getCountry());
        shippingAddressPayload.setPostCode(String.valueOf(shippingAddress.getPincode()));
        return shippingAddressPayload;
    }

    private BillingAddressPayload createDistributorOrderBillingAddressPayload(DistributorCustomerDetailsDto distributorCustomerDetailsDto){
        BillingAddressPayload billingAddressPayload = new BillingAddressPayload();
        List<DistributorCustomerAddressDetailsDto> customerAddressDetailsDtoList = distributorCustomerDetailsDto.getCustomerAddressDetails();
        DistributorCustomerAddressDetailsDto billingAddress = customerAddressDetailsDtoList.get(0).getAddressType().equals(AddressType.BILLING) ? customerAddressDetailsDtoList.get(0) : customerAddressDetailsDtoList.get(1);
        billingAddressPayload.setCity(billingAddress.getCity());
        billingAddressPayload.setCountry(billingAddress.getCountry());
        billingAddressPayload.setPhone(distributorCustomerDetailsDto.getMobile());
        billingAddressPayload.setEmail(distributorCustomerDetailsDto.getEmail());
        billingAddressPayload.setFirstName(distributorCustomerDetailsDto.getName());
        billingAddressPayload.setLastName("");
        billingAddressPayload.setState(billingAddress.getState());
        billingAddressPayload.setStreet1(billingAddress.getAddressLine1());
        billingAddressPayload.setStreet2(billingAddress.getAddressLine2());
        billingAddressPayload.setCountryCode(billingAddress.getCountry());
        billingAddressPayload.setPostCode(String.valueOf(billingAddress.getPincode()));
        return billingAddressPayload;
    }

    private CustomerPayload createCustomerPayload(DistributorCustomerDetailsDto customerDetails){
        //landline
        CustomerPayload customer = new CustomerPayload();
        customer.setEmail(customerDetails.getEmail());
        customer.setFirstName(customerDetails.getName());
        customer.setId(customerDetails.getId());
        customer.setMobileNumber(customerDetails.getMobile());
        return customer;
    }

    private DiscountPayload createDiscountPayload(){
        DiscountPayload discountPayload = new DiscountPayload();
        discountPayload.setCode("NA");
        discountPayload.setAmount(0.0);
        discountPayload.setType("IMPLICIT");
        return discountPayload;
    }

    private PaymentsPayload createPaymentsPayload(DistributorOrders distributorOrder){
        PaymentsPayload paymentsPayload = new PaymentsPayload();
        paymentsPayload.setPaymentMethod(PaymentMethodType.prepaid.name());
        paymentsPayload.setPaymentTransactionId(null);
        paymentsPayload.setPartialPayment(0.0);
        paymentsPayload.setPaymentMode("");
        paymentsPayload.setIsPaymentCaptured(true);
        paymentsPayload.setTotalCod(0.0);
        paymentsPayload.setTotalPrepaid(getTotal(distributorOrder));
        return paymentsPayload;
    }

    private List<LineItemPayload> createLineItems(DistributorOrders distributorOrders) throws Exception {
        //lineItemId is mandatory field. lineItemId is saved as magentoItemId
        List<DistributorOrderItems> orderItems = distributorOrders.getOrderItems();
        List<LineItemPayload> lineItemPayloadList = new ArrayList<>();
        for(DistributorOrderItems item: orderItems) {
            for(int qty = 1; qty <= item.getQuantity(); qty++) {
                Product product = catalogOpsConnector.findProductDetailsByProductId(Long.valueOf(item.getProductId()));
                LineItemPayload payload = new LineItemPayload();
                payload.setParentLineItemId(null);
                payload.setLineItemId(item.getId());
                payload.setSku(item.getProductId());
                payload.setOid( item.getProductName().matches("[0-9]+") ? String.valueOf(Integer.parseInt(item.getProductName()) + 1) : null); // will use this as fitting
                if(payload.getOid()!=null) {
                    payload.setItemType(item.getProductType());
                }else{
                    payload.setItemType(ItemTypeMapping.getItemTypeFromName(product.getHsnClassification()).name()); // check
                }
                payload.setQuantity(1);
                payload.setProductName(product.getValue());
                payload.setImageUrl(product.getProductImage());
                payload.setRetailPrice(item.getPrice());
                payload.setDiscountedPrice(item.getPrice());
                payload.setTaxCollected(0.0);
                payload.setDeliveryType(DeliveryType.STANDARD);
                payload.setLineItemProperties(null);
                payload.setProductDeliveryType(ProductDeliveryType.DTC.name());
                payload.setShipToStoreRequired(false);
                payload.setIsLocalFittingRequired(false);
                payload.setLocalFittingFacility(null);
                payload.setVirtualFacilityCode(null);
                payload.setContactLensId(null);
                payload.setDiscounts(Collections.singletonList(createDiscountPayload()));
                payload.setItemTotal(item.getPrice());
                payload.setShippingCharges(0.0);
                payload.setCartItemId(null);
                payload.setHubCode(distributorOrders.getFacility());
                payload.setHubCountry("IN");
                payload.setStoreInventory(null);
                payload.setDispatchDate(getDispatchDate());
                payload.setDeliveryDate(getDeliveryDate());
                payload.setThreeOrFree(false);
                payload.setWarrantyPeriod(0);
                lineItemPayloadList.add(payload);
            }
        }
        return lineItemPayloadList;
    }

    private List<OrderItemDto> updateUwItemId(List<OrderItemDto> orderItemDtoList){
        for(OrderItemDto orderItemDto: orderItemDtoList){
            orderItemDto.setUwItemId(orderItemDto.getId());
        }
        return  orderItemDtoList;
    }

    private void approveDoJitOrder(OmsOrderEvent omsOrderEvent) {
        DistributorOrders distributorOrdersDto = distributorOrdersService.findByIncrementId(String.valueOf(omsOrderEvent.getIncrementId()));
        if (distributorOrdersDto != null && distributorOrdersDto.getStatus() != null && DoStatus.CREATED.equals(distributorOrdersDto.getStatus())) {
            String incrementId = distributorOrdersDto.getIncrementId();
            OrderDto orderDto = orderService.findByIncrementId(Long.parseLong(incrementId));
            if (orderDto == null)  {
                throw new ApplicationException("Order has not been created in OMS. please try after some time");
            }
            OrderBackSyncTrackingDto orderBackSyncTrackingDto = populateOrderBackSyncDto(incrementId);
            orderBackSyncFacade.validateAndPushToTrackingQueue(orderBackSyncTrackingDto);
            distributorOrdersDto.setUpdatedBy(OMS_ORDER_SYSTEM);
            distributorOrdersDto.setStatus(DoStatus.APPROVED);
            distributorOrdersService.save(distributorOrdersService.convertToDto(distributorOrdersDto, new DistributorOrdersDto()));
        }
    }

    private OrderBackSyncTrackingDto populateOrderBackSyncDto(String incrementId) {
        OrderBackSyncTrackingDto orderBackSyncTrackingDto = new OrderBackSyncTrackingDto();
        orderBackSyncTrackingDto.setEventName(BackSyncEventName.ORDER_OPS_WH_SYNC_READY);
        orderBackSyncTrackingDto.setEntityType(BackSyncEntityType.INCREMENT_ID);
        orderBackSyncTrackingDto.setEntityId(incrementId);
        orderBackSyncTrackingDto.setEventStatus(BackSyncEventStatus.SUCCESS);
        orderBackSyncTrackingDto.setBackSyncSystem(BackSyncSystem.OMS);
        orderBackSyncTrackingDto.setMessage("Order approved by Finance team");
        return orderBackSyncTrackingDto;
    }

}
