package com.lenskart.oms.strategy;

import com.lenskart.oms.enums.OtcShipmentEventType;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.request.OtcShipmentEvent;
import lombok.AllArgsConstructor;

import java.util.Map;
import java.util.Objects;

@AllArgsConstructor
public class OtcShipmentEventStrategyExecutor {

    private final Map<OtcShipmentEventType, BaseOtcShipmentEventStrategy> executors;

    public void doExecute(OtcShipmentEvent otcShipmentEvent) throws Exception {
        if (Objects.isNull(otcShipmentEvent)) {
            throw new ApplicationException("OmsOrderEvent can not be null");
        }
        if (!executors.containsKey(otcShipmentEvent.getEventType())) {
                throw new ApplicationException("invalid event type: " + otcShipmentEvent.getEventType());
        }
        executors.get(otcShipmentEvent.getEventType()).doExecute(otcShipmentEvent);
    }
}
