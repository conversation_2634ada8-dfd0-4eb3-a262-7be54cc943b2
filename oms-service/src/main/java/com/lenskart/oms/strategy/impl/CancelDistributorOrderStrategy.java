package com.lenskart.oms.strategy.impl;

import com.lenskart.oms.connector.NexsWmsConnector;
import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.enums.CancellationType;
import com.lenskart.oms.enums.EventToOperationMap;
import com.lenskart.oms.enums.OrderEventType;
import com.lenskart.oms.enums.OrderItemStatus;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.mapper.WmsCancelOrderRequestMapper;
import com.lenskart.oms.model.Action;
import com.lenskart.oms.repository.DistributorOrdersRepository;
import com.lenskart.oms.repository.OrderItemRepository;
import com.lenskart.oms.repository.OrderRepository;
import com.lenskart.oms.repository.ShipmentRepository;
import com.lenskart.oms.request.OmsOrderEvent;
import com.lenskart.oms.request.WmsOrderActionRequest;
import com.lenskart.oms.strategy.BaseOrderEventStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class CancelDistributorOrderStrategy extends BaseOrderEventStrategy {

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private ShipmentRepository shipmentRepository;

    @Autowired
    private OrderItemRepository orderItemRepository;

    @Autowired
    private DistributorOrdersRepository distributorOrdersRepository;

    @Autowired
    private NexsWmsConnector nexsWmsConnector;

    @Autowired
    private WmsCancelOrderRequestMapper wmsCancelOrderRequestMapper;

    @Override
    protected OrderEventType supportedOrderEvents() {
        return OrderEventType.CANCEL_DISTRIBUTOR_ORDER;
    }

    @Override
    protected void preExecute(OmsOrderEvent orderRequest) throws Exception {
        validateCancelOrderDo(orderRequest);
    }

    @Override
    protected void execute(OmsOrderEvent orderRequest) throws Exception {
        processCancelOrderDo(orderRequest);
    }

    @Override
    protected void postExecute(OmsOrderEvent orderRequest) throws Exception {
        triggerCancellationInWms(orderRequest);
    }

    private void triggerCancellationInWms(OmsOrderEvent orderRequest) {
        try {
            logger.info("[CancelDistributorOrderStrategy -> triggerCancellationInWms] for {}", orderRequest.getIncrementId());
            List<Long> orderItemIds = orderRequest.getCancelOrderRequest().getOrderItems().stream()
                    .map(OrderItemDto::getId).collect(Collectors.toList());
            Long shipmentId = orderItemRepository.findDoShipmentIdByOrderItemId(orderItemIds.get(0));
            String wmsOrderCode = shipmentRepository.findWmsOrderCodeById(shipmentId);
            WmsOrderActionRequest orderActionRequest = wmsCancelOrderRequestMapper.orderRequestToWmsCancelDistributorOrderRequest(orderRequest.getIncrementId(), wmsOrderCode, orderItemIds);
            if (!CollectionUtils.isEmpty(orderActionRequest.getOrderItems()) && shipmentId != null && isShipmentSyncedToWMS(new ShipmentDto() {{ setId(shipmentId); }})) {
                nexsWmsConnector.triggerCancelOrderInNexsWms(orderActionRequest, orderRequest.getOrderDto().getOrderType());
            } else {
                logger.error("[CancelDistributorOrderStrategy -> triggerCancellationInWms] Cancel order wms call failed for {}", orderRequest.getIncrementId());
            }

        } catch (Exception e) {
            logger.error("[CancelDistributorOrderStrategyOrderStrategy -> triggerCancellationInWms] Cancel order wms call failed for incrementId - " + orderRequest.getIncrementId() + ", errorMessage - " + e.getMessage(), e);
            throw new ApplicationException("[CancelOrderStrategyOrderStrategy -> processCancelOrder] Cancel order wms call failed with exception " + e.getMessage(), e);
        }
    }

    private void processCancelOrderDo(OmsOrderEvent omsOrderEvent) throws ApplicationException {
        try {
            logger.info("[CancelDistributorOrderStrategy -> processCancelOrderDo] for {}", omsOrderEvent.getIncrementId());
            Long orderId;
            List<Long> orderItemIds;
            Long shipmentId;

            if (CollectionUtils.isEmpty(omsOrderEvent.getCancelOrderRequest().getOrderItems())) {
                orderId = orderRepository.findOrderIdByIncrementId(omsOrderEvent.getIncrementId());
                orderItemIds = orderItemRepository.findIdsByOrderId(orderId);
            } else {
                orderId = omsOrderEvent.getCancelOrderRequest().getOrderItems().get(0).getOrderId();
                orderItemIds = omsOrderEvent.getCancelOrderRequest().getOrderItems().stream()
                        .map(OrderItemDto::getId).collect(Collectors.toList());
            }

            String itemStatus = orderItemRepository.findStatusById(orderItemIds.get(0));
            Action transitionAction = omsTransitionUtil.getTransitionActionByOperationAndStatus(EventToOperationMap.CANCELLED.getOperation(), OrderItemStatus.valueOf(itemStatus));

            String newItemStatus = transitionAction.getItemStatus().getItemStatus().name();
            String newItemSubStatus = transitionAction.getItemStatus().getItemSubStatus().name();
            orderItemRepository.updateItemStatuses(orderItemIds, newItemStatus, newItemSubStatus);

            shipmentId = orderItemRepository.findDoShipmentIdByOrderItemId(orderItemIds.get(0));

            String newShipmentStatus = transitionAction.getShipmentStatus().getShipmentStatus().name();
            String newShipmentSubStatus = transitionAction.getShipmentStatus().getShipmentSubStatus().name();
            shipmentRepository.updateStatusAndSubStatusById(shipmentId, newShipmentStatus, newShipmentSubStatus);


            String newOrderStatus = transitionAction.getOrderStatus().getOrderStatus().name();
            String newOrderSubStatus = transitionAction.getOrderStatus().getOrderSubStatus().name();
            orderRepository.updateOrderStatus(orderId, newOrderStatus, newOrderSubStatus);
            OrderDto finalDto = orderService.findById(orderId);
            logger.info("Inside processCancelOrderDo : fetch shipmentids");
            omsOrderEvent.setOrderDto(finalDto);

        } catch (Exception e) {
            logger.error("[CancelDistributorOrderStrategy -> processCancelOrderDo] Failed for incrementId - {}, errorMessage - {}",
                    omsOrderEvent.getIncrementId(), e.getMessage(), e);
            throw new ApplicationException("Cancel order persistence failed with exception", e);
        }
    }

    private void validateCancelOrderDo(OmsOrderEvent orderRequest) {
        if(orderRequest.getCancelOrderRequest() == null || orderRequest.getCancelOrderRequest().getOrderItems() == null){
            logger.error("[CancelDistributorOrderStrategy -> validateCancelOrderDo] Invalid request, no items given for Cancellation {}", orderRequest);
            throw new ApplicationException("[validateCancelOrderDo] Invalid request, no items given for Cancellation", null);
        }
    }
}
