package com.lenskart.oms;

import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.EnableSchedulerLock;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@Slf4j
@SpringBootApplication(scanBasePackages = {"com.lenskart.*"})
@EnableTransactionManagement
@EnableRetry
@EnableScheduling
@EnableAsync
@EnableSchedulerLock(defaultLockAtMostFor = "PT30S")
public class OmsApplication {

	public static void main(String[] args) {
		try {
			SpringApplication.run(OmsApplication.class, args);
		} catch (Exception exception) {
			log.error("error while service startup ", exception);
		}
	}
}