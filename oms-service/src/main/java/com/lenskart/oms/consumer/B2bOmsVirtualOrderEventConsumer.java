package com.lenskart.oms.consumer;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.connector.OrderOpsConnector;
import com.lenskart.oms.dto.*;
import com.lenskart.oms.entity.Order;
import com.lenskart.oms.enums.OrderItemStatus;
import com.lenskart.oms.enums.OtcShipmentEventType;
import com.lenskart.oms.enums.ShipmentStatus;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.facade.OTCOrderFacade;
import com.lenskart.oms.model.UwOrder;
import com.lenskart.oms.request.OtcShipmentEvent;
import com.lenskart.oms.response.UwOrderResponse;
import com.lenskart.oms.service.OrderItemService;
import com.lenskart.oms.service.OrderService;
import com.lenskart.oms.service.ShipmentService;
import com.lenskart.oms.utils.ObjectHelper;
import com.lenskart.oms.utils.OmsCommonUtil;
import com.newrelic.api.agent.Trace;
import io.micrometer.core.annotation.Timed;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.lenskart.oms.constants.ApplicationConstants.UW_ITEM_ID_UPDATE_EVENT_TYPE_CREATE_ORDER;
import static com.lenskart.oms.constants.KafkaConstants.*;

@Component
public class B2bOmsVirtualOrderEventConsumer {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    @Autowired
    private OTCOrderFacade otcOrderFacade;
    @Autowired
    private ShipmentService shipmentService;
    @Autowired
    private OrderItemService orderItemService;
    @Autowired
    private OrderOpsConnector orderOpsConnector;
    @Autowired
    private OrderService orderService;
    @Autowired
    private OmsCommonUtil omsCommonUtil;

    @Timed
    @Trace(dispatcher = true)
    @Transactional(rollbackFor = Exception.class)
    @KafkaListener(
            topics = B2B_VIRTUAL_ORDER_EVENT_TOPIC,
            groupId = OMS_B2B_ORDER_EVENT_CONSUMER
    )
    public void listen(ConsumerRecord<String, String> consumerRecord) throws Exception {
        logger.info("[B2bOmsVirtualOrderEventConsumer --> listen] Order  update event received by OMS for orderId {}", consumerRecord.value());
        try{
            ShipmentDetailsForB2bEvent shipmentDetailsForB2bEvent = ObjectHelper.readValue(consumerRecord.value(), ShipmentDetailsForB2bEvent.class);
            if (Objects.isNull(shipmentDetailsForB2bEvent)) {
                throw new ApplicationException("invalid event payload");
            }
            logger.info("[B2bOmsVirtualOrderEventConsumer] ShipmentDetailsForB2bEvent {}", shipmentDetailsForB2bEvent);
            OrderDto orderDto =  orderService.findByIncrementId(shipmentDetailsForB2bEvent.getIncrementId());
            if(ObjectUtils.isEmpty(orderDto)) {
                logger.info("order {} does not exist", shipmentDetailsForB2bEvent.getIncrementId());
                return;
            }

            List<OrderItemDto> orderItemDtoList = new ArrayList<>(orderDto.getOrderItems());
            List<ShipmentDto> persistedShipmentDtoList = getShipmentDtos(orderDto);

            Map<Long, String> shipmentTypeMap = new HashMap<>();
            if(!CollectionUtils.isEmpty(persistedShipmentDtoList)) {
                shipmentTypeMap = persistedShipmentDtoList.stream().filter(s -> !ShipmentStatus.OMS_REASSIGNED.equals(s.getShipmentStatus())).collect(Collectors.toMap(ShipmentDto::getId, ShipmentDto::getShipmentType));
            }
            UwOrderResponse uwOrderResponse = null;
            if(!orderItemDtoList.isEmpty()) {
                uwOrderResponse = omsCommonUtil.fetchOrderOpsUwItemIdAndUpdateInOms(orderDto.getIncrementId(),  orderDto.getOrderItems(), UW_ITEM_ID_UPDATE_EVENT_TYPE_CREATE_ORDER,  shipmentTypeMap);
            }

            OrderItemDto orderItemDto = orderItemService.findBySearchTerms("uwItemId.eq:" + shipmentDetailsForB2bEvent.getUwItemId().get(0));
            if(ObjectUtils.isEmpty(orderItemDto)){
                return;
            }
            ShipmentDto shipmentDto = shipmentService.findById(orderItemDto.getShipmentId());
            logger.info("[B2bOmsVirtualOrderEventConsumer] shipmentDto {} and orderDto {} for incrementId", shipmentDto, orderDto,shipmentDetailsForB2bEvent.getIncrementId());

            Map<Long, String> barcodeMap = new HashMap<>();
            String targetOrderCode = shipmentDetailsForB2bEvent.getWmsOrderCode();

            for (UwOrder uw : uwOrderResponse.getUwOrders()) {
                logger.info("[B2bOmsVirtualOrderEventConsumer]  current  wmsOrderCode {}  b2b wmsOrderCode {}", uw.getUnicomOrderCode(), targetOrderCode);
                if (targetOrderCode != null && targetOrderCode.equals(uw.getUnicomOrderCode())) {
                    Long key = Long.valueOf(uw.getUwItemId());
                    String value = uw.getBarcode();
                    logger.info("[B2bOmsVirtualOrderEventConsumer] wmsOrderCode {} key {} value {}", targetOrderCode, key, value);
                    barcodeMap.put(key, value);
                }
            }
            ShipmentDto virtualShipment =  null;
            OrderItemDto virtualItem = null;
            for(OrderItemDto orderItemDto1 : shipmentDto.getOrderItems()) {
                if(orderItemDto1.getParentMagentoItemId() == null) {
                    virtualItem = orderItemService.findBySearchTerms("b2bReferenceItemId.eq:" + orderItemDto1.getId());

                    virtualItem.setItemBarcode(barcodeMap.get(orderItemDto1.getUwItemId()));
                    virtualShipment = shipmentService.findById(virtualItem.getShipmentId());
                    orderItemService.update(virtualItem, virtualItem.getId());
                }
            }
            OtcShipmentEventType otcShipmentEventType = getEventName(virtualItem);
            if(otcShipmentEventType == null){
                throw new ApplicationException("Virtual Item status in invalid");
            }
            OtcShipmentEvent otcShipmentEvent = new OtcShipmentEvent(getEventName(virtualItem),  virtualShipment.getId());
            otcOrderFacade.processOTCShipmentEvent(otcShipmentEvent);

        } catch (Exception e) {
            String errorMessage = MessageFormat.format("[OTCOrderConsumerConsumer --> listen] error while processing order update event for {0}",consumerRecord.value());
            logger.error(errorMessage, e);
            throw e;

        }
    }

    public  void trigger( ShipmentDetailsForB2bEvent shipmentDetailsForB2bEvent ) throws Exception {
        try{
            if (Objects.isNull(shipmentDetailsForB2bEvent)) {
                throw new ApplicationException("invalid event payload");
            }
            logger.info("[B2bOmsVirtualOrderEventConsumer] ShipmentDetailsForB2bEvent {}", shipmentDetailsForB2bEvent);
            OrderDto orderDto =  orderService.findByIncrementId(shipmentDetailsForB2bEvent.getIncrementId());

            List<OrderItemDto> orderItemDtoList = new ArrayList<>(orderDto.getOrderItems());
            List<ShipmentDto> persistedShipmentDtoList = getShipmentDtos(orderDto);

            Map<Long, String> shipmentTypeMap = new HashMap<>();
            if(!CollectionUtils.isEmpty(persistedShipmentDtoList)) {
                shipmentTypeMap = persistedShipmentDtoList.stream().filter(s -> !ShipmentStatus.OMS_REASSIGNED.equals(s.getShipmentStatus())).collect(Collectors.toMap(ShipmentDto::getId, ShipmentDto::getShipmentType));
            }
            UwOrderResponse uwOrderResponse = null;
            if(!orderItemDtoList.isEmpty()) {
               uwOrderResponse = omsCommonUtil.fetchOrderOpsUwItemIdAndUpdateInOms(orderDto.getIncrementId(),  orderDto.getOrderItems(), UW_ITEM_ID_UPDATE_EVENT_TYPE_CREATE_ORDER,  shipmentTypeMap);
            }

            OrderItemDto orderItemDto = orderItemService.findBySearchTerms("uwItemId.eq:" + shipmentDetailsForB2bEvent.getUwItemId().get(0));
            if(ObjectUtils.isEmpty(orderItemDto)){
                return;
            }
            ShipmentDto shipmentDto = shipmentService.findById(orderItemDto.getShipmentId());
            logger.info("[B2bOmsVirtualOrderEventConsumer] shipmentDto {} and orderDto {} for incrementId", shipmentDto, orderDto,shipmentDetailsForB2bEvent.getIncrementId());


            Map<Long, String> barcodeMap = new HashMap<>();
            String targetOrderCode = shipmentDetailsForB2bEvent.getWmsOrderCode();

            for (UwOrder uw : uwOrderResponse.getUwOrders()) {
                logger.info("[B2bOmsVirtualOrderEventConsumer]  current  wmsOrderCode {}  b2b wmsOrderCode {}", uw.getUnicomOrderCode(), targetOrderCode);
                if (targetOrderCode != null && targetOrderCode.equals(uw.getUnicomOrderCode())) {
                    Long key = Long.valueOf(uw.getUwItemId());
                    String value = uw.getBarcode();
                    logger.info("[B2bOmsVirtualOrderEventConsumer] wmsOrderCode {} key {} value {}", targetOrderCode, key, value);
                    barcodeMap.put(key, value);
                }
            }
            ShipmentDto virtualShipment =  null;
            OrderItemDto virtualItem = null;
            for(OrderItemDto orderItemDto1 : shipmentDto.getOrderItems()) {
                if(orderItemDto1.getParentMagentoItemId() == null) {
                    virtualItem = orderItemService.findBySearchTerms("b2bReferenceItemId.eq:" + orderItemDto1.getId());

                    virtualItem.setItemBarcode(barcodeMap.get(orderItemDto1.getUwItemId()));
                    virtualShipment = shipmentService.findById(virtualItem.getShipmentId());
                    orderItemService.update(virtualItem, virtualItem.getId());
                }
            }
            OtcShipmentEventType otcShipmentEventType = getEventName(virtualItem);
            if(otcShipmentEventType == null){
                throw new ApplicationException("Virtual Item status in invalid");
            }
            OtcShipmentEvent otcShipmentEvent = new OtcShipmentEvent(getEventName(virtualItem),  virtualShipment.getId());
            otcOrderFacade.processOTCShipmentEvent(otcShipmentEvent);
        } catch (Exception e) {
            String errorMessage = MessageFormat.format("[OTCOrderConsumerConsumer --> listen] error while processing order update event for {0}",shipmentDetailsForB2bEvent.getIncrementId());
            logger.error(errorMessage, e);
            throw e;

        }
    }

    private OtcShipmentEventType getEventName(OrderItemDto virtualItem) {
        if(OrderItemStatus.CREATED.equals(virtualItem.getItemStatus())) return OtcShipmentEventType.VIRTUAL_PICKED;
        if(OrderItemStatus.PICKED.equals(virtualItem.getItemStatus())) return OtcShipmentEventType.VIRTUAL_INVOICED;
        if(OrderItemStatus.INVOICED.equals(virtualItem.getItemStatus())) return OtcShipmentEventType.VIRTUAL_DISPATCHED;
        return  null;
    }


    protected List<ShipmentDto> getShipmentDtos(OrderDto orderDto) {
        List<ShipmentDto> shipmentDtoList = new ArrayList<>();
        Set<Long> shipmentIds = orderDto.getOrderItems().stream()
                .filter(orderItemDto -> !omsCommonUtil.isNonWarehouseProcessingOrder(orderItemDto))
                .map(OrderItemDto::getShipmentId)
                .collect(Collectors.toSet());

        for (Long shipmentId : shipmentIds) {
            shipmentDtoList.add(shipmentService.findById(shipmentId));
        }
        return shipmentDtoList;
    }
}
