package com.lenskart.oms.consumer;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.constants.KafkaConstants;
import com.lenskart.oms.dto.OrderBackSyncTrackingDto;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.facade.OrderBackSyncFacade;
import com.lenskart.oms.utils.ObjectHelper;
import com.newrelic.api.agent.Trace;
import io.micrometer.core.annotation.Timed;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.Header;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.util.Objects;

import static com.lenskart.oms.constants.KafkaConstants.OMS_BACKSYNC_TRACKING_EVENT_GROUP_ID;

@Component
@Setter(onMethod__ = {@Autowired})
public class BackSyncTrackingEventsConsumer {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    private OrderBackSyncFacade orderBackSyncFacade;

    @Trace(dispatcher = true)
    @Timed
    @KafkaListener(
            topics = "${oms.backsync.tracking.topic}",
            groupId = OMS_BACKSYNC_TRACKING_EVENT_GROUP_ID,
            containerFactory = "backsyncTrackingKafkaListenerContainerFactory"
    )
    public void listen(ConsumerRecord<String, String> consumerRecord) throws ApplicationException {
        logger.debug("[BackSyncTrackingEventsConsumer -> listen] VSM Callbacks From OMS for shipment id {}", consumerRecord.key());
        try {
            extractEventIdentifierKeyAndLog(consumerRecord);
            OrderBackSyncTrackingDto orderBackSyncTrackingDto = ObjectHelper.readValue(consumerRecord.value(), OrderBackSyncTrackingDto.class);
            if (Objects.isNull(orderBackSyncTrackingDto)) {
                throw new ApplicationException("invalid event payload", null);
            }
            orderBackSyncFacade.updateAcknowledgementAndProcessOrder(orderBackSyncTrackingDto);
        } catch (Exception e) {
            String errorMessage = MessageFormat.format("[BackSyncTrackingEventsConsumer -> listen] error while processing shipment create / update event for {0}", consumerRecord.key());
            logger.error(errorMessage, e);
            throw new ApplicationException(errorMessage, e);
        }
    }

    private void extractEventIdentifierKeyAndLog(ConsumerRecord<String, String> consumerRecord) {
        Header[] strings = consumerRecord.headers().toArray();
        for (Header header : strings) {
            if (header.key().equalsIgnoreCase(KafkaConstants.EVENT_IDENTIFIER_KEY)) {
                logger.info(KafkaConstants.EVENT_IDENTIFIER_KEY + " {} ", header.value());
            }
        }
    }
}
