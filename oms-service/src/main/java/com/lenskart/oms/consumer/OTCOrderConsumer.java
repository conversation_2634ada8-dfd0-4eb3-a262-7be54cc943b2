package com.lenskart.oms.consumer;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.facade.OTCOrderFacade;
import com.lenskart.oms.request.OtcShipmentEvent;
import com.lenskart.oms.utils.ObjectHelper;
import com.newrelic.api.agent.Trace;
import io.micrometer.core.annotation.Timed;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.text.MessageFormat;
import java.util.Objects;

import static com.lenskart.oms.constants.KafkaConstants.*;

@Component
public class OTCOrderConsumer {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    @Autowired
    private OTCOrderFacade otcOrderFacade;

    @Timed
    @Trace(dispatcher = true)
    @Transactional(rollbackFor = Exception.class)
    @KafkaListener(
            topics = OMS_OTC_ORDER_EVENTS_PROCESS_TOPIC,
            groupId = OMS_OTC_ORDER_EVENT_CONSUMER
    )
    public void listen(ConsumerRecord<String, String> consumerRecord) throws Exception {
        logger.info("[OTCOrderConsumerConsumer --> listen] Order  update event received by OMS for increment id {} and payload {}",
                consumerRecord.key(), consumerRecord.value());
        try {
            OtcShipmentEvent otcShipmentEvent = ObjectHelper.readValue(consumerRecord.value(), OtcShipmentEvent.class);
            if (Objects.isNull(otcShipmentEvent)) {
                throw new ApplicationException("invalid event payload");
            }
            otcOrderFacade.processOTCShipmentEvent(otcShipmentEvent);
        } catch (Exception e) {
            String errorMessage = MessageFormat.format("[OTCOrderConsumerConsumer --> listen] error while processing order update event for {0}", consumerRecord.value());
            logger.error(errorMessage, e);
            throw e;

        }
    }

}

