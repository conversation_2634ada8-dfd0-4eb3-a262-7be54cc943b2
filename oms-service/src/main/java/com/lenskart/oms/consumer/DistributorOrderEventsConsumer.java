package com.lenskart.oms.consumer;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.constants.KafkaConstants;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.facade.OrderEventsFacade;
import com.lenskart.oms.request.OrderEvent;
import com.lenskart.oms.utils.ObjectHelper;
import com.lenskart.oms.utils.OmsCommonUtil;
import com.newrelic.api.agent.Trace;
import io.micrometer.core.annotation.Timed;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.Header;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.text.MessageFormat;
import java.util.Objects;

import static com.lenskart.oms.constants.KafkaConstants.OMS_DISTRIBUTO_ORDER_EVENT_CONSUMER;

@Component
@Setter(onMethod__ = {@Autowired})
public class DistributorOrderEventsConsumer {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    private OmsCommonUtil omsCommonUtil;
    private OrderEventsFacade orderEventsFacade;

    @Trace(dispatcher = true)
    @Timed
    @Transactional(rollbackFor = Exception.class)
    @KafkaListener(
            topics = "oms-do-order-events-topic",
            groupId = OMS_DISTRIBUTO_ORDER_EVENT_CONSUMER
    )
    public void listen(ConsumerRecord<String, String> consumerRecord) throws ApplicationException {
        OrderEvent orderEvent = null;
        logger.info("[DistributorOrderEventsConsumer -> listen] Order create / update event received by OMS for increment id {}", consumerRecord.key());
        try {
            extractEventIdentifierKeyAndLog(consumerRecord);
            orderEvent = ObjectHelper.readValue(consumerRecord.value(), OrderEvent.class);
            if (Objects.isNull(orderEvent)) {
                throw new ApplicationException("invalid event payload", null);
            }
            orderEventsFacade.processInterceptorOrderEvent(orderEvent);
        } catch (Exception e) {
            String errorMessage = MessageFormat.format("[DistributorOrderEventsConsumer -> listen] error while processing order create / update event for {0}", consumerRecord.key());
            logger.error(errorMessage, e);
            throw new ApplicationException(errorMessage, e);
        }
    }

    private void extractEventIdentifierKeyAndLog(ConsumerRecord<String, String> consumerRecord) {
        Header[] strings = consumerRecord.headers().toArray();
        for (Header header : strings) {
            if (header.key().equalsIgnoreCase(KafkaConstants.EVENT_IDENTIFIER_KEY)) {
                logger.info(KafkaConstants.EVENT_IDENTIFIER_KEY + " {} ", header.value());
            }
        }
    }
}
