package com.lenskart.oms.consumer;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.constants.KafkaConstants;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.facade.OrderEventsFacade;
import com.lenskart.oms.request.OmsOrderEvent;
import com.lenskart.oms.utils.ObjectHelper;
import com.newrelic.api.agent.Trace;
import io.micrometer.core.annotation.Timed;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.Header;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.util.Objects;

import static com.lenskart.oms.constants.KafkaConstants.OMS_ORDER_EVENT_PROCESS_CONSUMER;

@Component
@Setter(onMethod__ = {@Autowired})
public class OmsOrderEventsConsumer {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    private OrderEventsFacade orderEventsFacade;

    @Trace(dispatcher = true)
    @Timed
    @KafkaListener(
            topics = "${oms.orders.event.process.topic}",
            groupId = OMS_ORDER_EVENT_PROCESS_CONSUMER
    )
    public void listen(ConsumerRecord<String, String> consumerRecord) throws ApplicationException {
        logger.info("[OmsOrderEventsConsumer -> listen] Order create / update event received by OMS for increment id {}", consumerRecord.key());
        try {
            extractEventIdentifierKeyAndLog(consumerRecord);
            OmsOrderEvent omsOrderEvent = ObjectHelper.readValue(consumerRecord.value(), OmsOrderEvent.class);
            if (Objects.isNull(omsOrderEvent)) {
                throw new ApplicationException("invalid event payload", null);
            }
            orderEventsFacade.processOrderEvent(omsOrderEvent);
        } catch (Exception e) {
            String errorMessage = MessageFormat.format("[OmsOrderEventsConsumer -> listen] error while processing order create update event failed with error : {0} for {1}", e.getMessage(), consumerRecord.key());
            logger.error(errorMessage, e);
            throw new ApplicationException(errorMessage, e);
        }
    }

    private void extractEventIdentifierKeyAndLog(ConsumerRecord<String, String> consumerRecord) {
        Header[] strings = consumerRecord.headers().toArray();
        for (Header header : strings) {
            if (header.key().equalsIgnoreCase(KafkaConstants.EVENT_IDENTIFIER_KEY)) {
                logger.info(KafkaConstants.EVENT_IDENTIFIER_KEY + " {} ", header.value());
            }
        }
    }
}
