package com.lenskart.oms.consumer;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.constants.KafkaConstants;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.facade.DistributorReturnOrderFacade;
import com.lenskart.oms.utils.ObjectHelper;
import com.newrelic.api.agent.Trace;
import io.micrometer.core.annotation.Timed;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.Header;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.text.MessageFormat;
import java.util.Objects;

import static com.lenskart.oms.constants.KafkaConstants.OMS_ORDER_EVENT_GROUP_ID;

@Component
@Setter(onMethod__ = {@Autowired})
public class FdsCreditNoteConsumer {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    @Setter(AccessLevel.NONE)
    @Value("${disable.fds.creditNoteEvent.consumer:false}")
    private boolean disableFdsCreditNoteEventConsumer;

    private DistributorReturnOrderFacade distributorReturnOrderFacade;

    @Trace(dispatcher = true)
    @Timed
    @Transactional(rollbackFor = Exception.class)
    @KafkaListener(topics = "fds-create-creditNote", groupId = OMS_ORDER_EVENT_GROUP_ID)
    public void listen(ConsumerRecord<String, String> consumerRecord) throws ApplicationException {
        Long itemId = null;
        logger.info("[FdsCreditNoteConsumer -> listen] Message {}", consumerRecord);
        if(disableFdsCreditNoteEventConsumer) {
            logger.info("[FdsCreditNoteConsumer -> listen] disabled for {}", consumerRecord.value());
            return;
        }
        try {
            extractEventIdentifierKeyAndLog(consumerRecord);
            itemId = ObjectHelper.readValue(consumerRecord.value(), Long.class);
            if (Objects.isNull(itemId)) {
                throw new ApplicationException("invalid event payload", null);
            }
            distributorReturnOrderFacade.createCreditNote(itemId);
        } catch (Exception e) {
            String errorMessage = MessageFormat.format("[FdsCreditNoteConsumer -> listen] error while processing event for {0}", consumerRecord.key());
            logger.error(errorMessage, e);
            throw new ApplicationException(errorMessage, e);
        }
    }

    private void extractEventIdentifierKeyAndLog(ConsumerRecord<String, String> consumerRecord) {
        Header[] strings = consumerRecord.headers().toArray();
        for (Header header : strings) {
            if (header.key().equalsIgnoreCase(KafkaConstants.EVENT_IDENTIFIER_KEY)) {
                logger.info(KafkaConstants.EVENT_IDENTIFIER_KEY + " {} ", header.value());
            }
        }
    }
}
