package com.lenskart.oms.consumer;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.constants.KafkaConstants;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.facade.OrderEventsFacade;
import com.lenskart.oms.model.MarkFoundAsyncPayload;
import com.lenskart.oms.request.OmsOrderEvent;
import com.lenskart.oms.strategy.impl.MarkItemSkippedStrategy;
import com.lenskart.oms.utils.ObjectHelper;
import com.newrelic.api.agent.Trace;
import io.micrometer.core.annotation.Timed;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.Header;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.util.Map;
import java.util.Objects;

import static com.lenskart.oms.constants.KafkaConstants.OMS_ORDER_EVENT_PROCESS_CONSUMER;

@Component
@Setter(onMethod__ = {@Autowired})
public class MarkOrderFoundConsumer {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    private MarkItemSkippedStrategy markItemSkippedStrategy;

    @Trace(dispatcher = true)
    @Timed
    @KafkaListener(topics = "${mark.order.found.topic:mark-order-found}", groupId = "MarkOrderFoundGroup")
    public void listen(ConsumerRecord<String, String> consumerRecord) throws ApplicationException {
        logger.info("[MarkOrderFoundConsumer -> listen] event received by OMS for {}", consumerRecord.key());
        try {
            extractEventIdentifierKeyAndLog(consumerRecord);
            MarkFoundAsyncPayload markFoundAsyncPayload = ObjectHelper.readValue(consumerRecord.value(), MarkFoundAsyncPayload.class);
            if (Objects.isNull(markFoundAsyncPayload)) {
                throw new ApplicationException("invalid event payload", null);
            }
            markItemSkippedStrategy.handleMarkFoundForDoAsSo(markFoundAsyncPayload.getPidQtyMap(), markFoundAsyncPayload.getFacilityCode());
        } catch (Exception e) {
            String errorMessage = MessageFormat.format("[OmsOrderEventsConsumer -> listen] error while processing, event failed with error : {0} for {1}", e.getMessage(), consumerRecord.key());
            logger.error(errorMessage, e);
            throw new ApplicationException(errorMessage, e);
        }
    }

    private void extractEventIdentifierKeyAndLog(ConsumerRecord<String, String> consumerRecord) {
        Header[] strings = consumerRecord.headers().toArray();
        for (Header header : strings) {
            if (header.key().equalsIgnoreCase(KafkaConstants.EVENT_IDENTIFIER_KEY)) {
                logger.info(KafkaConstants.EVENT_IDENTIFIER_KEY + " {} ", header.value());
            }
        }
    }
}
