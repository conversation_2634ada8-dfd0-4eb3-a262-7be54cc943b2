package com.lenskart.oms.consumer;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.constants.KafkaConstants;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.facade.ShipmentEventsFacade;
import com.lenskart.oms.request.ShipmentUpdateEvent;
import com.lenskart.oms.utils.ObjectHelper;
import com.newrelic.api.agent.Trace;
import io.micrometer.core.annotation.Timed;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.Header;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.util.Objects;

import static com.lenskart.oms.constants.KafkaConstants.WMS_DO_SHIPMENT_EVENT_GROUP_ID;


@Component
@Setter(onMethod__ = {@Autowired})
public class WmsDoShipmentEventsConsumer {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    private ShipmentEventsFacade shipmentEventsFacade;

    @Trace(dispatcher = true)
    @Timed
    @KafkaListener(
            topics = "${wms.do.shipment.backsync.topic}",
            groupId = WMS_DO_SHIPMENT_EVENT_GROUP_ID,
            properties = {ConsumerConfig.MAX_POLL_RECORDS_CONFIG + ":" + "${wms.do.shipment.backsync.max.poll.records:1}"}
    )
    public void listen(ConsumerRecord<String, String> consumerRecord) throws ApplicationException {
        logger.info("[WmsDoShipmentEventsConsumer -> listen] Shipment create / update event received by OMS for shipment id {}", consumerRecord.key());
        ShipmentUpdateEvent shipmentUpdateEvent = null;
        try {
            extractEventIdentifierKeyAndLog(consumerRecord);
            shipmentUpdateEvent = ObjectHelper.readValue(consumerRecord.value(), ShipmentUpdateEvent.class);
            if (Objects.isNull(shipmentUpdateEvent)) {
                throw new ApplicationException("[WmsDoShipmentEventsConsumer] invalid event payload", null);
            }
            shipmentEventsFacade.processShipmentEvent(shipmentUpdateEvent);
        } catch (Exception e) {
            String identifier = !Objects.isNull(shipmentUpdateEvent) ? shipmentUpdateEvent.getWmsOrderCode() : null;
            String errorMessage = MessageFormat.format("[WmsDoShipmentEventsConsumer -> listen] error while processing shipment create / update event for {0}", identifier);
            logger.error(errorMessage, e);
            throw new ApplicationException(errorMessage, e);
        }
    }

    private void extractEventIdentifierKeyAndLog(ConsumerRecord<String, String> consumerRecord) {
        Header[] strings = consumerRecord.headers().toArray();
        for (Header header : strings) {
            if (header.key().equalsIgnoreCase(KafkaConstants.EVENT_IDENTIFIER_KEY)) {
                logger.info(KafkaConstants.EVENT_IDENTIFIER_KEY + " {} ", header.value());
            }
        }
    }
}
