package com.lenskart.oms.facade;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.connector.OrderOpsConnector;
import com.lenskart.oms.constants.KafkaConstants;
import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.OrderBackSyncTrackingDto;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.enums.*;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.mapper.OmsOrderEventMapper;
import com.lenskart.oms.mapper.OrderBackSyncMapper;
import com.lenskart.oms.model.Action;
import com.lenskart.oms.producer.BackSyncTrackingEventProducer;
import com.lenskart.oms.producer.CommonKafkaProducer;
import com.lenskart.oms.producer.OrderOpsBackSyncProducer;
import com.lenskart.oms.request.OmsOrderEvent;
import com.lenskart.oms.request.OrderEvent;
import com.lenskart.oms.request.OrderOpsOrderEvent;
import com.lenskart.oms.request.OtcShipmentEvent;
import com.lenskart.oms.service.OrderBackSyncTrackingService;
import com.lenskart.oms.service.OrderItemService;
import com.lenskart.oms.service.OrderService;
import com.lenskart.oms.service.ShipmentService;
import com.lenskart.oms.strategy.impl.CreateOrderStrategy;
import com.lenskart.oms.utils.ObjectHelper;
import com.lenskart.oms.utils.OmsCommonUtil;
import com.lenskart.oms.utils.OmsTransitionUtil;
import com.lenskart.oms.utils.OrderBackSyncUtil;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.lenskart.oms.enums.BackSyncEventName.CREATE_ORDER;
import static com.lenskart.oms.enums.BackSyncEventName.ORDER_OPS_WH_SYNC_READY;

@Component
@Setter(onMethod__ = {@Autowired})
public class OrderBackSyncFacade {

    @CustomLogger
    @Setter(AccessLevel.PROTECTED)
    private Logger logger;

    private OrderService orderService;
    private ShipmentService shipmentService;
    private OrderItemService orderItemService;
    private OmsTransitionUtil omsTransitionUtil;
    private OrderOpsConnector orderOpsConnector;
    private OrderBackSyncUtil orderBackSyncUtil;
    private OrderBackSyncMapper orderBackSyncMapper;
    private OmsOrderEventMapper omsOrderEventMapper;
    private CreateOrderStrategy createOrderStrategy;
    private OrderOpsBackSyncProducer orderOpsBackSyncProducer;
    private OrderBackSyncTrackingService orderBackSyncTrackingService;
    private BackSyncTrackingEventProducer backSyncTrackingEventProducer;
    private OmsCommonUtil omsCommonUtil;
    private CommonKafkaProducer commonKafkaProducer;

    @Transactional(rollbackFor = Exception.class)
    public void createBackSyncEntryAndPushToOrderOpsAsync(Long incrementId, BackSyncEventName requestType, OrderEvent orderEvent) throws ApplicationException {
        orderBackSyncUtil.getAndMarkPendingBackSyncTrackingDto(incrementId, requestType);

        OrderOpsOrderEvent orderOpsOrderEvent = orderBackSyncMapper.populateOrderOpsOrderEvent(incrementId,
                requestType, orderEvent, null);
        orderOpsBackSyncProducer.sendMessage(orderOpsOrderEvent);
    }

    public void createBackSyncEntryAndUpdateOrderOpsSync(OmsOrderEvent omsOrderEvent, BackSyncEventName eventName) throws ApplicationException {
        OrderBackSyncTrackingDto orderBackSyncTrackingDto = orderBackSyncUtil.getAndMarkPendingBackSyncTrackingDto(omsOrderEvent.getOrderDto().getIncrementId(), eventName);

        try {
            OrderOpsOrderEvent orderOpsOrderEvent = orderBackSyncMapper.populateOrderOpsOrderEvent(omsOrderEvent.getOrderDto().getIncrementId(),
                    eventName, null, orderBackSyncMapper.populateOrderBackSyncRequest(omsOrderEvent));
            orderOpsConnector.updateOmsOrder(orderOpsOrderEvent);

            logger.info("[createBackSyncEntryAndUpdateOrderOpsSync] order-ops update successful for event {} and order id {}", eventName, omsOrderEvent.getOrderDto().getIncrementId());
            orderBackSyncTrackingDto.setEventStatus(BackSyncEventStatus.SUCCESS);
            orderBackSyncTrackingService.save(orderBackSyncTrackingDto);
        } catch (Exception exception) {
            logger.error("[createBackSyncEntryAndUpdateOrderOpsSync] order-ops update failed for event {} and order id {} with exception {}",
                    eventName, omsOrderEvent.getOrderDto().getIncrementId(), exception.getMessage(), exception);

            orderBackSyncTrackingDto.setEventStatus(BackSyncEventStatus.FAILED);
            orderBackSyncTrackingService.save(orderBackSyncTrackingDto);
            throw new ApplicationException("order-ops update failed for event " + eventName + " and order id " + omsOrderEvent.getOrderDto().getIncrementId() + " with exception " + exception, null);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void validateAndPushToTrackingQueue(OrderBackSyncTrackingDto orderBackSyncTrackingDto) throws ApplicationException {
        if (Objects.isNull(orderBackSyncTrackingDto)
                || StringUtils.isEmpty(orderBackSyncTrackingDto.getEntityId())) {
            logger.info("[validateAndPushToTrackingQueue] Request Validation Failure - back sync tracking event is NULL {}", orderBackSyncTrackingDto);
            throw new ApplicationException("Request Validation Failure - back sync tracking event is NULL");
        }
        backSyncTrackingEventProducer.sendMessage(orderBackSyncTrackingDto);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateAcknowledgementAndProcessOrder(OrderBackSyncTrackingDto orderBackSyncTrackingDto) throws ApplicationException {
        updateAcknowledgement(orderBackSyncTrackingDto);

        if (CREATE_ORDER.equals(orderBackSyncTrackingDto.getEventName())) {
            logger.info("[updateAcknowledgementAndProcessOrder] CREATE_ORDER back sync received from orderOps for order {}", orderBackSyncTrackingDto.getEntityId());
            resumeOrderProcess(Long.valueOf(orderBackSyncTrackingDto.getEntityId()));
        } else if (ORDER_OPS_WH_SYNC_READY.equals(orderBackSyncTrackingDto.getEventName())) {
            OrderDto orderDto = getOrderDto(Long.valueOf(orderBackSyncTrackingDto.getEntityId()));
            if (isOrderAlreadyProcessed(orderDto)) {
                logger.info("[updateAcknowledgementAndProcessOrder] ORDER_OPS_WH_SYNC_READY back sync received from orderOps but order {} is already processed", orderBackSyncTrackingDto.getEntityId());
            } else {
                logger.info("[updateAcknowledgementAndProcessOrder] ORDER_OPS_WH_SYNC_READY back sync received from orderOps. Going to process order {}", orderBackSyncTrackingDto.getEntityId());
                updateOrderAndItemStatus(orderDto);
                updateCreateOrderTrackingIfRequired(orderBackSyncTrackingDto);
                resumeOrderProcess(Long.valueOf(orderBackSyncTrackingDto.getEntityId()));
            }
        }
    }

    protected void updateCreateOrderTrackingIfRequired(OrderBackSyncTrackingDto orderBackSyncTrackingDto) {
        StringBuilder searchTerm = new StringBuilder("entityId.eq:")
                .append(orderBackSyncTrackingDto.getEntityId())
                .append("___eventName.eq:")
                .append(CREATE_ORDER);
        OrderBackSyncTrackingDto createOrderBackSyncTrackingDto = orderBackSyncTrackingService.findBySearchTerms(searchTerm.toString());

        if (createOrderBackSyncTrackingDto != null && !BackSyncEventStatus.SUCCESS.equals(createOrderBackSyncTrackingDto.getEventStatus())) {
            logger.info("[updateAcknowledgementAndProcessOrder -> updateCreateOrderTrackingIfRequired] Going to mark event CREATE_ORDER SUCCESS for Order {}.", orderBackSyncTrackingDto.getEntityId());
            createOrderBackSyncTrackingDto.setEventStatus(BackSyncEventStatus.SUCCESS);
            orderBackSyncTrackingService.save(createOrderBackSyncTrackingDto);
        }
    }

    private void updateOrderAndItemStatus(OrderDto orderDto) throws ApplicationException {
        List<ShipmentDto> shipmentDtoList = getShipmentDtos(orderDto);
        if(CollectionUtils.isEmpty(shipmentDtoList)) return;
        Action transitionAction = omsTransitionUtil.getTransitionActionByOperationAndStatus(
                EventToOperationMap.CREATED.getOperation(), shipmentDtoList.get(0).getOrderItems().get(0).getItemStatus()
        );
        logger.info("[updateOrderAndItemStatus] transitionAction {} and shipmentDtoList: {}", transitionAction, shipmentDtoList.size());
        for (OrderItemDto orderItemDto : orderDto.getOrderItems()) {
            if ((omsCommonUtil.isCentralFacilityCodePresent(orderItemDto) || ProductDeliveryType.OTC.equals(orderItemDto.getProductDeliveryType())) && !OrderItemStatus.PENDING.equals(orderItemDto.getItemStatus())) {
                logger.info("[OrderBackSyncFacade][updateOrderAndItemStatus] skipping OTC item status update to: {} for item_id: {}",
                        transitionAction.getItemStatus().getItemStatus(), orderItemDto.getId());
                continue;
            }
            orderItemDto.setItemStatus(transitionAction.getItemStatus().getItemStatus());
            orderItemService.update(orderItemDto, orderItemDto.getId());
        }

        for (ShipmentDto shipmentDto : shipmentDtoList) {
            shipmentDto.setShipmentStatus(transitionAction.getShipmentStatus().getShipmentStatus());
            shipmentService.update(shipmentDto, shipmentDto.getId());
        }

        orderDto.setOrderStatus(transitionAction.getOrderStatus().getOrderStatus());
        orderService.update(orderDto, orderDto.getId());
    }

    private void resumeOrderProcess(Long incrementId) throws ApplicationException {
        OrderDto orderDto = orderService.findByIncrementId(incrementId);
        if (Objects.isNull(orderDto)) {
            logger.error("[resumeOrderProcess] order not found for incrementId {}", incrementId);
            return;
        }

        Set<Long> uniqueShipmentIds = new HashSet<>();
        for (OrderItemDto orderItemDto : orderDto.getOrderItems()) {
            uniqueShipmentIds.add(orderItemDto.getShipmentId());
        }

        List<ShipmentDto> shipmentDtoList = shipmentService.search("id.in:" + StringUtils.join(uniqueShipmentIds, ","));
        for (ShipmentDto shipmentDto : shipmentDtoList) {
            if(omsCommonUtil.isNonWarehouseProcessingOrderExcludingB2b(shipmentDto.getOrderItems().get(0))){
                if (OrderItemStatus.CREATED.equals(shipmentDto.getOrderItems().get(0).getItemStatus())
                        && !FulfillmentType.LOCAL_FITTING.equals(shipmentDto.getOrderItems().get(0).getFulfillmentType())) {
                    logger.info("[resumeOrderProcess] OTC order so we will create invoice right now for shipment {}", shipmentDto.getId());
                    commonKafkaProducer.sendMessage(
                            KafkaConstants.OMS_OTC_ORDER_EVENTS_PROCESS_TOPIC,
                            String.valueOf(shipmentDto.getOrderItems().get(0).getOrderId()),
                            ObjectHelper.convertToString(new OtcShipmentEvent(OtcShipmentEventType.INVOICED, shipmentDto.getId())),
                            String.valueOf(shipmentDto.getId())
                    );
                } else {
                    logger.info("[resumeOrderProcess] skipping OTC item status update to: {} for item_id: {}",
                            OrderItemStatus.PENDING, shipmentDto.getOrderItems().get(0).getId());
                    logger.info("[resumeOrderProcess] not processing backsync order is OTC incrementId : {} wmsOrderCode: {}" , incrementId, shipmentDto.getWmsOrderCode());
                    continue;
                }
            }
            OmsOrderEvent omsOrderEvent = omsOrderEventMapper.getOmsOrderEventFromShipmentDto(shipmentDto, orderDto, OrderEventType.CREATE_ORDER);
            createOrderStrategy.validateAndSyncOrder(omsOrderEvent);
        }
    }

    private void updateAcknowledgement(OrderBackSyncTrackingDto orderBackSyncTrackingDto) {
        StringBuilder searchTerm = new StringBuilder("entityId.eq:")
                .append(orderBackSyncTrackingDto.getEntityId())
                .append("___eventName.eq:")
                .append(orderBackSyncTrackingDto.getEventName());
        OrderBackSyncTrackingDto persistedDto = orderBackSyncTrackingService.findBySearchTerms(searchTerm.toString());

        if (persistedDto != null) {
            persistedDto.setEventStatus(orderBackSyncTrackingDto.getEventStatus());
            orderBackSyncTrackingService.save(persistedDto);
        }
    }

    private OrderDto getOrderDto(Long incrementId) throws ApplicationException {
        OrderDto orderDto = orderService.findByIncrementId(incrementId);
        if (Objects.isNull(orderDto)) {
            throw new ApplicationException("invalid order for incrementId:" + incrementId, null);
        }
        return orderDto;
    }

    private List<ShipmentDto> getShipmentDtos(OrderDto orderDto) {
        Set<Long> shipmentIds = orderDto.getOrderItems().stream()
                .filter(item -> ((!omsCommonUtil.isNonWarehouseProcessingOrder(item)) || (omsCommonUtil.isNonWarehouseProcessingOrder(item) && OrderItemStatus.PENDING.equals(item.getItemStatus()))))
                .map(OrderItemDto::getShipmentId)
                .collect(Collectors.toSet());
        logger.info("[getShipmentDtos] for incrementId: {}  shipmentIds {}", orderDto.getIncrementId(), shipmentIds);
        if(CollectionUtils.isEmpty(shipmentIds)) return new ArrayList<>();
        return shipmentService.search("id.in:" + StringUtils.join(shipmentIds, ","));
    }

    private boolean isOrderAlreadyProcessed(OrderDto orderDto) {
        if (!OrderStatus.getNonProcessingStatus().contains(orderDto.getOrderStatus().name())) {
            logger.info("order {} status {}. Order is already processed.", orderDto.getIncrementId(), orderDto.getOrderStatus());
            return true;
        }

        return false;
    }
}
