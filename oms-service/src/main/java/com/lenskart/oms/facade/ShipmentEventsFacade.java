package com.lenskart.oms.facade;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.connector.OrderOpsConnector;
import com.lenskart.oms.connector.VSMConnecter;
import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.dto.ShipmentTimelineDto;
import com.lenskart.oms.enums.*;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.mapper.UpdateShipmentStatusMapper;
import com.lenskart.oms.producer.ShipmentEventOmsProducer;
import com.lenskart.oms.repository.OrderItemRepository;
import com.lenskart.oms.request.*;
import com.lenskart.oms.service.*;
import com.lenskart.oms.strategy.ShipmentEventStrategyExecutor;
import com.lenskart.oms.strategy.impl.MarkInvoicedVirtualShipmentStrategy;
import com.lenskart.oms.validators.ShipmentEventValidator;
import com.lenskart.order.interceptor.request.MarkLfOrderDispatchRequest;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.http.HttpStatus;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.persistence.Tuple;
import javax.validation.constraints.Email;
import javax.validation.constraints.NotEmpty;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.lenskart.oms.constants.KafkaConstants.MESSAGE_IDEMPOTENCY_KEY;

@Component
@Setter(onMethod__ = {@Autowired})
public class ShipmentEventsFacade {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    @Setter(AccessLevel.NONE)
    @Value("${wms.shipment.backsync.topic}")
    private String wmsShipmentBackSyncTopic;

    @Setter(AccessLevel.NONE)
    @Value("${wms.do.shipment.backsync.topic}")
    private String wmsDoShipmentBackSyncTopic;

    @Setter(AccessLevel.NONE)
    @Value("#{'${sync.events}'.split(',')}")
    private Set<String> syncEvents;

    private OrderService orderService;
    private ShipmentService shipmentService;
    private ShipmentEventStrategyExecutor shipmentEventStrategyExecutor;
    private ShipmentEventOmsProducer shipmentEventOmsProducer;
    private ShipmentEventValidator shipmentEventValidator;
    private VSMConnecter vsmConnecter;
    private ShipmentTimelineService shipmentTimelineService;
    private OrderBackSyncTrackingService orderBackSyncTrackingService;
    private UpdateShipmentStatusMapper updateShipmentStatusMapper;
    private OrderOpsConnector orderOpsConnector;
    private OTCOrderFacade otcOrderFacade;
    private OrderItemService orderItemService;

    @Setter(AccessLevel.NONE)
    @Value("${get.backsync.itemsV1.enabled:true}")
    private boolean getItemsV1Enabled;
    
    private MarkInvoicedVirtualShipmentStrategy markInvoicedVirtualShipmentStrategy;

    @Transactional(rollbackFor = Exception.class)
    public void processCallBacks(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        String apiResponse = null;
        List<OrderOpsUpdateShipmentStatus> orderOpsUpdateShipmentStatus = null;
        switch (shipmentUpdateEvent.getShipmentEvent()) {
            case MARK_ITEM_FULFILLABLE:
                apiResponse = vsmConnecter.markItemFulfillable(shipmentUpdateEvent);
                break;
            case MARK_CREATE_SHIPMENT:
                apiResponse = vsmConnecter.createShippingPackageId(shipmentUpdateEvent);
                break;
            case MARK_SHIPMENT_PICKED:
                apiResponse = vsmConnecter.markShipmentPicked(shipmentUpdateEvent);
                List<OrderOpsUpdateShipmentStatus> orderOpsUpdateShipmentStatusForPicking = updateShipmentStatusMapper.transform(shipmentUpdateEvent);
                orderOpsConnector.updateOrderOpsShipmentStatus(orderOpsUpdateShipmentStatusForPicking);
                break;
            case MARK_SHIPMENT_FITTED:
                break;
            case MARK_SHIPMENT_QC:
                orderOpsUpdateShipmentStatus = updateShipmentStatusMapper.transform(shipmentUpdateEvent);
                orderOpsConnector.updateOrderOpsShipmentStatus(orderOpsUpdateShipmentStatus);
                apiResponse = vsmConnecter.markShipmentQc(shipmentUpdateEvent);
                break;
            case CREATE_SHIPMENT_INVOICE:
            case CREATE_SHIPMENT_AWB:
                orderOpsUpdateShipmentStatus = updateShipmentStatusMapper.transform(shipmentUpdateEvent);
                apiResponse = orderOpsConnector.updateOrderOpsShipmentStatus(orderOpsUpdateShipmentStatus);
                break;
            case MARK_SHIPMENT_MANIFEST:
                apiResponse = processMarkShipmentManifest(shipmentUpdateEvent);
                orderOpsUpdateShipmentStatus = updateShipmentStatusMapper.transform(shipmentUpdateEvent);
                orderOpsConnector.updateOrderOpsShipmentStatus(orderOpsUpdateShipmentStatus);
                break;
            case MARK_SHIPMENT_DISPATCH:
                apiResponse = vsmConnecter.markShipmentDispatch(shipmentUpdateEvent);
                break;
            default:
                throw new ApplicationException("Unexpected event type: " + shipmentUpdateEvent.getShipmentEvent(), null);
        }
        orderBackSyncTrackingService.persistResponseInTracking(apiResponse, shipmentUpdateEvent, null, null);
    }

    public void processShipmentEvent(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        shipmentEventStrategyExecutor.doExecute(shipmentUpdateEvent);
    }

    @Transactional(rollbackFor = Exception.class)
    public void processBackSyncUpdate(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        logger.info("[processBackSyncUpdate]shipmentUpdateEvent={}", shipmentUpdateEvent);
        shipmentEventValidator.validateRequestPayload(shipmentUpdateEvent);
        ShipmentDto shipmentDto = null;
        if (getItemsV1Enabled) {
            shipmentDto = getShipmentDto(shipmentUpdateEvent);
        } else {
            shipmentDto = shipmentService.findBySearchTerms("wmsOrderCode.eq:" + shipmentUpdateEvent.getWmsOrderCode());
        }
        logger.info("[processBackSyncUpdate]shipmentDto={}", shipmentDto);
        appendOrderItemsToShipment(shipmentUpdateEvent, shipmentDto);
        Map<Long, OrderItemDto> persistedOrderItemMap = shipmentDto.getOrderItems().stream().collect(Collectors.toMap(OrderItemDto :: getId, Function.identity()));

        if (shipmentEventValidator.isDuplicateEvent(shipmentUpdateEvent, persistedOrderItemMap)) {
            return;
        }

        shipmentEventValidator.validateTransition(shipmentUpdateEvent, persistedOrderItemMap);
        shipmentEventStrategyExecutor.validator(shipmentUpdateEvent);
        Long junoOrderId;
        if (getItemsV1Enabled) {
            junoOrderId = Long.valueOf(shipmentUpdateEvent.getWmsOrderCode().split("-")[0]);
        } else {
            OrderDto orderDto = orderService.findById(shipmentDto.getOrderItems().get(0).getOrderId());
            junoOrderId = orderDto.getJunoOrderId();
        }
        if (syncEvents.contains(shipmentUpdateEvent.getShipmentEvent().toString())) {
            logger.info("[ShipmentEventsFacade][processBackSyncUpdate] Updating status in sync for {}", shipmentUpdateEvent);
            processShipmentEvent(shipmentUpdateEvent);
            return;
        }
        publishToKafka(shipmentUpdateEvent, junoOrderId);
    }

    public void triggerB2bEmail(@NotEmpty @Email String toEmail, OtcShipmentEvent otcShipmentEvent) throws Exception {
        ShipmentDto shipmentDto = shipmentService.findById(otcShipmentEvent.getShipmentId());
        markInvoicedVirtualShipmentStrategy.triggerInvoiceEmail(shipmentDto, toEmail);
    }

    private ShipmentDto getShipmentDto(ShipmentUpdateEvent shipmentUpdateEvent) {
        ShipmentDto shipmentDto = new ShipmentDto();
        List<OrderItemDto> orderItemDtoList = new ArrayList<>();
        List<Tuple> orderItemList = orderItemService.getOrderItems(shipmentUpdateEvent.getWmsOrderCode());
        logger.info("[getShipmentDto]orderItemList=" + orderItemList);
        for (Tuple tuple : orderItemList) {
            OrderItemDto orderItemDto = new OrderItemDto();
            orderItemDto.setId(Long.valueOf(String.valueOf(tuple.get("id"))));
            orderItemDto.setUwItemId(Long.valueOf(String.valueOf(tuple.get("uwItemId"))));
            orderItemDto.setItemStatus(OrderItemStatus.valueOf(String.valueOf(tuple.get("itemStatus"))));
            orderItemDto.setOrderId(Long.valueOf(String.valueOf(tuple.get("orderId"))));
            orderItemDtoList.add(orderItemDto);
        }
        shipmentDto.setOrderItems(orderItemDtoList);
        return shipmentDto;
    }

    @Transactional
    public void processLfDispatchUpdate(MarkLfOrderDispatchRequest markLfOrderDispatchRequest) throws Exception {
        ShipmentDto shipmentDto =  shipmentService.findBySearchTerms("wmsOrderCode.eq:" + markLfOrderDispatchRequest.getUnicomOrderCode());
        if(!FulfillmentType.LOCAL_FITTING.equals(shipmentDto.getOrderItems().get(0).getFulfillmentType())){
            throw new ApplicationException("Invalid Request: Order is not local_fitting order");
        }
        if(!isOrderInvoiced(shipmentDto)){
            throw new ApplicationException("Invalid Request: Order is not yet Invoiced");
        }

        if(!StringUtils.hasLength(shipmentDto.getFacility())){
            logger.error("[processLfDispatchUpdate] facility code not present for shipment {}", markLfOrderDispatchRequest.getUnicomOrderCode());
            throw new ApplicationException("Facility code is not present for this order");
        }
        if(!shipmentDto.getFacility().equalsIgnoreCase(markLfOrderDispatchRequest.getFittingFacilityCode())){
            logger.error("Facility code not matching exsiting facility {} and requestFacility {}", shipmentDto.getFacility(), markLfOrderDispatchRequest.getFittingFacilityCode());
            throw new ApplicationException("Facility code not matching exsiting facility {} and requestFacility {}");
        }
        OtcShipmentEvent otcShipmentEvent = new OtcShipmentEvent();
        otcShipmentEvent.setShipmentId(shipmentDto.getId());
        otcShipmentEvent.setEventType(OtcShipmentEventType.DISPATCHED);
        otcOrderFacade.processOTCShipmentEvent(otcShipmentEvent);

    }

    private String processMarkShipmentManifest(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        ShipmentDto shipmentDto = shipmentService.findBySearchTerms("wmsOrderCode.eq:" + shipmentUpdateEvent.getWmsOrderCode());
        List<ShipmentTimelineDto> shipmentTimelineDtoList = shipmentTimelineService.search("shipmentId.eq:" + shipmentDto.getId());
        ShipmentTimelineDto dto = shipmentTimelineDtoList.stream().filter(item -> item.getInvoiceTime() != null).findFirst().orElseThrow(() -> {
            return new ApplicationException(HttpStatus.SC_BAD_REQUEST, "ShipmentId Not Invoiced Yet : " + shipmentDto.getId());
        });
        return vsmConnecter.markShipmentManifest(shipmentUpdateEvent, shipmentDto, dto);
    }

    private void publishToKafka(ShipmentUpdateEvent shipmentUpdateEvent, Long hashKey) throws ApplicationException {
        Map<String, String> kafkaHeaders = new HashMap<>();
        kafkaHeaders.put(MESSAGE_IDEMPOTENCY_KEY, shipmentUpdateEvent.getShipmentEvent().name() + "_" + shipmentUpdateEvent.getWmsOrderCode());
        ShipmentDto shipmentDto = shipmentService.findBySearchTerms("wmsOrderCode.eq:" + shipmentUpdateEvent.getWmsOrderCode());
        if(OrderType.DISTRIBUTOR_ORDER.name().equals(shipmentDto.getShipmentSubType())
                || OrderSubType.DISTRIBUTOR_JIT_ORDER.name().equals(shipmentDto.getShipmentSubType())) {
            shipmentEventOmsProducer.sendMessage(shipmentUpdateEvent, wmsDoShipmentBackSyncTopic, kafkaHeaders, String.valueOf(hashKey));
        } else {
            shipmentEventOmsProducer.sendMessage(shipmentUpdateEvent, wmsShipmentBackSyncTopic, kafkaHeaders, String.valueOf(hashKey));
        }
    }

    private ShipmentUpdateEvent appendOrderItemsToShipment(ShipmentUpdateEvent shipmentUpdateEvent, ShipmentDto shipmentDto) {
        List<ShipmentItemUpdate> dbLineItems = new ArrayList<>();
        List<ShipmentItemUpdate> dbUwLineItems = new ArrayList<>();
        Map<Long, OrderItemDto> uwItemOrderMap = new HashMap<>();
        List<ShipmentItemUpdate> payloadLineItems = shipmentUpdateEvent.getOrderItemList();
        for (OrderItemDto item : shipmentDto.getOrderItems()) {
            dbLineItems.add(transformOrderItem(item));
            dbUwLineItems.add(transformUwOrderItem(item));
            uwItemOrderMap.put(item.getUwItemId(), item);
        }
        if (CollectionUtils.isEmpty(payloadLineItems))
            shipmentUpdateEvent.setOrderItemList(dbLineItems);
        else {
            List<ShipmentItemUpdate> toBeAddedLineItems = new ArrayList<>();
            for (ShipmentItemUpdate payLoadItem : payloadLineItems) {
                for (ShipmentItemUpdate dbLineItem : dbUwLineItems) {
                    if (payLoadItem.equals(dbLineItem)) {
                        // Re-Map uwItemId to orderItemId
                        toBeAddedLineItems.add(transformOrderItem(uwItemOrderMap.get(dbLineItem.getOrderItemId()), payLoadItem));
                    }
                }
            }
            shipmentUpdateEvent.setOrderItemList(toBeAddedLineItems);
        }
        return shipmentUpdateEvent;
    }

    private ShipmentItemUpdate transformOrderItem(OrderItemDto dto, ShipmentItemUpdate shipmentItemUpdate) {
        ShipmentItemUpdate newItem = new ShipmentItemUpdate();
        newItem.setOrderItemId(dto.getId());
        newItem.setEntityId(shipmentItemUpdate.getEntityId());
        newItem.setEventTime(shipmentItemUpdate.getEventTime());
        newItem.setOrderOpsShipmentStatus(shipmentItemUpdate.getOrderOpsShipmentStatus());
        newItem.setOrderOpsShipmentState(shipmentItemUpdate.getOrderOpsShipmentState());
        newItem.setUnicomShipmentStatus(shipmentItemUpdate.getUnicomShipmentStatus());
        return newItem;
    }

    private ShipmentItemUpdate transformOrderItem(OrderItemDto dto) {
        ShipmentItemUpdate newItem = new ShipmentItemUpdate();
        newItem.setOrderItemId(dto.getId());
        return newItem;
    }

    private ShipmentItemUpdate transformUwOrderItem(OrderItemDto dto) {
        ShipmentItemUpdate newItem = new ShipmentItemUpdate();
        newItem.setOrderItemId(dto.getUwItemId());
        return newItem;
    }

    private boolean isOrderInvoiced(ShipmentDto shipmentDto){
        return ShipmentStatus.INVOICED ==  shipmentDto.getShipmentStatus();
    }

    public void updateWmsOrderCode(UpdateWmsOrderCodeRequest updateWmsOrderCode) {
        logger.info("[updateWmsOrderCode] updateWmsOrderCodeRequest {}", updateWmsOrderCode);
        ShipmentDto isAlreadyPresentShipment = shipmentService.findBySearchTerms("wmsOrderCode.eq:" + updateWmsOrderCode.getUpdatedWmsOrderCode());

        if(!ObjectUtils.isEmpty(isAlreadyPresentShipment)) {
            logger.info("[updateWmsOrderCode] updatedUnicomOrderCode {} is already present in sensei db", updateWmsOrderCode);
            return;
        }

        ShipmentDto shipmentDto =  shipmentService.findBySearchTerms("wmsOrderCode.eq:" + updateWmsOrderCode.getCurrentWmsOrderCode());
        if(!validateWmsOrderCodeUpdateRequest(shipmentDto, updateWmsOrderCode.getCurrentWmsOrderCode())) return;

        shipmentDto.setWmsOrderCode(updateWmsOrderCode.getUpdatedWmsOrderCode());
        shipmentService.update(shipmentDto, shipmentDto.getId());
    }

    private boolean validateWmsOrderCodeUpdateRequest(ShipmentDto shipmentDto, String  wmsOrderCode) {
        if(ObjectUtils.isEmpty(shipmentDto)) {
            logger.error("[validateWmsOrderCodeUpdateRequest] wmsOrderCode {} is not present in system", wmsOrderCode);
            return false;
        }

        List<OrderItemDto> orderItemDtoList = shipmentDto.getOrderItems().stream()
                .filter(oi -> !(ProductDeliveryType.B2B.equals(oi.getProductDeliveryType()) && oi.getB2bReferenceItemId() == null))
                .collect(Collectors.toList());

        if(!orderItemDtoList.isEmpty()) {
            logger.error("[validateWmsOrderCodeUpdateRequest] wmsOrderCode {} contains non b2b or virtual item", wmsOrderCode);
            return false;
        }

        return true;
    }
}
