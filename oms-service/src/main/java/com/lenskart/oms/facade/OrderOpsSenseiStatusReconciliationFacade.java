package com.lenskart.oms.facade;

import com.google.common.collect.Lists;
import com.lenskart.nexs.commonMailer.connector.CommunicationConnector;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.connector.OrderOpsConnector;
import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.enums.OrderItemStatus;
import com.lenskart.oms.enums.OrderStatus;
import com.lenskart.oms.enums.ShipmentStatus;
import com.lenskart.oms.model.DescripancyOrdersWithOrderOpsPOJO;
import com.lenskart.oms.model.UwOrder;
import com.lenskart.oms.service.OrderService;
import com.lenskart.oms.service.ShipmentService;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.*;
import java.text.DateFormat;
import java.text.Format;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Setter(onMethod__ = {@Autowired})
public class OrderOpsSenseiStatusReconciliationFacade {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    @Value("${oms.discrepancy.scheduler.timePeriodInDays}")
    @Setter(AccessLevel.NONE)
    private Integer timePeriodInDays;

    @Value("${oms.discrepancy.scheduler.batchSize}")
    @Setter(AccessLevel.NONE)
    private Integer batchSize;

    @Value("${sync.pending.order.fromEmailId}")
    @Setter(AccessLevel.NONE)
    private String fromMailId;

    @Value("${sync.pending.order.toEmailIds}")
    @Setter(AccessLevel.NONE)
    private String[] toMailIds;

    @Value("${oms.discrepancy.scheduler.csvHeaders}")
    @Setter(AccessLevel.NONE)
    private String[] csvHeaders;

    @Value("${oms.discrepancy.scheduler.environment}")
    @Setter(AccessLevel.NONE)
    private String environment;

    @Value("${oms.discrepancy.scheduler.orderOpsCancellationStatuses}")
    @Setter(AccessLevel.NONE)
    private String[] orderOpsCancellationStatuses;

    @Value("${oms.discrepancy.scheduler.orderOpsPendingStatuses}")
    @Setter(AccessLevel.NONE)
    private String[] orderOpsPendingStatuses;

    private OrderService orderService;
    private ShipmentService shipmentService;
    private OrderOpsConnector orderOpsConnector;
    private CommunicationConnector communicationConnector;

    @Setter(AccessLevel.NONE)
    private List<String> CSV_HEADERS;

    @Setter(AccessLevel.NONE)
    private List<String> ORDER_OPS_CANCELLATION_STATUSES;

    @Setter(AccessLevel.NONE)
    private List<String> ORDER_OPS_POWER_PENDING_STATUSES;

    @PostConstruct
    public void init(){
        CSV_HEADERS = Arrays.asList(csvHeaders);
        ORDER_OPS_CANCELLATION_STATUSES = Arrays.asList(orderOpsCancellationStatuses);
        ORDER_OPS_POWER_PENDING_STATUSES = Arrays.asList(orderOpsPendingStatuses);
    }

    // bean lifecycle

    public void reconcile(){
        Date timeNow = new Date();
        try {
            logger.info("[alertOrderOpsAndOmsStatusDiscrepancy] Scheduler started at: {}", timeNow);
            Format formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
            Date endTimeStamp = new Date(System.currentTimeMillis() - 24 * 3600 * 1000L);
            Date startTimeStamp = new Date(endTimeStamp.getTime() - timePeriodInDays * 24 * 3600 * 1000L);
            String startDate = formatter.format(startTimeStamp);
            String endDate = formatter.format(endTimeStamp);
            logger.info("[alertOrderOpsAndOmsStatusDiscrepancy] fetching from orders table of sensei DB for start date: {}, end date: {} for the scheduler that started at: {}", startDate, endDate, timeNow);
            List<OrderDto> orderDtoList = orderService.search("updatedAt.gte:" + startDate + "___updatedAt.lte:" + endDate);
            logger.info("[alertOrderOpsAndOmsStatusDiscrepancy] total order list size fetched for last {} days is {} i.e. {} to {} for the scheduler that started at: {}. order dto list: {}", timePeriodInDays, orderDtoList.size(), startDate, endDate, timeNow, orderDtoList);
            if(CollectionUtils.isEmpty(orderDtoList)){
                logger.info("[alertOrderOpsAndOmsStatusDiscrepancy] orderDtoList is empty hence no discrepancy since last {} days. Therefore terminating the scheduler that started at: {}", timePeriodInDays, timeNow);
                return;
            }
            List<List<OrderDto>> orderDtoBatch = Lists.partition(orderDtoList, batchSize);
            List<DescripancyOrdersWithOrderOpsPOJO> discrepancyList = new ArrayList<>();
            List<DescripancyOrdersWithOrderOpsPOJO> discrepancyListForOmsReassigned = new ArrayList<>();
            List<String> errorList = new ArrayList<>();
            logger.info("[alertOrderOpsAndOmsStatusDiscrepancy] No. of batches created is: {} after breaking it into each batch size of: {} for the scheduler that started at: {}", orderDtoBatch.size(), batchSize, timeNow);
            fetchFromOrderOpsAndCompare(orderDtoBatch, timeNow, discrepancyList, errorList, discrepancyListForOmsReassigned);
            logger.info("[alertOrderOpsAndOmsStatusDiscrepancy] Number of discrepancy order items found is: {} during the scheduler that started at: {}", discrepancyList.size(),timeNow);
            generateCSVAndSendEmail(discrepancyList, timeNow, errorList, discrepancyListForOmsReassigned, startDate, endDate, timeNow);
        } catch (Exception e){
            logger.error("[alertOrderOpsAndOmsStatusDiscrepancy] Discrepancy check failed for the scheduler that started at: {} with exception: {}", timeNow, e);
            e.printStackTrace();
        }
    }

    private void fetchFromOrderOpsAndCompare(List<List<OrderDto>> orderDtoBatch, Date timeNow, List<DescripancyOrdersWithOrderOpsPOJO> discrepancyList, List<String> errorList, List<DescripancyOrdersWithOrderOpsPOJO> discrepancyListForOmsReassigned) {
        int batchNumber = 0;
        for (List<OrderDto> orderDto : orderDtoBatch) {
            logger.info("[alertOrderOpsAndOmsStatusDiscrepancy] processing batch no.: {} for the scheduler that started at: {}", batchNumber, timeNow);
            String step = "CallingOrderOps";
            try {
                List<Long> incrementIds = orderDto.stream().map(OrderDto::getIncrementId).collect(Collectors.toList());
                List<UwOrder> orderOpsResponseUwList = orderOpsConnector.getUwOrderDetailsByIncrementIdList(incrementIds);
                step = "CreatingUwOrderMap";
                Map<Long, List<UwOrder>> orderOpsResponse = new HashMap<>();
                for (UwOrder uwOrder : orderOpsResponseUwList) {
                    List<UwOrder> uwOrders = orderOpsResponse.getOrDefault(Long.valueOf(uwOrder.getIncrementId()), new ArrayList<>());
                    uwOrders.add(uwOrder);
                    orderOpsResponse.put(Long.valueOf(uwOrder.getIncrementId()), uwOrders);
                }
                logger.info("[alertOrderOpsAndOmsStatusDiscrepancy] - orderOps call successful, map size: {} for batch number: {} for the scheduler that started at: {}", orderOpsResponse.size(), batchNumber, timeNow);
                step = "CheckingForDiscrepancy";
                compareAndUpdateDiscrepancyList(orderDto, orderOpsResponse, discrepancyList, errorList, batchNumber, discrepancyListForOmsReassigned);
            } catch (Exception ex){
                logger.error("[alertOrderOpsAndOmsStatusDiscrepancy] Failed for batch number: {} at step: {}, with exception: {}", batchNumber, step, ex.getMessage());
                errorList.add("Failed for batch number: "+batchNumber+" at step: "+step+" with exception: "+ex.getMessage());
                ex.printStackTrace();
            }
            logger.info("[alertOrderOpsAndOmsStatusDiscrepancy] processing of batch no. successfully completed: {} for the scheduler that started at: {}", batchNumber, timeNow);
            batchNumber++;
        }
    }

    private void compareAndUpdateDiscrepancyList(List<OrderDto> orderDtoFromSensei, Map<Long, List<UwOrder>> orderOpsResponse, List<DescripancyOrdersWithOrderOpsPOJO> discrepancyList, List<String> errorList, int batchNumber, List<DescripancyOrdersWithOrderOpsPOJO> discrepancyListForOmsReassigned) throws Exception {
        for(OrderDto order:orderDtoFromSensei){
            List<UwOrder> orderItemsFromOrderOps = orderOpsResponse.getOrDefault(order.getIncrementId(), null);
            if(CollectionUtils.isEmpty(orderItemsFromOrderOps)){
                logger.error("[compareAndUpdateDiscrepancyList] order details not found in uw_orders for increment id: {}, batch number: {}", order.getIncrementId(), batchNumber);
                errorList.add("Order details not found in uw_orders for increment id: "+ order.getIncrementId()+", batchNumber: "+batchNumber);
            }
            List<OrderItemDto> orderItemsFromSensei = order.getOrderItems();
            if(CollectionUtils.isEmpty(orderItemsFromSensei)){
                logger.error("[compareAndUpdateDiscrepancyList] orderItem list empty in orderDto sensei for increment id: {}, batchNumber: {}", order.getIncrementId(), batchNumber);
                errorList.add("orderItem list empty in orderDto sensei for increment id: "+order.getIncrementId()+", batchNumber: "+ batchNumber);
            }
            Map<Long, OrderItemDto> senseiOrderItemMap = new HashMap<>();//orderItemsFromSensei.stream().collect(Collectors.toMap(OrderItemDto::getUwItemId, orderItemDto -> orderItemDto));
            for(OrderItemDto orderItemDto: orderItemsFromSensei){
                if(Objects.isNull(orderItemDto.getUwItemId()))
                    continue;
                senseiOrderItemMap.put(orderItemDto.getUwItemId(),orderItemDto);
            }
            if(senseiOrderItemMap.isEmpty())
                continue;
            logger.info("[compareAndUpdateDiscrepancyList] beginning of the mismatch checks for the increment id: {} of batchNumber: {}", order.getIncrementId(), batchNumber);
            for(UwOrder uwOrder: orderItemsFromOrderOps){
                boolean mismatchExists = isMismatchExistsAsPerUwShipmentState(order, uwOrder);
                OrderItemDto senseiOrderItem = senseiOrderItemMap.getOrDefault(Long.valueOf(uwOrder.getUwItemId()), null);
                if(Objects.isNull(senseiOrderItem) || Objects.isNull(senseiOrderItem.getShipmentId())){
                    logger.error("[compareAndUpdateDiscrepancyList] order item not found or it's shipment id is null in sensei for the order-ops uwItemId: {} of incrementId: {} for batchNumber: {}", uwOrder.getUwItemId(), uwOrder.getIncrementId(), batchNumber);
                    errorList.add("order item not found or it's shipment id is null in sensei for the order-ops uwItemId: "+uwOrder.getUwItemId()+" of incrementId: "+uwOrder.getIncrementId()+" for batchNumber: "+batchNumber);
                }
                ShipmentDto shipmentDto = shipmentService.findById(senseiOrderItem.getShipmentId());
                if(senseiOrderItem.getItemStatus().equals(OrderItemStatus.OMS_REASSIGNED)){
                    if(!uwOrder.getUnicomSynStatus().equalsIgnoreCase("YES")){
                        discrepancyListForOmsReassigned.add(createDescripancyPayload(uwOrder, senseiOrderItem, shipmentDto.getShipmentStatus(), order.getOrderStatus()));
                    }
                    continue;
                }
                if(!mismatchExists){
                    mismatchExists = isMismatchExistsAsPerUwShipmentStatus(uwOrder, senseiOrderItem, shipmentDto);
                }
                if(mismatchExists && !(uwOrder.getUnicomShipmentStatus().equalsIgnoreCase(OrderItemStatus.DISPATCHED.name()) && senseiOrderItem.getItemStatus().equals(OrderItemStatus.DISPATCHED))){
                    discrepancyList.add(createDescripancyPayload(uwOrder, senseiOrderItem, shipmentDto.getShipmentStatus(), order.getOrderStatus()));
                } else {
                    logger.info("[compareAndUpdateDiscrepancyList] No mismatch exists for the increment id: {} of batchNumber: {}", order.getIncrementId(), batchNumber);
                }
            }
            logger.info("[compareAndUpdateDiscrepancyList] ended mismatch checks successfully for the increment id: {} of batchNumber: {}", order.getIncrementId(), batchNumber);
        }
    }

    private boolean isMismatchExistsAsPerUwShipmentStatus(UwOrder uwOrder, OrderItemDto senseiOrderItem, ShipmentDto shipmentDto) {
        boolean mismatchExists = false;
        if(isCancelledStatusOnOrderOps(uwOrder) && !OrderItemStatus.CANCELLED.equals(senseiOrderItem.getItemStatus())){
            logger.info("[compareAndUpdateDiscrepancyList] orderOps state is cancelled but sensei status is not cancelled, hence adding it to discrepancy list for uwItemId: {}", uwOrder.getUwItemId());
            mismatchExists = true;
        } else if(isProcessingOnOrderOps(uwOrder) && !ShipmentStatus.PROCESSING.equals(shipmentDto.getShipmentStatus())){
            logger.info("[compareAndUpdateDiscrepancyList] orderOps state is processing but sensei status is not processing, hence adding it to discrepancy list for uwItemId: {}", uwOrder.getUwItemId());
            mismatchExists = true;
        } else if(ORDER_OPS_POWER_PENDING_STATUSES.contains(uwOrder.getShipmentStatus()) && !OrderItemStatus.CREATED.equals(senseiOrderItem.getItemStatus())
                && !OrderItemStatus.PENDING.equals(senseiOrderItem.getItemStatus())){
            logger.info("[compareAndUpdateDiscrepancyList] orderOps state is {} but sensei status is not PENDING nor in CREATED, hence adding it to discrepancy list for uwItemId: {}", senseiOrderItem.getItemStatus(), uwOrder.getUwItemId());
            mismatchExists = true;
        }
        return mismatchExists;
    }

    private boolean isMismatchExistsAsPerUwShipmentState(OrderDto order, UwOrder uwOrder) {
        boolean mismatchExists = false;
        switch (uwOrder.getShipmentState()){
            case "closed":
                if(!OrderStatus.DISPATCHED.equals(order.getOrderStatus()) && !OrderStatus.CANCELLED.equals(order.getOrderStatus())) {
                    logger.info("[compareAndUpdateDiscrepancyList] orderOps shipment state is closed but sensei order status is not in DISPATCHED nor CANCELLED, hence adding it to discrepancy list for uwItemId: {}", uwOrder.getUwItemId());
                    mismatchExists = true;
                }
                break;
            case "complete":
                if(!OrderStatus.DISPATCHED.equals(order.getOrderStatus()) && !OrderStatus.MANIFESTED.equals(order.getOrderStatus())) {
                    logger.info("[compareAndUpdateDiscrepancyList] orderOps shipment state is complete but sensei order status is not in DISPATCHED nor MANIFESTED, hence adding it to discrepancy list for uwItemId: {}", uwOrder.getUwItemId());
                    mismatchExists = true;
                }
                break;
            case "holded":
                if(Boolean.FALSE.equals(order.getIsOnHold())) { //!order.getIsOnHold()
                    logger.info("[compareAndUpdateDiscrepancyList] orderOps shipment state is holded but hold flag is not set in sensei orders table, hence adding it to discrepancy list for uwItemId: {}", uwOrder.getUwItemId());
                    mismatchExists = true;
                }
                break;
            case "new":
                if(!OrderStatus.PENDING.equals(order.getOrderStatus())) {
                    logger.info("[compareAndUpdateDiscrepancyList] orderOps shipment state is new but sensei order status is not in PENDING, hence adding it to discrepancy list for uwItemId: {}", uwOrder.getUwItemId());
                    mismatchExists = true;
                }
                break;
        }
        return mismatchExists;
    }

    private boolean isProcessingOnOrderOps(UwOrder uwOrder) {
        return uwOrder.getShipmentState().equalsIgnoreCase("processing") && uwOrder.getShipmentStatus().equalsIgnoreCase("processing");
    }

    private boolean isCancelledStatusOnOrderOps(UwOrder uwOrder) {
        return "cancelled".equalsIgnoreCase(uwOrder.getUnicomShipmentStatus()) || ORDER_OPS_CANCELLATION_STATUSES.contains(uwOrder.getShipmentState()) || ORDER_OPS_CANCELLATION_STATUSES.contains(uwOrder.getShipmentStatus());
    }

    private DescripancyOrdersWithOrderOpsPOJO createDescripancyPayload(UwOrder uwOrder, OrderItemDto senseiOrderItem, ShipmentStatus shipmentStatus, OrderStatus orderStatus) {
        return new DescripancyOrdersWithOrderOpsPOJO(uwOrder.getIncrementId(), senseiOrderItem.getUwItemId(), uwOrder.getUnicomShipmentStatus(), uwOrder.getShipmentState(), uwOrder.getShipmentStatus(), orderStatus.name(), shipmentStatus.name(), senseiOrderItem.getItemStatus().name(), uwOrder.getCreatedAt(), uwOrder.getUnicomSynStatus());
    }


    private void generateCSVAndSendEmail(List<DescripancyOrdersWithOrderOpsPOJO> discrepancyOrdersForCSV, Date time, List<String> errorList, List<DescripancyOrdersWithOrderOpsPOJO> discrepancyListForOmsReassigned, String startDate, String endDate, Date timeNow) throws Exception {
        logger.info("[generateCSVAndSendEmail] generating input stream for {} order items with discrepancy", discrepancyOrdersForCSV.size());
        InputStream in = getInputStream(discrepancyOrdersForCSV, errorList, discrepancyListForOmsReassigned);
        logger.info("[generateCSVAndSendEmail] input stream generated successfully for {} order items with discrepancy, for the scheduler that started at: {}. Hence proceeding to next step: generateFileFromInputStream", discrepancyOrdersForCSV.size(), time);
        File file = generateFileFromInputStream(in, "ORDER_STATUS_DISCREPANCY_REPORT_BETWEEN_SENSEI_AND_ORDER_OPS_");
        logger.info("[generateCSVAndSendEmail] file generated successfully from input stream for {} order items with discrepancy, for the scheduler that started at: {}. Hence proceeding to next step: sendEmail", discrepancyOrdersForCSV.size(), time);
        Map<String, InputStream> map = new HashMap<>();
        map.put(file.getName(), new FileInputStream(file));
//    @TODO: uncomment    communicationConnector.sendMail(fetchEmailBody(discrepancyOrdersForCSV.size(),discrepancyListForOmsReassigned.size(),errorList.size(), startDate, endDate, timeNow), fromMailId, toMailIds, "["+environment+"]ORDER_STATUS_DISCREPANCY_REPORT_BETWEEN_SENSEI_AND_ORDER_OPS_"+timeNow, map, file.getName());
    }

    private String fetchEmailBody(int discrepancyOrdersForCSVSize, int discrepancyListForOmsReassignedSize, int errorListSize, String startDate, String endDate, Date timeNow) {
        StringBuilder sb = new StringBuilder();
        sb.append("Order Status Discrepancy Report Between Sensei And OrderOps for scheduler that started at: ").append(timeNow);
        sb.append("<br>Discrepancy counts between ").append(startDate).append(" and ").append(endDate).append(": ").append("<br>");
        sb.append("Number of discrepancy orders items: ").append(discrepancyOrdersForCSVSize).append("<br>");
        sb.append("Number of discrepancy order items for OMS_REASSIGNED sensei orders: ").append(discrepancyListForOmsReassignedSize).append("<br><br>");
        sb.append("Number of errors encountered: ").append(errorListSize).append("<br><br>");
        sb.append("NOTE: The thorough details for the above mentioned counts can be found in the attached CSV file");
        return new String(sb);
    }

    private InputStream getInputStream(List<DescripancyOrdersWithOrderOpsPOJO> discrepancyOrdersForCSV, List<String> errorList, List<DescripancyOrdersWithOrderOpsPOJO> discrepancyListForOmsReassigned){
        StringBuilder sb = new StringBuilder();
        sb.append(">>>>List of discrepancy items between order-sensei and order-ops:").append("\n").append("\n");
        if(discrepancyOrdersForCSV.isEmpty()) {
            sb.append("No Discrepancy found").append("\n");
        } else {
            for (int i = 0; i < CSV_HEADERS.size(); i++) {
                sb.append(CSV_HEADERS.get(i));
                sb.append(i == CSV_HEADERS.size() - 1 ? "\n" : ",");
            }
            for (DescripancyOrdersWithOrderOpsPOJO entry : discrepancyOrdersForCSV) {
                sb.append(entry.getIncrementId()).append(",");
                sb.append(entry.getUwItemId()).append(",");
                sb.append(entry.getOrderOpsUnicomShipmentStatus()).append(",");
                sb.append(entry.getOrderOpsShipmentState()).append(",");
                sb.append(entry.getOrderOpsShipmentStatus()).append(",");
                sb.append(entry.getSenseiOrderStatus()).append(",");
                sb.append(entry.getSenseiOrderItemStatus()).append(",");
                sb.append(entry.getSenseiShipmentStatus()).append(",");
                sb.append(entry.getOrderOpsCreatedAt()).append("\n");
            }
        }
        sb.append("\n").append(">>>>List of discrepancy items which are OMS_REASSIGNED as per order-sensei(where unicom_syn_flag is not 'Yes' in Order-ops(Inventory DB) uw_orders table):").append("\n");
        if(discrepancyListForOmsReassigned.isEmpty()){
            sb.append("None found for OMS_REASSIGNMENT discrepancy").append("\n");
        }else {
            CSV_HEADERS = Arrays.asList("Increment Id", "UwItem Id", "Order-ops UnicomSyncStatus", "OrderOps created_at");
            for (int i = 0; i < CSV_HEADERS.size(); i++) {
                sb.append(CSV_HEADERS.get(i));
                sb.append(i == CSV_HEADERS.size() - 1 ? "\n" : ",");
            }
            for (DescripancyOrdersWithOrderOpsPOJO entry : discrepancyListForOmsReassigned) {
                sb.append(entry.getIncrementId()).append(",");
                sb.append(entry.getUwItemId()).append(",");
                sb.append(entry.getUnicomSynStatus()).append(",");
                sb.append(entry.getOrderOpsCreatedAt()).append("\n");
            }
        }
        sb.append("\n").append(">>>>List of errors encountered during the lifetime of the scheduler:").append("\n");
        if(CollectionUtils.isEmpty(errorList)){
            sb.append("No errors encountered");
        } else {
            for(String error:errorList){
                sb.append(error).append("\n");
            }
        }
        byte[] bytes = sb.toString().getBytes();
        return new ByteArrayInputStream(bytes);
    }

    public File generateFileFromInputStream(InputStream in, String fileNameWithOutSuffix) throws IOException {
        DateFormat dateFormatter = new SimpleDateFormat("yyyy-MM-dd_HH-mm-ss");
        String currentDateTime = dateFormatter.format(new Date());
        fileNameWithOutSuffix += currentDateTime;
        File file = File.createTempFile("/tmp/"+fileNameWithOutSuffix,".csv");
        try (FileOutputStream out = new FileOutputStream(file)) {
            IOUtils.copy(in, out);
        }
        return file;
    }
}
