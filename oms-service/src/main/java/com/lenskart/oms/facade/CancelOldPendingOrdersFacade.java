package com.lenskart.oms.facade;

import com.google.common.collect.Lists;
import com.lenskart.nexs.commonMailer.connector.CommunicationConnector;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.connector.OrderOpsConnector;
import com.lenskart.oms.dto.OrderDto;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;

import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.enums.ProductDeliveryType;
import com.lenskart.oms.service.ShipmentService;
import com.lenskart.oms.service.OrderService;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.text.Format;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.lenskart.oms.constants.ApplicationConstants.HOME_EYE_CHECK_UP_PROGRAM;
import static com.lenskart.oms.enums.OrderStatus.getNonProcessingStatus;

@Component
@Setter(onMethod__ = {@Autowired})
public class CancelOldPendingOrdersFacade {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    @Value("${sync.pending.order.fromEmailId}")
    @Setter(AccessLevel.NONE)
    private String fromMailId;

    @Value("${sync.pending.order.toEmailIds}")
    @Setter(AccessLevel.NONE)
    private String[] toMailIds;

    @Value("${oms.discrepancy.scheduler.environment}")
    @Setter(AccessLevel.NONE)
    private String environment;

    @Value("${oms.cancelOldOrders.scheduler.timePeriodInDays}")
    @Setter(AccessLevel.NONE)
    private Integer timePeriodInDays;

    @Value("${oms.cancelOldOrders.scheduler.batchSize}")
    @Setter(AccessLevel.NONE)
    private Integer batchSize;

    @Value("${oms.cancelOldOrders.scheduler.channels}")
    @Setter(AccessLevel.NONE)
    private String[] eligibleChannelsForAutoCancellation;

    @Value("${oms.cancelOldOrders.scheduler.enabled}")
    @Setter(AccessLevel.NONE)
    private Boolean isThisScriptEnabled;

    private ShipmentService shipmentService;
    private OrderOpsConnector orderOpsConnector;
    private OrderService orderService;
    private CommunicationConnector communicationConnector;
    private OrderOpsSenseiStatusReconciliationFacade orderOpsSenseiStatusReconciliationFacade;

    public void cancelOldOrders(){
        Date timeNow = new Date();
        String step = "validateIfScriptIsEnabled";
        try {
            logger.info("[omsCancelOldPendingOrdersScheduler] Scheduler started at: {}", timeNow);
            if(!isThisScriptEnabled){
                logger.info("[omsCancelOldPendingOrdersScheduler] This script is not enabled form the consul to run, hence terminating the scheduler that started at: {}", timeNow);
                return;
            }
            Format formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
            Date endTimeDate = new Date(System.currentTimeMillis() - (timePeriodInDays * 24 * 3600 * 1000L));
            String endTime = formatter.format(endTimeDate);
            logger.info("[omsCancelOldPendingOrdersScheduler] fetching from orders table of sensei DB for status in : CREATED or PENDING and with updated_at before: {} , for the scheduler that started at: {}", endTime, timeNow);
            step = "fetchingFromSenseiOrdersTable";
            List<Long> incrementIdList = orderService.findByOrderStatusInAndUpdatedAt(getNonProcessingStatus(), endTimeDate);
            List<Long> subIncrementIdList = incrementIdList.subList(0,batchSize);
            logger.info("[omsCancelOldPendingOrdersScheduler] total order increment id list size fetched for updated at before: {} days is {} i.e. before {} for the scheduler that started at: {}. Increment id list: {}", timePeriodInDays, incrementIdList.size(), endTime, timeNow, incrementIdList);
            if (CollectionUtils.isEmpty(subIncrementIdList)) {
                logger.info("[omsCancelOldPendingOrdersScheduler] incrementIdList is empty hence no old orders are present with the statues as : CREATED or PENDING before {}. Therefore terminating the scheduler that started at: {}", endTime, timeNow);
                return;
            }
            List<String> errorList = new ArrayList<>();
            List<String> csvDataList = new ArrayList<>();
            Map<Long, OrderDto> senseiOrderStateMap = new HashMap<>();
            List<OrderDto> orderDtoList = new ArrayList<>();
            for (Long incrementId : subIncrementIdList) {
                OrderDto orderDto = orderService.findByIncrementId(incrementId);
                logger.info("[omsCancelOldPendingOrdersScheduler] processing for increment id: {} for the scheduler that started at: {}", incrementId, timeNow);
                try {
                    step = "ValidatingEligibilityForAutoCancellation";
                    logger.info("[omsCancelOldPendingOrdersScheduler] validating for increment id: {} for the scheduler that started at: {}", orderDto.getIncrementId(), timeNow);
                    List<OrderItemDto> orderItems = orderDto.getOrderItems();
                    boolean isHtoOrder = false;
                    boolean isSCApplied = false;
                    boolean isChannelEligible = false;
                    boolean isErisEmail = false;
                    boolean isDTCOrder = false;
                    if (!CollectionUtils.isEmpty(orderItems)) {
                        for (OrderItemDto orderItem : orderItems) {
                            if (HOME_EYE_CHECK_UP_PROGRAM.equals(orderItem.getProductId())) {
                                isHtoOrder = true;
                            }
                            if (Objects.nonNull(orderItem.getOrderItemPrice().getScDiscount()) && orderItem.getOrderItemPrice().getScDiscount() > 0.0) {
                                isSCApplied = true;
                            }
                        }
                        isChannelEligible = Arrays.asList(eligibleChannelsForAutoCancellation).contains(orderItems.get(0).getChannel().name());
                        ShipmentDto shipmentDto = shipmentService.findById(orderItems.get(0).getShipmentId());
                        String customerEmail = Objects.isNull(shipmentDto.getBillingAddress()) ? null : shipmentDto.getBillingAddress().getEmail();
                        if (customerEmail == null || customerEmail.isEmpty() || customerEmail.contains("@eris.com")) {
                            isErisEmail = true;
                        }
                        isDTCOrder = ProductDeliveryType.DTC.equals(orderItems.get(0).getProductDeliveryType());
                    }
                    logger.info("[omsCancelOldPendingOrdersScheduler] results after validating for increment id: {}, isHtoOrder: {}, isSCApplied: {}, isChannelEligible: {}, isErisEmail: {}, isDTCOrder: {}",
                            orderDto.getIncrementId(), isHtoOrder, isSCApplied, isChannelEligible, isErisEmail, isDTCOrder);
                    step = "CreatingSenseiOrderMap";
                    if (!isHtoOrder && !isSCApplied && isChannelEligible && !isErisEmail && isDTCOrder) {
                        senseiOrderStateMap.put(orderDto.getIncrementId(), orderDto);
                        orderDtoList.add(orderDto);
                    }
                } catch (Exception ex) {
                    logger.error("[omsCancelOldPendingOrdersScheduler] Failed for increment id: {} at step: {}, with exception: {}", incrementId, step, ex.getMessage(), ex);
                    errorList.add("Failed for increment id: "+incrementId+" with exception: "+ex.getMessage());
                }
            }
            logger.info("[omsCancelOldPendingOrdersScheduler] senseiOrderStateMap keys: {}",senseiOrderStateMap.keySet());
            step = "CallingOrderOps";
            try {
                Map<Long, String> orderOpsResponsePayload = orderOpsConnector.verifyAndCancelOnOrderOps(senseiOrderStateMap.keySet());
                step = "AppendingToCsv";
                for (OrderDto order : orderDtoList) {
                    StringBuilder csvLine = new StringBuilder();
                    csvLine.append(order.getIncrementId()).append(",").append(order.getOrderStatus()).append(",");
                    csvLine.append(order.getUpdatedAt());
                    csvLine.append(orderOpsResponsePayload.getOrDefault(order.getIncrementId(), "Not found in Order-ops response")).append("\n");

                    csvDataList.add(csvLine.toString());
                }
            } catch (Exception ex){
                logger.error("[omsCancelOldPendingOrdersScheduler] Failed at step: {}, with exception: {}", step, ex.getMessage(), ex);
                errorList.add("Failed at step: "+step+" with exception: "+ex.getMessage());
            }
            logger.info("[omsCancelOldPendingOrdersScheduler] processing of increment id list:{} successfully completed for the scheduler that started at: {}", subIncrementIdList, timeNow);
            generateCsvFileAndSendEmail(endTime,timeNow,new StringBuilder(String.join("", csvDataList)),errorList,orderDtoList.size());
        } catch (Exception e){
            logger.error("[omsCancelOldPendingOrdersScheduler] Verify and cancel old pending orders failed at step: {} for the scheduler that started at: {} with exception: {}", step, timeNow, e.getMessage(), e);
        }
    }

    private InputStream getInputstream(String endTime, StringBuilder csvBodyData, List<String> errorList) {
        StringBuilder csvData = new StringBuilder();
        csvData.append(">>>>List of increment id's found till ").append(endTime).append(" that are still in either CREATED or PENDING").append("\n").append("\n");
        if(csvBodyData.length()==0){
            csvData.append("No such orders found").append("\n");
        } else {
            csvData.append("Increment Id,Sensei order status,Sensei updatedAt,Order-ops response\n");
            csvData.append(csvBodyData);
        }
        csvData.append("\n").append(">>>>List of errors encountered during the lifetime of the scheduler:").append("\n");
        csvData.append(CollectionUtils.isEmpty(errorList) ? "No errors encountered" : String.join("\n", errorList));
        byte[] bytes = csvData.toString().getBytes();
        return new ByteArrayInputStream(bytes);
    }

    private void generateCsvFileAndSendEmail(String endTime, Date timeNow, StringBuilder csvBodyData, List<String> errorList, int numberOfOldOrders) throws Exception {
        logger.info("[generateCsvFileAndSendEmail] generating input stream for the scheduler that started at: {}", timeNow);
        InputStream in = getInputstream(endTime, csvBodyData, errorList);
        logger.info("[generateCsvFileAndSendEmail] input stream generated successfully for the scheduler that started at: {}. Hence proceeding to next step: generateFileFromInputStream", timeNow);
        File file = orderOpsSenseiStatusReconciliationFacade.generateFileFromInputStream(in, "CANCEL_OLD_ORDERS_SUMMARY_REPORT_BETWEEN_SENSEI_AND_ORDER_OPS_");
        logger.info("[generateCsvFileAndSendEmail] file generated successfully from input stream for the scheduler that started at: {}. Hence proceeding to next step: sendEmail", timeNow);
        Map<String, InputStream> map = new HashMap<>();
        map.put(file.getName(), new FileInputStream(file));
        communicationConnector.sendMail(getEmailBody(errorList.size(), endTime, timeNow, numberOfOldOrders), fromMailId, toMailIds, "["+environment+"]CANCEL_OLD_ORDERS_SUMMARY_REPORT_BETWEEN_SENSEI_AND_ORDER_OPS_"+timeNow, map, file.getName());
    }

    private String getEmailBody(int errorListSize, String endDate, Date timeNow, int numberOfOldOrders) {
        StringBuilder sb = new StringBuilder();
        sb.append("Cancel old orders summary Report Between Sensei And OrderOps for scheduler that started at: ").append(timeNow);
        sb.append("<br>Total count of orders found: ").append(numberOfOldOrders).append(" (till ").append(endDate).append(")<br><br>");
        sb.append("Number of errors encountered: ").append(errorListSize).append("<br><br>");
        sb.append("NOTE: The thorough details for the above mentioned counts can be found in the attached CSV file");
        return new String(sb);
    }
}
