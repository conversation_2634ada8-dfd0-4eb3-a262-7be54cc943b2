package com.lenskart.oms.facade;

import com.lenskart.nexs.commonMailer.connector.CommunicationConnector;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.connector.OrderOpsConnector;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.entity.OrderItems;
import com.lenskart.oms.entity.Shipment;
import com.lenskart.oms.enums.*;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.model.Action;
import com.lenskart.oms.response.OrderStatusUpdateDetails;
import com.lenskart.oms.service.OrderService;
import com.lenskart.oms.service.ShipmentService;
import com.lenskart.oms.utils.OmsTransitionUtil;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.lenskart.oms.constants.ApplicationConstants.DATE_PATTERN;
import static com.lenskart.oms.enums.OrderStatus.getNonProcessingStatus;
import static com.lenskart.oms.enums.OrderStatus.getNonProcessingStatusEnum;

@Setter(onMethod__ = {@Autowired})
@Component
public class ReassignHelperFacade {

    @Setter(AccessLevel.NONE)
    private final DateFormat dateFormat = new SimpleDateFormat(DATE_PATTERN);

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    @Value("${spring.profiles.active}")
    @Setter(AccessLevel.NONE)
    private String currentEnv;

    @Value("${oms.reassign.pending.order.end.time}")
    @Setter(AccessLevel.NONE)
    private String omsReassignPendingOrderEndTime;

    @Value("${oms.reassign.pending.order.start.time}")
    @Setter(AccessLevel.NONE)
    private String omsReassignPendingOrderStartTime;

    @Value("${oms.reassign.pending.order.fromEmailId}")
    @Setter(AccessLevel.NONE)
    private String fromMailId;

    @Value("${oms.reassign.pending.order.toEmailIds}")
    @Setter(AccessLevel.NONE)
    private String[] toMailIds;

    @Value("${oms.reassignedShipmentSync.start.time}")
    @Setter(AccessLevel.NONE)
    private String omsReassignedShipmentSyncStartTime;

    @Value("${oms.reassignedShipmentSync.end.time}")
    @Setter(AccessLevel.NONE)
    private String omsReassignedShipmentSyncEndTime;

    private OrderService orderService;
    private ReassignOmsFacade reassignOmsFacade;
    private OrderBackSyncFacade orderBackSyncFacade;
    private CommunicationConnector communicationConnector;
    private ShipmentService shipmentService;
    private OrderOpsConnector orderOpsConnector;
    private OmsTransitionUtil omsTransitionUtil;

    public void omsReassignPendingOrders(Boolean isFullSync) {
        List<OrderDto> orderDtoList;
        Map<Long, String> omsReassignedOrders = new HashMap<>();
        Date endHour = new Date(System.currentTimeMillis() - 1000 * 60 * 60 * Long.parseLong(omsReassignPendingOrderEndTime));

        if (isFullSync) {
            logger.info("[omsReassignPendingOrders] fetching {} hours old pending orders list in orderStatus {} with full sync", endHour, getNonProcessingStatus());
            orderDtoList = orderService.search("orderStatus.in:" + StringUtils.join(getNonProcessingStatus(), ",") + "___createdAt.lte:" + endHour);
        } else {
            Date startHour = new Date(System.currentTimeMillis() - 1000 * 60 * 60 * Long.parseLong(omsReassignPendingOrderStartTime));
            logger.info("[omsReassignPendingOrders] fetching pending and created orders list in orderStatus {} from created at {} to {}", getNonProcessingStatus(), startHour, endHour);
            orderDtoList = orderService.findByOrderStatusInAndCreatedAtBetween(getNonProcessingStatusEnum(), startHour, endHour);
        }

        logger.info("[omsReassignPendingOrders] fetched pending and created orders list in orderStatus {} is {}", getNonProcessingStatus(), orderDtoList);
        for (OrderDto orderDto : orderDtoList) {
            List<ShipmentDto> shipmentDtoList = reassignOmsFacade.getShipmentDtos(orderDto);
            Set<ShipmentStatus> shipmentStatusSet = shipmentDtoList
                    .stream()
                    .map(ShipmentDto::getShipmentStatus)
                    .collect(Collectors.toSet());
            if (shipmentStatusSet.contains(ShipmentStatus.PENDING)
                    || shipmentStatusSet.contains(ShipmentStatus.CREATED)
            ) {
                try {
                    logger.info("[omsReassignPendingOrders] Going to reassign OMS order {}", orderDto.getIncrementId());
                    reassignOmsFacade.reassignOrderOms(shipmentDtoList.get(0).getWmsOrderCode(), OrderSubStatus.DELAYED_ORDER.name(), null);
                    orderBackSyncFacade.createBackSyncEntryAndPushToOrderOpsAsync(orderDto.getIncrementId(), BackSyncEventName.PUSH_TO_WH_READY_QUEUE, null);
                    omsReassignedOrders.put(orderDto.getIncrementId(), ApplicationConstants.SUCCESS);
                    logger.info("[omsReassignPendingOrders] OMS reassignment successful for order {}", orderDto.getIncrementId());
                } catch (Exception e) {
                    logger.error("[omsReassignPendingOrders] reassignment failed for order {} with exception " + e, orderDto.getIncrementId());
                    omsReassignedOrders.put(orderDto.getIncrementId(), ApplicationConstants.FAILED);
                }
            }
        }

        triggerMail(omsReassignedOrders);
    }

    public void reassignOrderOms(String wmsOrderCode, String reassignmentReason) throws ApplicationException {
        reassignOmsFacade.reassignOrderOms(wmsOrderCode, reassignmentReason, null);
        OrderDto orderDto = reassignOmsFacade.getOrderDtoByWmsOrderCode(wmsOrderCode);
        orderBackSyncFacade.createBackSyncEntryAndPushToOrderOpsAsync(orderDto.getIncrementId(), BackSyncEventName.PUSH_TO_WH_READY_QUEUE, null);
    }

    public void syncReassignedShipment() throws ApplicationException{
        Date startHour = new Date(System.currentTimeMillis() - 1000 * 60 * Long.parseLong(omsReassignedShipmentSyncStartTime));
        Date endHour = new Date(System.currentTimeMillis() - 1000 * 60 * Long.parseLong(omsReassignedShipmentSyncEndTime));
        logger.info("[syncReassignedShipment] scheduler started with start hour {} and end hour {}", startHour, endHour);
        List<ShipmentDto> shipmentList =  shipmentService.findByShipmentStatusAndUpdatedAtBetween(ShipmentStatus.OMS_REASSIGNED.name(), startHour, endHour);

        for(ShipmentDto shipment : shipmentList){
            OrderStatusUpdateDetails orderStatusUpdateDetails = orderOpsConnector.getOrderStatus(shipment.getWmsOrderCode());
            if("complete_dispatched".equalsIgnoreCase(orderStatusUpdateDetails.getStatus())){
                if(shipment.getShipmentSubStatus() != ShipmentSubStatus.DISPATCHED){
                    shipment.setShipmentSubStatus(ShipmentSubStatus.DISPATCHED);
                    updateOrderItems(shipment, OrderItemSubStatus.DISPATCHED);
                    shipmentService.update(shipment, shipment.getId());
                    Action transitionAction = omsTransitionUtil.getTransitionActionByOperationAndStatus(
                            EventToOperationMap.valueOf(ShipmentEvent.MARK_SHIPMENT_DISPATCH.name()).getOperation(),
                            shipment.getOrderItems().get(0).getItemStatus()
                    );
                    updateOrderDataIfRequired(shipment, ShipmentEvent.MARK_SHIPMENT_DISPATCH);
                }
            } else if("complete".equalsIgnoreCase(orderStatusUpdateDetails.getStatus())){
                if(shipment.getShipmentStatus() != ShipmentStatus.MANIFESTED) {
                    shipment.setShipmentSubStatus(ShipmentSubStatus.MANIFESTED);
                    updateOrderItems( shipment, OrderItemSubStatus.PACKED);
                    shipmentService.update(shipment, shipment.getId());
                    updateOrderDataIfRequired(shipment, ShipmentEvent.MARK_SHIPMENT_MANIFEST);
                }
            }
        }


    }

    private void triggerMail(Map<Long, String> omsReassignedOrders) {
        if (!CollectionUtils.isEmpty(omsReassignedOrders)) {
            String mailBody = buildMailBody(omsReassignedOrders);
            String subject = "Oms Reassign Pending orders summary [" + currentEnv.split("-")[0] + "] - " + dateFormat.format(new Date());

//    @TODO:uncomment        communicationConnector.sendMail(mailBody, fromMailId, toMailIds, subject, null, null);
        }
    }

    private String buildMailBody(Map<Long, String> omsReassignedOrders) {
        StringBuilder html = new StringBuilder("<html><body><h2>Oms Reassign Pending orders summary</h2>");

        StringBuilder table1 = new StringBuilder("<table border='1' width='700'>");
        table1.append("<tr><th style='background-color:yellow'>IncrementId</th><th style='background-color:yellow'>Status</th></tr>");
        for (Map.Entry<Long, String> entrySet : omsReassignedOrders.entrySet()) {
            table1.append("<tr>")
                    .append("<td align='middle'>")
                    .append(entrySet.getKey())
                    .append("</td>")
                    .append("<td align='middle'>")
                    .append(entrySet.getValue())
                    .append("</td>")
                    .append("</tr>");
        }
        table1.append("</table>");

        return html + table1.toString() + "</body></html>";
    }

    private void updateOrderItems(ShipmentDto shipment, OrderItemSubStatus orderItemSubStatus){
        for(OrderItemDto orderItems : shipment.getOrderItems()){
            orderItems.setItemSubStatus(orderItemSubStatus);
        }
    }

    private void updateOrderDataIfRequired(ShipmentDto shipmentDto, ShipmentEvent shipmentEvent) throws ApplicationException {
        Action transitionAction = omsTransitionUtil.getTransitionActionByOperationAndStatus(
                EventToOperationMap.valueOf(shipmentEvent.name()).getOperation(),
                shipmentDto.getOrderItems().get(0).getItemStatus()
        );

        OrderDto orderDto = orderService.findById(shipmentDto.getOrderItems().get(0).getOrderId());
        if (isOrderStatusUpdateRequired(transitionAction, orderDto)) {
            logger.info("Updating order status from {} to {} for orderId - {}", orderDto.getOrderItems(), transitionAction.getOrderStatus().getOrderStatus(), orderDto.getId());
            orderDto.setOrderStatus(transitionAction.getOrderStatus().getOrderStatus());
            orderDto.setOrderSubStatus(transitionAction.getOrderStatus().getOrderSubStatus());
            orderService.save(orderDto);
        }
    }

    protected Boolean isOrderStatusUpdateRequired(Action transitionAction, OrderDto orderDto) {
        List<ShipmentDto> shipmentDtoList = getShipmentDtos(orderDto);
        for (ShipmentDto shipmentDto : shipmentDtoList) {
            if (!shipmentDto.getShipmentStatus().name().equalsIgnoreCase(transitionAction.getOrderStatus().getOrderStatus().name())) {
                return false;
            }
        }
        return true;
    }

    protected List<ShipmentDto> getShipmentDtos(OrderDto orderDto) {
        List<ShipmentDto> shipmentDtoList = new ArrayList<>();
        Set<Long> shipmentIds = orderDto.getOrderItems().stream()
                .map(OrderItemDto::getShipmentId)
                .collect(Collectors.toSet());

        for (Long shipmentId : shipmentIds) {
            shipmentDtoList.add(shipmentService.findById(shipmentId));
        }
        return shipmentDtoList;
    }

}
