package com.lenskart.oms.facade;

import com.fasterxml.jackson.core.type.TypeReference;
import com.lenskart.core.model.Product;
import com.lenskart.fds.dto.DocumentDetailsDto;
import com.lenskart.fds.dto.DocumentItemDetailsDto;
import com.lenskart.fds.dto.DocumentItemTaxDetailsDto;
import com.lenskart.nexs.constants.RedisOps;
import com.lenskart.nexs.service.RedisHandler;
import com.lenskart.oms.connector.CatalogOpsConnector;
import com.lenskart.oms.connector.D365Connector;
import com.lenskart.oms.connector.FdsConnector;
import com.lenskart.oms.connector.OrderManagementServiceConnector;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.dto.*;
import com.lenskart.oms.entity.Order;
import com.lenskart.oms.enums.Channel;
import com.lenskart.oms.enums.NavChannel;
import com.lenskart.oms.enums.ProductDeliveryType;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.repository.OrderRepository;
import com.lenskart.oms.request.D365PublishDispatchRequest;
import com.lenskart.oms.request.HsnAndTaxDto;
import com.lenskart.oms.request.ShipmentUpdateEvent;
import com.lenskart.oms.response.D365PublishDispatchResponse;
import com.lenskart.oms.service.*;
import com.lenskart.oms.utils.D365AddressMap;
import com.lenskart.oms.utils.LegalOwnerUtil;
import com.lenskart.oms.utils.ObjectHelper;
import lombok.AccessLevel;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Setter(onMethod__ = {@Autowired})
@Component
public class D365Facade {

    private OrderRepository orderRepository;
    private FdsConnector fdsConnector;
    private ShipmentTimelineService shipmentTimelineService;
    private CatalogOpsConnector catalogOpsConnector;
    private ShipmentService shipmentService;
    private OrderService orderService;
    private LegalOwnerUtil legalOwnerUtil;
    private ShipmentMetaService shipmentMetaService;
    private D365Connector d365Connector;
    private static final String DISTRIBUTED_SALE_ORDER = "DISTRIBUTED_SALE_ORDER";
    private static final String SALE_ORDER = "SALE_ORDER";
    private OrderManagementServiceConnector orderManagementServiceConnector;
    @Setter(AccessLevel.NONE)
    @Value("#{'${d365.exempt.pids.list}'.split(',')}")
    private List<Long> exemptPidsList;
    private static final String CATALOG_OPS_CLASSIFICATIONS_ID_TO_NAME_MAP_KEY = "CATALOG_OPS_CLASSIFICATIONS_ID_TO_NAME_MAP_KEY";
    @Setter(AccessLevel.NONE)
    @Value("#{'${d365.gold.membership.pids.list:128269,148392,148393,151290,200193,210661,216617,216630}'.split(',')}")
    private List<Long> goldMembershipProductIds;
    @Setter(AccessLevel.NONE)
    @Value("${d365.tax.rate.for.shipping.charges:18}")
    private double taxRateForShippingCharges;
    @Setter(AccessLevel.NONE)
    @Value("#{'${d365.insurance.pids.list:135217,152747,152748,152749}'.split(',')}")
    private List<Long> insurancePidsList;



    public void publishDispatch(OrderDto orderDto, ShipmentDto shipmentDto) {
        try {
            if (Objects.isNull(shipmentDto)) {
                log.info("[D365Facade][publishDispatch] ShipmentDto is NULL for order: {}", orderDto.getIncrementId());
                return;
            }
            boolean isPublished = false;
            shipmentMetaService.saveOrUpdateByShipmentIdAndKey(shipmentDto.getId(), ApplicationConstants.D365_DISPATCH_SHIPMENT_KEY, "FALSE");
            D365PublishDispatchRequest d365PublishDispatchRequest = getD365PublishDispatchRequest(orderDto, shipmentDto);
            D365PublishDispatchResponse d365PublishDispatchResponse = d365Connector.publishDispatchOrderToD365(d365PublishDispatchRequest);
            if (d365PublishDispatchResponse == null)
                log.error("[D365Facade][publishDispatch] NULL response from D365 API");
            else if (d365PublishDispatchResponse.getData().getStatus().equals("SUCCESS")) {
                isPublished = true;
                shipmentMetaService.saveOrUpdateByShipmentIdAndKey(shipmentDto.getId(), ApplicationConstants.D365_DISPATCH_SHIPMENT_KEY, "TRUE");
            }
            log.info("[D365Facade][publishDispatch] Updated KEY= {} with VALUE= {} for Shipment: {} in shipment_meta_data", ApplicationConstants.D365_DISPATCH_SHIPMENT_KEY, isPublished, shipmentDto);
        } catch (Exception e) {
            log.error("[D365Facade][publishDispatch] Error occurred while publishing dispatch to D365. Updating flag as false in shipment_meta for Shipment ID:" + shipmentDto.getId(), e);
            shipmentMetaService.saveOrUpdateByShipmentIdAndKey(shipmentDto.getId(), ApplicationConstants.D365_DISPATCH_SHIPMENT_KEY, "FALSE");

        }

    }

    public D365PublishDispatchRequest getD365PublishDispatchRequest(OrderDto orderDto, ShipmentDto shipmentDto) {
        try {
            D365PublishDispatchRequest d365PublishDispatchRequest = new D365PublishDispatchRequest();
            d365PublishDispatchRequest.setEventTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            d365PublishDispatchRequest.setFacilityCode(shipmentDto.getFacility());
            d365PublishDispatchRequest.setShippingPackageId(shipmentDto.getWmsShippingPackageId());
            d365PublishDispatchRequest.setOrderId(orderDto.getId());
            d365PublishDispatchRequest.setLegalEntity(legalOwnerUtil.getLegalOwner(shipmentDto.getFacility(), legalOwnerUtil.getHubCode(shipmentDto), shipmentDto.getLegalOwnerCountry()));
            d365PublishDispatchRequest.setNavChannel(shipmentDto.getOrderItems().get(0).getNavChannel().name());
            d365PublishDispatchRequest.setSource("NEXS");
            d365PublishDispatchRequest.setEntityType(getD365EntityType(orderDto));
            List<Long> uwItemIds = new ArrayList<>();
            for(OrderItemDto orderItemDto : orderDto.getOrderItems()) {
                uwItemIds.add(orderItemDto.getUwItemId());
            }
            d365PublishDispatchRequest.setUwItemIds(uwItemIds);
            return d365PublishDispatchRequest;
        } catch(Exception e) {
            log.error("[getD365PublishDispatchRequest] Error while setting request || Error: {}",orderDto, e);
        }
        return null;
    }

    public String getD365EntityType(OrderDto orderDto) {
        return NavChannel.DODTC.equals(orderDto.getOrderItems().get(0).getNavChannel()) ? DISTRIBUTED_SALE_ORDER : SALE_ORDER;
    }

    public SaleOrderDto generateSaleOrderPayload(String shippingPackageId) throws Exception {
        try {
            SaleOrderDto saleOrderDto = new SaleOrderDto();
            ShipmentOrderDetails shipmentOrderDetails;
            shipmentOrderDetails = fetchShipmentAndOrder(shippingPackageId);
            ShipmentDto shipment = shipmentOrderDetails.getShipment();
            OrderDto order = shipmentOrderDetails.getOrder();
            List<OrderItemDto> orderItemsList = shipment.getOrderItems();
            List<String> fdsShippingPackageIds = new ArrayList<>();
            fdsShippingPackageIds.add(shippingPackageId);
            List<DocumentDetailsDto> documentDetailsDtos = fdsConnector.fetchInvoices(fdsShippingPackageIds);
            if (documentDetailsDtos == null || documentDetailsDtos.isEmpty()) {
                log.error("[D365Facade][generateSaleOrderPayload] Cannot fetch record from FDS. NOT FOUND or EMPTY for " + fdsShippingPackageIds);
                throw new ApplicationException("[D365Facade] Cannot fetch record from FDS. NOT FOUND or EMPTY for " + fdsShippingPackageIds);
            }
            boolean isB2bOrder = ProductDeliveryType.B2B.equals(orderItemsList.get(0).getProductDeliveryType());
            String storeFacility = isB2bOrder
                    ? order.getOrderMetaData().stream()
                    .filter(meta -> "FACILITY_CODE".equals(meta.getEntityKey()))
                    .map(OrderMetaDataDto::getEntityValue)
                    .findFirst()
                    .orElse("")
                    : "";
            DocumentDetailsDto documentDetailsDto = documentDetailsDtos.get(0);
            Map<Long, HsnAndTaxDto> hsnAndTaxDtoMap = populateHsnAndTaxDtoMap(documentDetailsDto);
            Map<Integer, String> classificationIdToNameMap = getClassificationMap();
            log.info("FDS response: {}", documentDetailsDtos);
            OrderAddressDto orderAddress = shipment.getShippingAddress();
            String legalOwner = legalOwnerUtil.getLegalOwner(shipment.getFacility(), legalOwnerUtil.getHubCode(shipment), shipment.getLegalOwnerCountry());
            saleOrderDto.setSalesOrderNumber(shipment.getWmsShippingPackageId() + "_" + (isB2bOrder ? storeFacility : shipment.getFacility()));
            saleOrderDto.setFirstName(orderAddress.getFirstName());
            saleOrderDto.setLastName(orderAddress.getLastName());
            saleOrderDto.setSubChannel(orderItemsList.get(0).getNavChannel().name());
            saleOrderDto.setGender("Non-Specific");
            saleOrderDto.setSalesChannel(shipment.getFacility());
            saleOrderDto.setInvoiceAccount(String.valueOf(order.getCustomerId()));
            saleOrderDto.setInventLocationId(shipment.getFacility());
            saleOrderDto.setCurrencyCode(order.getCurrencyCode());
            saleOrderDto.setOrderType("Sales Order");
            saleOrderDto.setExportReason(getExportReason(order, shipment));
            saleOrderDto.setIsWithTax("yes");
            saleOrderDto.setTCSGroup("");
            saleOrderDto.setTDSGroup("");
            List<ShipmentTimelineDto> shipmentTimelineDtoList = shipmentTimelineService.findByShipmentId(shipment.getId());
            if (shipmentTimelineDtoList == null || shipmentTimelineDtoList.isEmpty()) {
                log.error("[D365Facade] Shipment Timeline not found or null for shipment id: {}", shipment.getId());
                throw new ApplicationException("[D365Facade] Shipment Timeline not found or null for shipment id: " + shipment.getId());
            }
            saleOrderDto.setModeOfPayment(order.getPaymentMethod());
            saleOrderDto.setDeliveryName(orderAddress.getFirstName() + " " + orderAddress.getLastName());
            saleOrderDto.setAddressStreet(orderAddress.getStreet());
            saleOrderDto.setAddressCity(orderAddress.getCity());
            saleOrderDto.setAddressState(D365AddressMap.StateMapping.get(orderAddress.getRegion().toUpperCase()));
            saleOrderDto.setAddressZipCode(orderAddress.getPostcode());
            saleOrderDto.setAddressCountryRegionId(orderAddress.getCountryCode().equals("IN") ? "IND" : orderAddress.getCountryCode());
            saleOrderDto.setContactPersonId(orderAddress.getEmail());
            saleOrderDto.setEmail(orderAddress.getEmail());
            saleOrderDto.setPhone(orderAddress.getTelephone());
            saleOrderDto.setOrderCreationDate(new SimpleDateFormat("yyyy-MM-dd").format(order.getOrderDate()));
            saleOrderDto.setMethodOfPayment("Prepaid");
            saleOrderDto.setCourierCode(shipment.getCourierCode());
            saleOrderDto.setLegalEntity(legalOwner);
            saleOrderDto.setTermsOfPayment("");
            saleOrderDto.setModeOfDelivery("Air");
            saleOrderDto.setWebOrderNo(String.valueOf(order.getIncrementId()));
            saleOrderDto.setMiscChargesLines(new ArrayList<>());
            saleOrderDto.setMiscChargesHeader(new ArrayList<>());
            saleOrderDto.setRmaNumber("");
            saleOrderDto.setIsIntegrated(1);
            saleOrderDto.setFulfillmentWH(shipment.getFacility());
            String sbrtFlag = NavChannel.DODTC.equals(order.getOrderItems().get(0).getNavChannel()) ? "SBRT" : getSbrtFlag(shipment.getWmsOrderCode());
            String deliveryType = ProductDeliveryType.B2B.equals(orderItemsList.get(0).getProductDeliveryType()) && !NavChannel.FOFOB2B.equals(orderItemsList.get(0).getNavChannel()) ? "Direct Delivery" : "Stock";
            List<SaleOrderItemDto> saleOrderItemDtoList = new ArrayList<>();
            for (OrderItemDto orderItem : orderItemsList) {
                OrderItemPricesDto orderItemPrice = orderItem.getOrderItemPrice();
                SaleOrderItemDto saleOrderItemDto = new SaleOrderItemDto();
                saleOrderItemDto.setLineNumber(orderItem.getUwItemId());
                saleOrderItemDto.setItemNumber(String.valueOf(orderItem.getProductId()));
                saleOrderItemDto.setQtyOrdered(1);
                saleOrderItemDto.setSalesPrice(String.valueOf(orderItemPrice.getItemTotal()));
                saleOrderItemDto.setLineDiscountAmount("0");
                saleOrderItemDto.setDeliveryType(deliveryType);
                saleOrderItemDto.setSourcingVendor(legalOwner);
                saleOrderItemDto.setDeliveryModeCode(null);
                saleOrderItemDto.setInventLocationId(shipment.getFacility());
                saleOrderDto.setStores(isB2bOrder ? storeFacility : null);
                saleOrderDto.setCustomerAccount(order.getCustomerId().toString());
                saleOrderDto.setCustomerGroup(getCustomerGroup(order, shipment));
                saleOrderDto.setOrgInvoiceNo("");
                saleOrderItemDto.setInventSiteId("");
                saleOrderItemDto.setConfirmedReceiptDate(shipmentTimelineDtoList.get(0).getDispatchTime().toString());
                saleOrderItemDto.setConfirmedShipDate(new SimpleDateFormat("yyyy-MM-dd").format(order.getCreatedAt()));
                saleOrderItemDto.setSalesChannel(shipment.getFacility());
                saleOrderItemDto.setSubChannel(orderItem.getNavChannel().name());
                Product product = catalogOpsConnector.findProductDetailsByProductId(orderItem.getProductId());
                log.info("Product details for product id: {} are: {}", orderItem.getProductId(), product);
                saleOrderItemDto.setPartnerType("");
                saleOrderItemDto.setItemClassification(getItemClassificationName(product.getClassification(), classificationIdToNameMap));
                saleOrderItemDto.setBrand(product.getBrand());
                saleOrderItemDto.setMagentoItemId(Math.toIntExact(orderItem.getMagentoItemId()));
                saleOrderItemDto.setPurchPrice("0.00");
                saleOrderItemDto.setHsnCode(hsnAndTaxDtoMap.get(orderItem.getProductId()).getHsnCode());
                saleOrderItemDto.setSalesOrderItemCode(orderItem.getUwItemId().toString());
                String taxPercentage = String.format("%.2f", hsnAndTaxDtoMap.get(orderItem.getProductId()).getTaxPercentage());
                saleOrderItemDto.setTaxRateType(taxPercentage);
                saleOrderItemDto.setItemSalesTaxGrp(taxPercentage);
                saleOrderItemDto.setSalesTaxGrp(taxPercentage);
                saleOrderItemDto.setItemTemplateName("");
                saleOrderItemDto.setSalesOrderItemCode("");
                saleOrderItemDto.setSalesPool("");
                saleOrderItemDto.setSacCode("");
                saleOrderItemDto.setLkReferenceWh("");
                saleOrderItemDto.setOriginalSaleOrderNo(0);
                saleOrderItemDto.setOriginalSaleOrderLineNo(0);
                saleOrderItemDto.setExempt(getExemptFlag(order, shipment, orderItem.getProductId()) ? "Yes" : "No");
                saleOrderItemDto.setUnits(sbrtFlag);
                saleOrderDto.setStores(isB2bOrder ? storeFacility : null);

                saleOrderItemDtoList.add(saleOrderItemDto);
            }
            double shippingCharges = getShippingChargesForShipment(orderItemsList);
            if (shippingCharges > 0.0 && "IN".equals(order.getLkCountry())) {
                OrderItemDto freeGoldMembershipItem = getFreeGoldMembershipItem(orderItemsList);
                if (freeGoldMembershipItem == null) {
                    SaleOrderItemDto shippingChargeLine = getSoLineForShippingCharges(
                            orderItemsList.get(0),
                            shippingCharges,
                            getIsExportOrder(shipment.getShippingAddress(), shipment.getBillingAddress(), order),
                            shipmentTimelineDtoList.get(0).getDispatchTime().toString(),
                            order,
                            shipment
                    );
                    saleOrderItemDtoList.add(shippingChargeLine);
                } else {
                    double taxRateMultiplier = 1.0 + (taxRateForShippingCharges / 100.0);
                    double shippingChargeWithoutTax = shippingCharges / taxRateMultiplier;

                    saleOrderItemDtoList.stream()
                            .filter(item -> freeGoldMembershipItem.getUwItemId().equals(item.getLineNumber()))
                            .findFirst()
                            .ifPresent(existingGoldLine -> {
                                double currentSalesPrice = Double.parseDouble(existingGoldLine.getSalesPrice());
                                double updatedSalesPrice = currentSalesPrice + shippingChargeWithoutTax;
                                existingGoldLine.setSalesPrice(String.format("%.2f", updatedSalesPrice));
                            });
                }
            }
            saleOrderDto.setSoLines(saleOrderItemDtoList);
            return saleOrderDto;
        } catch (Exception e) {
            log.error("[D365Facade] Error in generating d365 SaleOrder response for " + shippingPackageId + " Error: " + e.getMessage(), e);
            throw new ApplicationException(e.getMessage(), e);
        }
    }

    private boolean getIsExportOrder(OrderAddressDto shippingAddress, OrderAddressDto billingAddress, OrderDto order) {
        // Return true in case of international shipping to avoid tax config setup
        if (Objects.isNull(shippingAddress) || Objects.isNull(billingAddress) || Objects.isNull(order)) {
            return false;
        }
        String shippingCountry = shippingAddress.getCountry();
        String billingCountry = billingAddress.getCountry();
        String orderCountry = order.getLkCountry();

        if (Objects.isNull(shippingCountry) || Objects.isNull(billingCountry) || Objects.isNull(orderCountry)) {
            return false;
        }

        return !"IN".equalsIgnoreCase(shippingCountry) && ("IN".equalsIgnoreCase(billingCountry) || "IN".equalsIgnoreCase(orderCountry));
    }

    private SaleOrderItemDto getSoLineForShippingCharges(OrderItemDto orderItem, double shippingCharges, boolean isExportOrder, String dispatchDate, OrderDto order, ShipmentDto shipmentDto) {
        SaleOrderItemDto soLine = new SaleOrderItemDto();
        String site = shipmentDto.getFacility();
        String subChannel = orderItem.getNavChannel().name();
        double taxRateMultiplier = 1.00D + (taxRateForShippingCharges / 100.00D);
        double shippingChargeWithoutTax = shippingCharges / taxRateMultiplier;
        boolean isB2bOrder = ProductDeliveryType.B2B.equals(orderItem.getProductDeliveryType());
        String storeFacility = isB2bOrder
                ? order.getOrderMetaData().stream()
                .filter(meta -> "FACILITY_CODE".equals(meta.getEntityKey()))
                .map(OrderMetaDataDto::getEntityValue)
                .findFirst()
                .orElse("")
                : "";
        soLine.setPartnerType("");
        soLine.setLineDiscountAmount(String.format("%.2f", 0.0D));
        if (orderItem.getProductDeliveryType().equals(ProductDeliveryType.B2B)) {
            if (!orderItem.getNavChannel().equals(NavChannel.FOFOB2B)) {
                soLine.setSourcingVendor("LKIN");
                soLine.setPurchPrice("1.00");
            } else {
                soLine.setPurchPrice("0.00");
                soLine.setSourcingVendor("");
            }
            soLine.setDeliveryType("Direct Delivery");
            soLine.setConfirmedShipDate(dispatchDate);
        } else {
            soLine.setDeliveryType("Stock");
            soLine.setConfirmedShipDate(new SimpleDateFormat("yyyy-MM-dd").format(order.getCreatedAt()));
            soLine.setSourcingVendor("");
            soLine.setPurchPrice("0.00");
        }
        soLine.setSalesPrice(String.format("%.2f", shippingChargeWithoutTax));

        if (orderItem.getNavChannel().equals(NavChannel.FOFOB2B)) {
            soLine.setDeliveryType("Stock");
        }
        soLine.setConfirmedReceiptDate(dispatchDate);
        soLine.setLineNumber(1L);
        soLine.setItemNumber("SHIPPING");
        soLine.setQtyOrdered(1);
        soLine.setSalesOrderItemCode("");
        soLine.setTaxRateType(String.format("%.2f", taxRateForShippingCharges));
        soLine.setItemSalesTaxGrp(String.format("%.2f", taxRateForShippingCharges));
        soLine.setSalesTaxGrp(String.format("%.2f", taxRateForShippingCharges));
        soLine.setItemTemplateName("");
        soLine.setInventLocationId(site);
        soLine.setStores(isB2bOrder ? storeFacility : null);
        soLine.setInventSiteId("");
        soLine.setBrand("Free");
        soLine.setSubChannel(subChannel);
        soLine.setSalesChannel(site);
        soLine.setMagentoItemId(null);
        soLine.setItemClassification("Prescription Lens");
        soLine.setHsnCode("");
        soLine.setSacCode("997212");
        soLine.setExempt(getExemptFlagValue(isExportOrder, soLine.getItemNumber()));
        return soLine;
    }

    private String getExemptFlagValue(boolean isExportOrder, String productId) {
        if (isExportOrder || (!CollectionUtils.isEmpty(exemptPidsList) && exemptPidsList.contains(productId))) {
            return "Yes";
        }
        return "No";
    }


    private OrderItemDto getFreeGoldMembershipItem(List<OrderItemDto> orderItems) {
        for (OrderItemDto orderItem : orderItems){
            if(isFreeGoldMembershipItem(orderItem)){
                return orderItem;
            }
        }
        return null;
    }

    private boolean isFreeGoldMembershipItem(OrderItemDto orderItem) {
        return !CollectionUtils.isEmpty(goldMembershipProductIds) && goldMembershipProductIds.contains(orderItem.getProductId()) && isZeroValueItem(orderItem);
    }

    private boolean isZeroValueItem(OrderItemDto orderItem) {
        double salePrice = fetchCostPriceForOrderItem(orderItem);
        if(0.00D==salePrice){
            return true;
        }
        return false;
    }

    double fetchCostPriceForOrderItem(OrderItemDto orderItem) {
        OrderItemPricesDto itemWisePriceDetails = orderItem.getOrderItemPrice();
        double costPrice = itemWisePriceDetails.getItemTotalAfterDiscount() - itemWisePriceDetails.getShippingCharges();
        double totalTaxCollected = itemWisePriceDetails.getTaxCollected();
            if (insurancePidsList.contains(orderItem.getProductId())) {
                return costPrice; // with tax
            } else {
                return costPrice - totalTaxCollected; // without tax
            }
    }



    private Map<Integer, String> getClassificationMap() throws Exception {
        Object classificationMapObject = RedisHandler.redisOps(RedisOps.GET, CATALOG_OPS_CLASSIFICATIONS_ID_TO_NAME_MAP_KEY);
        if (Objects.nonNull(classificationMapObject)) {
            log.info("[D365Facade][getClassificationMap] Found Classifications Map in Redis: {}", classificationMapObject);
            return ObjectHelper.getObjectMapper().readValue(String.valueOf(classificationMapObject), new TypeReference<Map<Integer, String>>() {});
        }
        return reloadClassificationMapFromCatalogOps();
    }

    private Map<Integer, String> reloadClassificationMapFromCatalogOps() throws Exception {
        log.info("[D365Facade][getClassificationMap] Fetching latest classification map from catalog ops");
        Map<Integer, String> classificationsMap = catalogOpsConnector.getClassificationMap();
        RedisHandler.redisOps(RedisOps.SETVALUETTL, CATALOG_OPS_CLASSIFICATIONS_ID_TO_NAME_MAP_KEY, ObjectHelper.convertToString(classificationsMap), 7L, TimeUnit.DAYS);
        return classificationsMap;
    }

    private String getItemClassificationName(Integer classificationId, Map<Integer, String> classificationMap) throws Exception {
        if (classificationId == 21567) {
            return "Sunglasses";
        }
        if (classificationId == 19429) {
            return "Contact Lens";
        }
        String classificationName = classificationMap.getOrDefault(classificationId, null);
        if (classificationName == null) {
            classificationMap = reloadClassificationMapFromCatalogOps();
        }
        return classificationMap.getOrDefault(classificationId, null);
    }

    public String getSbrtFlag(String wmsOrderCode) {
        return orderManagementServiceConnector.fetchSbrtFlagForUnicomOrderCode(wmsOrderCode) ? "SBRT" : "Non_SBRT";
    }

    private boolean getExemptFlag(OrderDto orderDto, ShipmentDto shipmentDto, Long productId) {
        if (exemptPidsList.contains(productId)) {
            return true;
        }
        OrderAddressDto shippingAddress = shipmentDto.getShippingAddress();
        OrderAddressDto billingAddress = shipmentDto.getBillingAddress();
        return !"IN".equalsIgnoreCase(shippingAddress.getCountry()) && ("IN".equalsIgnoreCase(billingAddress.getCountry()) || "IN".equalsIgnoreCase(orderDto.getLkCountry()));

    }

    private String getCustomerGroup(OrderDto orderDto, ShipmentDto shipmentDto) {
        String customerGroup = "";
        String lkCountry = orderDto.getLkCountry();
        String type = "IN".equals(lkCountry) ? "Domestic" : "International";


        if (NavChannel.DODTC.equals(shipmentDto.getOrderItems().get(0).getNavChannel())) {
            customerGroup = "B2B" + type;
        } else if ("SG".equals(lkCountry) && ApplicationConstants.SHIPPING_DESTINATION_STORE.equalsIgnoreCase(shipmentDto.getOrderItems().get(0).getShippingDestinationType())) {
            customerGroup = "Intercompany";
        } else if (ProductDeliveryType.DTC.equals(shipmentDto.getOrderItems().get(0).getProductDeliveryType()) || ProductDeliveryType.OTC.equals(shipmentDto.getOrderItems().get(0).getProductDeliveryType())) {
            customerGroup = "B2C" + type;
        } else if (ApplicationConstants.SHIPPING_DESTINATION_STORE.equalsIgnoreCase(shipmentDto.getOrderItems().get(0).getShippingDestinationType())) {
            customerGroup = "B2B" + type;
        } else if (ProductDeliveryType.B2B.equals(shipmentDto.getOrderItems().get(0).getProductDeliveryType())) {
            customerGroup = "DK trade";
        }
        return customerGroup;
    }

    private String getExportReason(OrderDto orderDto, ShipmentDto shipmentDto) {
        String taxCheck;
        OrderAddressDto billingAddress = shipmentDto.getBillingAddress();
        if (billingAddress.getCountry().equals(orderDto.getLkCountry())) {
            taxCheck = "yes";
        } else {
            taxCheck = "no";
        }
        return taxCheck;
    }

    private Map<Long, HsnAndTaxDto> populateHsnAndTaxDtoMap(DocumentDetailsDto documentDetailsDto) {
        HashMap<Long, HsnAndTaxDto> hsnAndTaxDtoMap = new HashMap<>();
        for (DocumentItemDetailsDto documentItem : documentDetailsDto.getDocumentItems()) {
            double taxPercentage = 0.0;
            List<DocumentItemTaxDetailsDto> documentItemTaxDetailsDtoList = documentItem.getDocumentItemTaxDetailsDto();
            if (documentItemTaxDetailsDtoList != null && !documentItemTaxDetailsDtoList.isEmpty()) {
                taxPercentage = documentItemTaxDetailsDtoList.stream().mapToDouble(DocumentItemTaxDetailsDto::getPercentage).sum();
            }
            String hsnCode = documentItem.getHsnCode();
            if (!hsnAndTaxDtoMap.containsKey(documentItem.getProductId())) {
                hsnAndTaxDtoMap.put(documentItem.getProductId(), new HsnAndTaxDto(taxPercentage, hsnCode));
            }
        }
        return hsnAndTaxDtoMap;
    }

    public PackingSlipDto generatePackingSlipPayload(String shippingPackageId) throws ApplicationException {
        try {
            PackingSlipDto packingSlipDto = new PackingSlipDto();
            ShipmentOrderDetails shipmentOrderDetails;
            shipmentOrderDetails = fetchShipmentAndOrder(shippingPackageId);
            ShipmentDto shipment = shipmentOrderDetails.getShipment();
            OrderDto order = shipmentOrderDetails.getOrder();
            List<OrderItemDto> orderItemsList = shipmentOrderDetails.getOrderItemsList();
            List<ShipmentTimelineDto> shipmentTimelineDtoList = shipmentTimelineService.findByShipmentId(shipment.getId());
            if (shipmentTimelineDtoList == null || shipmentTimelineDtoList.isEmpty()) {
                log.error("[D365Facade] Shipment Timeline not found or null for shipment id: {}", shipment.getId());
                throw new ApplicationException("[D365Facade] Shipment Timeline not found or null for shipment id: " + shipment.getId());
            }
            boolean isB2bOrder = ProductDeliveryType.B2B.equals(orderItemsList.get(0).getProductDeliveryType());
            String storeFacility = isB2bOrder
                    ? order.getOrderMetaData().stream()
                    .filter(meta -> "FACILITY_CODE".equals(meta.getEntityKey()))
                    .map(OrderMetaDataDto::getEntityValue)
                    .findFirst()
                    .orElse("")
                    : "";
            packingSlipDto.setSalesId(shipment.getWmsShippingPackageId() + "_" + (isB2bOrder ? storeFacility : shipment.getFacility()));
            packingSlipDto.setPackingSlipId(shipment.getWmsOrderCode());
            packingSlipDto.setLegalEntity(legalOwnerUtil.getLegalOwner(shipment.getFacility(), legalOwnerUtil.getHubCode(shipment), shipment.getLegalOwnerCountry()));
            packingSlipDto.setSalesInvoiceNo(shipment.getInvoiceNumber());
            packingSlipDto.setPackingSlipDate(new SimpleDateFormat("yyyy-MM-dd").format(shipment.getCreatedAt()));
            packingSlipDto.setDescription(null);

            List<PackingSlipItemDto> packingSlipItemDtoList = new ArrayList<>();
            String facilityCode = shipment.getFacility();

            for (OrderItemDto orderItem : orderItemsList) {
                PackingSlipItemDto packingSlipItemDto = new PackingSlipItemDto();
                packingSlipItemDto.setSalesId(shipment.getWmsShippingPackageId() + "_" + (isB2bOrder ? storeFacility : shipment.getFacility()));
                packingSlipItemDto.setItemNumber(orderItem.getProductId().toString());
                packingSlipItemDto.setLineNumber(orderItem.getUwItemId());
                packingSlipItemDto.setQuantity("1");
                packingSlipItemDto.setBatchId("");
                packingSlipItemDto.setSerialId(orderItem.getItemBarcode());
                packingSlipItemDtoList.add(packingSlipItemDto);
            }
            double shippingCharges = getShippingChargesForShipment(orderItemsList);
            if (shippingCharges > 0.0 && "IN".equals(order.getLkCountry())) {
                OrderItemDto freeGoldMembershipItem = getFreeGoldMembershipItem(orderItemsList);
                if (freeGoldMembershipItem == null) {
                    String saleId = shipment.getWmsShippingPackageId() + "_" + (isB2bOrder ? storeFacility : shipment.getFacility());
                    PackingSlipItemDto salesLineListForShippingCharges = getSalesLineListForShippingCharges(saleId);
                    packingSlipItemDtoList.add(salesLineListForShippingCharges);
                }
            }
            packingSlipDto.setSalesLineList(packingSlipItemDtoList);

            return packingSlipDto;
        } catch (Exception e) {
            log.error("[D365Facade] Error in generating d365 PackingSlip response for " + shippingPackageId + " Error: " + e.getMessage(), e);
            throw new ApplicationException("[D365Facade] Error in generating PackingSlip Payload. Error: " + e);
        }
    }

    PackingSlipItemDto getSalesLineListForShippingCharges(String salesId){
        PackingSlipItemDto salesLineList = new PackingSlipItemDto();
        salesLineList.setSalesId(salesId);
        salesLineList.setQuantity("1");
        salesLineList.setBatchId("");
        salesLineList.setItemNumber("SHIPPING");
        salesLineList.setLineNumber(Long.valueOf(1));
        salesLineList.setSerialId("");
        return  salesLineList;
    }


    public ShipmentOrderDetails fetchShipmentAndOrder(String shippingPackageId) throws ApplicationException {
        ShipmentOrderDetails shipmentOrderDetails = new ShipmentOrderDetails();
        ShipmentDto shipment = shipmentService.findBySearchTerms("wmsShippingPackageId.eq:" + shippingPackageId);
        if (shipment == null) {
            log.error("[D365Facade] Shipment not found for shipping package id : {}", shippingPackageId);
            throw new ApplicationException("Shipment not found for shipping package id : " + shippingPackageId);
        }
        List<OrderItemDto> orderItemsList = shipment.getOrderItems();
        if (orderItemsList == null || orderItemsList.isEmpty()) {
            log.error("[D365Facade] Order Items not found or empty for shipping package id : {}", shippingPackageId);
            throw new ApplicationException("Order Items not found or empty for shipping package id : " + shippingPackageId);
        }
        Optional<Order> orderOptional = orderRepository.findById(shipment.getOrderItems().get(0).getOrderId());
        if (!orderOptional.isPresent()) {
            log.error("[D365Facade] Order not found for shipping package id : {}", shippingPackageId);
            throw new ApplicationException("Order not found for shipping package id : " + shippingPackageId);
        }
        OrderDto orderDto = new OrderDto();
        orderDto = orderService.convertToDto(orderOptional.get(), orderDto);
        shipmentOrderDetails.setShipment(shipment);
        shipmentOrderDetails.setOrder(orderDto);
        shipmentOrderDetails.setOrderItemsList(orderItemsList);

        return shipmentOrderDetails;
    }

    private double getShippingChargesForShipment(List<OrderItemDto> orderItems) {
        Double shippingCharges = 0.00D;
        List<OrderItemPricesDto> orderItemsPricesList = orderItems.stream().map(OrderItemDto::getOrderItemPrice).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(orderItemsPricesList)){
            for (OrderItemPricesDto itemWisePriceDetails : orderItemsPricesList) {
                if (itemWisePriceDetails.getShippingCharges() != null) {
                    shippingCharges += itemWisePriceDetails.getShippingCharges();
                }
            }
        }
        return shippingCharges;
    }

}
