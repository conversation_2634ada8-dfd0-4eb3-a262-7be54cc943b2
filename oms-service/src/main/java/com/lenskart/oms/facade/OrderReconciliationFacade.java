package com.lenskart.oms.facade;

import com.lenskart.nexs.commonMailer.connector.CommunicationConnector;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.request.OrderReconciliationRequest;
import com.lenskart.oms.service.OrderService;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.lenskart.oms.constants.ApplicationConstants.*;

@Component
@Setter(onMethod__ = {@Autowired})
public class OrderReconciliationFacade {

    @Setter(AccessLevel.NONE)
    private final DateFormat dateFormat = new SimpleDateFormat(DATE_PATTERN);

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    @Value("${spring.profiles.active}")
    @Setter(AccessLevel.NONE)
    private String currentEnv;

    @Value("${sync.pending.order.end.time}")
    @Setter(AccessLevel.NONE)
    private String syncPendingOrderEndTime;

    @Value("${sync.pending.order.start.time}")
    @Setter(AccessLevel.NONE)
    private String syncPendingOrderStartTime;

    @Value("${sync.pending.order.fromEmailId}")
    @Setter(AccessLevel.NONE)
    private String fromMailId;

    @Value("${sync.pending.order.toEmailIds}")
    @Setter(AccessLevel.NONE)
    private String[] toMailIds;

    @Value("${sync.pending.order.success.threshold}")
    @Setter(AccessLevel.NONE)
    private Integer syncPendingOrderSuccessThreshold;

    private OrderService orderService;
    private OrderEventsFacade orderEventsFacade;
    private CommunicationConnector communicationConnector;

    public void syncPendingOrdersToProcessKafka(OrderReconciliationRequest orderReconciliationRequest) {
        List<OrderDto> orderDtoList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(orderReconciliationRequest.getIncrementIdList())) {
            logger.info("[syncPendingOrderToProcessKafka] fetching order list for increment ids {}", orderReconciliationRequest.getIncrementIdList());
            orderDtoList.addAll(orderService.search("incrementId.in:" + StringUtils.join(orderReconciliationRequest.getIncrementIdList(), ",")));
        } else if (orderReconciliationRequest.getIsFullSync()) {
            logger.info("[syncPendingOrderToProcessKafka] fetching order list for orderStatus {} with full sync", orderReconciliationRequest.getOrderStatus());
            orderDtoList = orderService.search("orderStatus.eq:" + orderReconciliationRequest.getOrderStatus());
        } else {
            Date endHour = new Date(System.currentTimeMillis() - 1000 * 60 * Long.parseLong(syncPendingOrderEndTime));
            Date startHour = new Date(System.currentTimeMillis() - 1000 * 60 * Long.parseLong(syncPendingOrderStartTime));
            logger.info("[syncPendingOrderToProcessKafka] fetching order list for orderStatus {} from updated at {} to {}", orderReconciliationRequest.getOrderStatus(), startHour, endHour);
            orderDtoList = orderService.findByOrderStatusAndUpdatedAtBetween(orderReconciliationRequest.getOrderStatus(), startHour, endHour);
        }

        Map<Long, String> syncPendingOrdersWithStatus = new HashMap<>();
        for (OrderDto orderDto : orderDtoList) {
            orderEventsFacade.syncPendingOrderToProcessKafka(orderDto, syncPendingOrdersWithStatus);
        }

        triggerMail(syncPendingOrdersWithStatus);
    }

    private void triggerMail(Map<Long, String> syncPendingOrdersWithStatus) {
        if (!CollectionUtils.isEmpty(syncPendingOrdersWithStatus)
                && isSuccessThresholdBreached(syncPendingOrdersWithStatus)
        ) {
            String mailBody = buildMailBody(syncPendingOrdersWithStatus);
            String subject = "Sync Pending OMS orders summary [" + currentEnv.split("-")[0] + "] - " + dateFormat.format(new Date());

//   @TODO: uncomment         communicationConnector.sendMail(mailBody, fromMailId, toMailIds, subject, null, null);
        }
    }

    private boolean isSuccessThresholdBreached(Map<Long, String> syncPendingOrdersWithStatus) {
        if (!syncPendingOrdersWithStatus.values()
                .stream()
                .filter(message -> message.equalsIgnoreCase(FAILED))
                .collect(Collectors.toList())
                .isEmpty()) {
            return true;
        }

        if (syncPendingOrdersWithStatus.values()
                .stream()
                .filter(message -> message.equalsIgnoreCase(SUCCESS))
                .collect(Collectors.toList())
                .size() > syncPendingOrderSuccessThreshold) {
            return true;
        }

        return false;
    }

    private String buildMailBody(Map<Long, String> syncPendingOrdersWithStatus) {
        StringBuilder html = new StringBuilder("<html><body><h2>Sync Pending OMS Order summary</h2>");

        StringBuilder table1 = new StringBuilder("<table border='1' width='700'>");
        table1.append("<tr><th style='background-color:yellow'>IncrementId</th><th style='background-color:yellow'>Status</th></tr>");
        for (Map.Entry<Long, String> entrySet : syncPendingOrdersWithStatus.entrySet()) {
            table1.append("<tr>")
                    .append("<td align='middle'>")
                    .append(entrySet.getKey())
                    .append("</td>")
                    .append("<td align='middle'>")
                    .append(entrySet.getValue())
                    .append("</td>")
                    .append("</tr>");
        }
        table1.append("</table>");

        return html + table1.toString() + "</body></html>";
    }

}
