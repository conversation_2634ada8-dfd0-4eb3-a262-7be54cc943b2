package com.lenskart.oms.facade;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.enums.BackSyncEventName;
import com.lenskart.oms.enums.OrderEventType;
import com.lenskart.oms.enums.ProductDeliveryType;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.mapper.*;
import com.lenskart.oms.producer.OrderEventOmsProducer;
import com.lenskart.oms.request.OmsOrderEvent;
import com.lenskart.oms.request.OrderEvent;
import com.lenskart.oms.service.OrderService;
import com.lenskart.oms.service.ShipmentService;
import com.lenskart.oms.strategy.OrderEventStrategyExecutor;
import com.lenskart.oms.strategy.impl.CreateOrderStrategy;
import com.lenskart.oms.utils.ObjectHelper;
import com.lenskart.oms.utils.OmsCommonUtil;
import com.lenskart.order.interceptor.request.*;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.lenskart.oms.constants.ApplicationConstants.FAILED;
import static com.lenskart.oms.constants.ApplicationConstants.SUCCESS;

@Component
@Setter(onMethod__ = {@Autowired})
public class OrderEventsFacade {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    private CreateOrderMapper createOrderMapper;
    private CancelOrderMapper cancelOrderMapper;
    private OrderBackSyncFacade orderBackSyncFacade;
    private OrderEventOmsProducer orderEventOmsProducer;
    private UpdatePowerMapper updatePowerMapper;
    private OrderEventStrategyExecutor orderEventStrategyExecutor;
    private UpdateAddressTransformer updateAddressTransformer;
    private PaymentUpdateMapper paymentUpdateMapper;
    private HoldOrderMapper holdOrderMapper;
    private UnHoldOrderMapper unHoldOrderMapper;
    private ShipmentService shipmentService;
    private OmsOrderEventMapper omsOrderEventMapper;
    private CreateOrderStrategy createOrderStrategy;
    private OrderService orderService;
    private OmsCommonUtil omsCommonUtil;

    public void processInterceptorOrderEvent(OrderEvent orderEvent) throws Exception {
        logger.info("[processInterceptorOrderEvent] orderEvent: {}", orderEvent);
        OmsOrderEvent omsOrderEvent = new OmsOrderEvent();
        switch (OrderEventType.valueOf(orderEvent.getRequestType())) {
            case CREATE_ORDER:
                orderBackSyncFacade.createBackSyncEntryAndPushToOrderOpsAsync(Long.valueOf(orderEvent.getOrderId()), BackSyncEventName.valueOf(orderEvent.getRequestType()), orderEvent);
                CreateOrderRequest createOrderRequest = ObjectHelper.readValue(orderEvent.getRequestData(), CreateOrderRequest.class);
                logger.debug("[processInterceptorOrderEvent] CREATE ORDER event received with payload {}", createOrderRequest);
                omsOrderEvent = createOrderMapper.createOrderRequestToOmsOrderEvent(createOrderRequest, orderEvent.getRequestType(), String.valueOf(orderEvent.getOrderId()), omsOrderEvent);
                break;

            case CANCEL_ORDER:

            case CANCEL_DISTRIBUTOR_ORDER:
                CancelOrderRequest cancelOrderRequest = ObjectHelper.readValue(orderEvent.getRequestData(), CancelOrderRequest.class);
                    omsOrderEvent = cancelOrderMapper.cancelOrderRequestToOmsOrderEvent(cancelOrderRequest, orderEvent.getRequestType(), orderEvent.getOrderId());
                break;

            case UPDATE_PAYMENT:

            case UPDATE_PAYMENT_CAPTURE:
                PaymentUpdateRequest paymentUpdate = ObjectHelper.readValue(orderEvent.getRequestData(), PaymentUpdateRequest.class);
                omsOrderEvent = paymentUpdateMapper.updatePaymentUpdateRequestToOmsOrderEvent(paymentUpdate, orderEvent.getRequestType(), orderEvent.getOrderId());
                break;

            case UPDATE_ADDRESS:
                orderBackSyncFacade.createBackSyncEntryAndPushToOrderOpsAsync(Long.valueOf(orderEvent.getOrderId()), BackSyncEventName.valueOf(orderEvent.getRequestType()), orderEvent);
                UpdateAddressRequest updateAddressRequest = ObjectHelper.readValue(orderEvent.getRequestData(), UpdateAddressRequest.class);
                omsOrderEvent = updateAddressTransformer.updateAddressRequestToOmsOrderEvent(updateAddressRequest, orderEvent.getRequestType(), orderEvent.getOrderId());
                break;

            case UPDATE_POWER:
                orderBackSyncFacade.createBackSyncEntryAndPushToOrderOpsAsync(Long.valueOf(orderEvent.getOrderId()), BackSyncEventName.valueOf(orderEvent.getRequestType()), orderEvent);
                UpdatePowerRequest updatePowerRequest = ObjectHelper.readValue(orderEvent.getRequestData(), UpdatePowerRequest.class);
                omsOrderEvent = updatePowerMapper.updatePowerRequestToOmsOrderEvent(updatePowerRequest, orderEvent.getRequestType(), orderEvent.getOrderId());
                break;

            case HOLD_ORDER:
                HoldOrderRequest holdOrderRequest = ObjectHelper.readValue(orderEvent.getRequestData(), HoldOrderRequest.class);
                omsOrderEvent = holdOrderMapper.map(holdOrderRequest, orderEvent.getRequestType());
                break;

            case UN_HOLD_ORDER:
                UnHoldOrderRequest unHoldOrderRequest = ObjectHelper.readValue(orderEvent.getRequestData(), UnHoldOrderRequest.class);
                omsOrderEvent = unHoldOrderMapper.map(unHoldOrderRequest, orderEvent.getRequestType());
                break;

            case CREATE_DISTRIBUTOR_ORDER:
                omsOrderEvent = createOrderMapper.createDistributorOrderRequestToOmsOrderEvent(orderEvent);
                break;

            default:
                throw new ApplicationException("Unexpected event type: " + OrderEventType.valueOf(orderEvent.getRequestType()), null);
        }

        orderEventStrategyExecutor.doExecute(omsOrderEvent);
    }

    public void processOrderEvent(OmsOrderEvent omsOrderEvent) throws Exception {
        Long incrementId = omsOrderEvent.getIncrementId();
        if(isPureNonWareHouseOrder(incrementId)){
            logger.info("[OrderEventsFacade][processOrderEvent] order {} is pure non warehouse order ", incrementId);
            return;
        }
        orderEventStrategyExecutor.doExecute(omsOrderEvent);
        if (omsOrderEvent.isNeedToPublishWhReady() && OrderEventType.POST_CREATE_ORDER.equals(omsOrderEvent.getEventType())) {
            logger.info("Going to publish PUSH_TO_WH_READY_QUEUE OrderOps event for order {}", omsOrderEvent.getIncrementId());
            orderBackSyncFacade.createBackSyncEntryAndPushToOrderOpsAsync(omsOrderEvent.getIncrementId(), BackSyncEventName.PUSH_TO_WH_READY_QUEUE, null);
        }
    }

    public void publishOrderEvent(OrderEvent orderEvent) throws ApplicationException {
        orderEventOmsProducer.sendMessage(ObjectHelper.writeValue(orderEvent), Long.valueOf(orderEvent.getOrderId()), orderEvent.getRequestType());
    }

    @Transactional(rollbackFor = Exception.class)
    public void syncPendingOrderToProcessKafka(OrderDto orderDto, Map<Long, String> syncPendingOrdersWithStatus) {
        try {
            logger.info("[syncPendingOrderToProcessKafka] going to sync order {} to process Kafka.", orderDto.getIncrementId());
            Set<Long> shipmentIds =  orderDto.getOrderItems().stream().map(OrderItemDto::getShipmentId).collect(Collectors.toSet());
            List<ShipmentDto> shipmentDtoList = shipmentService.search("id.in:" + StringUtils.join(shipmentIds, ","));
            for(ShipmentDto shipmentDto : shipmentDtoList) {
                OmsOrderEvent omsOrderEvent = omsOrderEventMapper.getOmsOrderEventFromShipmentDto(shipmentDto, orderDto, OrderEventType.CREATE_ORDER);
                if (createOrderStrategy.validateAndSyncOrder(omsOrderEvent)) {
                    syncPendingOrdersWithStatus.put(orderDto.getIncrementId(), SUCCESS);
                }
            }
            logger.info("[syncPendingOrderToProcessKafka] order {} sync to process Kafka completed.", orderDto.getIncrementId());
        } catch (Exception exception) {
            syncPendingOrdersWithStatus.put(orderDto.getIncrementId(), FAILED);
            logger.error("[syncPendingOrderToProcessKafka] sync order {} to process Kafka failed.", orderDto.getIncrementId(), exception);
        }
    }

    private boolean isPureNonWareHouseOrder(Long incrementId){
        OrderDto orderDto = orderService.findByIncrementId(incrementId);

        List<OrderItemDto> wareHouseItemList =  orderDto.getOrderItems().stream()
                .filter(o -> !omsCommonUtil.isNonWarehouseProcessingOrder(o))
                .collect(Collectors.toList());

        return  wareHouseItemList.isEmpty();

    }
}
