package com.lenskart.oms.facade;

import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.enums.OrderStatus;
import com.lenskart.oms.enums.ShipmentEvent;
import com.lenskart.oms.enums.ShipmentStatus;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.mapper.WmsOrderEventMapper;
import com.lenskart.oms.model.UwOrder;
import com.lenskart.oms.producer.OrderEventWmsProducerV1;
import com.lenskart.oms.request.OrderReconciliationRequest;
import com.lenskart.oms.request.WmsOrderEvent;
import com.lenskart.oms.service.OrderService;
import com.lenskart.oms.service.ShipmentService;
import com.lenskart.oms.utils.OmsCommonUtil;
import lombok.AccessLevel;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
@Setter(onMethod__ = {@Autowired})
public class ReadyForWhStuckOrderFacade {

    @Value("${oms.readyForWhStuckOrder.start.time}")
    @Setter(AccessLevel.NONE)
    private String omsReadyForWhStuckOrderStartTime;

    @Value("${oms.readyForWhStuckOrder.end.time}")
    @Setter(AccessLevel.NONE)
    private String omsReadyForWhStuckOrderEndTime;

    private OrderService orderService;
    private ShipmentService shipmentService;
    private OrderEventWmsProducerV1 orderEventWmsProducerV1;
    private WmsOrderEventMapper wmsOrderEventMapper;
    private OmsCommonUtil omsCommonUtil;

    public void handleReadyForWhStuckOrder() {
        try {
            Set<OrderStatus> orderStatuses = new HashSet<>();
            orderStatuses.add(OrderStatus.READY_FOR_WH);
            Date startHour = new Date(System.currentTimeMillis() - 1000 * 60 * Long.parseLong(omsReadyForWhStuckOrderStartTime));
            Date endHour = new Date(System.currentTimeMillis() - 1000 * 60 * Long.parseLong(omsReadyForWhStuckOrderEndTime));
            log.info("[handleReadyForWhStuckOrder] scheduler started with start hour {} and end hour {}", startHour, endHour);

            List<OrderDto> orderDtoList = orderService.findByOrderStatusInAndUpdatedAtBetween(orderStatuses, startHour, endHour);
            List<Long> incrementIdList =  orderDtoList.stream().map(OrderDto::getIncrementId).collect(Collectors.toList());
            log.info("[handleReadyForWhStuckOrder]  READY_FOR_WH scheduler {}", incrementIdList);

            for (OrderDto orderDto: orderDtoList) {
                Set<Long> shipmentIds = orderDto.getOrderItems().stream()
                        .filter(orderItemDto -> !omsCommonUtil.isNexsStoreOrder(orderItemDto))
                        .filter(orderItemDto -> orderItemDto.getB2bReferenceItemId() == null)
                        .map(OrderItemDto::getShipmentId)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());
                if(CollectionUtils.isEmpty(shipmentIds)){
                    log.info("[handleReadyForWhStuckOrder] no shipment found for order {}", orderDto.getIncrementId());
                    continue;
                }
                List<ShipmentDto> shipmentDtoList = shipmentService.search("id.in:" + StringUtils.join(shipmentIds, ","));
                for (ShipmentDto shipmentDto : shipmentDtoList) {
                    if(!ShipmentStatus.READY_FOR_WH.equals(shipmentDto.getShipmentStatus())){
                        log.info("[handleReadyForWhStuckOrder] wmsOrderCode {} already Reassigned skippping ", shipmentDto.getWmsOrderCode());
                        continue;
                    }

                    WmsOrderEvent wmsOrderEvent = wmsOrderEventMapper.getWmsOrderEventFromShipmentDto(shipmentDto, orderDto, ShipmentEvent.CREATE_ORDER);
                    log.info("[handleReadyForWhStuckOrder] pushing wmsOrderEvent for shipment {}", shipmentDto.getWmsOrderCode());
                    orderEventWmsProducerV1.sendMessage(wmsOrderEvent);
                }
            }
        } catch (Exception exception) {
            log.error("[ReadyForWhStuckOrderFacade][handleReadyForWhStuckOrder] error: " + exception.getMessage(), exception);
        }
    }

    public void pushWmsOrderEvent(List<UwOrder> uwOrderList) {
        log.info("[pushingOrderEvent] uwOrderList {}", uwOrderList);
        for (UwOrder uwOrder : uwOrderList) {
            try {
                OrderDto orderDto = orderService.findByIncrementId(Long.valueOf(uwOrder.getIncrementId()));
                ShipmentDto shipmentDto = shipmentService.findBySearchTerms("wmsOrderCode.eq:" + uwOrder.getUnicomOrderCode());
                WmsOrderEvent wmsOrderEvent = wmsOrderEventMapper.getWmsOrderEventFromShipmentDto(shipmentDto, orderDto, ShipmentEvent.CREATE_ORDER);
                log.info("pushing order event for uwOrder {} , wmsOrderEvent {}", uwOrder.getUnicomOrderCode(), wmsOrderEvent);
                orderEventWmsProducerV1.sendMessage(wmsOrderEvent);
            } catch (Exception ex) {
                log.error("Exception while pushing event to wms order event kafka {}", uwOrder.getUnicomOrderCode());
            }
        }

    }

    public void  handleReadyForWhStuckOrder(OrderReconciliationRequest orderReconciliationRequest) {
        if (CollectionUtils.isEmpty(orderReconciliationRequest.getIncrementIdList())) {
            throw new ApplicationException("Order List can't be empty");
        }
        List<OrderDto> orderDtoList = new ArrayList<>();
        orderDtoList.addAll(orderService.search("incrementId.in:" + StringUtils.join(orderReconciliationRequest.getIncrementIdList(), ",")));

        for (OrderDto orderDto : orderDtoList) {
            Set<Long> shipmentIds = orderDto.getOrderItems().stream()
                    .filter(orderItemDto -> !omsCommonUtil.isNexsStoreOrder(orderItemDto))
                    .filter(orderItemDto -> orderItemDto.getB2bReferenceItemId() == null)
                    .map(OrderItemDto::getShipmentId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            if(CollectionUtils.isEmpty(shipmentIds)){
                log.info("[handleReadyForWhStuckOrder] no shipment found for order {}", orderDto.getIncrementId());
                continue;
            }
            log.info("[handleReadyForWhStuckOrder] incrementId: {}  shipmentIds: {}", orderDto.getIncrementId(), shipmentIds);
            List<ShipmentDto> shipmentDtoList = shipmentService.search("id.in:" + StringUtils.join(shipmentIds, ","));
            for (ShipmentDto shipmentDto : shipmentDtoList) {
                if (!ShipmentStatus.READY_FOR_WH.equals(shipmentDto.getShipmentStatus())) {
                    log.info("[handleReadyForWhStuckOrder] wmsOrderCode {} already Reassigned skippping ", shipmentDto.getWmsOrderCode());
                    continue;
                }

                WmsOrderEvent wmsOrderEvent = wmsOrderEventMapper.getWmsOrderEventFromShipmentDto(shipmentDto, orderDto, ShipmentEvent.CREATE_ORDER);
                orderEventWmsProducerV1.sendMessage(wmsOrderEvent);
            }
        }
    }

}
