package com.lenskart.oms.facade;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.core.model.Product;
import com.lenskart.fds.dto.DocumentDetailsDto;
import com.lenskart.inventoryadapter.request.GetSearchRequest;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.connector.*;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.dto.*;
import com.lenskart.oms.enums.*;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.producer.DistributorOrderEventOmsProducer;
import com.lenskart.oms.repository.ShipmentRepository;
import com.lenskart.oms.request.distributor.*;
import com.lenskart.oms.request.OrderEvent;
import com.lenskart.oms.response.DoJitCreateOrderResponse;
import com.lenskart.oms.response.DoUploadOrderResponse;
import com.lenskart.oms.response.ShipmentInvoiceResponse;
import com.lenskart.oms.service.DistributorCustomerDetailsService;
import com.lenskart.oms.service.DistributorOrdersService;
import com.lenskart.oms.service.OrderService;
import com.lenskart.oms.utils.ObjectHelper;
import com.lenskart.optima.dto.InventoryDTO;
import com.lenskart.optima.response.AvailableInventoryResponse;
import com.lenskart.order.interceptor.enums.Client;
import com.lenskart.order.interceptor.request.CancelOrderRequest;
import com.opencsv.CSVReader;
import com.opencsv.exceptions.CsvValidationException;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.persistence.Tuple;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.Reader;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Component
public class DistributorOrderFacade {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    @Setter(onMethod__ = {@Autowired})
    private CidConnector cidConnector;

    @Setter(onMethod__ = {@Autowired})
    private JunoConnector junoConnector;

    @Setter(onMethod__ = {@Autowired})
    private DistributorCustomerDetailsService customerDetailsService;

    @Setter(onMethod__ = {@Autowired})
    private DistributorOrdersService distributorOrdersService;

    @Setter(onMethod__ = {@Autowired})
    private OrderEventsFacade orderEventsFacade;

    @Setter(onMethod__ = {@Autowired})
    private DistributorOrderEventOmsProducer distributorOrderEventOmsProducer;

    @Setter(onMethod__ = {@Autowired})
    private OrderBackSyncFacade orderBackSyncFacade;

    @Setter(onMethod__ = {@Autowired})
    private ShipmentRepository shipmentRepository;

    @Setter(onMethod__ = {@Autowired})
    private FdsConnector fdsConnector;

    @Setter(onMethod__ = {@Autowired})
    private OrderService orderService;

    @Value("${so.default.facilities}")
    private Pattern soFacilities;

    @Value("${enable.do.cancel.new.flow:true}")
    private boolean enableDoCancelNewFlow;

    @Setter(onMethod__ = {@Autowired})
    private InventoryAdaptorConnector inventoryAdaptorConnector;

    @Value("${so.max.item.limit:500}")
    private Integer doMaxItemLimit;

    @Value("#{${so.nexsFacilityLegalOwnerMapping}}")
    @Setter(AccessLevel.NONE)
    private Map<String, String> nexsFacilityLegalOwnerMapping;

    @Setter(onMethod__ = {@Autowired})
    private CatalogOpsConnector catalogOpsConnector;

    public DoUploadOrderResponse uploadOrder(MultipartFile file, String poNumber, String userName, String customerCode, String facilityCode, DoType doType) {
        DoUploadOrderResponse doUploadOrderResponse = new DoUploadOrderResponse();
        InventoryAvailabilityCountRR inventoryAvailabilityList = null;
        List<DistributorCsvLineItems> csvList = fetchDetailsFromCSV(file, doUploadOrderResponse);
        validateCsvData(csvList, doUploadOrderResponse);
        if (soFacilities.matcher(facilityCode).matches())
            inventoryAvailabilityList = performInventoryAvailabilityAtUnicom(csvList, facilityCode, userName, doUploadOrderResponse);
        else
            inventoryAvailabilityList = performInventoryAvailabilityAtNexs(csvList, facilityCode, userName, doUploadOrderResponse);
        if (CollectionUtils.isNotEmpty(doUploadOrderResponse.getErrors()))
            return doUploadOrderResponse;
        List<DistributorCustomerDetailsDto> customerDetailsDtoList = customerDetailsService.search("code.eq:" + customerCode);
        persistDoDetails(inventoryAvailabilityList, csvList, customerDetailsDtoList, poNumber, userName, doUploadOrderResponse, facilityCode, doType);
        return doUploadOrderResponse;
    }

    @Transactional(rollbackFor = Exception.class)
    public Long createOrder(String userName, String referenceOrderId) {
        logger.info("[DistributorOrderFacade][createOrder] started {}", referenceOrderId);
        DistributorOrdersDto distributorOrdersDto = distributorOrdersService.findById(Long.valueOf(referenceOrderId));
        if (distributorOrdersDto != null && distributorOrdersDto.getStatus() != null && DoStatus.UPLOADED.equals(distributorOrdersDto.getStatus())) {
            String incrementId = distributorOrdersDto.getIncrementId();
            if (StringUtils.isEmpty(incrementId)) {
                incrementId = junoConnector.fetchLatestOrderCounter(OrderCounterEntity.order);
                distributorOrdersDto.setIncrementId(incrementId);
            }
            if (StringUtils.isEmpty(distributorOrdersDto.getJunoOrderId())) {
                String junoOrderId = junoConnector.fetchLatestOrderCounter(OrderCounterEntity.orderEntityId);
                distributorOrdersDto.setJunoOrderId(junoOrderId);
            }
            distributorOrdersDto.setUpdatedBy(userName);
            distributorOrdersDto.setStatus(DoStatus.CREATED);
            distributorOrdersDto = distributorOrdersService.update(distributorOrdersDto, distributorOrdersDto.getId());
            pushToKafkaForOrderCreation(incrementId);
            return Long.valueOf(distributorOrdersDto.getIncrementId());
        }
        return -1L;
    }

    public Long approveOrder(String userName, String referenceOrderId) throws ApplicationException {
        DistributorOrdersDto distributorOrdersDto = distributorOrdersService.findById(Long.valueOf(referenceOrderId));
        if (distributorOrdersDto != null && distributorOrdersDto.getStatus() != null && DoStatus.CREATED.equals(distributorOrdersDto.getStatus())) {
            String incrementId = distributorOrdersDto.getIncrementId();
            OrderDto orderDto = orderService.findByIncrementId(Long.parseLong(incrementId));
            if (orderDto == null)  {
                throw new ApplicationException("Order has not been created in OMS. please try after some time");
            }
            OrderBackSyncTrackingDto orderBackSyncTrackingDto = populateOrderBackSyncDto(incrementId);
            orderBackSyncFacade.validateAndPushToTrackingQueue(orderBackSyncTrackingDto);
            distributorOrdersDto.setUpdatedBy(userName);
            distributorOrdersDto.setStatus(DoStatus.APPROVED);
            distributorOrdersService.save(distributorOrdersDto);
            return distributorOrdersDto.getId();
        }
        return -1L;
    }

    public Long rejectOrder(String userName, String referenceOrderId, String newStatus, String cancellationReason) throws Exception {
        DistributorOrdersDto distributorOrdersDto = distributorOrdersService.findById(Long.valueOf(referenceOrderId));
        if (distributorOrdersDto != null && distributorOrdersDto.getStatus() != null) {
            if (DoStatus.REJECTED.equals(DoStatus.valueOf(newStatus)) || DoStatus.CANCELLED.equals(DoStatus.valueOf(newStatus))) {
                if (DoStatus.CANCELLED.equals(DoStatus.valueOf(newStatus))) {
                    OrderEvent orderEvent = populateOrderCancellationEvent(distributorOrdersDto, userName);
                    orderEventsFacade.processInterceptorOrderEvent(orderEvent);
                }
                distributorOrdersDto.setUpdatedBy(userName);
                distributorOrdersDto.setCancellationReason(cancellationReason);
                distributorOrdersDto.setStatus(DoStatus.valueOf(newStatus));
                distributorOrdersService.save(distributorOrdersDto);
                return distributorOrdersDto.getId();
            }
        }
        return -1L;
    }

    public List<ShipmentInvoiceResponse> orderDetails(String userName, String incrementId) {
        List<ShipmentInvoiceResponse> results = new ArrayList<>();
        Map<String, ShipmentDto> shipmentDtoMap = new HashMap<>();
        List<Tuple> shipmentDtos = shipmentRepository.findDoShipmentsByIncrementId(incrementId);
        List<ShipmentDto> shipmentDtoList = mapShipmentTuples(shipmentDtos);
        if (CollectionUtils.isNotEmpty(shipmentDtoList)) {
            List<String> shippingPackageIds = new ArrayList<>();
            for (ShipmentDto currentShipmentDto : shipmentDtoList) {
                if (StringUtils.isNotBlank(currentShipmentDto.getWmsShippingPackageId())) {
                    shipmentDtoMap.putIfAbsent(currentShipmentDto.getWmsShippingPackageId(), currentShipmentDto);
                    shippingPackageIds.add(currentShipmentDto.getWmsShippingPackageId());
                }
            }
            if (CollectionUtils.isNotEmpty(shippingPackageIds)) {
                List<DocumentDetailsDto> invoiceList = fdsConnector.fetchInvoices(shippingPackageIds);
                for (String shippingPackageId : shipmentDtoMap.keySet()) {
                    ShipmentInvoiceResponse shipmentInvoiceResponse = new ShipmentInvoiceResponse();
                    shipmentInvoiceResponse.setShippingPackageId(shippingPackageId);
                    if (CollectionUtils.isNotEmpty(invoiceList)) {
                        Map<String, List<DocumentDetailsDto>> invoiceGroupByShipmentMapping = invoiceList.stream().collect(Collectors.groupingBy(DocumentDetailsDto::getDocumentSourceRefId));
                        shipmentInvoiceResponse.setTotalPrice(invoiceGroupByShipmentMapping.get(shippingPackageId).get(0).getTotalPrice());
                        shipmentInvoiceResponse.setInvoiceNumber(invoiceGroupByShipmentMapping.get(shippingPackageId).get(0).getDocumentNo());
                        shipmentInvoiceResponse.setDocumentLink(invoiceGroupByShipmentMapping.get(shippingPackageId).get(0).getDocumentLink());
                        shipmentInvoiceResponse.setAwbNumber(shipmentDtoMap.get(shippingPackageId).getAwbNumber());
                        shipmentInvoiceResponse.setCourierCode(shipmentDtoMap.get(shippingPackageId).getCourierCode());
                    }
                    results.add(shipmentInvoiceResponse);
                }
            }
        }
        return results;
    }

    private List<ShipmentDto> mapShipmentTuples(List<Tuple> shipmentDtos) {
        List<ShipmentDto> shipmentDtoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(shipmentDtos)) {
            for (Tuple currentTuple : shipmentDtos) {
                ShipmentDto newShipmentDto = new ShipmentDto();
                newShipmentDto.setManifestNumber(currentTuple.get("manifest_number", String.class));
                newShipmentDto.setCourierCode(currentTuple.get("courier_code", String.class));
                newShipmentDto.setWmsShippingPackageId(currentTuple.get("wms_shipping_package_id", String.class));
                newShipmentDto.setFacility(currentTuple.get("facility", String.class));
                newShipmentDto.setAwbNumber(currentTuple.get("awb_number", String.class));
                newShipmentDto.setInvoiceNumber(currentTuple.get("invoice_number", String.class));
                newShipmentDto.setWmsOrderCode(currentTuple.get("wms_order_code", String.class));
                shipmentDtoList.add(newShipmentDto);
            }
        }
        return shipmentDtoList;
    }

    private OrderEvent populateOrderCancellationEvent(DistributorOrdersDto distributorOrdersDto, String userName) throws JsonProcessingException {
        OrderEvent cancellationOrderEvent = new OrderEvent();
        cancellationOrderEvent.setOrderId(distributorOrdersDto.getIncrementId());
        cancellationOrderEvent.setIsOmsOrder(Boolean.TRUE);
        cancellationOrderEvent.setLkCountry("IN");
        cancellationOrderEvent.setNavChannel("BULK_TO_VENDOR");
        cancellationOrderEvent.setRequestType(enableDoCancelNewFlow ? OrderEventType.CANCEL_DISTRIBUTOR_ORDER.name() : OrderEventType.CANCEL_ORDER.name());
        cancellationOrderEvent.setRequestedAt(new Date());
        cancellationOrderEvent.setCancellationSubType("FULL_CANCELLATION");
        cancellationOrderEvent.setClientId(Client.OS.getUsername());
        cancellationOrderEvent.setRequestData(new ObjectMapper().writeValueAsString(createCancelOrderRequest(userName)));
        return cancellationOrderEvent;
    }

    private CancelOrderRequest createCancelOrderRequest(String updatedBy) {
        CancelOrderRequest cancelOrderRequest = new CancelOrderRequest();
        cancelOrderRequest.setPaymentMethod("prepaid");
        cancelOrderRequest.setReasonDetail("Cancelled by Finance user");
        cancelOrderRequest.setSource("nexs-ui");
        cancelOrderRequest.setInitiatedBy(updatedBy);
        cancelOrderRequest.setCancellationType("FULL_CANCELLATION");
        cancelOrderRequest.setWalletRefundEligible(false);
        cancelOrderRequest.setCancelRefund(false);
        return cancelOrderRequest;
    }

    private OrderBackSyncTrackingDto populateOrderBackSyncDto(String incrementId) {
        OrderBackSyncTrackingDto orderBackSyncTrackingDto = new OrderBackSyncTrackingDto();
        orderBackSyncTrackingDto.setEventName(BackSyncEventName.ORDER_OPS_WH_SYNC_READY);
        orderBackSyncTrackingDto.setEntityType(BackSyncEntityType.INCREMENT_ID);
        orderBackSyncTrackingDto.setEntityId(incrementId);
        orderBackSyncTrackingDto.setEventStatus(BackSyncEventStatus.SUCCESS);
        orderBackSyncTrackingDto.setBackSyncSystem(BackSyncSystem.OMS);
        orderBackSyncTrackingDto.setMessage("Order approved by Finance team");
        return orderBackSyncTrackingDto;
    }

    private void persistDoDetails(InventoryAvailabilityCountRR inventoryAvailabilityList, List<DistributorCsvLineItems> csvList, List<DistributorCustomerDetailsDto> customerDetailsDtoList, String poNumber, String userName, DoUploadOrderResponse doUploadOrderResponse, String facilityCode, DoType doType) {
        if (CollectionUtils.isNotEmpty(inventoryAvailabilityList.getItems()) && CollectionUtils.isNotEmpty(customerDetailsDtoList)) {
            DistributorOrdersDto distributorOrdersDto = new DistributorOrdersDto();
            distributorOrdersDto.setCustomer(customerDetailsDtoList.get(0));
            distributorOrdersDto.setPoNumber(poNumber);
            distributorOrdersDto.setFacility(facilityCode);
            distributorOrdersDto.setCreatedBy(userName);
            distributorOrdersDto.setUpdatedBy(userName);
            distributorOrdersDto.setStatus(DoStatus.UPLOADED);
            distributorOrdersDto.setDoType(doType);

            List<DistributorOrderItemsDto> distributorOrderItemsDtoList = new ArrayList<>();
            Map<Integer, List<DistributorCsvLineItems>> incomingPidMap = csvList.stream().collect(Collectors.groupingBy(DistributorCsvLineItems::getProductId));

            for (InventoryItemCountRR lineItem : inventoryAvailabilityList.getItems()) {
                Integer currentPid = lineItem.getProductId();
                distributorOrderItemsDtoList.add(getDoItem(currentPid, incomingPidMap, userName));
            }

            DistributorCustomerDetailsDto customerDetailsDto = new DistributorCustomerDetailsDto();
            customerDetailsDto.setCode(customerDetailsDtoList.get(0).getCode());
            customerDetailsDto.setName(customerDetailsDtoList.get(0).getName());
            doUploadOrderResponse.setCustomer(customerDetailsDto);
            doUploadOrderResponse.setFacilityCode(facilityCode);
            doUploadOrderResponse.setDoTotalPrice(csvList.stream().mapToDouble((item) -> (item.getQuantity() * item.getPrice())).sum());
            doUploadOrderResponse.setPidListing(csvList);

            distributorOrdersDto.setOrderItems(distributorOrderItemsDtoList);
            distributorOrdersDto = distributorOrdersService.save(distributorOrdersDto);
            doUploadOrderResponse.setReferenceOrderId(distributorOrdersDto.getId());
        }
    }

    private DistributorOrderItemsDto getDoItem(Integer currentPid, Map<Integer, List<DistributorCsvLineItems>> incomingPidMap, String userName) {
        DistributorOrderItemsDto newItem = new DistributorOrderItemsDto();
        newItem.setProductId(currentPid);
        newItem.setProductName(incomingPidMap.get(currentPid).get(0).getProductName());
        newItem.setProductType(incomingPidMap.get(currentPid).get(0).getProductType());
        newItem.setPrice(incomingPidMap.get(currentPid).get(0).getPrice());
        newItem.setQuantity(incomingPidMap.get(currentPid).get(0).getQuantity());
        newItem.setCreatedBy(userName);
        newItem.setUpdatedBy(userName);
        return newItem;
    }

    private InventoryAvailabilityCountRR performInventoryAvailabilityAtUnicom(List<DistributorCsvLineItems> csvList, String facilityCode, String userName, DoUploadOrderResponse doUploadOrderResponse) {
        List<InventoryItemCountRR> items = new ArrayList<>();
        InventoryAvailabilityCountRR inventoryAvailabilityRequest = new InventoryAvailabilityCountRR();
        inventoryAvailabilityRequest.setFacility(facilityCode);
        inventoryAvailabilityRequest.setRequestedBy(userName);
        for (DistributorCsvLineItems item : csvList)
            items.add(new InventoryItemCountRR(item.getProductId(), item.getQuantity()));
        inventoryAvailabilityRequest.setItems(items);
        InventoryAvailabilityCountRR inventoryAvailabilityResponse = triggerInventoryAvailabilityCountAtUnicom(inventoryAvailabilityRequest);
        if (CollectionUtils.isNotEmpty(inventoryAvailabilityResponse.getItems())) {
            for (InventoryItemCountRR item : inventoryAvailabilityResponse.getItems()) {
                if (item.getUnFulfillableQty() > 0)
                    doUploadOrderResponse.getErrors().add(getLineError(null, item.getProductId(), item.getUnFulfillableQty() + ApplicationConstants.CSV_ERRORS.UNFULFILLABLE_PRODUCT));
                itemTypeValidation(item, doUploadOrderResponse);
            }
        }
        return inventoryAvailabilityResponse;
    }

    private InventoryAvailabilityCountRR triggerInventoryAvailabilityCountAtUnicom(InventoryAvailabilityCountRR inventoryAvailabilityRequest) {
        if (inventoryAvailabilityRequest != null && CollectionUtils.isNotEmpty(inventoryAvailabilityRequest.getItems())) {
            for (InventoryItemCountRR item : inventoryAvailabilityRequest.getItems()) {
                GetSearchRequest getSearchRequest = new GetSearchRequest();
                getSearchRequest.setFacilityCode(inventoryAvailabilityRequest.getFacility());
                getSearchRequest.setSkuId(String.valueOf(item.getProductId()));
                AvailableInventoryResponse currentPidInventory = inventoryAdaptorConnector.triggerInventoryAvailabilityCount(getSearchRequest);
                Long pidUnicomQty = 0L;
                if (currentPidInventory != null && currentPidInventory.getResult() != null && CollectionUtils.isNotEmpty(currentPidInventory.getResult().getInventoryDTOs())) {
                    Optional<Integer> pidCountOptional = currentPidInventory.getResult().getInventoryDTOs().stream().filter(x -> "GOOD_INVENTORY".equalsIgnoreCase(x.getType())).map(InventoryDTO::getQuantity).findFirst();
                    if (pidCountOptional.isPresent())
                        pidUnicomQty = pidCountOptional.get().longValue();
                }
                item.setTotalAvailableQty(pidUnicomQty);
                item.setFulfillableQty(item.getRequestedQty() >= pidUnicomQty ? pidUnicomQty : item.getRequestedQty());
                item.setUnFulfillableQty(item.getRequestedQty() >= pidUnicomQty ? item.getRequestedQty() - pidUnicomQty : 0);
            }
        }
        logger.info("triggerInventoryAvailabilityCountAtUnicom - inventoryAvailabilityRequest : {}", inventoryAvailabilityRequest);
        return inventoryAvailabilityRequest;
    }

    private InventoryAvailabilityCountRR performInventoryAvailabilityAtNexs(List<DistributorCsvLineItems> csvList, String facilityCode, String userName, DoUploadOrderResponse doUploadOrderResponse) {
        List<InventoryItemCountRR> items = new ArrayList<>();
        InventoryAvailabilityCountRR inventoryAvailabilityRequest = new InventoryAvailabilityCountRR();
        inventoryAvailabilityRequest.setFacility(facilityCode);
        inventoryAvailabilityRequest.setRequestedBy(userName);
        inventoryAvailabilityRequest.setLegalOwner(nexsFacilityLegalOwnerMapping.getOrDefault(facilityCode, "LKIN"));
        for (DistributorCsvLineItems item : csvList)
            items.add(new InventoryItemCountRR(item.getProductId(), item.getQuantity()));
        inventoryAvailabilityRequest.setItems(items);
        InventoryAvailabilityCountRR inventoryAvailabilityResponse = cidConnector.triggerInventoryAvailabilityCount(inventoryAvailabilityRequest);
        if (inventoryAvailabilityResponse != null && CollectionUtils.isNotEmpty(inventoryAvailabilityResponse.getItems())) {
            for (InventoryItemCountRR item : inventoryAvailabilityResponse.getItems()) {
                if (item.getUnFulfillableQty() > 0)
                    doUploadOrderResponse.getErrors().add(getLineError(null, item.getProductId(), item.getUnFulfillableQty() + ApplicationConstants.CSV_ERRORS.UNFULFILLABLE_PRODUCT));
                itemTypeValidation(item, doUploadOrderResponse);
            }
        }
        return inventoryAvailabilityResponse;
    }

    private void itemTypeValidation(InventoryItemCountRR item, DoUploadOrderResponse doUploadOrderResponse){
        try {
            Product product = catalogOpsConnector.findProductDetailsByProductId(Long.valueOf(item.getProductId()));
            ItemTypeMapping productItemType = ItemTypeMapping.getItemTypeFromName(product.getHsnClassification());
            if(productItemType == null) throw new IllegalArgumentException(ApplicationConstants.CSV_ERRORS.UNSUPPORTED_ITEM_TYPE);
            ItemType itemType = ItemType.valueOf(productItemType.name());
        } catch (IllegalArgumentException e) {
            logger.error("itemTypeValidation - PID : " + item.getProductId() + ", Error : " + e.getMessage(), e);
            doUploadOrderResponse.getErrors().add(getLineError(null, item.getProductId(), ApplicationConstants.CSV_ERRORS.UNSUPPORTED_ITEM_TYPE));
        } catch (Exception e) {
            logger.error("itemTypeValidation - PID : " + item.getProductId() + ", Error : " + e.getMessage(), e);
            doUploadOrderResponse.getErrors().add(getLineError(null, item.getProductId(), ApplicationConstants.CSV_ERRORS.INTERNAL_ERROR));
        }
    }

    private void validateCsvData(List<DistributorCsvLineItems> csvList, DoUploadOrderResponse doUploadOrderResponse) {
        Set<Integer> productIds = new HashSet<>();
        Integer totalItemCount = 0, i = 0;
        for (i = 0; i < csvList.size(); i++) {
            if (!productIds.add(csvList.get(i).getProductId()))
                doUploadOrderResponse.getErrors().add(getLineError(i + 1, csvList.get(i).getProductId(), ApplicationConstants.CSV_ERRORS.DUPLICATE_PRODUCT));
            if (csvList.get(i).getQuantity() <= 0)
                doUploadOrderResponse.getErrors().add(getLineError(i + 1, csvList.get(i).getProductId(), ApplicationConstants.CSV_ERRORS.NEGATIVE_QTY));
            if (csvList.get(i).getPrice() < 0)
                doUploadOrderResponse.getErrors().add(getLineError(i + 1, csvList.get(i).getProductId(), ApplicationConstants.CSV_ERRORS.NEGATIVE_PRICE));
            if (csvList.get(i).getQuantity() > doMaxItemLimit)
                doUploadOrderResponse.getErrors().add(getLineError(i + 1, csvList.get(i).getProductId(), ApplicationConstants.CSV_ERRORS.MAX_QTY_EXCEED + doMaxItemLimit + "."));
            totalItemCount += csvList.get(i).getQuantity();
        }
        if (totalItemCount > doMaxItemLimit && csvList.size() > 1)
            doUploadOrderResponse.getErrors().add(getLineError(i + 1, null, ApplicationConstants.CSV_ERRORS.MAX_QTY_EXCEED + doMaxItemLimit + "."));

    }

    private List<DistributorCsvLineItems> fetchDetailsFromCSV(MultipartFile file, DoUploadOrderResponse doUploadOrderResponse) {
        List<DistributorCsvLineItems> list = new ArrayList<>();
        try (Reader reader = new BufferedReader((new InputStreamReader(file.getInputStream())))) {
            Integer lineNo = 1;
            try (CSVReader csvReader = new CSVReader(reader)) {
                String[] line;
                while ((line = csvReader.readNext()) != null) {
                    if (lineNo != 1)
                        list.add(new DistributorCsvLineItems(Integer.valueOf(line[0]), line[1], line[2], Integer.valueOf(line[3]), Double.valueOf(line[4])));
                    lineNo++;
                }
            } catch (CsvValidationException | IOException e2) {
                doUploadOrderResponse.getErrors().add(getLineError(lineNo, null, e2.getMessage()));
            }
        } catch (IOException e1) {
            doUploadOrderResponse.getErrors().add(getLineError(null, null, "File Read Error - " + e1.getMessage()));
        }
        return list;
    }

    @Transactional
    public void pushToKafkaForOrderCreation(String incrementId) {
        try {
            OrderEvent orderEvent = createOrderEvent(incrementId);
            distributorOrderEventOmsProducer.sendMessage(ObjectHelper.getObjectMapper().writeValueAsString(orderEvent), incrementId, orderEvent.getRequestType());
        } catch (Exception e) {
            logger.error("pushToKafkaForOrderCreation error incrementID {} error msg {}", incrementId, e.getMessage(), e);
        }
    }

    private OrderEvent createOrderEvent(String incrementId) {
        OrderEvent orderEvent = new OrderEvent();
        orderEvent.setRequestType(OrderEventType.CREATE_DISTRIBUTOR_ORDER.name());
        orderEvent.setClientId("DistributorOrder");
        orderEvent.setRequestedAt(new Date());
        orderEvent.setOrderId(incrementId);
        return orderEvent;
    }

    private CsvErrors getLineError(Integer lineNo, Integer productId, String error) {
        return new CsvErrors(lineNo, productId, error);
    }

    @Transactional(rollbackFor = Exception.class)
    public DoJitCreateOrderResponse createJitDitributorOrder(DistributorOrderJITRequest distributorOrderJITRequest) {
        List<DistributorCustomerDetailsDto> customerDetailsDtoList = customerDetailsService.search("code.eq:" + distributorOrderJITRequest.getCustomerCode());
        Long doRefId = persistDoJitDetails(distributorOrderJITRequest.getLineItemsList(), customerDetailsDtoList,  distributorOrderJITRequest.getPoNumber(), distributorOrderJITRequest.getPoNumber(), distributorOrderJITRequest.getFacilityCode());
        if(doRefId == null || doRefId == -1) {
            return DoJitCreateOrderResponse.builder().isSuccess(false).incrementId(null).build();
        }
        Long incrementId = createOrder(distributorOrderJITRequest.getUserName(), String.valueOf(doRefId));

        return DoJitCreateOrderResponse.builder().isSuccess(true).incrementId(incrementId).error(null).build();
    }

    private Long persistDoJitDetails(List<DistributorJitLineItems> csvList, List<DistributorCustomerDetailsDto> customerDetailsDtoList, String poNumber, String userName, String facilityCode) {
        DistributorOrdersDto distributorOrdersDto = new DistributorOrdersDto();
        distributorOrdersDto.setCustomer(customerDetailsDtoList.get(0));
        distributorOrdersDto.setPoNumber(poNumber);
        distributorOrdersDto.setFacility(facilityCode);
        distributorOrdersDto.setCreatedBy(userName);
        distributorOrdersDto.setUpdatedBy(userName);
        distributorOrdersDto.setStatus(DoStatus.UPLOADED);
        distributorOrdersDto.setDoType(DoType.JIT);
        List<DistributorOrderItemsDto> distributorOrderItemsDtoList = new ArrayList<>();
        for(DistributorJitLineItems distributorJitLineItems :csvList){
            distributorOrderItemsDtoList.add(getDoJitItem(distributorJitLineItems.getProductId(), distributorJitLineItems, userName));
        }
        DistributorCustomerDetailsDto customerDetailsDto = new DistributorCustomerDetailsDto();
        customerDetailsDto.setCode(customerDetailsDtoList.get(0).getCode());
        customerDetailsDto.setName(customerDetailsDtoList.get(0).getName());
        distributorOrdersDto.setOrderItems(distributorOrderItemsDtoList);
        distributorOrdersDto = distributorOrdersService.save(distributorOrdersDto);
        return distributorOrdersDto.getId();
    }

    private DistributorOrderItemsDto getDoJitItem(Integer currentPid, DistributorJitLineItems incomingPidMap, String userName) {
        DistributorOrderItemsDto newItem = new DistributorOrderItemsDto();
        newItem.setProductId(currentPid);
        newItem.setProductName(String.valueOf(incomingPidMap.getFittingId()));
        newItem.setProductType(incomingPidMap.getProductType());
        newItem.setPrice(incomingPidMap.getPrice());
        newItem.setQuantity(incomingPidMap.getQuantity());
        newItem.setCreatedBy(userName);
        newItem.setUpdatedBy(userName);
        return newItem;
    }

    public void triggerUnsyncedApprovedDO() {
        List<Long> doIds = distributorOrdersService.findAllUnsyncedApprovedDO();
        if(CollectionUtils.isNotEmpty(doIds)){
            for(Long refId : doIds)
                approveOrder("Sensei Scheduler", refId.toString());
        }
    }
}
