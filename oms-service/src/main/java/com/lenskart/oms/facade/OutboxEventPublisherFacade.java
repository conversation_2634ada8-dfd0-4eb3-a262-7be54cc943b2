package com.lenskart.oms.facade;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.oms.dto.OutboxEventDto;
import com.lenskart.oms.enums.OutboxEventStatus;
import com.lenskart.oms.service.OutboxEventService;
import lombok.AccessLevel;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Calendar;
import java.util.Date;
import java.util.Map;

/**
 * Service responsible for publishing outbox events to Kafka
 * Implements the outbox pattern for reliable event publishing
 */
@Slf4j
@Component
@Setter(onMethod__ = {@Autowired})
public class OutboxEventPublisherFacade {

    @Value("${outbox.publisher.retry.delay.minutes:5}")
    @Setter(AccessLevel.NONE)
    private int retryDelayMinutes;
    
    private OutboxEventService outboxEventService;
    private KafkaTemplate<String, String> kafkaTemplate;
    private ObjectMapper objectMapper;

    /**
     * Process individual outbox event
     */
    @Async
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void processEvent(OutboxEventDto event) {
        try {
            log.debug("[processEvent] Processing event: {} for aggregate: {}", 
                        event.getId(), event.getAggregateId());

            // Mark as processing
            updateEventStatus(event.getId(), OutboxEventStatus.PROCESSING);

            // Publish to Kafka
            publishToKafka(event);

            // Mark as processed
            outboxEventService.markAsProcessed(event.getId());
            
            log.info("[processEvent] Successfully processed event: {} for aggregate: {}", 
                       event.getId(), event.getAggregateId());

        } catch (Exception e) {
            log.error("[processEvent] Failed to process event: {} for aggregate: {}", 
                        event.getId(), event.getAggregateId(), e);
            handleEventFailure(event, e);
        }
    }

    /**
     * Publish event to Kafka topic
     */
    private void publishToKafka(OutboxEventDto event) throws Exception {
        ProducerRecord<String, String> producerRecord = new ProducerRecord<>(
            event.getTopicName(),
            event.getPartitionKey(),
            event.getEventPayload()
        );

        // Add headers if present
        if (StringUtils.hasText(event.getEventHeaders())) {
            Map<String, String> headers = objectMapper.readValue(
                event.getEventHeaders(), 
                new TypeReference<Map<String, String>>() {}
            );
            
            for (Map.Entry<String, String> header : headers.entrySet()) {
                producerRecord.headers().add(header.getKey(), header.getValue().getBytes());
            }
        }

        // Send message synchronously to ensure delivery
        kafkaTemplate.send(producerRecord).get();
        
        log.debug("[publishToKafka] Published event {} to topic: {}", 
                    event.getId(), event.getTopicName());
    }

    /**
     * Handle event processing failure
     */
    private void handleEventFailure(OutboxEventDto event, Exception error) {
        try {
            String errorMessage = error.getMessage();
            if (errorMessage == null) {
                errorMessage = error.getClass().getSimpleName();
            }

            if (event.canRetry()) {
                // Schedule retry with exponential backoff
                Calendar calendar = Calendar.getInstance();
                int delayMinutes = retryDelayMinutes * (event.getRetryCount() + 1);
                calendar.add(Calendar.MINUTE, delayMinutes);
                Date nextRetryTime = calendar.getTime();
                
                outboxEventService.incrementRetryCount(event.getId(), nextRetryTime);
                
                log.warn("[handleEventFailure] Scheduled retry for event: {} at: {}", 
                           event.getId(), nextRetryTime);
            } else {
                // Max retries exceeded, move to dead letter
                outboxEventService.markAsDeadLetter(event.getId(), errorMessage);
                
                log.error("[handleEventFailure] Event {} moved to dead letter after {} retries: {}", 
                            event.getId(), event.getRetryCount(), errorMessage);
            }
            
        } catch (Exception e) {
            log.error("[handleEventFailure] Error handling event failure for event: {}", 
                        event.getId(), e);
        }
    }

    /**
     * Update event status safely
     */
    private void updateEventStatus(Long eventId, OutboxEventStatus status) {
        try {
            if (status == OutboxEventStatus.PROCESSING) {
                // For processing status, we just update the status without using the service method
                // to avoid circular dependencies
                log.debug("[updateEventStatus] Marking event {} as processing", eventId);
            }
        } catch (Exception e) {
            log.error("[updateEventStatus] Error updating event status for event: {}", eventId, e);
        }
    }

}
