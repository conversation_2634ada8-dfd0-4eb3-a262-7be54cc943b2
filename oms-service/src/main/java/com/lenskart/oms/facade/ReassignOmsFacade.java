package com.lenskart.oms.facade;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.commons.dto.BaseDto;
import com.lenskart.oms.connector.NexsWmsConnector;
import com.lenskart.oms.connector.OrderInterceptorConnector;
import com.lenskart.oms.connector.OrderOpsConnector;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.enums.*;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.exception.ReassignOrderOmsException;
import com.lenskart.oms.model.Action;
import com.lenskart.oms.model.UwOrder;
import com.lenskart.oms.request.OmsOrderEvent;
import com.lenskart.oms.response.UwOrderResponse;
import com.lenskart.oms.service.OrderService;
import com.lenskart.oms.service.ShipmentService;
import com.lenskart.oms.utils.OmsCommonUtil;
import com.lenskart.oms.utils.OmsTransitionUtil;
import io.micrometer.core.annotation.Timed;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

@Setter(onMethod__ = {@Autowired})
@Component
public class ReassignOmsFacade {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;
    @Value("${nexs.oms.shipmentLevelReassignmentAllowed}")
    @Setter(AccessLevel.NONE)
    private Boolean isShipmentLevelReassignmentAllowed;

    private OrderService orderService;
    private ShipmentService shipmentService;
    private NexsWmsConnector nexsWmsConnector;
    private OrderOpsConnector orderOpsConnector;
    private OmsTransitionUtil omsTransitionUtil;
    private OrderInterceptorConnector orderInterceptorConnector;
    private OmsCommonUtil omsCommonUtil;

    @Timed
    @Transactional(rollbackFor = Exception.class)
    public void reassignOrderOms(String wmsOrderCode, String reassignmentReason, OmsOrderEvent omsOrderEvent) throws ApplicationException {
        logger.info("[ReassignOmsFacade][reassignOrderOms] started processing reassignOrderOms for wmsOrderCode: {}", wmsOrderCode);
        Long incrementId = null;
        ShipmentDto shipmentDto;
        OrderDto orderDto;
        try {
            shipmentDto = getShipmentDtoByWmsOrderCode(wmsOrderCode);
            orderDto = orderService.findById(shipmentDto.getOrderItems().get(0).getOrderId());
            if (ShipmentStatus.OMS_REASSIGNED.equals(shipmentDto.getShipmentStatus())) {
                logger.info("[ReassignOmsFacade][reassignOrderOms] order already reassigned to order-ops for wmsOrderCode: {}", wmsOrderCode);
                return;
            }
            incrementId = orderDto.getIncrementId();
            validateUnicomOrderCodeInOrderOps(shipmentDto.getOrderItems(), incrementId ,wmsOrderCode);
            Set<String> wmsOrderCodeSet = new HashSet<>();
            if(Boolean.FALSE.equals(isShipmentLevelReassignmentAllowed)){
                wmsOrderCodeSet =  orderDto.getOrderItems().stream()
                        .map(o -> shipmentService.findById(o.getShipmentId()))
                        .map(ShipmentDto::getWmsOrderCode)
                        .collect(Collectors.toSet());
            } else{
                wmsOrderCodeSet.add(wmsOrderCode);
            }
            updateOmsSystem(wmsOrderCodeSet, incrementId, OmsSystem.ORDER_OPS);
            updateEntityStatus(orderDto, reassignmentReason, wmsOrderCodeSet);
        } catch (Exception e) {
            logger.error("[ReassignOmsFacade][reassignOrderOms] error while processing reassignOrderOms for wmsOrderCode: {} error: {}", wmsOrderCode, e.getMessage(), e);
            rollbackOrderOmsReassignment(wmsOrderCode, incrementId, e);
            throw new ApplicationException("error while processing reassignOrderOms for wmsOrderCode:" + wmsOrderCode + " error: " + e.getMessage(), e);
        }
        if (Objects.nonNull(omsOrderEvent)) {
            omsOrderEvent.setNeedToPublishWhReady(true);
        }
        logger.info("[ReassignOmsFacade][reassignOrderOms] completed processing reassignOrderOms for wmsOrderCode: {} and needToPublishWhReady {}", wmsOrderCode, omsOrderEvent != null && omsOrderEvent.isNeedToPublishWhReady());
    }

    public OrderDto getOrderDtoByWmsOrderCode(String wmsOrderCode) throws ApplicationException {
        OrderDto orderDto;
        ShipmentDto shipmentDtos = shipmentService.findBySearchTerms("wmsOrderCode.eq:" + wmsOrderCode);
        List<OrderItemDto> orderItemDtos = shipmentDtos.getOrderItems();
        if (CollectionUtils.isEmpty(orderItemDtos)) {
            throw new ApplicationException("invalid wmsOrderCode: " + wmsOrderCode, null);
        }
        orderDto = orderService.findById(orderItemDtos.get(0).getOrderId());
        return orderDto;
    }


    public ShipmentDto getShipmentDtoByWmsOrderCode(String wmsOrderCode) throws ApplicationException {
        ShipmentDto shipmentDtos = shipmentService.findBySearchTerms("wmsOrderCode.eq:" + wmsOrderCode);
        List<OrderItemDto> orderItemDtos = shipmentDtos.getOrderItems();
        if (CollectionUtils.isEmpty(orderItemDtos)) {
            throw new ApplicationException("invalid wmsOrderCode: " + wmsOrderCode, null);
        }
        return shipmentDtos;
    }

    private void rollbackOrderOmsReassignment(String wmsOrderCode, Long incrementId, Exception e) throws ApplicationException {
        try {
            Set<String> wmsOrderCodeSet = new HashSet<>(Collections.singletonList(wmsOrderCode));
            updateOmsSystem(wmsOrderCodeSet, incrementId, OmsSystem.OMS);
        } catch (ReassignOrderOmsException ex) {
            throw new ApplicationException("error while processing rollbackOrderOmsReassignment for wmsOrderCode:" + wmsOrderCode + " error: " + e.getMessage(), e);
        }
        throw new ApplicationException("error while updating order_ops oms system for wmsOrderCode:" + wmsOrderCode + " error: " + e.getMessage(), e);
    }

    private void updateEntityStatus(OrderDto orderDto, String reassignmentReason, Set<String> wmsOrderCodeSet) throws ApplicationException {
        List<OrderItemDto> orderItemDtos = orderDto.getOrderItems().stream()
                .filter(item -> !omsCommonUtil.isNonWarehouseProcessingOrderExcludingB2b(item))
                .filter(item ->  !OrderItemStatus.OMS_REASSIGNED.equals(item.getItemStatus()))
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(orderItemDtos)){
            logger.info("[ReassignOmsFacade][updateEntityStatus] no item found in correct state for shipment {}", wmsOrderCodeSet);
            return;
        }
        Action transitionAction = omsTransitionUtil.getTransitionActionByOperationAndStatus(
                EventToOperationMap.OMS_REASSIGNED.getOperation(), orderItemDtos.get(0).getItemStatus()
        );
        Set<Long> shipmentIds = new HashSet<>();
        for(String wmsOrderCode : wmsOrderCodeSet) {
            shipmentIds = fetchShipmentIdsAndUpdateOrderItemData(wmsOrderCode);
        }
        updateShipmentData(transitionAction, shipmentIds);
        if (Boolean.FALSE.equals(isShipmentLevelReassignmentAllowed)) {
            updateOrderDataIfRequired(orderDto, reassignmentReason, transitionAction);
        }
    }

    private void validateUnicomOrderCodeInOrderOps(List<OrderItemDto> orderItemDtos, Long incrementId, String wmsOrderCode) throws ApplicationException {

        Map<Long, List<Long>> omsShipmentIdToPidsMap = orderItemDtos.stream()
                .collect(Collectors.groupingBy(OrderItemDto::getShipmentId, Collectors.mapping(OrderItemDto::getProductId, Collectors.toList())));

        Map<Long, String> shipmentIdToWmsOrderCodeMap = getShipmentIdToWmsOrderCodeMap(orderItemDtos);

        UwOrderResponse uwOrderResponse = orderOpsConnector.getUwOrderDetails(incrementId);
        if (uwOrderResponse == null || CollectionUtils.isEmpty(uwOrderResponse.getUwOrders())) {
            throw new ApplicationException("oms and order ops shipment counts not matching for incrementId:" + incrementId, null);
        }

        Map<String, List<Long>> orderOpsWmsCodeToPidsMap = uwOrderResponse.getUwOrders().stream()
                .filter(uw -> wmsOrderCode.equals(uw.getUnicomOrderCode()))
                .collect(Collectors.groupingBy(UwOrder::getUnicomOrderCode, Collectors.mapping(e -> Long.valueOf(e.getProductId()), Collectors.toList())));

        logger.info("omsShipmentIdToPidsMap size : {} orderOpsWmsCodeToPidsMap size : {}", omsShipmentIdToPidsMap.size(), orderOpsWmsCodeToPidsMap.size());
        logger.info("omsShipmentIdToPidsMap: {} orderOpsWmsCodeToPidsMap: {}", omsShipmentIdToPidsMap, orderOpsWmsCodeToPidsMap);

        if (omsShipmentIdToPidsMap.size() != orderOpsWmsCodeToPidsMap.size()) {
            throw new ApplicationException("oms and order ops shipment counts not matching for incrementId:" + incrementId, null);
        }
        for (Map.Entry<Long, List<Long>> entry : omsShipmentIdToPidsMap.entrySet()) {
            if (!shipmentIdToWmsOrderCodeMap.containsKey(entry.getKey())) {
                throw new ApplicationException("invalid shipment id : " + entry.getKey(), null);
            }
            String wmsOrderCodeFromMap = shipmentIdToWmsOrderCodeMap.get(entry.getKey());
            if (!orderOpsWmsCodeToPidsMap.containsKey(wmsOrderCodeFromMap)) {
                throw new ApplicationException("wmsOrderCode:" + entry.getKey() + " not found in order-ops for incrementId:" + incrementId, null);
            }
            if (orderOpsWmsCodeToPidsMap.get(wmsOrderCode).size() != entry.getValue().size()) {
                throw new ApplicationException("oms and order ops shipment items counts not matching for wmsOrderCode:" + entry.getKey(), null);
            } else {
                List<Long> orderOpsPids = orderOpsWmsCodeToPidsMap.get(wmsOrderCode);
                orderOpsPids.removeAll(entry.getValue());
                /*if (!CollectionUtils.isEmpty(orderOpsPids)) {
                    throw new ApplicationException("oms and order ops shipment item not matching for wmsOrderCode:" + entry.getKey(), null);
                }*/
            }
        }

    }

    private Map<Long, String> getShipmentIdToWmsOrderCodeMap(List<OrderItemDto> orderItemDtos) {
        Set<Long> shipmentIds = orderItemDtos.stream().map(OrderItemDto::getShipmentId).collect(Collectors.toSet());
        List<ShipmentDto> shipmentDtos = shipmentService.search("id.in:" + StringUtils.join(shipmentIds, ","));
        return shipmentDtos.stream()
                .collect(Collectors.toMap(BaseDto::getId, ShipmentDto::getWmsOrderCode));
    }

    private void updateOmsSystem(Set<String> wmsOrderCodeSet, Long incrementId, OmsSystem omsSystem) throws ReassignOrderOmsException {
        for(String wmsOrderCode : wmsOrderCodeSet){
            CompletableFuture<Object> reassignOrderOmsToOrderOps = orderOpsConnector.reassignOrderOms(incrementId, omsSystem, wmsOrderCode);
            CompletableFuture<Object> reassignOrderOmsToWms = nexsWmsConnector.reassignOrderOms(incrementId, omsSystem, wmsOrderCode);
            try {
                reassignOrderOmsToOrderOps.get();
                reassignOrderOmsToWms.get();
            } catch (InterruptedException | ExecutionException e) {
                throw new ReassignOrderOmsException("error while updating oms system for wmsOrderCode: " + wmsOrderCode + " error: " + e.getMessage(), e);
            }
        }

        if(wmsOrderCodeSet.size() > 1 && !isShipmentLevelReassignmentAllowed){
            logger.info("[updateOmsSystem] wmsOrderCodeSet size is {} and isShipmentLevelReassignmentAllowed {} going ahead with order level reassignment", wmsOrderCodeSet.size(), isShipmentLevelReassignmentAllowed);
            CompletableFuture<Object> reassignOrderOmsToOrderOps1 = orderInterceptorConnector.reassignOrderOms(incrementId, omsSystem);
            CompletableFuture<Object> reassignOrderOmsToOrderOps3 = nexsWmsConnector.reassignOrderOms(incrementId, omsSystem, null);
        }
    }

    public List<ShipmentDto> getShipmentDtos(OrderDto orderDto) {
        List<ShipmentDto> shipmentDtoList = new ArrayList<>();
        Set<Long> shipmentIds = orderDto.getOrderItems().stream()
                .filter(e -> !omsCommonUtil.isNonWarehouseProcessingOrderExcludingB2b(e))
                .map(OrderItemDto::getShipmentId)
                .collect(Collectors.toSet());

        for (Long shipmentId : shipmentIds) {
            shipmentDtoList.add(shipmentService.findById(shipmentId));
        }
        return shipmentDtoList;
    }

    private boolean isOrderStatusUpdateRequired(Action transitionAction, OrderDto orderDto) {
        List<ShipmentDto> shipmentDtoList = getShipmentDtos(orderDto);
        for (ShipmentDto shipmentDto : shipmentDtoList) {
            if (!shipmentDto.getShipmentStatus().name().equalsIgnoreCase(transitionAction.getOrderStatus().getOrderStatus().name()) ) {
                return false;
            }
        }
        return true;
    }

    private void updateOrderDataIfRequired(OrderDto orderDto, String reassignmentReason, Action transitionAction) {
        if (isOrderStatusUpdateRequired(transitionAction, orderDto)) {
            orderDto.setOrderStatus(transitionAction.getOrderStatus().getOrderStatus());
            if (StringUtils.isNotEmpty(reassignmentReason)
                    && transitionAction.getOrderStatus().getOrderStatus().allowedSubStatusList.contains(reassignmentReason)
            ) {
                orderDto.setOrderSubStatus(OrderSubStatus.valueOf(reassignmentReason));
            } else {
                orderDto.setOrderSubStatus(transitionAction.getOrderStatus().getOrderSubStatus());
            }

            if (StringUtils.isNotEmpty(MDC.get(ApplicationConstants.USER_ID))) {
                orderDto.setUpdatedBy(MDC.get(ApplicationConstants.USER_ID));
            }
            orderDto.setUpdatedAt(new Date());
            orderService.save(orderDto);
        }
    }

    private void updateShipmentData(Action transitionAction, Set<Long> shipmentIds) {
        logger.info("[ReassignOmsFacade][updateShipmentData] shipmentIds {} and transitionAction {}", shipmentIds, transitionAction);
        List<ShipmentDto> shipmentDtoList = new ArrayList<>();
        for (Long shipmentId : shipmentIds) {
            shipmentDtoList.add(shipmentService.findById(shipmentId));
        }
        for (ShipmentDto shipmentDto : shipmentDtoList) {
            shipmentDto.getOrderItems().forEach(orderItemDto -> {
                orderItemDto.setItemStatus(transitionAction.getItemStatus().getItemStatus());
                orderItemDto.setItemSubStatus(transitionAction.getItemStatus().getItemSubStatus());
                if (StringUtils.isNotEmpty(MDC.get(ApplicationConstants.USER_ID))) {
                    orderItemDto.setUpdatedBy(MDC.get(ApplicationConstants.USER_ID));
                }
                orderItemDto.setUpdatedAt(new Date());
            });
            shipmentDto.setShipmentStatus(transitionAction.getShipmentStatus().getShipmentStatus());
            shipmentDto.setShipmentSubStatus(transitionAction.getShipmentStatus().getShipmentSubStatus());
            logger.info("[ReassignOmsFacade][updateShipmentData] updating shipment  {} status {} and subStatus {}", shipmentDto.getWmsOrderCode(), transitionAction.getShipmentStatus().getShipmentStatus(), transitionAction.getShipmentStatus().getShipmentSubStatus());

            if (StringUtils.isNotEmpty(MDC.get(ApplicationConstants.USER_ID))) {
                shipmentDto.setUpdatedBy(MDC.get(ApplicationConstants.USER_ID));
            }
            shipmentDto.setUpdatedAt(new Date());
            shipmentService.save(shipmentDto);
        }
    }

    private Set<Long> fetchShipmentIdsAndUpdateOrderItemData(String wmsOrderCode) {
        Set<Long> shipmentIds = new HashSet<>();
        ShipmentDto shipmentDto = shipmentService.findBySearchTerms("wmsOrderCode.eq:" + wmsOrderCode);
        shipmentDto.getOrderItems().stream()
                .filter(oi -> !omsCommonUtil.isNonWarehouseProcessingOrderExcludingB2b(oi))
                .forEach(orderItemDto -> shipmentIds.add(orderItemDto.getShipmentId()));
        return shipmentIds;
    }
}
