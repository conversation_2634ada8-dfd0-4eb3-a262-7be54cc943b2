package com.lenskart.oms.facade;

import com.lenskart.inventoryadapter.request.GetMarkNotFoundRequest;
import com.lenskart.inventoryadapter.response.GetMarkNotFoundResponse;
import com.lenskart.nexs.cid.request.StockBlockOrderItemRequest;
import com.lenskart.nexs.cid.request.StockBlockOrderRequest;
import com.lenskart.nexs.cid.response.StockBlockOrderItemResponse;
import com.lenskart.nexs.cid.response.StockBlockOrderResponse;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.ims.request.OrderItemStockCheck;
import com.lenskart.nexs.ims.request.OrderStockCheckRequest;
import com.lenskart.nexs.ims.request.StockCheck;
import com.lenskart.oms.connector.CidConnector;
import com.lenskart.oms.connector.InventoryAdaptorConnector;
import com.lenskart.oms.connector.LsmServiceabilityConnector;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.connector.OrderAdaptorConnector;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.connector.OrderAdaptorConnector;
import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.OrderItemMetaDataDto;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.enums.*;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.request.WarehouseAssignmentResponse;
import com.lenskart.oms.response.OmsResponse;
import com.lenskart.oms.response.Serviceability;
import com.lenskart.oms.response.DailyOrderCounterResponse;
import com.lenskart.oms.service.OrderItemMetaService;
import com.lenskart.oms.service.OrderItemService;
import com.lenskart.oms.service.OrderService;
import com.lenskart.oms.utils.BulkStockBlockUtil;
import com.lenskart.oms.utils.OmsCommonUtil;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.lenskart.oms.constants.ApplicationConstants.*;

@Component
public class OptimaFacade {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    @Setter(onMethod__ = {@Autowired})
    private CidConnector cidConnector;

    @Setter(onMethod__ = {@Autowired})
    private OmsCommonUtil omsCommonUtil;

    @Setter(onMethod__ = {@Autowired})
    private OrderItemService orderItemService;

    @Setter(onMethod__ = {@Autowired})
    private OrderService orderService;

    @Setter(onMethod__ = {@Autowired})
    private LsmServiceabilityConnector lsmServiceabilityConnector;

    @Setter(onMethod__ = {@Autowired})
    private OrderAdaptorConnector orderAdaptorConnector;

    @Setter(onMethod__ = {@Autowired})
    private OrderItemMetaService orderItemMetaService;

    @Setter(onMethod__ = {@Autowired})
    private BulkStockBlockUtil bulkStockBlockUtil;

    @Setter(onMethod__ = {@Autowired})
    private InventoryAdaptorConnector inventoryAdaptorConnector;

    @Value("${nexs.manesar.facility.code}")
    private String nexsManesarFacilityCode;

    @Value("${nexs.bhiwadi.facility.code}")
    private String nexsBhiwadiFacilityCode;

    @Value("${nexs.order.criteria.productId}")
    private String nexsOrderCriteriaProductId;

    @Value("${nexs.order.criteria.payMethod}")
    private String nexsOrderCriteriaPayMethod;

    @Value("${nexs.order.criteria.navChannel}")
    private String nexsOrderCriteriaNavChannel;

    @Value("${nexs.order.criteria.shipCountry}")
    private String nexsOrderCriteriaShipCountry;

    @Value("${nexs.order.criteria.stsFacilities}")
    private String nexsOrderCriteriaSTSFacilities;

    @Value("${nexs.order.criteria.storeFacilityCode}")
    private String nexsOrderCriteriaStoreFacilityCode;

    @Value("#{'${nexs.order.criteria.facilities}'.split(',')}")
    private Set<String> nexsOrderCriteriaFacilities;

    @Value("#{'${oms.allowed.warehouse.facilities}'.split(',')}")
    private Set<String> omsAllowedWarehouseFacilities;

    @Value("#{'${oms.allowed.Uae.facilities}'.split(',')}")
    private Set<String> uaeFacilities;

    @Value("${hubcode.not.eligible.for.nexs-uae}")
    private String hubCodeNotEligibleForUaeInNexs;
    
    @Value("#{'${hubcodes.not.eligible.for.sensei}'.split(',')}")
    private List<String> hubcodesNotEligibleForSensei;

    @Value("#{${so.unicomFacilityMapping}}")
    private Map<String, String> unicomFacilityMapping;

    public WarehouseAssignmentResponse assignWarehouseAndBlockInventory(ShipmentDto shipmentDto, OrderDto orderDto) {
        WarehouseAssignmentResponse warehouseAssignmentResponse = new WarehouseAssignmentResponse();
        try {
            if(omsCommonUtil.isDistributorOrder(orderDto)){
                warehouseAssignmentResponse.setAssignedFacility(shipmentDto.getFacility());
                warehouseAssignmentResponse.setWarehouseAssigned(true);
            } else if(omsCommonUtil.isUaeOrder(orderDto, shipmentDto)) {
                logger.info("skipping nexs criteria match for facility {} for shipment {}", shipmentDto.getFacility(), shipmentDto.getId());
                OrderItemDto orderItemDto = shipmentDto.getOrderItems().get(0);
                String hubCode = null;
                for(OrderItemMetaDataDto orderItemMetaDataDto : orderItemDto.getOrderItemMetaData()) {
                    if(orderItemMetaDataDto.getEntityKey().equals(ORDER_ITEM_META_KEY_HUB_CODE)) {
                        hubCode = orderItemMetaDataDto.getEntityValue();
                    }
                }
                warehouseAssignmentResponse.setWarehouseAssigned(Objects.isNull(hubCode) || !hubCodeNotEligibleForUaeInNexs.contains(hubCode));
            } else {
                isNexsCriteriaMatching(shipmentDto, orderDto, warehouseAssignmentResponse);
            }
            if (warehouseAssignmentResponse.isWarehouseAssigned()) {
                for (OrderItemDto orderItem : shipmentDto.getOrderItems()) {
                    if (FulfillmentType.LOCAL_FITTING.equals(orderItem.getFulfillmentType())) {
                        logger.error("[assignWarehouseAndBlockInventory] orderItem {} is local fitting required. Failing NEXS criteria check.", orderItem.getId());
                        warehouseAssignmentResponse.setAssignmentFailedReason(OrderSubStatus.LOCAL_FITTING_CRITERIA_FAILED.name());
                        throw new ApplicationException("Local fitting required shipment", null);
                    }
                }

                blockInventoryAndFinalizeNexsFacility(shipmentDto, orderDto.getJunoOrderId(), warehouseAssignmentResponse);
                if (StringUtils.isEmpty(warehouseAssignmentResponse.getAssignedFacility())) {
                    throw new ApplicationException("NEXS facility assignment failed.", null);
                }

                logger.info("[assignWarehouseAndBlockInventory] NEXS facility assignment successful with facility code {} for shipment {}", warehouseAssignmentResponse.getAssignedFacility(), shipmentDto.getId());
                shipmentDto.setFacility(warehouseAssignmentResponse.getAssignedFacility());
                warehouseAssignmentResponse.setWarehouseAssigned(true);
            } else {
                warehouseAssignmentResponse.setWarehouseAssigned(false);
            }
            return warehouseAssignmentResponse;
        } catch (Exception e) {
            logger.error("[assignWarehouseAndBlockInventory] NEXS facility assignment failed for shipment {} with warehouseAssignmentResponse {} exception {}", shipmentDto.getId(), warehouseAssignmentResponse, e.getMessage(), e);
            warehouseAssignmentResponse.setWarehouseAssigned(false);
            return warehouseAssignmentResponse;
        }
    }

    private WarehouseAssignmentResponse isNexsCriteriaMatching(ShipmentDto shipmentDto, OrderDto orderDto, WarehouseAssignmentResponse warehouseAssignmentResponse) {
        for (OrderItemDto orderItem : shipmentDto.getOrderItems()) {
            if(!validRegexCriteria(nexsOrderCriteriaProductId, String.valueOf(orderItem.getProductId()))) {
                logger.info("[assignWarehouseAndBlockInventory -> isNexsCriteriaMatching] Product Id criteria check failed for Shipment {}", shipmentDto.getId());
                warehouseAssignmentResponse.setAssignmentFailedReason(OrderSubStatus.PRODUCT_ID_CRITERIA_FAILED.name());
                warehouseAssignmentResponse.setWarehouseAssigned(false);
                return warehouseAssignmentResponse;
            }

            if(!validRegexCriteria(nexsOrderCriteriaShipCountry, shipmentDto.getShippingAddress().getCountryCode())) {
                logger.info("[assignWarehouseAndBlockInventory -> isNexsCriteriaMatching] Ship Country criteria check failed for Shipment {}", shipmentDto.getId());
                warehouseAssignmentResponse.setAssignmentFailedReason(OrderSubStatus.SHIP_COUNTRY_CRITERIA_FAILED.name());
                warehouseAssignmentResponse.setWarehouseAssigned(false);
                return warehouseAssignmentResponse;
            }

            if(!validRegexCriteria(nexsOrderCriteriaPayMethod, orderDto.getPaymentMethod())) {
                logger.info("[assignWarehouseAndBlockInventory -> isNexsCriteriaMatching] Payment Method criteria check failed for Shipment {}", shipmentDto.getId());
                warehouseAssignmentResponse.setAssignmentFailedReason(OrderSubStatus.PAYMENT_METHOD_CRITERIA_FAILED.name());
                warehouseAssignmentResponse.setWarehouseAssigned(false);
                return warehouseAssignmentResponse;
            }

            if(!validBooleanRegexCriteria(nexsOrderCriteriaStoreFacilityCode, orderItem.getShippingDestinationType(), omsCommonUtil.getOrderMetaValueFromKey(orderDto.getOrderMetaData(), ORDER_META_KEY_FACILITY_CODE))) {
                logger.info("[assignWarehouseAndBlockInventory -> isNexsCriteriaMatching] Store Facility criteria check failed for Shipment {}", shipmentDto.getId());
                warehouseAssignmentResponse.setAssignmentFailedReason(OrderSubStatus.STORE_FACILITY_CRITERIA_FAILED.name());
                warehouseAssignmentResponse.setWarehouseAssigned(false);
                return warehouseAssignmentResponse;
            }

            if(!validRegexCriteria(nexsOrderCriteriaNavChannel, orderItem.getNavChannel().name())) {
                logger.info("[assignWarehouseAndBlockInventory -> isNexsCriteriaMatching] Nav Channel criteria check failed for Shipment {}", shipmentDto.getId());
                warehouseAssignmentResponse.setAssignmentFailedReason(OrderSubStatus.NAV_CHANNEL_CRITERIA_FAILED.name());
                warehouseAssignmentResponse.setWarehouseAssigned(false);
                return warehouseAssignmentResponse;
            }
        }

        logger.info("[assignWarehouseAndBlockInventory -> isNexsCriteriaMatching] NEXS criteria matching passed for shipment {}", shipmentDto.getId());
        warehouseAssignmentResponse.setWarehouseAssigned(true);
        return warehouseAssignmentResponse;
    }

    private boolean validRegexCriteria(String criteriaValue, String orderValue) {
        if (criteriaValue == null) {
            return true;
        } else {
//            if (StringUtils.containsIgnoreCase(criteriaValue, orderValue)) {
//                return true;
//            }
            Pattern pattern;
            Matcher matcher;
            if (criteriaValue.contains(",")) {
                String[] criteriaValueList = criteriaValue.split(",");
                for (String criteriaPattern : criteriaValueList) {
                    pattern = Pattern.compile(criteriaPattern);
                    matcher = pattern.matcher(orderValue);
                    if (matcher.matches()) {
                        return true;
                    }
                }
            } else {
                pattern = Pattern.compile(criteriaValue);
                matcher = pattern.matcher(orderValue);
                return matcher.matches();
            }

            return false;
        }
    }

    private boolean validBooleanRegexCriteria(String criteriaValue, String shippingDestinationType, String storeFacilityCode) {
        if (!SHIPPING_DESTINATION_STORE.equalsIgnoreCase(shippingDestinationType)) {
            return validRegexCriteria(criteriaValue, storeFacilityCode);
        } else {
            return validRegexCriteriaNonBlank(nexsOrderCriteriaSTSFacilities, storeFacilityCode);
        }
    }

    private boolean validRegexCriteriaNonBlank(String criteriaValue, String orderValue) {
        if (criteriaValue == null) {
            return false;
        } else {
            Pattern pattern;
            Matcher matcher;
            if (criteriaValue.contains(",")) {
                String[] criteriaValueList = criteriaValue.split(",");
                for (String criteriaPattern : criteriaValueList) {
                    pattern = Pattern.compile(criteriaPattern);
                    matcher = pattern.matcher(orderValue);
                    if (matcher.matches()) {
                        return true;
                    }
                }
            } else {
                pattern = Pattern.compile(criteriaValue);
                matcher = pattern.matcher(orderValue);
                return matcher.matches();
            }
        }
        return false;
    }

    public WarehouseAssignmentResponse blockInventoryAndFinalizeNexsFacility(ShipmentDto shipmentDto, Long orderId, WarehouseAssignmentResponse warehouseAssignmentResponse) {
        boolean fr0Order = false;
        OrderDto order = orderService.findById(orderId);
        if(Objects.isNull(order)){
            order = orderService.findBySearchTerms("junoOrderId.eq:"+orderId);
        }
        Set<String> facilitesToTryBlockingInventory;
        boolean isDoOrder = omsCommonUtil.isDistributorOrder(order);
        boolean isUaeOrder = omsCommonUtil.isUaeOrder(order, shipmentDto);
        if(isDoOrder){
            facilitesToTryBlockingInventory = Collections.singleton(shipmentDto.getFacility());
        } else if(isInternationalOrder(order)) {
            facilitesToTryBlockingInventory = getFacilityOnHubCodeForInternationalOrders(shipmentDto.getOrderItems().get(0).getId());
        } else {
            facilitesToTryBlockingInventory = StringUtils.isNotBlank(warehouseAssignmentResponse.getAssignedFacility()) ? new HashSet<>(Collections.singletonList(warehouseAssignmentResponse.getAssignedFacility())) : nexsOrderCriteriaFacilities;
        }
        logger.info("[assignWarehouseAndBlockInventory -> blockInventoryAndFinalizeNexsFacility] going to check facilities {} for order {}", facilitesToTryBlockingInventory, orderId);
        for (String nexsFacility : facilitesToTryBlockingInventory) {
            if (StringUtils.isEmpty(nexsFacility)) {
                continue;
            }

            if (isOrderLimitReached(nexsFacility,fr0Order) && !isDoOrder) {
                warehouseAssignmentResponse.setWarehouseAssigned(false);
                warehouseAssignmentResponse.setAssignmentFailedReason(OrderSubStatus.ORDER_LIMIT_CRITERIA_FAILED.name());
                continue;
            }

            if (!isPinCodeServiceableForFacility(shipmentDto.getShippingAddress().getCountryCode(), orderId, nexsFacility, shipmentDto.getShippingAddress().getPostcode()) && !isDoOrder) {
                warehouseAssignmentResponse.setWarehouseAssigned(false);
                warehouseAssignmentResponse.setAssignmentFailedReason(OrderSubStatus.PINCODE_SERVICEABILITY_CRITERIA_FAILED.name());
                continue;
            }

            List<StockCheck> stockCheckList = new ArrayList<>();
            for (OrderItemDto orderItemDto : shipmentDto.getOrderItems()) {
                OrderItemDto orderItem = orderItemService.findById(orderItemDto.getId());
                StockCheck stockCheck = getStockCheckObject(orderItem.getProductId(), nexsFacility, orderItem.getUwItemId(), shipmentDto.getLegalOwner());
                stockCheckList.add(stockCheck);
            }

            if (stockCheckList.isEmpty()) {
                logger.error("[assignWarehouseAndBlockInventory -> blockInventoryAndFinalizeNexsFacility] stock check request is NULL for order {}", orderId);
                warehouseAssignmentResponse.setWarehouseAssigned(false);
                warehouseAssignmentResponse.setAssignmentFailedReason(OrderSubStatus.INVENTORY_CHECK_CRITERIA_FAILED.name());
                return warehouseAssignmentResponse;
            }

            stockCheckList = groupListByPid(stockCheckList);
            if (performStockCheck(shipmentDto, stockCheckList, orderId, isDoOrder)) {
                warehouseAssignmentResponse.setAssignedFacility(nexsFacility);
                warehouseAssignmentResponse.setWarehouseAssigned(true);
                return warehouseAssignmentResponse;
            } else {
                warehouseAssignmentResponse.setWarehouseAssigned(false);
                warehouseAssignmentResponse.setAssignmentFailedReason(OrderSubStatus.INVENTORY_CHECK_CRITERIA_FAILED.name());
                continue;
            }
        }

        return warehouseAssignmentResponse;
    }

    Set<String> getFacilityOnHubCodeForInternationalOrders(Long itemId) {
        StringBuilder searchTerm = new StringBuilder("orderItemId.eq:")
                .append(itemId)
                .append("___")
                .append("entityKey.eq:")
                .append(ORDER_ITEM_META_KEY_HUB_CODE);
        OrderItemMetaDataDto orderItemMetaDataDto = orderItemMetaService.findBySearchTerms(searchTerm.toString());
        logger.info("search term {} ,orderItemMetaDataDto {} for shipment {}", searchTerm, orderItemMetaDataDto, itemId);
        return !Objects.isNull(orderItemMetaDataDto) && !hubcodesNotEligibleForSensei.contains(orderItemMetaDataDto.getEntityValue()) ? Collections.singleton(orderItemMetaDataDto.getEntityValue()) : new HashSet<>();
    }

    private Boolean isInternationalOrder(OrderDto order) {
        if(!"IN".equalsIgnoreCase(order.getLkCountry())) {
            return true;
        }
        return false;
    }


    private boolean isOrderLimitReached(String nexsFacility,boolean fr0Order){
        try{
            DailyOrderCounterResponse dailyOrderCounterResponse = orderAdaptorConnector.getDailyOrderLimitCount(nexsFacility,fr0Order);
            if (dailyOrderCounterResponse == null ) {
                throw new ApplicationException("[isOrderLimitReached] Null response body coming from order-adaptor for facility code " + nexsFacility, null);
            }
            if(dailyOrderCounterResponse.getTotalOrderSync() >= dailyOrderCounterResponse.getTotalOrderLimit()){
                logger.info("[isOrderLimitReached] daily order limit reached for facility code {} and system preference Order limit {} and order sync count {}", dailyOrderCounterResponse.getTotalOrderLimit(), dailyOrderCounterResponse.getTotalOrderSync());
                return true;
            }
            return false;
        }catch (Exception exception){
            logger.error("[isOrderLimitReached] fetch daily order count failed for facility code {} with exception {} ", nexsFacility, exception.getMessage(), exception);
            return true;
        }
    }

    private boolean isPinCodeServiceableForFacility(String lkCountry, Long orderId, String nexsFacility, String pincode) {
        try {
            OmsResponse<List<Serviceability>> serviceabilityList = lsmServiceabilityConnector.fetchServiceabilityByPincodeAndFacility(lkCountry, pincode, nexsFacility);
            if (serviceabilityList != null && !serviceabilityList.getResult().isEmpty()) {
                for (Serviceability serviceability : serviceabilityList.getResult()) {
                    if (serviceability.getIs_enabled())
                        return true;
                     else
                        logger.info("[assignWarehouseAndBlockInventory -> isPinCodeServiceableForFacility] pincode {} is not serviceable for facility {} for order {}.", pincode, nexsFacility, orderId);
                }
            }
            return false;
        } catch (Exception e) {
            logger.error("[assignWarehouseAndBlockInventory -> isPinCodeServiceableForFacility] Pincode serviceability check failed for facility {} and pincode {} for order {} with exception", nexsFacility, pincode, orderId, e);
            throw e;
        }
    }

    private boolean performStockCheck(ShipmentDto shipmentDto,List<StockCheck> stockCheckList, Long orderId, boolean isDoOrder) {
        if (CollectionUtils.isNotEmpty(stockCheckList)) {
            if (isDoOrder && OrderSubType.DISTRIBUTOR_ORDER.name().equalsIgnoreCase(shipmentDto.getShipmentSubType())) {
                StockBlockOrderRequest stockBlockOrderRequest = bulkStockBlockUtil.generateBulkStockBlockRequest(shipmentDto, stockCheckList, orderId);
                StockBlockOrderResponse stockBlockOrderResponse = cidConnector.triggerBulkStockCheck(stockBlockOrderRequest);
                updateFulfillability(shipmentDto, stockBlockOrderResponse);
            } else if (isDoOrder && OrderSubType.DISTRIBUTOR_SUPER_ORDER.name().equalsIgnoreCase(shipmentDto.getShipmentSubType())) {
                StockBlockOrderRequest stockBlockOrderRequest = bulkStockBlockUtil.generateBulkStockBlockRequest(shipmentDto, stockCheckList, orderId);
                StockBlockOrderResponse stockBlockOrderResponse = blockInventoryAtUnicom(stockBlockOrderRequest);
                updateFulfillability(shipmentDto, stockBlockOrderResponse);
            } else if (isDoOrder && OrderSubType.DISTRIBUTOR_JIT_ORDER.name().equalsIgnoreCase(shipmentDto.getShipmentSubType())) {
                return true;
            } else {
                OrderStockCheckRequest stockCheckRequestForCID = getOrderStockCheckRequest(stockCheckList, orderId);
                return cidConnector.triggerStockCheck(stockCheckRequestForCID);
            }
        }
        return true;
    }

    private StockBlockOrderResponse blockInventoryAtUnicom(StockBlockOrderRequest stockBlockOrderRequest) {
        StockBlockOrderResponse stockBlockOrderResponse = new StockBlockOrderResponse();
        List<StockBlockOrderItemResponse> stockBlockOrderItemResponseList = new ArrayList<>();
        for (StockBlockOrderItemRequest currentItem : stockBlockOrderRequest.getItems()) {
            GetMarkNotFoundRequest markNotFoundRequest = new GetMarkNotFoundRequest();
            markNotFoundRequest.setQuantityNotFound(currentItem.getRequiredQuantity());
            markNotFoundRequest.setShelf(DEFAULT_SHELF);
            markNotFoundRequest.setFacilityCode(unicomFacilityMapping.get(stockBlockOrderRequest.getFacility()));
            markNotFoundRequest.setSkuId(currentItem.getProductId().toString());
            GetMarkNotFoundResponse markNotFoundResponse = null;
            try {
                markNotFoundResponse = inventoryAdaptorConnector.triggerInventoryMarkNotFound(markNotFoundRequest);
            } catch (Exception exception) {
                logger.error("OptimaFacade.blockInventoryAtUnicom.Exception For Request - " + markNotFoundRequest, exception);
            }
            if (markNotFoundResponse != null && markNotFoundResponse.isResult()) {
                currentItem.getOrderItems().forEach(orderItemId -> {
                    stockBlockOrderItemResponseList.add(addStockBlockOrderItem(true, currentItem.getProductId(), orderItemId));
                });
            } else {
                currentItem.getOrderItems().forEach(orderItemId -> {
                    stockBlockOrderItemResponseList.add(addStockBlockOrderItem(false, currentItem.getProductId(), orderItemId));
                });
            }
        }
        stockBlockOrderResponse.setItems(stockBlockOrderItemResponseList);
        return stockBlockOrderResponse;
    }

    private StockBlockOrderItemResponse addStockBlockOrderItem(boolean isFulfillable, Long productId, Integer orderItemId) {
        StockBlockOrderItemResponse stockBlockOrderItemResponse = new StockBlockOrderItemResponse();
        stockBlockOrderItemResponse.setFulfillable(isFulfillable);
        stockBlockOrderItemResponse.setProductId(productId);
        stockBlockOrderItemResponse.setOrderItemId(orderItemId);
        return stockBlockOrderItemResponse;
    }

    private void updateFulfillability(ShipmentDto shipmentDto, StockBlockOrderResponse stockBlockOrderResponse) {
        if (shipmentDto != null && CollectionUtils.isNotEmpty(shipmentDto.getOrderItems()) && stockBlockOrderResponse != null && CollectionUtils.isNotEmpty(stockBlockOrderResponse.getItems())) {
            Map<Integer, Boolean> orderItemsFfMap = stockBlockOrderResponse.getItems().stream().collect(Collectors.toMap(StockBlockOrderItemResponse::getOrderItemId, StockBlockOrderItemResponse::isFulfillable));
            for(OrderItemDto orderItemDto : shipmentDto.getOrderItems()) {
                orderItemDto.setOmaStatus(orderItemsFfMap.get(orderItemDto.getId().intValue()) ? "FULFILLABLE" : "NON_FULFILLABLE");
                orderItemService.save(orderItemDto);
                orderItemService.save(orderItemDto);
            }
            orderItemService.updateFulfillableOrderItems(shipmentDto, orderItemsFfMap);
        }
    }

    private OrderStockCheckRequest getOrderStockCheckRequest(List<StockCheck> cidStockCheckList, Long orderId) {
        OrderStockCheckRequest stockCheckRequest = new OrderStockCheckRequest();
        List<OrderItemStockCheck> orderItemStockCheckList = new ArrayList<>();
        orderItemStockCheckList.add(getOrderItemStockCheckObject(cidStockCheckList, orderId));
        stockCheckRequest.setOrderItemStockCheckList(orderItemStockCheckList);
        return stockCheckRequest;
    }

    private OrderItemStockCheck getOrderItemStockCheckObject(List<StockCheck> stockCheckList, Long orderId) {
        OrderItemStockCheck orderItemStockCheck = new OrderItemStockCheck();
        orderItemStockCheck.setOrderId(Math.toIntExact(orderId));
        orderItemStockCheck.setStockCheckList(stockCheckList);
        return orderItemStockCheck;
    }

    private List<StockCheck> groupListByPid(List<StockCheck> stockCheckList) {
        Map<Integer, StockCheck> stockCheckMap = new HashMap<>();
        for (StockCheck stockCheck : stockCheckList) {
            StockCheck unRefStockCheckObj = new StockCheck();
            BeanUtils.copyProperties(stockCheck, unRefStockCheckObj);

            if (stockCheckMap.containsKey(stockCheck.getPid())) {
                StockCheck existingStockCheck = stockCheckMap.get(stockCheck.getPid());
                existingStockCheck.setRequiredQuantity(existingStockCheck.getRequiredQuantity() + stockCheck.getRequiredQuantity());

                Set<Integer> unRefStockCheckItemsSet = new HashSet<>();
                unRefStockCheckItemsSet.addAll(existingStockCheck.getOrderItems());
                unRefStockCheckItemsSet.addAll(stockCheck.getOrderItems());
                existingStockCheck.setOrderItems(new ArrayList<>(unRefStockCheckItemsSet));
            } else {
                stockCheckMap.put(stockCheck.getPid(), unRefStockCheckObj);
            }
        }

        return new ArrayList<>(stockCheckMap.values());
    }

    private StockCheck getStockCheckObject(Long productId, String nexsFacility, Long uwItemId, String legalOwner) {
        StockCheck stockCheck = new StockCheck();

        stockCheck.setPid(Math.toIntExact(productId));
        stockCheck.setFacility(nexsFacility);
        stockCheck.setRequiredQuantity(1);
        stockCheck.setLegalOwner(legalOwner);

        List<Integer> itemIdList = new ArrayList<>();
        itemIdList.add(Math.toIntExact(uwItemId));
        stockCheck.setOrderItems(itemIdList);

        return stockCheck;
    }
}
