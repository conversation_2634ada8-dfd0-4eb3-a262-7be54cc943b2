package com.lenskart.oms.facade;

import com.lenskart.oms.request.OtcShipmentEvent;
import com.lenskart.oms.strategy.OtcShipmentEventStrategyExecutor;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Setter(onMethod__ = {@Autowired})
public class OTCOrderFacade {
    private OtcShipmentEventStrategyExecutor otcShipmentEventStrategyExecutor;
    public void processOTCShipmentEvent(OtcShipmentEvent otcShipmentEvent) throws Exception {
        otcShipmentEventStrategyExecutor.doExecute(otcShipmentEvent);
    }
}
