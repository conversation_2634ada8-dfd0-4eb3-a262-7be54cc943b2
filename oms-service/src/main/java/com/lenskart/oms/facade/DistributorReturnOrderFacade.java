package com.lenskart.oms.facade;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lenskart.fds.enums.DocumentType;
import com.lenskart.fds.request.CreateDocumentRequest;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.constants.RedisOps;
import com.lenskart.nexs.service.RedisHandler;
import com.lenskart.oms.connector.*;
import com.lenskart.oms.dto.DistributorReturnOrderDto;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.entity.UnicomMpSaleOrderItem;
import com.lenskart.oms.enums.DistributorReturnOrderStatus;
import com.lenskart.oms.enums.ShipmentEvent;
import com.lenskart.oms.enums.WmsReturnCondition;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.exception.DistributorReturnOrderException;
import com.lenskart.oms.mapper.DistributorReturnOrderMapper;
import com.lenskart.oms.model.BarcodeDetails;
import com.lenskart.oms.producer.FdsCreditNoteProducer;
import com.lenskart.oms.repository.UnicomMpSaleOrderItemRepository;
import com.lenskart.oms.request.*;
import com.lenskart.oms.request.distributor.orderReturn.ReturnOrderRequest;
import com.lenskart.oms.response.DistributorReturnOrderResponse;
import com.lenskart.oms.service.DistributorReturnOrderService;
import com.lenskart.oms.service.OrderItemService;
import com.lenskart.oms.utils.ObjectHelper;
import com.lenskart.putawayservice.dto.*;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import static com.lenskart.oms.enums.QcStatus.BAD;

@Component
public class DistributorReturnOrderFacade {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    @Setter(onMethod__ = {@Autowired})
    private CatalogOpsConnector catalogOpsConnector;

    @Setter(onMethod__ = {@Autowired})
    private OrderItemService orderItemService;

    @Setter(onMethod__ = {@Autowired})
    private ShipmentEventsFacade shipmentEventsFacade;

    @Setter(onMethod__ = {@Autowired})
    private DistributorReturnOrderMapper distributorReturnOrderMapper;

    @Setter(onMethod__ = {@Autowired})
    private DistributorReturnOrderService distributorReturnOrderService;

    @Setter(onMethod__ = {@Autowired})
    private NexsWmsConnector nexsWmsConnector;

    @Setter(onMethod__ = {@Autowired})
    private PutawayConnector putawayConnector;

    @Setter(onMethod__ = {@Autowired})
    private FdsConnector fdsConnector;

    @Setter(onMethod__ = {@Autowired})
    private StringRedisTemplate stringRedisTemplate;

    @Setter(onMethod__ = {@Autowired})
    private FdsCreditNoteProducer fdsCreditNoteProducer;

    @Setter(onMethod__ = {@Autowired})
    private InventoryAdaptorConnector inventoryAdapterConnector;

    @Value("${manesar.facility}")
    private String lenskartDefaultFacilityCode;

    private static final String RETURN_BARCODE_DETAILS_REDIS_KEY = "RETURN_BARCODE_DETAILS_";

    @Setter(onMethod__ = {@Autowired})
    private UnicomMpSaleOrderItemRepository unicomMpSaleOrderItemRepository;

    public Object fetchProductDetailsWithBarcode(String barcode, String returnFacilityCode) throws Exception {
        BarcodeDetails barcodeDetails = getReturnItemDetails(barcode, returnFacilityCode);
        return catalogOpsConnector.findProductDetailsByProductId(barcodeDetails.getProductId());
    }

    private BarcodeDetails getReturnItemDetails(String barcode, String returnFacilityCode) throws JsonProcessingException, ApplicationException {
        BarcodeDetails barcodeDetails = getBarcodeDetails(barcode);
        logger.info("[DistributorReturnOrderFacade][getReturnItemDetails] BarcodeDetails {}", barcodeDetails);
        validateReturnFacility(returnFacilityCode, barcodeDetails.getDispatchFacility());
        return barcodeDetails;
    }

    private void validateReturnFacility(String returnFacilityCode, String dispatchFacilityCode) {
        //todo validate if returns are applicable for any other unicommerce facilities?
        if (!returnFacilityCode.equalsIgnoreCase("NXS2")) {
            throw new DistributorReturnOrderException("Return Facility criteria not met");
        }
    }

    private BarcodeDetails getBarcodeDetails(String barcode) throws JsonProcessingException, ApplicationException {
        String barcodeRedisKey = RETURN_BARCODE_DETAILS_REDIS_KEY + barcode;
        BarcodeDetails barcodeDetails = getBarcodeDetailsFromRedis(barcodeRedisKey);
        if (Objects.nonNull(barcodeDetails)) {
            return barcodeDetails;
        }
        OrderItemDto orderItemDto = orderItemService.findBySearchTerms("itemBarcode.eq:" + barcode + "___itemStatus.eq:DISPATCHED");
        if (Objects.isNull(orderItemDto)) {
            logger.info("[DistributorReturnOrderFacade][getBarcodeDetails]Barcode details not found in sensei, fetching details from unicommerce for {}", barcode);
            barcodeDetails = distributorReturnOrderMapper.getBarcodeDetailsFromUnicomMpSaleOrderItem(getUnicomMapSaleOrderItemDetails(barcode));
        } else {
            barcodeDetails = distributorReturnOrderMapper.convertOrderItemDtoToBarcodeDetails(orderItemDto);
        }
        saveBarcodeDetailsInRedis(barcodeRedisKey, barcodeDetails);
        return barcodeDetails;
    }

    private UnicomMpSaleOrderItem getUnicomMapSaleOrderItemDetails(String barcode) {
        List<UnicomMpSaleOrderItem> unicomMpSaleOrderItemList = unicomMpSaleOrderItemRepository.findByBarcode(barcode);
        if (CollectionUtils.isEmpty(unicomMpSaleOrderItemList)) {
            throw new ApplicationException("Barcode details not present in unicom mp sale order table");
        } else if (unicomMpSaleOrderItemList.size() == 1) {
            return unicomMpSaleOrderItemList.get(0);
        } else {
            UnicomMpSaleOrderItem unicomMpSaleOrderItem = unicomMpSaleOrderItemList.get(0);
            for (UnicomMpSaleOrderItem unicomMpSaleOrderItem1 : unicomMpSaleOrderItemList) {
                if (unicomMpSaleOrderItem.getCreatedAt().before(unicomMpSaleOrderItem1.getCreatedAt())) {
                    unicomMpSaleOrderItem = unicomMpSaleOrderItem1;
                }
            }
            return unicomMpSaleOrderItem;
        }
    }

    private BarcodeDetails getBarcodeDetailsFromRedis(String redisKey) {
        try {
            if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(redisKey))) {
                return ObjectHelper.getObjectMapper().readValue(String.valueOf(RedisHandler.redisOps(RedisOps.GET, redisKey)), BarcodeDetails.class);
            }
        } catch (Exception e) {
            logger.error("[getBarcodeDetails]Exception while getting from Redis for " + redisKey + e.getMessage(), e);
        }
        return null;
    }

    private void saveBarcodeDetailsInRedis(String barcodeRedisKey, BarcodeDetails barcodeDetails) {
        try {
            RedisHandler.redisOps(RedisOps.SETVALUETTL, barcodeRedisKey, ObjectHelper.convertToString(barcodeDetails), 1L, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("[getBarcodeDetails]Exception while saving value in Redis for " + barcodeRedisKey + e.getMessage(), e);
        }
    }

    public DistributorReturnOrderResponse returnOrder(ReturnOrderRequest returnOrderRequest) throws Exception {
        logger.info("[DistributorReturnOrderFacade][returnOrder]Request received {}", returnOrderRequest);
        BarcodeDetails barcodeDetails = getReturnItemDetails(returnOrderRequest.getScannedBarcode(), returnOrderRequest.getReturnFacility());
        DistributorReturnOrderDto distributorReturnOrderDto = distributorReturnOrderService.findBySearchTerms("scannedBarcode.eq:" + returnOrderRequest.getScannedBarcode() + "___wmsOrderCode.eq:" + barcodeDetails.getWmsOrderCode());
        if (Objects.isNull(distributorReturnOrderDto)) {
            distributorReturnOrderDto = new DistributorReturnOrderDto(returnOrderRequest.getReturnType(), returnOrderRequest.getScannedBarcode(), barcodeDetails.getItemId(), barcodeDetails.getWmsOrderCode(), returnOrderRequest.getQcStatus(), returnOrderRequest.getQcFailReason(), returnOrderRequest.getReturnReason(), DistributorReturnOrderStatus.RETURN_STARTED, returnOrderRequest.getReturnFacility(), null);
            distributorReturnOrderDto.setCreatedBy(returnOrderRequest.getUserName());
            distributorReturnOrderDto.setUpdatedBy(returnOrderRequest.getUserName());
            distributorReturnOrderDto = distributorReturnOrderService.save(distributorReturnOrderDto);
        }
        try {
            DistributorReturnOrderResponse distributorReturnOrderResponse = new DistributorReturnOrderResponse();
            logger.info("[DistributorReturnOrderFacade][returnOrder]distributorReturnOrderDto={}", distributorReturnOrderDto);
            switch (distributorReturnOrderDto.getStatus()) {
                case RETURN_STARTED:
                    markOrderReturned(barcodeDetails, distributorReturnOrderDto, returnOrderRequest.getUserName());
                case RETURN_CREATED:
                    createPutaway(barcodeDetails, distributorReturnOrderDto, returnOrderRequest.getUserName());
                case PUTAWAY_CREATED:
                    if (lenskartDefaultFacilityCode.equalsIgnoreCase(barcodeDetails.getDispatchFacility())) {
                        distributorReturnOrderDto.setStatus(DistributorReturnOrderStatus.COMPLETE);
                        distributorReturnOrderDto.setUpdatedBy(returnOrderRequest.getUserName());
                        distributorReturnOrderService.update(distributorReturnOrderDto, distributorReturnOrderDto.getId());
                    } else {
                        triggerCreditNoteEvent(barcodeDetails.getItemId().toString());
                    }
                    distributorReturnOrderMapper.generateDistributorReturnOrderResponseFromDistributorReturnOrderDto(distributorReturnOrderResponse, distributorReturnOrderDto);
                    break;
                case CREDIT_NOTE_CREATED:
                case COMPLETE:
                    distributorReturnOrderMapper.generateDistributorReturnOrderResponseFromDistributorReturnOrderDto(distributorReturnOrderResponse, distributorReturnOrderDto);
                    break;
                default:
                    throw new DistributorReturnOrderException("Status not valid");
            }
            return distributorReturnOrderResponse;
        } catch (Exception e) {
            logger.error("[DistributorReturnOrderFacade][returnOrder]Exception while returning item " + returnOrderRequest.getScannedBarcode() + e.getMessage(), e);
            throw e;
        }
    }

    private void markOrderReturned(BarcodeDetails barcodeDetails, DistributorReturnOrderDto distributorReturnOrderDto, String userName) throws ApplicationException {
        try {
            updateReturnStatusAtWms(barcodeDetails, distributorReturnOrderDto);
            if (!lenskartDefaultFacilityCode.equalsIgnoreCase(barcodeDetails.getDispatchFacility())) {
                shipmentEventsFacade.processShipmentEvent(getRequestForShipmentReturnUpdate(distributorReturnOrderDto.getWmsOrderCode(), distributorReturnOrderDto.getScannedBarcode(), distributorReturnOrderDto.getItemId(), ShipmentEvent.MARK_ITEM_RETURNED));
            }
            distributorReturnOrderDto.setStatus(DistributorReturnOrderStatus.RETURN_CREATED);
            distributorReturnOrderDto.setUpdatedBy(userName);
            distributorReturnOrderService.update(distributorReturnOrderDto, distributorReturnOrderDto.getId());
        } catch (Exception e) {
            logger.error("[DistributorReturnOrderFacade][markOrderReturned] Exception while marking order return for " + barcodeDetails.getBarcode() + e.getMessage(), e);
            throw new DistributorReturnOrderException("Exception while marking order return. ErrorMessage=" + e.getMessage());
        }
    }

    public ShipmentUpdateEvent getRequestForShipmentReturnUpdate(String wmsOrderCode, String barcode, Long itemId, ShipmentEvent shipmentEvent) {
        ShipmentUpdateEvent shipmentUpdateEvent = new ShipmentUpdateEvent();
        shipmentUpdateEvent.setWmsOrderCode(wmsOrderCode);
        shipmentUpdateEvent.setEntityId(barcode);
        shipmentUpdateEvent.setShipmentEvent(shipmentEvent);
        ShipmentItemUpdate shipmentItemUpdate = new ShipmentItemUpdate();
        shipmentItemUpdate.setOrderItemId(itemId);
        shipmentUpdateEvent.setOrderItemList(Collections.singletonList(shipmentItemUpdate));
        return shipmentUpdateEvent;
    }

    private void updateReturnStatusAtWms(BarcodeDetails barcodeDetails, DistributorReturnOrderDto distributorReturnOrderDto) throws ApplicationException {
        WmsOrderActionRequest wmsOrderActionRequest = new WmsOrderActionRequest();
        WmsOrderItemActionRequest wmsOrderItemActionRequest = new WmsOrderItemActionRequest();
        wmsOrderActionRequest.setOperation("RETURNED");
        wmsOrderActionRequest.setReceivingFacility(distributorReturnOrderDto.getReturnFacility());
        wmsOrderActionRequest.setDispatchFacility(barcodeDetails.getDispatchFacility());
        wmsOrderActionRequest.setClient("warehouse");
        wmsOrderActionRequest.setIncrementId(barcodeDetails.getIncrementId());
        wmsOrderItemActionRequest.setOrderCode(barcodeDetails.getWmsOrderCode());
        wmsOrderItemActionRequest.setOrderItemId(Math.toIntExact(barcodeDetails.getItemId()));
        wmsOrderItemActionRequest.setCondition(WmsReturnCondition.valueOf(distributorReturnOrderDto.getQcStatus().name()));
        wmsOrderItemActionRequest.setProductId(Math.toIntExact(barcodeDetails.getProductId()));
        wmsOrderItemActionRequest.setBarcode(barcodeDetails.getBarcode());
        wmsOrderActionRequest.setOrderItems(Collections.singletonList(wmsOrderItemActionRequest));
        nexsWmsConnector.triggerReturnOrderInNexsWms(wmsOrderActionRequest);
    }

    private void createPutaway(BarcodeDetails barcodeDetails, DistributorReturnOrderDto distributorReturnOrderDto, String userName) {
        String putawayCode = putawayConnector.createPutaway(generatePutawayCreationRequest(barcodeDetails, distributorReturnOrderDto, userName));
        distributorReturnOrderDto.setPutawayCode(putawayCode);
        distributorReturnOrderDto.setStatus(DistributorReturnOrderStatus.PUTAWAY_CREATED);
        distributorReturnOrderDto.setUpdatedBy(userName);
        distributorReturnOrderService.update(distributorReturnOrderDto, distributorReturnOrderDto.getId());
    }

    private PutawayCreationRequest generatePutawayCreationRequest(BarcodeDetails barcodeDetails, DistributorReturnOrderDto distributorReturnOrderDto, String userName) {
        PutawayCreationRequest putawayCreationRequest = new PutawayCreationRequest();
        PutawayCreationBarcode putawayCreationBarcode = new PutawayCreationBarcode();
        PutawayCreationReferences putawayCreationReferences = new PutawayCreationReferences();
        putawayCreationBarcode.setAction(PutawayAction.CREATE);
        String qcStatusForPutaway = BAD.name().equalsIgnoreCase(distributorReturnOrderDto.getQcStatus().name()) ? "fail" : "pass";
        putawayCreationBarcode.setCondition(qcStatusForPutaway);
        putawayCreationBarcode.setPid(barcodeDetails.getProductId().toString());
        putawayCreationBarcode.setBarcodeNo(distributorReturnOrderDto.getScannedBarcode());
        putawayCreationRequest.setBarcode(Collections.singletonList(putawayCreationBarcode));
        putawayCreationRequest.setFacilityCode(distributorReturnOrderDto.getReturnFacility());
        putawayCreationRequest.setType(PutawayType.PUTAWAY_RECEIVED_RETURNS);
        putawayCreationRequest.setGrnNo(userName);
        putawayCreationRequest.setReferences(putawayCreationReferences);
        putawayCreationRequest.setUser(distributorReturnOrderDto.getCreatedBy());
        return putawayCreationRequest;
    }

    public void triggerCreditNoteEvent(String itemId) {
        try {
            fdsCreditNoteProducer.sendMessage(itemId);
            logger.info("[DistributorReturnOrderFacade][triggerCreditNoteEvent] sent successfully for itemId={}", itemId);
        } catch (Exception e) {
            logger.error("[DistributorReturnOrderFacade][triggerCreditNoteEvent]Exception while pushing kafkaEvent for " + itemId + e.getMessage(), e);
        }
    }

    public void createCreditNote(Long itemId) throws Exception {
        DistributorReturnOrderDto distributorReturnOrderDto = distributorReturnOrderService.findBySearchTerms("itemId.eq:" + itemId);
        CreateDocumentRequest createDocumentRequest = getCreateDocumentRequest("DISTRIBUTOR_RETURN_ORDER", DocumentType.CREDIT_NOTE, "FDS", itemId.toString(), distributorReturnOrderDto.getWmsOrderCode(), distributorReturnOrderDto.getReturnFacility());
        String creditNo = fdsConnector.createDocument(createDocumentRequest);
        updateStatus(itemId.toString(), "FDS", DistributorReturnOrderStatus.CREDIT_NOTE_CREATED);
        logger.info("[DistributorReturnOrderFacade][createCreditNote] CreditNoteNumber is {} for returnItemId={}", creditNo, itemId);
    }

    private CreateDocumentRequest getCreateDocumentRequest(String documentSource, DocumentType documentType, String documentProvider, String documentReferenceId, String wmsOrderCode, String returnFacility) {
        CreateDocumentRequest createDocumentRequest = new CreateDocumentRequest();
        createDocumentRequest.setFacility(returnFacility);
        createDocumentRequest.setLenskartGeneratedUnicomOrderCode(wmsOrderCode);
        createDocumentRequest.setDocumentSource(documentSource);
        createDocumentRequest.setDocumentType(documentType);
        createDocumentRequest.setDocumentProvider(documentProvider);
        createDocumentRequest.setDocumentSourceReferenceId(documentReferenceId);
        return createDocumentRequest;
    }

    public Long updateStatus(String orderItemId, String userName, DistributorReturnOrderStatus newStatus) throws Exception {
        DistributorReturnOrderDto distributorReturnOrderDto = distributorReturnOrderService.findBySearchTerms("itemId.eq:" + orderItemId);
        if (distributorReturnOrderDto == null) {
            throw new ApplicationException("Order has not been returned in OMS. please try after some time");
        }
        validateReturnOrderStatus(distributorReturnOrderDto, newStatus);
        if (!distributorReturnOrderDto.getStatus().equals((newStatus))) {
            distributorReturnOrderDto.setUpdatedBy(userName);
            distributorReturnOrderDto.setStatus(newStatus);
            distributorReturnOrderService.update(distributorReturnOrderDto, distributorReturnOrderDto.getId());
            return distributorReturnOrderDto.getId();
        }
        return distributorReturnOrderDto.getId();
    }

    public void validateReturnOrderStatus(DistributorReturnOrderDto distributorReturnOrderDto, DistributorReturnOrderStatus newStatus) throws ApplicationException {
        if (distributorReturnOrderDto.getStatus().equals(newStatus)) {
            logger.info("Order {} Already in {} state", distributorReturnOrderDto.getItemId(), newStatus);
            return;
        }
        if (!(DistributorReturnOrderStatus.PUTAWAY_CREATED.equals(distributorReturnOrderDto.getStatus()) && newStatus.equals(DistributorReturnOrderStatus.CREDIT_NOTE_CREATED))) {
            throw new ApplicationException("Order status update transition not valid");
        }
    }
}
