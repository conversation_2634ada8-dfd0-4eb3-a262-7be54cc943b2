package com.lenskart.oms.mapper;

import com.lenskart.oms.dto.DistributorReturnOrderDto;
import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.entity.UnicomMpSaleOrderItem;
import com.lenskart.oms.model.BarcodeDetails;
import com.lenskart.oms.response.DistributorReturnOrderResponse;
import com.lenskart.oms.service.OrderService;
import com.lenskart.oms.service.ShipmentService;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Setter(onMethod__ = {@Autowired})
public class DistributorReturnOrderMapper {
    private ShipmentService shipmentService;
    private OrderService orderService;

    @Value("${manesar.facility}")
    @Setter(AccessLevel.NONE)
    private String lenskartDefaultFacilityCode;

    public BarcodeDetails convertOrderItemDtoToBarcodeDetails(OrderItemDto orderItemDto) {
        BarcodeDetails barcodeDetails = new BarcodeDetails();
        ShipmentDto shipmentDto = shipmentService.findById(orderItemDto.getShipmentId());
        OrderDto orderDto = orderService.findById(orderItemDto.getOrderId());
        barcodeDetails.setBarcode(orderItemDto.getItemBarcode());
        barcodeDetails.setProductId(orderItemDto.getProductId());
        barcodeDetails.setDispatchFacility(shipmentDto.getFacility());
        barcodeDetails.setItemId(orderItemDto.getId());
        barcodeDetails.setWmsOrderCode(shipmentDto.getWmsOrderCode());
        barcodeDetails.setIncrementId(Math.toIntExact(orderDto.getIncrementId()));
        return barcodeDetails;
    }

    public void generateDistributorReturnOrderResponseFromDistributorReturnOrderDto(DistributorReturnOrderResponse distributorReturnOrderResponse, DistributorReturnOrderDto distributorReturnOrderDto) {
        if (StringUtils.isNotBlank(distributorReturnOrderDto.getPutawayCode())) {
            distributorReturnOrderResponse.setPutawayCode(distributorReturnOrderDto.getPutawayCode());
            distributorReturnOrderResponse.setPutawayFacility(distributorReturnOrderDto.getReturnFacility());
            distributorReturnOrderResponse.setSuccessful(true);
        } else {
            distributorReturnOrderResponse.setSuccessful(false);
        }
    }

    public BarcodeDetails getBarcodeDetailsFromUnicomMpSaleOrderItem(UnicomMpSaleOrderItem unicomMapSaleOrderItemDetails) {
        BarcodeDetails barcodeDetails = new BarcodeDetails();
        barcodeDetails.setBarcode(unicomMapSaleOrderItemDetails.getBarcode());
        barcodeDetails.setProductId(Long.valueOf(unicomMapSaleOrderItemDetails.getProductId()));
        barcodeDetails.setItemId(Long.valueOf(unicomMapSaleOrderItemDetails.getItemId()));
        barcodeDetails.setWmsOrderCode(unicomMapSaleOrderItemDetails.getWmsOrderCode());
        barcodeDetails.setDispatchFacility(lenskartDefaultFacilityCode);
        barcodeDetails.setIncrementId(0);
        return barcodeDetails;
    }
}
