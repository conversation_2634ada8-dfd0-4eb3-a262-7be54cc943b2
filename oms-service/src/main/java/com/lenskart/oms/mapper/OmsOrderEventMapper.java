package com.lenskart.oms.mapper;

import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.enums.OrderEventType;
import com.lenskart.oms.enums.OrderType;
import com.lenskart.oms.request.OmsOrderEvent;
import com.lenskart.order.interceptor.enums.Client;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.Collections;
import java.util.List;

@Component
public class OmsOrderEventMapper {

    public OmsOrderEvent getOmsOrderEventFromShipmentDto(ShipmentDto shipmentDto, OrderDto orderDto, OrderEventType orderEventType) {
        OmsOrderEvent omsOrderEvent = new OmsOrderEvent();

        List<OrderItemDto> orderItemDtoList = shipmentDto.getOrderItems();
        orderDto.setOrderItems(orderItemDtoList);

        omsOrderEvent.setOrderDto(orderDto);
        omsOrderEvent.setShipmentDtoList(Collections.singletonList(shipmentDto));
        omsOrderEvent.setEventType(orderEventType);
        if(!ObjectUtils.isEmpty(orderDto.getOrderType()) && OrderType.DISTRIBUTOR_ORDER.equals(orderDto.getOrderType())){
            omsOrderEvent.setClientId(Client.OS.getUsername());
        }
        return omsOrderEvent;
    }
}
