package com.lenskart.oms.mapper;

import com.lenskart.oms.dto.*;
import com.lenskart.oms.enums.OrderEventType;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.request.OmsOrderEvent;
import com.lenskart.oms.service.OnHoldMasterService;
import com.lenskart.order.interceptor.request.HoldOrderRequest;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Setter(onMethod__ = {@Autowired})
@Component
public class HoldOrderMapper {

    private OnHoldMasterService onHoldMasterService;

    public OmsOrderEvent map(HoldOrderRequest request, String requestType) throws ApplicationException {
        OmsOrderEvent omsOrderEvent = new OmsOrderEvent();
        omsOrderEvent.setIncrementId(request.getIncrementId());
        omsOrderEvent.setEventType(OrderEventType.valueOf(requestType));
        OrderDto orderDto = new OrderDto();
        orderDto.setIncrementId(request.getIncrementId());
        Long holdReasonId = getOnHoldMasterDto(request);
        orderDto.setOnHoldReasonId(holdReasonId);
        orderDto.setIsOnHold(true);
        omsOrderEvent.setOrderDto(orderDto);
        return omsOrderEvent;
    }

    private Long getOnHoldMasterDto(HoldOrderRequest request) throws ApplicationException {
        OnHoldMasterDto onHoldMasterDto = onHoldMasterService.findBySearchTerms("code.eq:" + request.getHoldReasonCode());
        if (Objects.isNull(onHoldMasterDto)) {
            throw new ApplicationException("invalid hold reason code passed", null);
        }
        return onHoldMasterDto.getId();
    }

}
