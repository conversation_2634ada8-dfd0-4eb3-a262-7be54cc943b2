package com.lenskart.oms.mapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import java.util.Map;

@Component
public class MailContentMapper {
    private TemplateEngine templateEngine;

    @Autowired
    public MailContentMapper(TemplateEngine templateEngine) {
        this.templateEngine = templateEngine;
    }

    public String build(Map map, String invoiceSenderMailTemplate) {
        Context context = new Context();
        context.setVariables(map);
        return this.templateEngine.process(invoiceSenderMailTemplate, context);
    }
}
