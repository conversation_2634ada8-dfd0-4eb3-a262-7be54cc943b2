package com.lenskart.oms.mapper;

import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.enums.ItemType;
import com.lenskart.oms.enums.ShipmentEvent;
import com.lenskart.oms.request.WmsOrderEvent;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class WmsOrderEventMapper {

    public WmsOrderEvent getWmsOrderEventFromShipmentDto(ShipmentDto shipmentDto, OrderDto orderDto, ShipmentEvent shipmentEvent) {
        WmsOrderEvent wmsOrderEvent = new WmsOrderEvent();

        List<OrderItemDto> orderItemDtoList = shipmentDto.getOrderItems();
        skipPowerPayloadForLoyalty(orderItemDtoList);
        orderDto.setOrderItems(orderItemDtoList);

        wmsOrderEvent.setOrderDto(orderDto);
        wmsOrderEvent.setShipmentDto(shipmentDto);
        wmsOrderEvent.setShipmentEvent(shipmentEvent);

        return wmsOrderEvent;
    }

    private void skipPowerPayloadForLoyalty(List<OrderItemDto> orderItemDtoList) {
        orderItemDtoList.stream().forEach(orderItemDto -> {
            if (ItemType.LOYALTY_SERVICES.equals(orderItemDto.getItemType())) {
                orderItemDto.setItemPower(null);
            }
        });
    }
}
