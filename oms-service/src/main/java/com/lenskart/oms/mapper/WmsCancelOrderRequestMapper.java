package com.lenskart.oms.mapper;

import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.enums.OrderStatus;
import com.lenskart.oms.request.OmsOrderEvent;
import com.lenskart.oms.request.WmsOrderActionRequest;
import com.lenskart.oms.request.WmsOrderItemActionRequest;
import com.lenskart.oms.service.ShipmentService;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
@Setter(onMethod__ = {@Autowired})
public class WmsCancelOrderRequestMapper {

    private ShipmentService shipmentService;

    public WmsOrderActionRequest orderRequestToWmsCancelOrderRequest(OmsOrderEvent orderRequest) {
        WmsOrderActionRequest wmsOrderActionRequest = new WmsOrderActionRequest();
        wmsOrderActionRequest.setIncrementId(Math.toIntExact(orderRequest.getIncrementId()));
        wmsOrderActionRequest.setOperation("CANCELLED");
        wmsOrderActionRequest.setOrderItems(
                orderRequest.getCancelOrderRequest().getOrderItems().stream()
                .map(orderItemDto -> populateWmsOrderItemAction(orderItemDto))
                        .collect(Collectors.toList()));
        return wmsOrderActionRequest;
    }

    private WmsOrderItemActionRequest populateWmsOrderItemAction(OrderItemDto orderItemDto) {
        WmsOrderItemActionRequest wmsOrderItemActionRequest = new WmsOrderItemActionRequest();
        wmsOrderItemActionRequest.setOrderCode(shipmentService.findById(orderItemDto
                .getShipmentId()).getWmsOrderCode());
        wmsOrderItemActionRequest.setOrderItemId(Math.toIntExact(orderItemDto.getUwItemId()));
        return wmsOrderItemActionRequest;
    }

    public WmsOrderActionRequest orderRequestToWmsCancelDistributorOrderRequest(Long incrementId, String wmsOrderCode, List<Long> orderItemIds) {
        WmsOrderActionRequest wmsOrderActionRequest = new WmsOrderActionRequest();
        wmsOrderActionRequest.setIncrementId(Math.toIntExact(incrementId));
        wmsOrderActionRequest.setOperation(OrderStatus.CANCELLED.name());
        wmsOrderActionRequest.setOrderItems(
                orderItemIds.stream()
                        .map(itemId -> populateWmsOrderItemActionDo(itemId, wmsOrderCode))
                        .collect(Collectors.toList()));
        return wmsOrderActionRequest;
    }

    private WmsOrderItemActionRequest populateWmsOrderItemActionDo(Long orderItemId, String wmsCode) {
        WmsOrderItemActionRequest wmsOrderItemActionRequest = new WmsOrderItemActionRequest();
        wmsOrderItemActionRequest.setOrderCode(wmsCode);
        wmsOrderItemActionRequest.setOrderItemId(Math.toIntExact(orderItemId));
        return wmsOrderItemActionRequest;
    }


}
