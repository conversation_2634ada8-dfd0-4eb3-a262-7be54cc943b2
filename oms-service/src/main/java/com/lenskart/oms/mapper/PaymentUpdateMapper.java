package com.lenskart.oms.mapper;

import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.dto.OrderMetaDataDto;
import com.lenskart.oms.enums.OrderEventType;
import com.lenskart.oms.enums.ShipmentEvent;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.request.WmsOrderEvent;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.request.OmsOrderEvent;
import com.lenskart.order.interceptor.request.PaymentUpdateRequest;
import org.springframework.stereotype.Component;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Component
public class PaymentUpdateMapper {

    public OmsOrderEvent updatePaymentUpdateRequestToOmsOrderEvent(PaymentUpdateRequest request, String requestType, String incrementId) throws ApplicationException{
        OmsOrderEvent omsOrderEvent = new OmsOrderEvent();
        omsOrderEvent.setIncrementId(Long.valueOf(incrementId));
        omsOrderEvent.setEventType(OrderEventType.valueOf(requestType));
        OrderDto paymentUpdateOrderDto = populatePaymentUpdateOrderDto(request, incrementId);
        omsOrderEvent.setOrderDto(paymentUpdateOrderDto);
        return omsOrderEvent;
    }

    public OrderDto populatePaymentUpdateOrderDto(PaymentUpdateRequest paymentUpdateRequest, String incrementId) throws ApplicationException{
        OrderDto orderDto = new OrderDto();
        orderDto.setIncrementId(Long.valueOf(incrementId));
        orderDto.setPaymentCaptured(paymentUpdateRequest.isPaymentCaptured());
        orderDto.setPaymentMethod(paymentUpdateRequest.getPaymentMethod());
        orderDto.setOrderMetaData(populatePaymentUpdateMetaData(paymentUpdateRequest));
        return orderDto;
    }

    public List<OrderMetaDataDto> populatePaymentUpdateMetaData(PaymentUpdateRequest paymentUpdate){
        Map<String, String> paymentUpdateMetaMap  = populatePaymentUpdateMetaMap(paymentUpdate);
        List<OrderMetaDataDto> paymentUpdateMetaDataList = new ArrayList<>();

        for(Map.Entry<String, String> orderMetaMapEntry : paymentUpdateMetaMap.entrySet()){
            OrderMetaDataDto paymentUpdateMetaData = new OrderMetaDataDto();

            paymentUpdateMetaData.setEntityKey(orderMetaMapEntry.getKey());
            paymentUpdateMetaData.setEntityValue(orderMetaMapEntry.getValue());
            paymentUpdateMetaDataList.add(paymentUpdateMetaData);
        }
        return paymentUpdateMetaDataList;
    }

    public Map<String, String> populatePaymentUpdateMetaMap(PaymentUpdateRequest paymentUpdate){
        Map<String, String> orderMetaMap = new HashMap<>();
        orderMetaMap.put(ApplicationConstants.PAYMENT_TRANSACTION_ID, paymentUpdate.getPaymentTransactionId());
        orderMetaMap.put(ApplicationConstants.PAYMENT_PAY_U_ID, paymentUpdate.getPayuId());
        orderMetaMap.put(ApplicationConstants.TOTAL_PREPAID, String.valueOf(paymentUpdate.getTotalPrepaid()));
        orderMetaMap.put(ApplicationConstants.TOTAL_COD, String.valueOf(paymentUpdate.getTotalCod()));
        orderMetaMap.put(ApplicationConstants.PARTIAL_PAYMENT, String.valueOf(paymentUpdate.isPartialPayment()));
        orderMetaMap.put(ApplicationConstants.PAYMENT_GATEWAY, paymentUpdate.getPaymentGateway());
        return orderMetaMap;
    }

    public WmsOrderEvent getWmsOrderEventForPaymentUpdate(OrderDto order, ShipmentEvent shipmentEvent, ShipmentDto shipment) {
        WmsOrderEvent wmsOrderEvent = new WmsOrderEvent();
        wmsOrderEvent.setShipmentEvent(shipmentEvent);
        wmsOrderEvent.setOrderDto(order);
        wmsOrderEvent.setShipmentDto(shipment);

        return wmsOrderEvent;
    }
}
