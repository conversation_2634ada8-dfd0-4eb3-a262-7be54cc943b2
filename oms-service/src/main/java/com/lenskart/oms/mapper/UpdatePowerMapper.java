package com.lenskart.oms.mapper;

import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.OrderItemPowerDto;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.enums.ShipmentEvent;
import com.lenskart.oms.request.WmsOrderEvent;
import com.lenskart.oms.enums.ItemType;
import com.lenskart.oms.enums.OrderEventType;
import com.lenskart.oms.request.LineItemPayloadEvent;
import com.lenskart.oms.request.OmsOrderEvent;
import com.lenskart.oms.response.AssignPowerDetails;
import com.lenskart.order.interceptor.dto.createorder.LineItemPayload;
import com.lenskart.order.interceptor.dto.createorder.LineItemPropertiesPayload;
import com.lenskart.order.interceptor.dto.createorder.PowerPayload;
import com.lenskart.order.interceptor.dto.createorder.Relationship;
import com.lenskart.order.interceptor.request.UpdatePowerRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.lenskart.oms.constants.ApplicationConstants.*;

@Component
public class UpdatePowerMapper {

    public OmsOrderEvent updatePowerRequestToOmsOrderEvent(UpdatePowerRequest updatePowerRequest, String requestType, String incrementId){
        OmsOrderEvent omsOrderEvent = new OmsOrderEvent();
        omsOrderEvent.setEventType(StringUtils.isNotBlank(requestType) ? OrderEventType.valueOf(requestType) : null);

        OrderDto orderDto = new OrderDto();
        Long incrementIdLong = Objects.isNull(incrementId) ? null : Long.valueOf(incrementId);
        orderDto.setIncrementId(incrementIdLong);
        omsOrderEvent.setIncrementId(incrementIdLong);

        if(Objects.isNull(updatePowerRequest) || CollectionUtils.isEmpty(updatePowerRequest.getLineItems()))
            return omsOrderEvent;

        orderDto.setJunoOrderId(Objects.isNull(updatePowerRequest.getOrderId()) ? null : Long.valueOf(updatePowerRequest.getOrderId()));
        List<LineItemPayloadEvent> lineItemPayloadEventList = populateLineItemPayloadEvent(updatePowerRequest.getLineItems());

        omsOrderEvent.setLineItemPayloadEventList(lineItemPayloadEventList);
        omsOrderEvent.setOrderDto(orderDto);
        return omsOrderEvent;
    }

    public List<LineItemPayloadEvent> populateLineItemPayloadEvent(List<LineItemPayload> lineItemPayloads) {
        List<LineItemPayloadEvent> lineItemPayloadEventList = new ArrayList<>();

        if(CollectionUtils.isEmpty(lineItemPayloads))
            return lineItemPayloadEventList;

        for(LineItemPayload lineItemPayload:lineItemPayloads){
            LineItemPayloadEvent lineItemPayloadEvent = new LineItemPayloadEvent();
            populateLineItemPayload(lineItemPayload, lineItemPayloadEvent);

            if(Objects.nonNull(lineItemPayload.getLineItemProperties())){
                LineItemPropertiesPayload lineItemProperty = lineItemPayload.getLineItemProperties();
                populateLineItemProperty(lineItemPayloadEvent, lineItemProperty);
                lineItemPayloadEvent.setLineItemPropertiesNull(false);

                if(Objects.nonNull(lineItemProperty.getRelationship())){
                    populateLineItemRelationShip(lineItemPayloadEvent,lineItemProperty.getRelationship());
                    lineItemPayloadEvent.setRelationShipNull(false);
                }
            }
            lineItemPayloadEventList.add(lineItemPayloadEvent);
        }
        return lineItemPayloadEventList;
    }


    public OrderItemPowerDto populateExistingPowerFromRequested(AssignPowerDetails powerWisePid, OrderItemPowerDto orderItemPowerDto, OrderItemDto orderItemDto, String powerType) {
        OrderItemPowerDto existingOrderItemPower = orderItemDto.getItemPower();

        Float sph = 0.00f;
        Float cyl = 0.00f;
        String ap = "0.00";
        Integer axis = null;
        Integer effectiveDia = null;

        if (orderItemPowerDto != null) {
            if (!POWER_TYPE_ZERO_POWER.equals(powerType) && !POWER_TYPE_LENS_ONLY_ZERO_POWER.equals(powerType)) {
                sph = Float.parseFloat(orderItemPowerDto.getSph());
                cyl = (StringUtils.isNotBlank(orderItemPowerDto.getCyl()) && !orderItemPowerDto.getCyl().contains(STATIC_CALL_ME_FOR_POWER)) ? Float.parseFloat(orderItemPowerDto.getCyl()) : 0.00f;
                ap = (StringUtils.isNotBlank(orderItemPowerDto.getAp()) && !orderItemPowerDto.getAp().contains(STATIC_CALL_ME_FOR_POWER)) ? orderItemPowerDto.getAp() : "0.00";
                axis = (StringUtils.isNotBlank(orderItemPowerDto.getAxis()) && !orderItemPowerDto.getAxis().contains(STATIC_CALL_ME_FOR_POWER)) ? Integer.parseInt(orderItemPowerDto.getAxis()) : null;
                effectiveDia = (StringUtils.isNotBlank(orderItemPowerDto.getEffectiveDia()) && !orderItemPowerDto.getEffectiveDia().contains(STATIC_CALL_ME_FOR_POWER)) ? Integer.parseInt(orderItemPowerDto.getEffectiveDia()) : null;

                if (cyl > 0) {
                    sph = sph + cyl;
                    cyl = -cyl;
                    if(null != axis) {
                        if (axis <= 90) {
                            axis = axis + 90;
                        } else {
                            axis = axis - 90;
                        }
                    }
                }

                if (POWER_TYPE_BIFOCAL.equals(powerType) && effectiveDia == null) {
                    if (sph > 0) {
                        effectiveDia = 65;
                    } else {
                        effectiveDia = 70;
                    }
                }
            }

            if ((cyl == 0.00 || cyl == 0) && (StringUtils.isBlank(orderItemPowerDto.getAxis()))) {
                axis = 0;
            }

            existingOrderItemPower.setPd((StringUtils.isNotBlank(orderItemPowerDto.getPd()) && !orderItemPowerDto.getPd().contains(STATIC_CALL_ME_FOR_POWER)) ? orderItemPowerDto.getPd() : null);
            existingOrderItemPower.setLensHeight((StringUtils.isNotBlank(orderItemPowerDto.getLensHeight()) && !orderItemPowerDto.getLensHeight().contains(STATIC_CALL_ME_FOR_POWER)) ? orderItemPowerDto.getLensHeight() : null);
            existingOrderItemPower.setLensWidth((StringUtils.isNotBlank(orderItemPowerDto.getLensWidth()) && !orderItemPowerDto.getLensWidth().contains(STATIC_CALL_ME_FOR_POWER)) ? orderItemPowerDto.getLensWidth() : null);
            existingOrderItemPower.setThickness((StringUtils.isNotBlank(orderItemPowerDto.getThickness()) && !orderItemPowerDto.getThickness().contains(STATIC_CALL_ME_FOR_POWER)) ? orderItemPowerDto.getThickness() : null);
            existingOrderItemPower.setNearPD((StringUtils.isNotBlank(orderItemPowerDto.getNearPD()) && !orderItemPowerDto.getNearPD().contains(STATIC_CALL_ME_FOR_POWER)) ? orderItemPowerDto.getNearPD() : null);
            existingOrderItemPower.setEdgeDistance((StringUtils.isNotBlank(orderItemPowerDto.getEdgeDistance()) && !orderItemPowerDto.getEdgeDistance().contains(STATIC_CALL_ME_FOR_POWER)) ? orderItemPowerDto.getEdgeDistance() : null);
            existingOrderItemPower.setTopDistance((StringUtils.isNotBlank(orderItemPowerDto.getTopDistance()) && !orderItemPowerDto.getTopDistance().contains(STATIC_CALL_ME_FOR_POWER)) ? orderItemPowerDto.getTopDistance() : null);
            existingOrderItemPower.setBottomDistance((StringUtils.isNotBlank(orderItemPowerDto.getBottomDistance()) && !orderItemPowerDto.getBottomDistance().contains(STATIC_CALL_ME_FOR_POWER)) ? orderItemPowerDto.getBottomDistance() : null);
            existingOrderItemPower.setTint(StringUtils.isNotBlank(orderItemPowerDto.getTint()) ? orderItemPowerDto.getTint() : null);
            existingOrderItemPower.setColor(StringUtils.isNotBlank(orderItemPowerDto.getColor()) ? orderItemPowerDto.getColor() : null);
            existingOrderItemPower.setBaseCurve((StringUtils.isNotBlank(orderItemPowerDto.getBaseCurve()) && !orderItemPowerDto.getBaseCurve().contains(STATIC_CALL_ME_FOR_POWER)) ? orderItemPowerDto.getBaseCurve() : null);
            existingOrderItemPower.setSpvd((StringUtils.isNotBlank(orderItemPowerDto.getSpvd()) && !orderItemPowerDto.getSpvd().contains(STATIC_CALL_ME_FOR_POWER)) ? orderItemPowerDto.getSpvd() : null);
            existingOrderItemPower.setBoxQty(orderItemPowerDto.getBoxQty() != null ? orderItemPowerDto.getBoxQty() : 0);
        }

        existingOrderItemPower.setSph(String.valueOf(sph));
        existingOrderItemPower.setCyl(String.valueOf(cyl));
        existingOrderItemPower.setAp(ap);
        existingOrderItemPower.setAxis(axis != null ? String.valueOf(axis) : String.valueOf(0));
        existingOrderItemPower.setEffectiveDia(effectiveDia != null ? String.valueOf(effectiveDia) : null);
        existingOrderItemPower.setPackageName(powerWisePid.getPackageName());
        existingOrderItemPower.setCoatingOid(powerWisePid.getCoatingOid());
        existingOrderItemPower.setPowerType(powerType);
        if (ItemType.LEFT_LENS.equals(orderItemDto.getItemType())) {
            existingOrderItemPower.setProductId(powerWisePid.getLeftPowerWisePID());
            existingOrderItemPower.setLensIndex(powerWisePid.getLeftLensIndex());
            existingOrderItemPower.setShellId(powerWisePid.getLeftLensShellId());
        } else if (ItemType.RIGHT_LENS.equals(orderItemDto.getItemType())) {
            existingOrderItemPower.setProductId(powerWisePid.getRightPowerWisePID());
            existingOrderItemPower.setLensIndex(powerWisePid.getRightLensIndex());
            existingOrderItemPower.setShellId(powerWisePid.getRightLensShellId());
        }

        return existingOrderItemPower;
    }

    private void populateLineItemRelationShip(LineItemPayloadEvent lineItemPayloadEvent, Relationship relationship) {
        lineItemPayloadEvent.setRelationType(relationship.getType());
        lineItemPayloadEvent.setRelationName(relationship.getName());
        lineItemPayloadEvent.setRelationTelephone(relationship.getTelephone());
        lineItemPayloadEvent.setRelationPhoneCode(relationship.getPhoneCode());
        lineItemPayloadEvent.setRelationGender(relationship.getGender());
        lineItemPayloadEvent.setRelationYearOfBirth(relationship.getYearOfBirth());
    }

    private static void populateLineItemPayload(LineItemPayload lineItemPayload, LineItemPayloadEvent lineItemPayloadEvent) {
        lineItemPayloadEvent.setLineItemId(lineItemPayload.getLineItemId());
        lineItemPayloadEvent.setParentLineItemId(lineItemPayload.getParentLineItemId());
        lineItemPayloadEvent.setSku(lineItemPayload.getSku());
        lineItemPayloadEvent.setOid(lineItemPayload.getOid());
        lineItemPayloadEvent.setItemType(StringUtils.isNotBlank(lineItemPayload.getItemType()) ? ItemType.valueOf(lineItemPayload.getItemType()) : null);
        lineItemPayloadEvent.setQuantity(lineItemPayload.getQuantity());
        lineItemPayloadEvent.setProductName(lineItemPayload.getProductName());
        lineItemPayloadEvent.setProductDeliveryType(lineItemPayload.getProductDeliveryType());
        lineItemPayloadEvent.setShipToStoreRequired(lineItemPayload.getShipToStoreRequired());
        lineItemPayloadEvent.setIsLocalFittingRequired(lineItemPayload.getIsLocalFittingRequired());
        lineItemPayloadEvent.setLocalFittingFacility(lineItemPayload.getLocalFittingFacility());
        lineItemPayloadEvent.setVirtualFacilityCode(lineItemPayload.getVirtualFacilityCode());
        lineItemPayloadEvent.setContactLensId(lineItemPayload.getContactLensId());
        lineItemPayloadEvent.setItemTotal(lineItemPayload.getItemTotal());
    }

    private void populateLineItemProperty(LineItemPayloadEvent lineItemPayloadEvent, LineItemPropertiesPayload lineItemProperty) {
        lineItemPayloadEvent.setPowerType(lineItemProperty.getPowerType());
        lineItemPayloadEvent.setType(lineItemProperty.getType());
        lineItemPayloadEvent.setPatientName(lineItemProperty.getPatientName());
        lineItemPayloadEvent.setNotes(lineItemProperty.getNotes());
        lineItemPayloadEvent.setLeft(powerPayloadToItemPowerDtoMapper(lineItemProperty.getLeft(),lineItemProperty));
        lineItemPayloadEvent.setRight(powerPayloadToItemPowerDtoMapper(lineItemProperty.getRight(),lineItemProperty));
        lineItemPayloadEvent.setIsPower(lineItemProperty.getIsPower());
        lineItemPayloadEvent.setPrescriptionUrl(lineItemProperty.getPrescriptionUrl());
        lineItemPayloadEvent.setUpdatedAt(lineItemProperty.getUpdatedAt());
        lineItemPayloadEvent.setSource(lineItemProperty.getSource());
        lineItemPayloadEvent.setSalesCode(lineItemProperty.getSalesCode());
    }

    private OrderItemPowerDto powerPayloadToItemPowerDtoMapper(PowerPayload power, LineItemPropertiesPayload lineItemProperty) {
        if (Objects.nonNull(power)) {
            OrderItemPowerDto itemPowerDto = new OrderItemPowerDto();
            itemPowerDto.setPowerType(StringUtils.isNotBlank(lineItemProperty.getPowerType()) ? lineItemProperty.getPowerType() : null);
            itemPowerDto.setProductId(Objects.isNull(power.getSku()) ? null : Long.valueOf(power.getSku()));
            itemPowerDto.setSph(StringUtils.isNotBlank(power.getSph()) ? power.getSph() : org.apache.commons.lang3.StringUtils.EMPTY);
            itemPowerDto.setCyl(StringUtils.isNotBlank(power.getCyl()) ? power.getCyl() : org.apache.commons.lang3.StringUtils.EMPTY);
            itemPowerDto.setAxis(StringUtils.isNotBlank(power.getAxis()) ? power.getAxis() : null);
            itemPowerDto.setPd(StringUtils.isNotBlank(power.getPd()) ? power.getPd() : org.apache.commons.lang3.StringUtils.EMPTY);
            itemPowerDto.setEffectiveDia(StringUtils.isNotBlank(power.getEffectiveDia()) ? power.getEffectiveDia() : org.apache.commons.lang3.StringUtils.EMPTY);
            itemPowerDto.setNearPD(StringUtils.isNotBlank(power.getNearPD()) ? power.getNearPD() : org.apache.commons.lang3.StringUtils.EMPTY);
            itemPowerDto.setTopDistance(StringUtils.isNotBlank(power.getTopDistance()) ? power.getTopDistance() : org.apache.commons.lang3.StringUtils.EMPTY);
            itemPowerDto.setBottomDistance(StringUtils.isNotBlank(power.getBottomDistance()) ? power.getBottomDistance() : org.apache.commons.lang3.StringUtils.EMPTY);
            itemPowerDto.setAp(power.getAp());
            itemPowerDto.setLensHeight(power.getLensHeight());
            itemPowerDto.setLensWidth(power.getLensWidth());
            itemPowerDto.setThickness(power.getThickness());
            itemPowerDto.setEdgeDistance(power.getEdgeDistance());
            itemPowerDto.setTint(power.getTint());
            itemPowerDto.setColor(power.getColor());
            itemPowerDto.setBaseCurve(power.getBaseCurve());
            itemPowerDto.setSpvd(power.getSpvd());
            itemPowerDto.setBoxQty(power.getBoxQty());

            return itemPowerDto;
        }

        return null;
    }

    public WmsOrderEvent getWmsOrderEventForPaymentUpdate(OrderDto order, ShipmentEvent updatePower, ShipmentDto shipmentDto) {
        WmsOrderEvent wmsOrderEvent = new WmsOrderEvent();
        wmsOrderEvent.setShipmentEvent(updatePower);
        wmsOrderEvent.setOrderDto(order);
        wmsOrderEvent.setShipmentDto(shipmentDto);

        return wmsOrderEvent;
    }

    public List<LineItemPayload> mapOmsLineItemObjectToInterceptorLineItemPayload(List<LineItemPayloadEvent> lineItemPayloadEventList) {
        List<LineItemPayload> lineItems = new ArrayList<>();
        for(LineItemPayloadEvent omsLineItem : lineItemPayloadEventList){
            LineItemPayload lineItem = new LineItemPayload();

            lineItem.setLineItemId(omsLineItem.getLineItemId());
            lineItem.setParentLineItemId(omsLineItem.getParentLineItemId());
            lineItem.setSku(omsLineItem.getSku());
            lineItem.setOid(omsLineItem.getOid());
            lineItem.setItemType(omsLineItem.getItemType().name());
            lineItem.setQuantity(omsLineItem.getQuantity());
            lineItem.setProductName(omsLineItem.getProductName());
            lineItem.setProductDeliveryType(omsLineItem.getProductDeliveryType());
            lineItem.setShipToStoreRequired(omsLineItem.getShipToStoreRequired());
            lineItem.setLocalFittingFacility(omsLineItem.getLocalFittingFacility());
            lineItem.setIsLocalFittingRequired(omsLineItem.getIsLocalFittingRequired());
            lineItem.setLocalFittingFacility(omsLineItem.getLocalFittingFacility());
            lineItem.setVirtualFacilityCode(omsLineItem.getVirtualFacilityCode());
            lineItem.setContactLensId(omsLineItem.getContactLensId());
            lineItem.setItemTotal(omsLineItem.getItemTotal());

            if(!omsLineItem.isLineItemPropertiesNull()){
                LineItemPropertiesPayload properties = new LineItemPropertiesPayload();

                properties.setPowerType(omsLineItem.getPowerType());
                properties.setType(omsLineItem.getType());
                properties.setPatientName(omsLineItem.getPatientName());
                properties.setNotes(omsLineItem.getNotes());
                properties.setIsPower(omsLineItem.getIsPower());
                properties.setPrescriptionUrl(omsLineItem.getPrescriptionUrl());
                properties.setUpdatedAt(omsLineItem.getUpdatedAt());
                properties.setSource(omsLineItem.getSource());
                properties.setSalesCode(omsLineItem.getSalesCode());

                if (Objects.nonNull(omsLineItem.getLeft())
                        && (StringUtils.isNotBlank(omsLineItem.getLeft().getSph())
                            || StringUtils.isNotBlank(omsLineItem.getLeft().getAxis())
                            || StringUtils.isNotBlank(omsLineItem.getLeft().getCyl())
                            || StringUtils.isNotBlank(omsLineItem.getLeft().getAp())
                            || StringUtils.isNotBlank(omsLineItem.getLeft().getPd()))
                ) {
                    PowerPayload left = getPowerPayLoad(omsLineItem.getLeft());
                    properties.setLeft(left);
                }

                if (Objects.nonNull(omsLineItem.getRight())
                        && (StringUtils.isNotBlank(omsLineItem.getRight().getSph())
                            || StringUtils.isNotBlank(omsLineItem.getRight().getAxis())
                            || StringUtils.isNotBlank(omsLineItem.getRight().getCyl())
                            || StringUtils.isNotBlank(omsLineItem.getRight().getAp())
                            || StringUtils.isNotBlank(omsLineItem.getRight().getPd()))
                ) {
                    PowerPayload right = getPowerPayLoad(omsLineItem.getRight());
                    properties.setRight(right);
                }

                if(!omsLineItem.isRelationShipNull()){
                    Relationship relationShip = new Relationship();
                    relationShip.setType(omsLineItem.getRelationType());
                    relationShip.setName(omsLineItem.getRelationName());
                    relationShip.setTelephone(omsLineItem.getRelationTelephone());
                    relationShip.setPhoneCode(omsLineItem.getRelationPhoneCode());
                    relationShip.setGender(omsLineItem.getRelationGender());
                    relationShip.setYearOfBirth(omsLineItem.getRelationYearOfBirth());

                    properties.setRelationship(relationShip);
                }

                lineItem.setLineItemProperties(properties);
            }

            lineItems.add(lineItem);
        }
        return lineItems;
    }

    private PowerPayload getPowerPayLoad(OrderItemPowerDto power) {
        PowerPayload payload = new PowerPayload();

        payload.setSku(power.getProductId() != null ? power.getProductId().toString() : null);
        payload.setSph(StringUtils.isNotBlank(power.getSph()) ? power.getSph() : org.apache.commons.lang3.StringUtils.EMPTY);
        payload.setCyl(StringUtils.isNotBlank(power.getCyl()) ? power.getCyl() : org.apache.commons.lang3.StringUtils.EMPTY);
        payload.setAxis(StringUtils.isNotBlank(power.getAxis()) ? power.getAxis() : null);
        payload.setPd(StringUtils.isNotBlank(power.getPd()) ? power.getPd() : org.apache.commons.lang3.StringUtils.EMPTY);
        payload.setEffectiveDia(StringUtils.isNotBlank(power.getEffectiveDia()) ? power.getEffectiveDia() : org.apache.commons.lang3.StringUtils.EMPTY);
        payload.setNearPD(StringUtils.isNotBlank(power.getNearPD()) ? power.getNearPD() : org.apache.commons.lang3.StringUtils.EMPTY);
        payload.setTopDistance(StringUtils.isNotBlank(power.getTopDistance()) ? power.getTopDistance() : org.apache.commons.lang3.StringUtils.EMPTY);
        payload.setBottomDistance(StringUtils.isNotBlank(power.getBottomDistance()) ? power.getBottomDistance() : org.apache.commons.lang3.StringUtils.EMPTY);
        payload.setAp(power.getAp());
        payload.setLensHeight(power.getLensHeight());
        payload.setLensWidth(power.getLensWidth());
        payload.setThickness(power.getThickness());
        payload.setEdgeDistance(power.getEdgeDistance());
        payload.setTint(power.getTint());
        payload.setColor(power.getColor());
        payload.setBaseCurve(power.getBaseCurve());
        payload.setSpvd(power.getSpvd());
        payload.setBoxQty(power.getBoxQty());

        return payload;
    }
}
