package com.lenskart.oms.mapper;

import com.lenskart.oms.dto.OrderAddressDto;
import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.enums.AddressType;
import com.lenskart.oms.enums.OrderEventType;
import com.lenskart.oms.enums.ShipmentEvent;
import com.lenskart.oms.request.OmsOrderEvent;
import com.lenskart.oms.request.WmsOrderEvent;
import com.lenskart.order.interceptor.dto.createorder.BillingAddressPayload;
import com.lenskart.order.interceptor.dto.createorder.ShippingAddressPayload;
import com.lenskart.order.interceptor.request.UpdateAddressRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import static com.lenskart.oms.constants.ApplicationConstants.DEFAULT_OMS_USER;

@Component
public class UpdateAddressTransformer {
    public OmsOrderEvent updateAddressRequestToOmsOrderEvent(UpdateAddressRequest request, String requestType, String orderId) {
        OmsOrderEvent omsOrderEvent = new OmsOrderEvent();
        omsOrderEvent.setIncrementId(Long.valueOf(orderId));
        omsOrderEvent.setEventType(OrderEventType.valueOf(requestType));
        omsOrderEvent.setShipmentDtoList(populateShipmentDto(request));
        return omsOrderEvent;
    }

    private List<ShipmentDto> populateShipmentDto(UpdateAddressRequest request) {
        ShipmentDto shipmentDto = new ShipmentDto();

        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        if(request.getBillingAddress() != null && !request.getBillingAddress().isEmpty()){
            BillingAddressPayload billingAddressPayload = request.getBillingAddress().get(0);
            OrderAddressDto billingAddress = new OrderAddressDto();

            billingAddress.setAddressType(AddressType.BILLING);
            billingAddress.setCity(billingAddressPayload.getCity());
            billingAddress.setCountry(billingAddressPayload.getCountryCode() == null
                    ? StringUtils.EMPTY
                    : billingAddressPayload.getCountryCode()
            );
            billingAddress.setCountryCode(billingAddressPayload.getCountryCode() == null
                    ? StringUtils.EMPTY
                    : billingAddressPayload.getCountryCode().substring(0, 2)
            );
            billingAddress.setCreatedAt(timestamp);
            billingAddress.setEmail(billingAddressPayload.getEmail());
            billingAddress.setCity(billingAddressPayload.getCity());
            billingAddress.setFirstName(billingAddressPayload.getFirstName());
            billingAddress.setLastName(billingAddressPayload.getLastName());
            billingAddress.setPostcode(billingAddressPayload.getPostCode().trim());
            billingAddress.setRegion(billingAddressPayload.getState());
            String street = "";
            if(billingAddressPayload.getStreet1() != null){
                street = billingAddressPayload.getStreet1() + " ";
            }
            if(billingAddressPayload.getStreet2()!=null){
                street =  street.concat(billingAddressPayload.getStreet2());
            }
            billingAddress.setStreet(street);
            billingAddress.setTelephone(billingAddressPayload.getPhone());
            billingAddress.setUpdatedBy(DEFAULT_OMS_USER);
            billingAddress.setCreatedBy(DEFAULT_OMS_USER);
            shipmentDto.setBillingAddress(billingAddress);
        }

        if(request.getShippingAddress() != null && !request.getShippingAddress().isEmpty()){
            ShippingAddressPayload shippingAddressPayload = request.getShippingAddress().get(0);

            OrderAddressDto shipmentAddress = new OrderAddressDto();
            shipmentAddress.setAddressType(AddressType.SHIPPING);
            shipmentAddress.setCity(shippingAddressPayload.getCity());
            shipmentAddress.setCountry(shippingAddressPayload.getCountryCode() == null
                    ? StringUtils.EMPTY
                    : shippingAddressPayload.getCountryCode()
            );
            shipmentAddress.setCountryCode(shippingAddressPayload.getCountryCode() == null
                    ? StringUtils.EMPTY
                    : shippingAddressPayload.getCountryCode().substring(0, 2)
            );
            shipmentAddress.setCreatedAt(timestamp);
            shipmentAddress.setEmail(shippingAddressPayload.getEmail());
            shipmentAddress.setFirstName(shippingAddressPayload.getFirstName());
            shipmentAddress.setLastName(shippingAddressPayload.getLastName());
            shipmentAddress.setPostcode(shippingAddressPayload.getPostCode().trim());
            shipmentAddress.setRegion(shippingAddressPayload.getState());
            String street = "";
            if(shippingAddressPayload.getStreet1() != null){
                street = shippingAddressPayload.getStreet1() + " ";
            }
            if(shippingAddressPayload.getStreet2()!=null){
                street =  street.concat(shippingAddressPayload.getStreet2());
            }
            shipmentAddress.setStreet(street);
            shipmentAddress.setTelephone(shippingAddressPayload.getPhone());
            shipmentAddress.setCreatedBy(DEFAULT_OMS_USER);
            shipmentAddress.setUpdatedBy(DEFAULT_OMS_USER);

            shipmentDto.setShippingAddress(shipmentAddress);
        }

        List<ShipmentDto> shipmentDtoList = new ArrayList<>();
        shipmentDtoList.add(shipmentDto);
        return shipmentDtoList;

    }

    public WmsOrderEvent getWmsOrderEventFromShipmentDto(ShipmentDto shipmentDto, OrderDto orderDto, ShipmentEvent shipmentEvent) {
        WmsOrderEvent wmsOrderEvent = new WmsOrderEvent();

        List<OrderItemDto> orderItemDtoList = shipmentDto.getOrderItems();
        shipmentDto.setOrderItems(orderItemDtoList);

        wmsOrderEvent.setOrderDto(orderDto);
        wmsOrderEvent.setShipmentDto(shipmentDto);
        wmsOrderEvent.setShipmentEvent(shipmentEvent);

        return wmsOrderEvent;
    }

}
