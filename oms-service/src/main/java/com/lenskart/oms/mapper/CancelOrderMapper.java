package com.lenskart.oms.mapper;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.enums.*;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.request.BankDetails;
import com.lenskart.oms.request.CancelOrderRequest;
import com.lenskart.oms.request.OmsOrderEvent;
import com.lenskart.oms.service.OrderItemService;
import com.lenskart.oms.service.OrderService;
import com.lenskart.oms.utils.OmsCommonUtil;
import com.lenskart.order.interceptor.dto.cancelorder.BankDetail;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.lenskart.oms.constants.ApplicationConstants.UW_ITEM_ID_UPDATE_EVENT_TYPE_CANCELLATION;

@Component
@Setter(onMethod__ = {@Autowired})
public class CancelOrderMapper {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    private OrderService orderService;
    private OmsCommonUtil omsCommonUtil;
    private OrderItemService orderItemService;

    public OmsOrderEvent cancelOrderRequestToOmsOrderEvent(com.lenskart.order.interceptor.request.CancelOrderRequest cancelOrderRequest, String requestType, String incrementId) throws ApplicationException {
        OmsOrderEvent omsOrderEvent = new OmsOrderEvent();
        omsOrderEvent.setEventType(OrderEventType.valueOf(requestType));
        omsOrderEvent.setIncrementId(Long.valueOf(incrementId));

        CancelOrderRequest omsCancelOrderRequest = populateCancelOrderDto(cancelOrderRequest, Long.valueOf(incrementId));
        logger.info("[cancelOrderRequestToOmsOrderEvent] omsCancelOrderRequest: {}", omsCancelOrderRequest);
        omsOrderEvent.setCancelOrderRequest(omsCancelOrderRequest);
        return omsOrderEvent;
    }

    private CancelOrderRequest populateCancelOrderDto(com.lenskart.order.interceptor.request.CancelOrderRequest cancelOrderRequestFomInterceptor, Long incrementId) throws ApplicationException {
        CancelOrderRequest cancelOrderRequest = new CancelOrderRequest();
        cancelOrderRequest.setCancelRefund(cancelOrderRequestFomInterceptor.isCancelRefund());
        if (StringUtils.hasLength(cancelOrderRequestFomInterceptor.getCancellationType())) {
            cancelOrderRequest.setCancellationType(CancellationType.valueOf(cancelOrderRequestFomInterceptor.getCancellationType().toUpperCase()));
        } else {
            cancelOrderRequestFomInterceptor.setCancellationType("full_cancellation");
            cancelOrderRequest.setCancellationType(CancellationType.FULL_CANCELLATION);
        }
        cancelOrderRequest.setCancellationSubType(cancelOrderRequestFomInterceptor.getCancellationSubType());
        cancelOrderRequest.setBankDetail(populateBankDetail(cancelOrderRequestFomInterceptor.getBankDetail()));
        cancelOrderRequest.setLkCountry(cancelOrderRequestFomInterceptor.getLkCountry() != null
                ? LKCountry.valueOf(cancelOrderRequestFomInterceptor.getLkCountry().name())
                : null);
        cancelOrderRequest.setIsWalletRefundEligible(cancelOrderRequestFomInterceptor.isWalletRefundEligible());
        cancelOrderRequest.setOrderItems(validateAndGetOrderItemsForPartialCancel(cancelOrderRequestFomInterceptor, incrementId));
        cancelOrderRequest.setCsOrderId(cancelOrderRequestFomInterceptor.getCsOrderId());
        cancelOrderRequest.setPaymentMethod(cancelOrderRequestFomInterceptor.getPaymentMethod());
        cancelOrderRequest.setInitiatedBy(cancelOrderRequestFomInterceptor.getInitiatedBy());
        cancelOrderRequest.setSource(cancelOrderRequestFomInterceptor.getSource());
        cancelOrderRequest.setReasonDetail(cancelOrderRequestFomInterceptor.getReasonDetail());
        cancelOrderRequest.setReasonId(cancelOrderRequestFomInterceptor.getReasonId());
        cancelOrderRequest.setUnicomCode(cancelOrderRequestFomInterceptor.getUnicomCode());
        cancelOrderRequest.setMagentoItemId(cancelOrderRequestFomInterceptor.getMagentoItemId());

        return cancelOrderRequest;
    }

    private List<OrderItemDto> validateAndGetOrderItemsForPartialCancel(com.lenskart.order.interceptor.request.CancelOrderRequest cancelOrderRequestFomInterceptor, Long incrementId) throws ApplicationException {
        if (CancellationType.PARTIAL_CANCELLATION.name().equalsIgnoreCase(cancelOrderRequestFomInterceptor.getCancellationType().toUpperCase())) {
            if (cancelOrderRequestFomInterceptor.getMagentoItemId() == null) {
                logger.info("[validateAndGetOrderItemsForPartialCancel] Magento Item Id is NULL in the partial cancellation request {}", cancelOrderRequestFomInterceptor);
                throw new ApplicationException("Magento Item Id is NULL in the partial cancellation request for order " + incrementId);
            }

            List<OrderItemDto> orderItemDtoList = orderItemService.search("magentoItemId.eq:" + cancelOrderRequestFomInterceptor.getMagentoItemId());
            if (CollectionUtils.isEmpty(orderItemDtoList)) {
                logger.info("[validateAndGetOrderItemsForPartialCancel] No items found for Magento Item Id {} and request {}", cancelOrderRequestFomInterceptor);
                throw new ApplicationException("No items found for Magento Item Id " + cancelOrderRequestFomInterceptor.getMagentoItemId() + " and order " + incrementId);
            }

            return orderItemDtoList;
        } else {
            OrderDto orderDto = orderService.findByIncrementId(incrementId);
            return orderDto.getOrderItems()
                    .stream()
                    .filter(orderItemDto -> !OrderItemStatus.CANCELLED.equals(orderItemDto.getItemStatus()))
                    .collect(Collectors.toList());
        }
    }

    private BankDetails populateBankDetail(BankDetail bankDetail) {
        BankDetails bankDetails = new BankDetails();

        if (bankDetail != null) {
            bankDetails.setBankName(bankDetail.getBankName());
            bankDetails.setAccountNumber(bankDetail.getAccountNumber());
            bankDetails.setBeneficiaryName(bankDetail.getBeneficiaryName());
            bankDetails.setIfscCode(bankDetail.getIfscCode());
        }

        return bankDetails;
    }
}
