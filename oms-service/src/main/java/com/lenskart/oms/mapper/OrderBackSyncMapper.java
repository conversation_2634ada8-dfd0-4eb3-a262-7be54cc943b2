package com.lenskart.oms.mapper;

import com.lenskart.oms.dto.OrderBackSyncTrackingDto;
import com.lenskart.oms.enums.BackSyncEntityType;
import com.lenskart.oms.enums.BackSyncEventName;
import com.lenskart.oms.enums.BackSyncEventStatus;
import com.lenskart.oms.enums.BackSyncSystem;
import com.lenskart.oms.request.OmsOrderEvent;
import com.lenskart.oms.request.OrderBackSyncRequest;
import com.lenskart.oms.request.OrderEvent;
import com.lenskart.oms.request.OrderOpsOrderEvent;
import org.springframework.stereotype.Component;

import static com.lenskart.oms.constants.ApplicationConstants.DEFAULT_OMS_USER;
import static com.lenskart.oms.enums.BackSyncEntityType.INCREMENT_ID;

@Component
public class OrderBackSyncMapper {

    public OrderOpsOrderEvent populateOrderOpsOrderEvent(Long orderId, BackSyncEventName eventName, OrderEvent orderEvent, OrderBackSyncRequest orderBackSyncRequest) {
        OrderOpsOrderEvent orderOpsOrderEvent = new OrderOpsOrderEvent();
        orderOpsOrderEvent.setOrderId(orderId);
        orderOpsOrderEvent.setEventName(eventName);
        orderOpsOrderEvent.setOrderEvent(orderEvent);
        orderOpsOrderEvent.setOrderBackSyncRequest(orderBackSyncRequest);

        return orderOpsOrderEvent;
    }

    public OrderBackSyncTrackingDto populateBackSyncTrackingDto(BackSyncEventName eventName, String entityId, BackSyncEventStatus eventStatus, BackSyncSystem backSyncSystem, BackSyncEntityType causingEntity, String message) {
        OrderBackSyncTrackingDto orderBackSyncTrackingDto = new OrderBackSyncTrackingDto();
        orderBackSyncTrackingDto.setEventName(eventName);
        orderBackSyncTrackingDto.setEntityId(entityId);
        orderBackSyncTrackingDto.setEntityType(causingEntity);
        orderBackSyncTrackingDto.setEventStatus(eventStatus);
        orderBackSyncTrackingDto.setBackSyncSystem(backSyncSystem);
        orderBackSyncTrackingDto.setCreatedBy(DEFAULT_OMS_USER);
        orderBackSyncTrackingDto.setUpdatedBy(DEFAULT_OMS_USER);
        orderBackSyncTrackingDto.setMessage(message);
        return orderBackSyncTrackingDto;
    }

    public OrderBackSyncRequest populateOrderBackSyncRequest(OmsOrderEvent omsOrderEvent) {
        OrderBackSyncRequest orderBackSyncRequest = new OrderBackSyncRequest();
        orderBackSyncRequest.setOrderDto(omsOrderEvent.getOrderDto());
        orderBackSyncRequest.setShipmentDtoList(omsOrderEvent.getShipmentDtoList());

        return orderBackSyncRequest;
    }
}
