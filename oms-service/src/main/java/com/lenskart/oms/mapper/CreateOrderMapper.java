package com.lenskart.oms.mapper;

import com.google.gson.Gson;
import com.lenskart.core.model.Product;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.connector.CatalogOpsConnector;
import com.lenskart.oms.connector.JunoConnector;
import com.lenskart.oms.connector.OrderOpsConnector;
import com.lenskart.oms.connector.POSConnector;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.dto.*;
import com.lenskart.oms.entity.DistributorOrders;
import com.lenskart.oms.enums.*;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.exception.MarginPriceException;
import com.lenskart.oms.model.Action;
import com.lenskart.oms.model.PackageProductMapping;
import com.lenskart.oms.request.*;
import com.lenskart.oms.response.AssignPowerDetails;
import com.lenskart.oms.response.AssignPowerResponse;
import com.lenskart.oms.response.AssignPowerResponseWrapper;
import com.lenskart.oms.service.DistributorOrdersService;
import com.lenskart.oms.service.OrderItemService;
import com.lenskart.oms.utils.LegalOwnerUtil;
import com.lenskart.oms.utils.OmsCommonUtil;
import com.lenskart.oms.utils.OmsTransitionUtil;
import com.lenskart.optima.enums.ShipmentType;
import com.lenskart.order.interceptor.dto.createorder.*;
import com.lenskart.order.interceptor.enums.createorder.State;
import com.lenskart.order.interceptor.enums.createorder.Status;
import com.lenskart.order.interceptor.request.CreateOrderRequest;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.lenskart.oms.enums.OrderSubType.DISTRIBUTOR_JIT_ORDER;
import static java.math.BigDecimal.ZERO;

import static com.lenskart.oms.constants.ApplicationConstants.*;

@Component
@Setter(onMethod__ = {@Autowired})
public class CreateOrderMapper {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    protected Logger logger;

    @Value("${smart.glass.classification.id:26021}")
    @Setter(AccessLevel.NONE)
    private Integer smartGlassClassificationId;

    @Value("#{'${ojos.facility.codes}'.split(',')}")
    @Setter(AccessLevel.NONE)
    private List<String> ojosFacilityCodes;

    @Value("#{'${fitting.classification.ids}'.split(',')}")
    @Setter(AccessLevel.NONE)
    private List<String> fittingClassifications;

    @Value("#{'${zeroPower.classification.ids}'.split(',')}")
    @Setter(AccessLevel.NONE)
    private List<String> zeroPowerClassifications;

    @Value("${get.margin.price.from.juno.for.intl:true}")
    @Setter(AccessLevel.NONE)
    public boolean getMarginPriceFromJunoForIntl;

    @Value("${so.default.facilities}")
    @Setter(AccessLevel.NONE)
    private Pattern soFacilities;
    @Value("${nexs.shipment.DeliveryDate.interval:2}")
    @Setter(AccessLevel.NONE)
    private Integer intervalDeliveryDate;
    @Value("${insurance.price.enabled:false}")
    @Setter(AccessLevel.NONE)
    public boolean insurancePriceEnabled;

    private OrderItemService orderItemService;
    private DistributorOrdersService distributorOrdersService;
    private OmsTransitionUtil omsTransitionUtil;
    private CatalogOpsConnector catalogOpsConnector;
    private JunoConnector junoConnector;
    private UpdatePowerMapper updatePowerMapper;
    private OmsCommonUtil commonUtil;
    private LegalOwnerUtil legalOwnerUtil;
    private POSConnector posConnector;
    private OrderOpsConnector orderOpsConnector;
    private final List<Integer> LENS_ONLY_PRODUCT_IDS = Arrays.asList(96504, 96505, 96506, 103509, 237271, 237281, 237282);


    public OmsOrderEvent createOrderRequestToOmsOrderEvent(CreateOrderRequest request, String requestType, String incrementId, OmsOrderEvent omsOrderEvent) throws Exception {
        omsOrderEvent.setIncrementId(Long.valueOf(incrementId));
        omsOrderEvent.setEventType(OrderEventType.valueOf(requestType));

        Action transitionAction;
        if ((State.NEW.equals(request.getState())
                && (Status.PROCESSING_NEW.equals(request.getStatus()) || Status.PENDING.equals(request.getStatus())))
                || Status.PROCESSING_CONFIRMATION_PENDING.equals(request.getStatus())
        ) {
            transitionAction = omsTransitionUtil.getTransitionActionByOperationAndStatus(
                    EventToOperationMap.PENDING.getOperation(), OrderItemStatus.DEFAULT
            );
        } else {
            transitionAction = omsTransitionUtil.getTransitionActionByOperationAndStatus(
                    EventToOperationMap.CREATED.getOperation(), OrderItemStatus.DEFAULT
            );
        }

        OrderDto orderDto = populateOrderDto(request, incrementId, transitionAction);
        List<ShipmentDto> shipmentDtoList = populateShipmentDto(request, transitionAction, omsOrderEvent, orderDto);

        omsOrderEvent.setOrderDto(orderDto);
        omsOrderEvent.setShipmentDtoList(shipmentDtoList);
        omsOrderEvent.setLineItemPayloadEventList(updatePowerMapper.populateLineItemPayloadEvent(request.getLineItems()));
        return omsOrderEvent;
    }

    private OrderDto populateOrderDto(CreateOrderRequest orderPayload, String incrementId, Action transitionAction) throws Exception {
        OrderDto orderDto = new OrderDto();
        orderDto.setIncrementId(Long.valueOf(incrementId));
        orderDto.setJunoOrderId(Long.valueOf(orderPayload.getOrderId()));
        if (orderPayload.getSourceName().equals(DISTRIBUTOR_ORDER_SOURCE_NAME)) {
                orderDto.setOrderType(OrderType.DISTRIBUTOR_ORDER);

            DistributorOrders distributorOrder = distributorOrdersService.findByIncrementId(incrementId);
            if (soFacilities.matcher(distributorOrder.getFacility()).matches())
                orderDto.setOrderSubType(OrderSubType.SUPER_ORDER);
            if(!StringUtils.isEmpty(orderPayload.getLineItems().get(0).getOid())) {
                orderDto.setOrderSubType(DISTRIBUTOR_JIT_ORDER);
            }
            else
                orderDto.setOrderSubType(OrderSubType.DISTRIBUTOR_ORDER);
        }
        orderDto.setOrderStatus(transitionAction.getOrderStatus().getOrderStatus());
        orderDto.setOrderSubStatus(transitionAction.getOrderStatus().getOrderSubStatus());
        orderDto.setOrderDate(orderPayload.getCreatedAt());
        orderDto.setLkCountry(orderPayload.getLkCountry().name());
        orderDto.setCurrencyCode(orderPayload.getCurrencyCode().name());
        orderDto.setCustomerId(orderPayload.getCustomerPayload().getId());
        orderDto.setMerchantId(orderPayload.getMerchantId());
        orderDto.setOrderSource(orderPayload.getSourceName());
        orderDto.setStoreId(Long.valueOf(orderPayload.getStoreId()));
        orderDto.setExchangeFlag(orderPayload.getExchangeFlag() == 1);
        if (orderPayload.getExchangeItemId() != null) {
            orderDto.setExchangeItemId(Long.valueOf(orderPayload.getExchangeItemId()));
        }
        orderDto.setPaymentCaptured(orderPayload.getIsPaymentCaptured());
        orderDto.setPaymentMethod(orderPayload.getPayments().getPaymentMethod());
        orderDto.setOrderItems(populateOrderItemDto(orderPayload, transitionAction, orderDto));
        orderDto.setOrderMetaData(populateOrderMetaData(orderPayload));
        orderDto.setCreatedBy(DEFAULT_OMS_USER);
        orderDto.setUpdatedBy(DEFAULT_OMS_USER);
        return orderDto;
    }

    private List<OrderMetaDataDto> populateOrderMetaData(CreateOrderRequest orderPayload) {
        HashMap<String, String> orderMetaMap = populateOrderMetaMap(orderPayload);
        List<OrderMetaDataDto> orderMetaDataList = new ArrayList<>();

        for (Map.Entry<String, String> orderMetaEntry : orderMetaMap.entrySet()) {
            OrderMetaDataDto orderMetaData = new OrderMetaDataDto();

            orderMetaData.setEntityKey(orderMetaEntry.getKey());
            orderMetaData.setEntityValue(orderMetaEntry.getValue());
            orderMetaData.setCreatedBy(DEFAULT_OMS_USER);
            orderMetaData.setUpdatedBy(DEFAULT_OMS_USER);

            orderMetaDataList.add(orderMetaData);
        }

        return orderMetaDataList;
    }

    private List<OrderItemDto> populateOrderItemDto(CreateOrderRequest orderPayload, Action transitionAction, OrderDto orderDto) throws Exception {
        List<OrderItemDto> orderItemsList = new ArrayList<>();
        Map<Long, Long> productPackageMap = new HashMap<>();
        Map<Long, Double> priceDetails = new HashMap<>();
        Map<Long, String> magentoItemIdProductDeliveryTypeMapping = orderPayload.getLineItems().stream().filter(item -> StringUtils.isNotBlank(item.getProductDeliveryType()))
                .collect(Collectors.toMap(LineItemPayload::getLineItemId, LineItemPayload::getProductDeliveryType, (existing, replacement) -> existing));
        logger.info("[populateOrderItemDto] orderId {} magentoItemIdProductDeliveryTypeMapping {}",orderPayload.getOrderId(),new Gson().toJson(magentoItemIdProductDeliveryTypeMapping));
        for (LineItemPayload orderItem : orderPayload.getLineItems()) {
            OrderItemDto orderItemDto = new OrderItemDto();

            logger.info("[CreateOrderMapper -> populateOrderItemDto] itemType:{} barcode:{} orderId:{} is barcode update req :{}",
                    orderItem.getItemType(),orderItem.getItemBarcode(),orderPayload.getOrderId(), isBarcodeUpdateReq(orderItem));
            orderItemDto.setMagentoItemId(orderItem.getLineItemId());
            if (StringUtils.isNotBlank(orderItem.getItemBarcode()) &&
                    isBarcodeUpdateReq(orderItem)){
                orderItemDto.setItemBarcode(orderItem.getItemBarcode());
            }
            if (orderItem.getParentLineItemId() != null) {
                orderItemDto.setParentMagentoItemId(Long.valueOf(orderItem.getParentLineItemId()));
            }
            orderItemDto.setItemStatus(transitionAction.getItemStatus().getItemStatus());
            orderItemDto.setItemSubStatus(transitionAction.getItemStatus().getItemSubStatus());

            if (ItemType.CONTACT_LENS.name().equalsIgnoreCase(orderItem.getItemType())
                    && orderItem.getContactLensId() != null
                    && orderItem.getContactLensId() >= 90000000
            ) {
                orderItemDto.setProductId(Long.valueOf(orderItem.getContactLensId()));
            } else {
                orderItemDto.setProductId(Long.valueOf(orderItem.getSku()));
            }

            orderItemDto.setItemType(getItemType(orderItem.getItemType()));
            Product product = getProductFromCatalogOps(orderItem);
            orderItemDto.setFittingType(isFittingRequired(product, orderItem));
            ProcessingType processingType;
            processingType = getProcessingType(orderItemDto, orderDto);
            orderItemDto.setFulfillmentType(getFulfillmentType(orderItem));
            //orderItemDto.setLocalFitting(orderItem.getIsLocalFittingRequired());

            String channel = getChannel(orderPayload, orderDto);
            if (!Channel.JOHNJACOBS.name().equalsIgnoreCase(channel)
                    && !Channel.OJOS.name().equalsIgnoreCase(channel)
                    && orderItem.getProductDeliveryType() != null
                    && commonUtil.isNonWarehouseProcessingOrder(orderItem)
            ) {
                orderItemDto.setChannel(Channel.CUSTOM);
            } else {
                orderItemDto.setChannel(Channel.valueOf(channel));
            }

            orderItemDto.setNavChannel(NavChannel.valueOf(orderItemService.getNavChannel(getNavChannelRequest(orderItem, orderPayload, orderDto))));
            if (!StringUtils.isEmpty(orderItem.getProductDeliveryType())) {
                orderItemDto.setProductDeliveryType(ProductDeliveryType.valueOf(orderItem.getProductDeliveryType()));
            }
            orderItemDto.setShippingDestinationType(getShippingDestination(orderPayload.getLineItems()));
            orderItemDto.setDeliveryPriority(DeliveryPriority.valueOf(orderPayload.getDeliveryType().name()));
            orderItemDto.setPromisedShipDate(setDispatchDate(orderItem));
            orderItemDto.setPromisedDeliveryDate(setDeliveryDate(orderItem));
            orderItemDto.setDeliveryStoreId(String.valueOf(orderPayload.getStoreId()));
            orderItemDto.setCreatedBy(DEFAULT_OMS_USER);
            orderItemDto.setUpdatedBy(DEFAULT_OMS_USER);

            orderItemDto.setItemPower(populateItemPowerDto(orderItem, processingType, orderItemDto.getProductId()));
            orderItemDto.setOrderItemPrice(populateOrderItemPricesDto(orderPayload, orderItem));
            if(OrderType.DISTRIBUTOR_ORDER.equals(orderDto.getOrderType())) {
                orderItemDto.setOrderItemMetaData(new ArrayList<>());
            } else {
                orderItemDto.setOrderItemMetaData(populateOrderItemMetaData(orderItem, processingType));
            }
            if (ItemType.getFrameItemTypes().contains(orderItemDto.getItemType().name())) {
                if (productPackageMap.containsKey(orderItemDto.getProductId())) {
                    orderItemDto.setProductId(productPackageMap.get(orderItemDto.getProductId()));
                } else {
                    List<Integer> productIds = catalogOpsConnector.getFramePIdByPackage(orderItemDto.getProductId());
                    Long productId = CollectionUtils.isEmpty(productIds) ? orderItemDto.getProductId() : Long.valueOf(productIds.get(productIds.size() - 1));
                    orderItemDto.setProductId(productId);
                    productPackageMap.put(orderItemDto.getProductId(), productId);
                }
            }

            if (LENS_ONLY_PRODUCT_IDS.contains(Math.toIntExact(orderItemDto.getProductId()))) {
                orderItemDto.setProductId(Long.valueOf(catalogOpsConnector.getProductIdForLensOnlyItem(Math.toIntExact(orderItemDto.getProductId()), Math.toIntExact(orderItemDto.getMagentoItemId()), orderItemDto.getItemBarcode())));
            }

            orderItemDto.setItemPower(populateItemPowerDto(orderItem, processingType, orderItemDto.getProductId()));
            OrderItemDto virtualOrderItemDto = null;
            if ((orderItem.getLineItemProperties() != null
                    && orderItem.getLineItemProperties().getIsPower() != 0)
            ) {
                if ((SUNGLASSES_CLASSIFICATION_ID == product.getClassification() || EYEFRAME_CLASSIFICATION_ID == product.getClassification())
                        || (orderItem.getLineItemProperties().getIsPower() == 1 && ZERO_POWER_CLASSIFICATION_ID == product.getClassification())
                        || smartGlassClassificationId == product.getClassification()
                ) {
                    orderItemsList.add(addTemporaryLensItem(orderItemDto, Long.valueOf(TEMP_LEFT_LENS_PID), ItemType.LEFT_LENS, orderItemDto.getFulfillmentType()));
                    orderItemsList.add(addTemporaryLensItem(orderItemDto, Long.valueOf(TEMP_RIGHT_LENS_PID), ItemType.RIGHT_LENS, orderItemDto.getFulfillmentType()));
                }

            }

            if (orderItem.getProductDeliveryType() != null && ProductDeliveryType.B2B.name().equalsIgnoreCase(orderItem.getProductDeliveryType())) {
                virtualOrderItemDto = saveB2BItems(orderItemDto, orderDto,magentoItemIdProductDeliveryTypeMapping,priceDetails);
            }
            orderItemsList.add(orderItemDto);
            if (!ObjectUtils.isEmpty(virtualOrderItemDto)) {
                orderItemsList.add(virtualOrderItemDto);
            }
            updatePowerWiseIdForContactLens(orderPayload, orderDto, orderItem, orderItemDto);
            if(orderPayload.getSourceName().equals(DISTRIBUTOR_ORDER_SOURCE_NAME)) {
                if(orderDto.getOrderSubType().equals(DISTRIBUTOR_JIT_ORDER)) {
                    orderItemDto.setFittingId(Long.valueOf(orderItem.getOid()));
                }
            }
        }
        if (orderPayload.isInsuranceOrder() && insurancePriceEnabled) {
            storeInsurancePriceData(orderPayload, orderItemsList);
        }
        return orderItemsList;
    }

    private void updatePowerWiseIdForContactLens(CreateOrderRequest orderPayload, OrderDto orderDto, LineItemPayload orderItem, OrderItemDto orderItemDto) throws Exception {
        if (!OrderType.DISTRIBUTOR_ORDER.equals(orderDto.getOrderType())  &&  ItemType.CONTACT_LENS.name().equals(orderItem.getItemType())) {
            CatalogOpsPowerWiseIdRequest catalogOpsPowerWiseIdRequest = populatePowerWiseIdRequest(orderItemDto, orderDto, orderPayload, orderItem);
            catalogOpsPowerWiseIdRequest.setFacilityCode(orderPayload.getFacilityCode());
            catalogOpsPowerWiseIdRequest.setPowerRequestType(CONTACT_LENS_TYPE_POWER);
            AssignPowerResponseWrapper assignPowerResponseWrapper = catalogOpsConnector.fetchPowerWisePidsFromCatalogOps(catalogOpsPowerWiseIdRequest);
            if (Objects.isNull(assignPowerResponseWrapper.getData())) {
                logger.error("[populateOrderItemDto] Catalog response object has null/empty for assignPowerResponseWrapper.getData() for increment id: {}", orderDto.getIncrementId());
                throw new Exception("Catalog response object has null/empty for assignPowerResponseWrapper.getData() for increment id: " + orderDto.getIncrementId());
            }
            AssignPowerResponse powerResponse = assignPowerResponseWrapper.getData();
            List<AssignPowerDetails> lineItemWisePids = powerResponse.getPowerWiseIdDetails();
            for (AssignPowerDetails assignPowerDetails : lineItemWisePids) {
                if (orderItem.getLineItemId().equals(assignPowerDetails.getMagentoItemId())) {
                    if(!StringUtils.isEmpty(assignPowerDetails.getCoatingOid())) {
                        orderItemDto.setProductId(Long.valueOf(assignPowerDetails.getCoatingOid()));
                    }
                }
            }
        }
    }

    private void storeInsurancePriceData(CreateOrderRequest orderPayload, List<OrderItemDto> orderItemsList) {
        logger.info("[ItemWisePriceServiceImpl][storeInsurancePriceData] Storing for {}", orderPayload.getOrderId());
        HashMap<Long, List<OrderItemDto>> packageOrderItemsList = new HashMap<>();
        for (OrderItemDto orderItemDto : orderItemsList) {
            if (packageOrderItemsList.containsKey(orderItemDto.getMagentoItemId())) {
                packageOrderItemsList.get(orderItemDto.getMagentoItemId()).add(orderItemDto);
            } else {
                List<OrderItemDto> tempOrderItemDtoList = new ArrayList<>();
                tempOrderItemDtoList.add(orderItemDto);
                packageOrderItemsList.put(orderItemDto.getMagentoItemId(), tempOrderItemDtoList);
            }
        }
        for (LineItemPayload lineItemPayload : orderPayload.getLineItems()) {
            calculateAndUpdateInsurancePrices(packageOrderItemsList, lineItemPayload);
        }
    }

    private void calculateAndUpdateInsurancePrices(HashMap<Long, List<OrderItemDto>> packageOrderItemsList, LineItemPayload lineItemPayload) {
        if (lineItemPayload.getInsurancePriceDetails() != null) {
            InsurancePriceDetails insurancePriceDetails = lineItemPayload.getInsurancePriceDetails();
            BigDecimal totalDiscount = BigDecimal.ZERO;
            if (!CollectionUtils.isEmpty(insurancePriceDetails.getDiscounts())) {
                for (DiscountPayload discountPayload : insurancePriceDetails.getDiscounts()) {
                    if (DiscountType.IMPLICIT.name().equalsIgnoreCase(discountPayload.getType())) {
                        totalDiscount = totalDiscount.add(BigDecimal.valueOf(discountPayload.getAmount()));
                    }
                }
            }
            BigDecimal framePrice = BigDecimal.valueOf(insurancePriceDetails.getFramePrice());
            BigDecimal lensPrice = BigDecimal.valueOf(insurancePriceDetails.getLensPrice());
            BigDecimal packageCost = framePrice.add(lensPrice);
            BigDecimal framePriceRatio = BigDecimal.ZERO;
            BigDecimal lensPriceRatio = BigDecimal.ZERO;
            if (packageCost.intValue() > 0) {
                framePriceRatio = framePrice.divide(packageCost, 4, RoundingMode.CEILING);
                lensPriceRatio = lensPrice.divide(packageCost, 4, RoundingMode.CEILING);
            }
            BigDecimal frameCost = framePrice.subtract(totalDiscount.multiply(framePriceRatio));
            BigDecimal lensCost = lensPrice.subtract(totalDiscount.multiply(lensPriceRatio));
            List<OrderItemDto> orderItemDtoList = packageOrderItemsList.get(lineItemPayload.getLineItemId());
            if (frameCost.compareTo(ZERO) <= 0 && lensCost.compareTo(ZERO) <= 0) {
                frameCost = BigDecimal.ONE;
                logger.info("[calculateAndUpdateInsurancePrices] setting itemTotal to one for {}", lineItemPayload.getLineItemId());
            }
            for (OrderItemDto orderItemDto : orderItemDtoList) {
                if (Objects.isNull(orderItemDto.getParentMagentoItemId())) {
                    orderItemDto.getOrderItemPrice().setItemTotal(frameCost.setScale(4, RoundingMode.CEILING).doubleValue());
                } else {
                    orderItemDto.getOrderItemPrice().setItemTotal(lensCost.divide(BigDecimal.valueOf(2), 4, RoundingMode.CEILING).doubleValue());
                }
            }
        } else {
            logger.info("[ItemWisePriceServiceImpl][calculateAndUpdateInsurancePrices]No Insurance Payload present for {} ", lineItemPayload.getLineItemId());
        }
    }

    private NavChannelRequest getNavChannelRequest(LineItemPayload orderItem, CreateOrderRequest orderPayload, OrderDto orderDto) {
        NavChannelRequest navChannelRequest = new NavChannelRequest();
        navChannelRequest.setProductDeliveryType(orderItem.getProductDeliveryType());
        navChannelRequest.setFacilityCode(orderPayload.getFacilityCode());
        navChannelRequest.setStoreId(orderPayload.getStoreId());
        navChannelRequest.setStoreType(orderPayload.getStoreType());
        navChannelRequest.setIsBulkOrder(orderPayload.getIsBulkOrder());
        navChannelRequest.setIsDistributorOrder(commonUtil.isDistributorOrder(orderDto));
        navChannelRequest.setGetIsDistributorJitOrder(commonUtil.isDistributorJitOrder(orderDto));
        navChannelRequest.setClientOrg(orderPayload.getClientOrg());
        return navChannelRequest;
    }
    private boolean isBarcodeUpdateReq(LineItemPayload orderItem) {
        if(commonUtil.isNonWarehouseProcessingOrder(orderItem)){
            logger.info("[isBarcodeUpdateReq] it is a nexs store order and frame :{}",
                    ItemType.getFrameItemTypes().contains(orderItem.getItemType()));
            if(orderItem.getIsLocalFittingRequired()){
                return ItemType.getFrameItemTypes().contains(orderItem.getItemType());
            }else {
                return true;
            }
        }
        return false;
    }


    private OrderItemDto addTemporaryLensItem(OrderItemDto orderItemDto, Long tempLensPid, ItemType itemType, FulfillmentType fulfillmentType) {
        OrderItemDto tempLensOrderItem = new OrderItemDto();
        BeanUtils.copyProperties(orderItemDto, tempLensOrderItem);
        tempLensOrderItem.setProductId(tempLensPid);
        tempLensOrderItem.setFittingType(FittingType.NOT_REQD);
        tempLensOrderItem.setItemType(itemType);
        tempLensOrderItem.setParentMagentoItemId(orderItemDto.getMagentoItemId());

        populateItemMetaDataForTempLensItem(orderItemDto, tempLensOrderItem);
        populateItemPriceForTempLensItem(orderItemDto, tempLensOrderItem);
        tempLensOrderItem.setItemPower(populateEmptyPowerDto(tempLensPid));
        if (FulfillmentType.LOCAL_FITTING.equals(fulfillmentType)) {
            tempLensOrderItem.setItemBarcode(null);
        }

        return tempLensOrderItem;
    }

    private void populateItemMetaDataForTempLensItem(OrderItemDto orderItemDto, OrderItemDto tempLensOrderItem) {
        List<OrderItemMetaDataDto> orderItemMetaDataDtoList = new ArrayList<>();
        for (OrderItemMetaDataDto frameOrderItemMetaDataDto : orderItemDto.getOrderItemMetaData()) {
            OrderItemMetaDataDto orderItemMetaDataDto = new OrderItemMetaDataDto();
            BeanUtils.copyProperties(frameOrderItemMetaDataDto, orderItemMetaDataDto);

            orderItemMetaDataDtoList.add(orderItemMetaDataDto);
        }

        for (OrderItemMetaDataDto orderItemMetaDataDto : orderItemMetaDataDtoList) {
            if (ORDER_ITEM_META_KEY_PROCESSING_TYPE.equalsIgnoreCase(orderItemMetaDataDto.getEntityKey())) {
                orderItemMetaDataDto.setEntityValue(ProcessingType.PL.name());
            }
        }

        tempLensOrderItem.setOrderItemMetaData(orderItemMetaDataDtoList);
    }

    private void populateItemPriceForTempLensItem(OrderItemDto orderItemDto, OrderItemDto tempLensOrderItem) {
        OrderItemPricesDto orderItemPricesDto = new OrderItemPricesDto();
        BeanUtils.copyProperties(orderItemDto.getOrderItemPrice(), orderItemPricesDto);
        orderItemPricesDto.setItemGrandTotal(0.0);
        orderItemPricesDto.setItemTotalAfterDiscount(0.0);
        orderItemPricesDto.setScDiscount(0.0);
        orderItemPricesDto.setGvDiscount(0.0);
        orderItemPricesDto.setGiftCardDiscount(0.0);
        orderItemPricesDto.setPrepaidDiscount(0.0);
        orderItemPricesDto.setCouponDiscount(0.0);
        orderItemPricesDto.setLenskartDiscount(0.0);
        orderItemPricesDto.setLenskartPlusDiscount(0.0);
        orderItemPricesDto.setImplicitDiscount(0.0);
        orderItemPricesDto.setExchangeDiscount(0.0);
        orderItemPricesDto.setFcDiscount(0.0);
        orderItemPricesDto.setShippingCharges(0.0);
        orderItemPricesDto.setItemTotal(0.0);
        orderItemPricesDto.setInsuranceBenefitDiscount(0.0);

        tempLensOrderItem.setOrderItemPrice(orderItemPricesDto);
    }

    private Product getProductFromCatalogOps(LineItemPayload orderItem) throws ApplicationException {
        Product product;
        try {
            if (LENS_PACKAGE_PID.equals(Long.valueOf(orderItem.getSku()))
                    || LENS_COATING_PID.equals(Long.valueOf(orderItem.getSku()))
            ) {
                product = new Product();
            } else {
                product = catalogOpsConnector.findProductDetailsByProductId(Long.valueOf(orderItem.getSku()));
            }
        } catch (Exception exception) {
            logger.error("[CreateOrderMapper -> populateOrderItemDto] catalog-ops called failed for product id {}", orderItem.getSku(), exception);
            throw new ApplicationException("catalog-ops called failed for product id " + orderItem.getSku() + " with exception " + exception, null);
        }
        return product;
    }

    private FulfillmentType getFulfillmentType(LineItemPayload orderItem) {
        if (orderItem.getIsLocalFittingRequired()) {
            return FulfillmentType.LOCAL_FITTING;
        }

        return null;
    }

    private OrderItemPowerDto populateItemPowerDto(LineItemPayload orderItem, ProcessingType processingType, Long productId) {
        if (orderItem.getLineItemProperties() != null) {
            OrderItemPowerDto itemPowerDto = new OrderItemPowerDto();
            LineItemPropertiesPayload powerBasePayload = orderItem.getLineItemProperties();
            PowerPayload powerPayload = new PowerPayload();

            itemPowerDto.setPowerType(powerBasePayload.getPowerType());
            itemPowerDto.setProductId(productId);

            if (powerBasePayload.getLeft() != null) {
                powerPayload = powerBasePayload.getLeft();
            } else if (powerBasePayload.getRight() != null) {
                powerPayload = powerBasePayload.getRight();
            }

            if (powerPayload != null) {
                itemPowerDto.setShellId(Long.valueOf(orderItem.getSku()));
                itemPowerDto.setSph(StringUtils.isNotBlank(powerPayload.getSph()) && !powerPayload.getSph().contains(STATIC_CALL_ME_FOR_POWER) ? String.valueOf(Float.valueOf(powerPayload.getSph())) : StringUtils.EMPTY);
                itemPowerDto.setCyl(StringUtils.isNotBlank(powerPayload.getCyl()) && !powerPayload.getCyl().contains(STATIC_CALL_ME_FOR_POWER) ? String.valueOf(Float.valueOf(powerPayload.getCyl())) : StringUtils.EMPTY);
                itemPowerDto.setAxis(StringUtils.isNotBlank(powerPayload.getAxis()) && !powerPayload.getAxis().contains(STATIC_CALL_ME_FOR_POWER) ? powerPayload.getAxis() : null);
                itemPowerDto.setAp(StringUtils.isNotBlank(powerPayload.getAp()) && !powerPayload.getAp().contains(STATIC_CALL_ME_FOR_POWER) ? powerPayload.getAp() : "0.00");
                itemPowerDto.setPd(StringUtils.isNotBlank(powerPayload.getPd()) && !powerPayload.getPd().contains(STATIC_CALL_ME_FOR_POWER) ? String.valueOf(Float.valueOf(powerPayload.getPd())) : StringUtils.EMPTY);
                itemPowerDto.setLensHeight(StringUtils.isNotBlank(powerPayload.getLensHeight()) && !powerPayload.getLensHeight().contains(STATIC_CALL_ME_FOR_POWER) ? powerPayload.getLensHeight() : StringUtils.EMPTY);
                itemPowerDto.setLensWidth(StringUtils.isNotBlank(powerPayload.getLensWidth()) && !powerPayload.getLensWidth().contains(STATIC_CALL_ME_FOR_POWER) ? powerPayload.getLensWidth() : StringUtils.EMPTY);
                itemPowerDto.setEffectiveDia(StringUtils.isNotBlank(powerPayload.getEffectiveDia()) && !powerPayload.getEffectiveDia().contains(STATIC_CALL_ME_FOR_POWER) ? String.valueOf(Integer.valueOf(powerPayload.getEffectiveDia())) : StringUtils.EMPTY);
                itemPowerDto.setNearPD(StringUtils.isNotBlank(powerPayload.getNearPD()) && !powerPayload.getNearPD().contains(STATIC_CALL_ME_FOR_POWER) ? String.valueOf(Float.valueOf(powerPayload.getNearPD())) : StringUtils.EMPTY);
                itemPowerDto.setEdgeDistance(StringUtils.isNotBlank(powerPayload.getEdgeDistance()) && !powerPayload.getEdgeDistance().contains(STATIC_CALL_ME_FOR_POWER) ? powerPayload.getEdgeDistance() : StringUtils.EMPTY);
                itemPowerDto.setTopDistance(StringUtils.isNotBlank(powerPayload.getTopDistance()) && !powerPayload.getTopDistance().contains(STATIC_CALL_ME_FOR_POWER) ? String.valueOf(Integer.valueOf(powerPayload.getTopDistance())) : StringUtils.EMPTY);
                itemPowerDto.setBottomDistance(StringUtils.isNotBlank(powerPayload.getBottomDistance()) && !powerPayload.getBottomDistance().contains(STATIC_CALL_ME_FOR_POWER) ? String.valueOf(Integer.valueOf(powerPayload.getBottomDistance())) : StringUtils.EMPTY);
                itemPowerDto.setLensPackageType(getLensPackageType(orderItem.getItemType(), itemPowerDto.getPowerType(), processingType));
                itemPowerDto.setPatientName(powerBasePayload.getPatientName());
                itemPowerDto.setPatientComments(powerBasePayload.getNotes());
                itemPowerDto.setPrescriptionUrl(powerBasePayload.getPrescriptionUrl());
                itemPowerDto.setCreatedBy(DEFAULT_OMS_USER);
                itemPowerDto.setUpdatedBy(DEFAULT_OMS_USER);
            }

            itemPowerDto.setCreatedBy(DEFAULT_OMS_USER);
            itemPowerDto.setUpdatedBy(DEFAULT_OMS_USER);

            return itemPowerDto;
        }

        return populateEmptyPowerDto(productId);
    }


    private OrderItemPowerDto populateEmptyPowerDto(Long productId) {
        OrderItemPowerDto itemPowerDto = new OrderItemPowerDto();
        itemPowerDto.setPowerType(POWER_TYPE_ZERO_POWER);
        itemPowerDto.setProductId(productId);
        itemPowerDto.setShellId(productId);
        itemPowerDto.setSph(StringUtils.EMPTY);
        itemPowerDto.setCyl(StringUtils.EMPTY);
        itemPowerDto.setAxis("0");
        itemPowerDto.setAp(StringUtils.EMPTY);
        itemPowerDto.setPd(StringUtils.EMPTY);
        itemPowerDto.setLensHeight(StringUtils.EMPTY);
        itemPowerDto.setLensWidth(StringUtils.EMPTY);
        itemPowerDto.setEffectiveDia(StringUtils.EMPTY);
        itemPowerDto.setNearPD(StringUtils.EMPTY);
        itemPowerDto.setEdgeDistance(StringUtils.EMPTY);
        itemPowerDto.setTopDistance(StringUtils.EMPTY);
        itemPowerDto.setBottomDistance(StringUtils.EMPTY);
        itemPowerDto.setCreatedBy(DEFAULT_OMS_USER);
        itemPowerDto.setUpdatedBy(DEFAULT_OMS_USER);

        return itemPowerDto;
    }

    public String getLensPackageType(String itemType, String powerType, ProcessingType processingType) {
        String lensPackageType = null;

        if (ProcessingType.PL.equals(processingType)) {
            switch (powerType) {
                case POWER_TYPE_SINGLE_VISION:
                    if (ItemType.SUNGLASSES.name().equals(itemType)) {
                        lensPackageType = LENS_PACKAGE_TYPE_SUNGLASS;
                    } else {
                        lensPackageType = LENS_PACKAGE_TYPE_NORMAL;
                    }
                    break;

                case POWER_TYPE_BIFOCAL:
                    lensPackageType = LENS_PACKAGE_TYPE_BIFOCAL;
                    break;

                case POWER_TYPE_PROGRESSIVE:
                    lensPackageType = LENS_PACKAGE_TYPE_PROGRESSIVE;
                    break;

                case POWER_TYPE_ZERO_POWER:
                    lensPackageType = LENS_PACKAGE_TYPE_ZERO_POWER;
                    break;

                default:
                    break;
            }
        }

        logger.info("[getLensPackageType] lensPackageType is {} for itemType: {}, powerType: {}, processingType: {}", lensPackageType, itemType, powerType, processingType);
        return lensPackageType;
    }

    private OrderItemPricesDto populateOrderItemPricesDto(CreateOrderRequest orderPayload, LineItemPayload orderItem) {
        OrderItemPricesDto orderItemPricesDto = new OrderItemPricesDto();

        orderItemPricesDto.setFcDiscount(0.00);
        orderItemPricesDto.setItemTotalAfterDiscount(orderItem.getItemTotal() != null ? orderItem.getItemTotal() : 0.0);
        orderItemPricesDto.setItemGrandTotal(orderPayload.getGrandTotal() != null ? orderPayload.getGrandTotal() : 0.0);
        orderItemPricesDto.setShippingCharges(orderItem.getShippingCharges() != null ? orderItem.getShippingCharges() : 0.0);

        double tax = orderItem.getTaxCollected() == null ? 0 : orderItem.getTaxCollected();
        orderItemPricesDto.setTaxCollected(tax);

        if (!CollectionUtils.isEmpty(orderItem.getDiscounts())) {
            for (DiscountPayload discount : orderItem.getDiscounts()) {
                logger.info("[CreateOrderMapper][populateOrderItemPricesDto] : discountType :{} and value :{}",
                        discount.getType(), discount.getAmount());
                switch (DiscountType.valueOf(discount.getType())) {
                    case GIFTVOUCHER:
                        orderItemPricesDto.setGvDiscount(discount.getAmount() != null ? discount.getAmount() : 0.0);
                        break;

                    case STORECREDIT:
                        orderItemPricesDto.setScDiscount(discount.getAmount() != null ? discount.getAmount() : 0.0);
                        break;

                    case PREPAID:
                        orderItemPricesDto.setPrepaidDiscount(discount.getAmount() != null ? discount.getAmount() : 0.0);
                        break;

                    case COUPONCODE:
                        orderItemPricesDto.setCouponDiscount(discount.getAmount() != null ? discount.getAmount() : 0.0);
                        break;

                    case LENSKART:
                        orderItemPricesDto.setLenskartDiscount(discount.getAmount() != null ? discount.getAmount() : 0.0);
                        break;

                    case LENSKARTPLUS:
                        orderItemPricesDto.setLenskartPlusDiscount(discount.getAmount() != null ? discount.getAmount() : 0.0);
                        break;

                    case IMPLICIT:
                        orderItemPricesDto.setImplicitDiscount(discount.getAmount() != null ? discount.getAmount() : 0.0);
                        break;

                    case EXCHANGE:
                        orderItemPricesDto.setExchangeDiscount(discount.getAmount() != null ? discount.getAmount() : 0.0);
                        break;

                    case GIFTCARD:
                        orderItemPricesDto.setGiftCardDiscount(discount.getAmount() != null ? discount.getAmount() : 0.0);
                        break;

                    case INSURANCE_BENEFIT:
                        orderItemPricesDto.setInsuranceBenefitDiscount(discount.getAmount() != null ? discount.getAmount() : 0.0);
                        break;

                    default:
                        break;

                }
            }
        }

        orderItemPricesDto.setItemTotal(calculateItemTotal(orderItem,orderItemPricesDto));
        orderItemPricesDto.setCreatedBy(DEFAULT_OMS_USER);
        orderItemPricesDto.setUpdatedBy(DEFAULT_OMS_USER);

        return orderItemPricesDto;
    }

    private Double calculateItemTotal(LineItemPayload orderItem, OrderItemPricesDto orderItemPricesDto) {
        BigDecimal itemTotal = ZERO;
        BigDecimal discountedPrice = BigDecimal.valueOf(orderItem.getQuantity() * (orderItem.getDiscountedPrice()));
        BigDecimal grandTotal = BigDecimal.valueOf(orderItem.getQuantity() * orderItem.getRetailPrice());
        BigDecimal taxCollected = Objects.nonNull(orderItem.getTaxCollected()) ? BigDecimal.valueOf(orderItem.getTaxCollected()) : ZERO;
        BigDecimal itemDiscount = BigDecimal.valueOf(orderItem.getQuantity() * (orderItem.getRetailPrice() - orderItem.getDiscountedPrice()));
        logger.info("[calculateItemTotal]grandTotal={} , taxCollected={} , itemDiscount={} , discountedPrice={}", grandTotal, taxCollected, itemDiscount, discountedPrice);
        double prepaidDiscount = orderItemPricesDto.getPrepaidDiscount() == null ? 0.0 : orderItemPricesDto.getPrepaidDiscount();
        double gvDiscount = orderItemPricesDto.getGvDiscount() == null ? 0.0 : orderItemPricesDto.getGvDiscount();
        double lenskartDiscount = orderItemPricesDto.getLenskartDiscount() == null ? 0.0 : orderItemPricesDto.getLenskartDiscount();
        double lenskartPlusDiscount = orderItemPricesDto.getLenskartPlusDiscount() == null ? 0.0 : orderItemPricesDto.getLenskartPlusDiscount();
        logger.info("[calculateItemTotal] giftVoucherDiscount={} , prepaidDiscount={} , lenskartDiscount={} , lenskartPlusDiscount={}"
                , gvDiscount, prepaidDiscount, lenskartDiscount, lenskartPlusDiscount);
        itemTotal = grandTotal
                .add(taxCollected)
                .subtract(itemDiscount)
                .subtract(BigDecimal.valueOf(prepaidDiscount))
                .subtract(BigDecimal.valueOf(gvDiscount))
                .subtract(BigDecimal.valueOf(lenskartDiscount))
                .subtract(BigDecimal.valueOf(lenskartPlusDiscount));
        logger.info("[calculateItemTotal]itemTotal={} for {}", itemTotal, orderItem.getLineItemId());
        if (itemTotal.compareTo(ZERO) <= 0) {
            itemTotal = BigDecimal.ONE;
            logger.info("[calculateItemTotal] setting itemTotal to one for {}", orderItem.getLineItemId());
        }
        return itemTotal.setScale(2, RoundingMode.HALF_UP).doubleValue();
    }

    private List<OrderItemMetaDataDto> populateOrderItemMetaData(LineItemPayload orderItem, ProcessingType processingType) {
        HashMap<String, String> orderMetaMap = populateOrderItemMetaMap(orderItem, processingType);
        List<OrderItemMetaDataDto> orderItemMetaDataList = new ArrayList<>();

        for (Map.Entry<String, String> orderMetaEntry : orderMetaMap.entrySet()) {
            OrderItemMetaDataDto orderItemMetaData = new OrderItemMetaDataDto();

            orderItemMetaData.setEntityKey(orderMetaEntry.getKey());
            String entityValue = orderMetaEntry.getValue();
            orderItemMetaData.setEntityValue((StringUtils.isNotBlank(entityValue) && entityValue.length() > ApplicationConstants.ENTITY_VALUE_MAX_LENGTH) ? entityValue.substring(0, ENTITY_VALUE_MAX_LENGTH - 1) : entityValue);
            orderItemMetaData.setCreatedBy(DEFAULT_OMS_USER);
            orderItemMetaData.setUpdatedBy(DEFAULT_OMS_USER);

            orderItemMetaDataList.add(orderItemMetaData);
        }

        return orderItemMetaDataList;
    }

    private List<ShipmentDto> populateShipmentDto(CreateOrderRequest orderPayload, Action transitionAction, OmsOrderEvent omsOrderEvent, OrderDto orderDto) {
        List<ShipmentDto> shipmentDtoList = new ArrayList<>();
        Map<Long, String> shipmentLegalOwnerMap = new HashMap<>();
        for (LineItemPayload lineItemPayload : orderPayload.getLineItems()) {
            //caontact lens , solution >
            logger.info("line item id {} item type {}, facility {}, legal owner {} ", lineItemPayload.getLineItemId(), lineItemPayload.getItemType(), orderPayload.getFacilityCode(), orderPayload.getLegalOwnerCountry().getValue());
            if (Objects.isNull(lineItemPayload.getParentLineItemId())) {
                shipmentLegalOwnerMap.put(Long.valueOf(lineItemPayload.getLineItemId()), legalOwnerUtil.getLegalOwner(null, lineItemPayload.getHubCode(), orderPayload.getLegalOwnerCountry().getValue()));
            }
        }
        omsOrderEvent.setShipmentLegalOwnerMap(shipmentLegalOwnerMap);

        ShipmentDto shipmentDto = new ShipmentDto();

        shipmentDto.setWmsOrderCode(String.valueOf(orderPayload.getOrderId()));
        shipmentDto.setShipmentStatus(transitionAction.getShipmentStatus().getShipmentStatus());
        shipmentDto.setShipmentSubStatus(transitionAction.getShipmentStatus().getShipmentSubStatus());
//        shipmentDto.setExpectedShipDate();
//        shipmentDto.setExpectedDeliveryDate();

        shipmentDto.setShippingAddress(populateShippingAddress(orderPayload.getShippingAddress()));
        shipmentDto.setBillingAddress(populateBillingAddress(orderPayload.getBillingAddress()));
        shipmentDto.setShipmentMetaData(populateShipmentMetaData(orderPayload, orderDto));

        shipmentDto.setCreatedBy(DEFAULT_OMS_USER);
        shipmentDto.setUpdatedBy(DEFAULT_OMS_USER);
        shipmentDto.setLegalOwnerCountry(orderPayload.getLegalOwnerCountry().getValue());
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_YEAR, intervalDeliveryDate);
        Date d = cal.getTime();
        shipmentDto.setExpectedDeliveryDate(d);
        if (commonUtil.isDistributorOrder(orderDto)) {
            shipmentDto.setFacility(orderPayload.getFacilityCode());
        }

        shipmentDtoList.add(shipmentDto);

        return shipmentDtoList;
    }

    private OrderAddressDto populateBillingAddress(List<BillingAddressPayload> billingAddressList) {
        OrderAddressDto orderAddressDto = new OrderAddressDto();

        if (!CollectionUtils.isEmpty(billingAddressList)) {
            BillingAddressPayload billingAddressPayload = billingAddressList.get(0);

            orderAddressDto.setAddressType(AddressType.BILLING);

            StringBuilder streetBuilder = new StringBuilder();
            if (StringUtils.isNotEmpty(billingAddressPayload.getStreet1())) {
                streetBuilder.append(billingAddressPayload.getStreet1()).append(" ");
            }
            if (StringUtils.isNotEmpty(billingAddressPayload.getStreet2())) {
                streetBuilder.append(billingAddressPayload.getStreet2());
            }
            orderAddressDto.setStreet(streetBuilder.toString());

            orderAddressDto.setRegion(billingAddressPayload.getState());
            orderAddressDto.setCity(billingAddressPayload.getCity());
            orderAddressDto.setCountry(billingAddressPayload.getCountryCode() == null
                    ? StringUtils.EMPTY
                    : billingAddressPayload.getCountryCode()
            );
            orderAddressDto.setPostcode(billingAddressPayload.getPostCode().trim());
            orderAddressDto.setCountryCode(billingAddressPayload.getCountryCode() == null
                    ? StringUtils.EMPTY
                    : billingAddressPayload.getCountryCode().substring(0, 2)
            );
            orderAddressDto.setFirstName(billingAddressPayload.getFirstName());
            orderAddressDto.setLastName(billingAddressPayload.getLastName());
            orderAddressDto.setEmail(billingAddressPayload.getEmail());
            orderAddressDto.setTelephone(billingAddressPayload.getPhone());
            orderAddressDto.setUpdatedBy(DEFAULT_OMS_USER);
            orderAddressDto.setCreatedBy(DEFAULT_OMS_USER);
        }

        return orderAddressDto;
    }

    private OrderAddressDto populateShippingAddress(List<ShippingAddressPayload> shippingAddressList) {
        OrderAddressDto orderAddressDto = new OrderAddressDto();

        if (!CollectionUtils.isEmpty(shippingAddressList)) {
            ShippingAddressPayload shippingAddressPayload = shippingAddressList.get(0);

            orderAddressDto.setAddressType(AddressType.SHIPPING);

            StringBuilder streetBuilder = new StringBuilder();
            if (StringUtils.isNotEmpty(shippingAddressPayload.getStreet1())) {
                streetBuilder.append(shippingAddressPayload.getStreet1()).append(" ");
            }
            if (StringUtils.isNotEmpty(shippingAddressPayload.getStreet2())) {
                streetBuilder.append(shippingAddressPayload.getStreet2());
            }
            orderAddressDto.setStreet(streetBuilder.toString());

            orderAddressDto.setRegion(shippingAddressPayload.getState());
            orderAddressDto.setCity(shippingAddressPayload.getCity());
            orderAddressDto.setCountry(shippingAddressPayload.getCountryCode() == null
                    ? StringUtils.EMPTY
                    : shippingAddressPayload.getCountryCode()
            );
            orderAddressDto.setPostcode(shippingAddressPayload.getPostCode().trim());
            orderAddressDto.setCountryCode(shippingAddressPayload.getCountryCode() == null
                    ? StringUtils.EMPTY
                    : shippingAddressPayload.getCountryCode().substring(0, 2)
            );
            orderAddressDto.setFirstName(shippingAddressPayload.getFirstName());
            orderAddressDto.setLastName(shippingAddressPayload.getLastName());
            orderAddressDto.setEmail(shippingAddressPayload.getEmail());
            orderAddressDto.setTelephone(shippingAddressPayload.getPhone());
            orderAddressDto.setUpdatedBy(DEFAULT_OMS_USER);
            orderAddressDto.setCreatedBy(DEFAULT_OMS_USER);
        }

        return orderAddressDto;
    }

    private List<ShipmentMetaDataDto> populateShipmentMetaData(CreateOrderRequest orderPayload, OrderDto orderDto) {
        HashMap<String, String> orderMetaMap = populateShipmentMetaMap(orderPayload, orderDto);
        List<ShipmentMetaDataDto> shipmentMetaDataList = new ArrayList<>();

        for (Map.Entry<String, String> orderMetaEntry : orderMetaMap.entrySet()) {
            ShipmentMetaDataDto shipmentMetaData = new ShipmentMetaDataDto();

            shipmentMetaData.setEntityKey(orderMetaEntry.getKey());
            shipmentMetaData.setEntityValue(orderMetaEntry.getValue());
            shipmentMetaData.setCreatedBy(DEFAULT_OMS_USER);
            shipmentMetaData.setUpdatedBy(DEFAULT_OMS_USER);

            shipmentMetaDataList.add(shipmentMetaData);
        }

        return shipmentMetaDataList;
    }

    private HashMap<String, String> populateOrderMetaMap(CreateOrderRequest orderPayload) {
        HashMap<String, String> orderMetaMap = new HashMap<>();

        orderMetaMap.put(ORDER_META_KEY_CART_ID, orderPayload.getCartId());
        orderMetaMap.put(ORDER_META_KEY_MALL, StringUtils.isNotBlank(orderPayload.getMall()) ? orderPayload.getMall() : StringUtils.EMPTY);
        orderMetaMap.put(ORDER_META_KEY_TOTAL_TAX, String.valueOf(orderPayload.getTotalTax()));
        orderMetaMap.put(ORDER_META_KEY_SHIPPING_CHARGE, String.valueOf(orderPayload.getShippingCharge()));
        orderMetaMap.put(ORDER_META_KEY_SUB_TOTAL, String.valueOf(orderPayload.getSubTotal()));
        orderMetaMap.put(ORDER_META_KEY_GRAND_TOTAL, String.valueOf(orderPayload.getGrandTotal()));
        if (orderPayload.getGiftMessage() != null) {
            orderMetaMap.put(ORDER_META_KEY_GIFT_MESSAGE, orderPayload.getGiftMessage().getMessage());
            orderMetaMap.put(ORDER_META_KEY_GIFT_MESSAGE_TO, orderPayload.getGiftMessage().getTo());
            orderMetaMap.put(ORDER_META_KEY_GIFT_MESSAGE_FROM, orderPayload.getGiftMessage().getFrom());
        }
        orderMetaMap.put(ORDER_META_KEY_STORE_TYPE, orderPayload.getStoreType());
        if (orderPayload.getSalesmanDetails() != null) {
            orderMetaMap.put(ORDER_META_KEY_SALESMAN_NAME, orderPayload.getSalesmanDetails().getSalesmanName());
            orderMetaMap.put(ORDER_META_KEY_SALESMAN_EMAIL, orderPayload.getSalesmanDetails().getEmail());
            orderMetaMap.put(ORDER_META_KEY_SALESMAN_PHONE, orderPayload.getSalesmanDetails().getPhone());
        }
        orderMetaMap.put(ORDER_META_KEY_QUOTE_ID, StringUtils.isNotBlank(orderPayload.getCartId()) ? orderPayload.getCartId() : null);
        orderMetaMap.put(ORDER_META_KEY_SALES_CODE, orderPayload.getSalesCode());
        orderMetaMap.put(ORDER_META_KEY_PROMISED_SHIP_DATE, String.valueOf(orderPayload.getDispatchDate()));
        orderMetaMap.put(ORDER_META_KEY_PROMISED_DELIVERY_DATE, String.valueOf(orderPayload.getDeliveryType()));
        orderMetaMap.put(ORDER_META_KEY_OFFER_3ORFREE, orderPayload.getOffer3orfree());
        orderMetaMap.put(ORDER_META_KEY_PAYMENT_GATEWAY, orderPayload.getPayments().getPaymentGateway());
        orderMetaMap.put(ORDER_META_KEY_PARTIAL_PAYMENT, String.valueOf(orderPayload.getPayments().getPartialPayment()));
        orderMetaMap.put(ORDER_META_KEY_TRANSACTION_ID, orderPayload.getTransactionId());
        orderMetaMap.put(ORDER_META_KEY_FACILITY_CODE, orderPayload.getFacilityCode());
        orderMetaMap.put(ORDER_META_KEY_CUSTOMER_COMMENTS, getCustomerComments(orderPayload.getNoteAttributes()));
        orderMetaMap.put(ORDER_META_KEY_PRESCRIPTION_TYPE, getPrescriptionType(orderPayload.getLineItems()));
        orderMetaMap.put(ORDER_META_KEY_PRESCRIPTION_METHOD, "enter_prescription");
        orderMetaMap.put(ORDER_META_KEY_JIT_FLAG, "false");
        orderMetaMap.put(ORDER_META_KEY_CUSTOMER_ID, String.valueOf(orderPayload.getCustomerPayload().getId()));
        orderMetaMap.put(ORDER_META_KEY_CUSTOMER_NAME, orderPayload.getCustomerPayload().getFirstName());
        orderMetaMap.put(ORDER_META_KEY_CUSTOMER_EMAIL, orderPayload.getCustomerPayload().getEmail());
        orderMetaMap.put(ORDER_META_KEY_CUSTOMER_NUMBER, orderPayload.getCustomerPayload().getMobileNumber());
        orderMetaMap.put(ORDER_META_KEY_JUNO_INITIAL_STATE, orderPayload.getState().getValue());
        orderMetaMap.put(ORDER_META_KEY_JUNO_INITIAL_STATUS, orderPayload.getStatus().getValue());
        orderMetaMap.put(ORDER_META_KEY_BULK_ORDER, String.valueOf(orderPayload.getIsBulkOrder()));
        orderMetaMap.put(ORDER_META_KEY_DUAL_COMPANY_ENABLED, String.valueOf(orderPayload.getIsDualCompanyEnabled()));
        orderMetaMap.put(IS_INSURANCE_BENEFIT_ORDER, String.valueOf(orderPayload.isInsuranceBenefitFlow()));
        orderMetaMap.put(IS_INSURANCE_ORDER,String.valueOf(orderPayload.isInsuranceOrder()));
        orderMetaMap.put(INSURANCE_PROVIDER,orderPayload.getInsuranceProvider());
        orderMetaMap.put(FRAME_BROKEN_GV_FLOW, String.valueOf(orderPayload.getIsFrameBrokenGvFlow()));

        populateDiscountDetails(orderPayload, orderMetaMap);

        return orderMetaMap;
    }

    private HashMap<String, String> populateOrderItemMetaMap(LineItemPayload orderItem, ProcessingType processingType) {
        HashMap<String, String> orderItemMetaMap = new HashMap<>();

        orderItemMetaMap.put(ORDER_ITEM_META_KEY_PRODUCT_NAME, orderItem.getProductName());
        orderItemMetaMap.put(ORDER_ITEM_META_KEY_IMAGE_URL, orderItem.getImageUrl());
        orderItemMetaMap.put(ORDER_ITEM_META_KEY_LOCAL_FITTING_FACILITY, orderItem.getLocalFittingFacility());
        orderItemMetaMap.put(ORDER_ITEM_META_KEY_VIRTUAL_FACILITY_CODE, orderItem.getVirtualFacilityCode());
        orderItemMetaMap.put(ORDER_ITEM_META_KEY_CONTACT_LENS_ID, orderItem.getVirtualFacilityCode());
        orderItemMetaMap.put(ORDER_ITEM_META_KEY_CART_ITEM_ID, String.valueOf(orderItem.getCartItemId()));
        orderItemMetaMap.put(ORDER_ITEM_META_KEY_HUB_CODE, orderItem.getHubCode());
        orderItemMetaMap.put(ORDER_ITEM_META_KEY_HUB_COUNTRY, orderItem.getHubCountry());
        orderItemMetaMap.put(ORDER_ITEM_META_KEY_PROCESSING_TYPE, processingType.name());
        orderItemMetaMap.put(ORDER_ITEM_META_KEY_3ORFREE, String.valueOf(orderItem.getThreeOrFree()));
        orderItemMetaMap.put(OREDER_ITEM_META_KEY_CENTRAL_FACILITY_CODE, orderItem.getCentralFacilityCode());
        if (orderItem.getLineItemProperties() != null) {
            orderItemMetaMap.put(ORDER_ITEM_META_KEY_IS_POWER, String.valueOf(orderItem.getLineItemProperties().getIsPower()));
        }
        if (orderItem.getLineItemProperties() != null && ApplicationConstants.ORDER_TYPE_RIMLESS.equalsIgnoreCase(orderItem.getLineItemProperties().getType()) && null != orderItem.getRimlessBarProductId()) {
            orderItemMetaMap.put(ORDER_ITEM_META_KEY_RIMLESS_BAR_PID, String.valueOf(orderItem.getRimlessBarProductId()));
        }

        return orderItemMetaMap;
    }

    private HashMap<String, String> populateShipmentMetaMap(CreateOrderRequest orderPayload, OrderDto orderDto) {
        HashMap<String, String> shipmentMetaMap = new HashMap<>();
        //shipmentMetaMap.put(SHIPMENT_META_KEY_LENS_TYPE, getShipmentLensType(orderPayload));
        shipmentMetaMap.put(SHIPMENT_META_KEY_IS_SHIPMENT_SHIPPED, "");
        shipmentMetaMap.put(SHIPMENT_META_KEY_SHIPPING_TIME, "");
        shipmentMetaMap.put(SHIPMENT_META_KEY_COMPLETE_TIME, "");
        shipmentMetaMap.put(SHIPMENT_META_KEY_UPDATED_CRM, "");
        shipmentMetaMap.put(SHIPMENT_META_KEY_UPDATED_MAGENTO, "");
        shipmentMetaMap.put(SHIPMENT_META_KEY_IS_SHIPMENT_PACKED, "");
        shipmentMetaMap.put(SHIPMENT_META_KEY_DELIVERED_TIME, "");
        shipmentMetaMap.put(SHIPMENT_META_KEY_PICK_TIME, "");
        shipmentMetaMap.put(SHIPMENT_META_KEY_PICKED_BY, "");
        shipmentMetaMap.put(SHIPMENT_META_KEY_AWB_ASSIGNED_AT, "");
        shipmentMetaMap.put(SHIPMENT_META_KEY_FOLLOWED_UP_DATE, "");
        shipmentMetaMap.put(SHIPMENT_META_KEY_LAST_FOLLOWED_UP_DATE, "");
        shipmentMetaMap.put(SHIPMENT_META_KEY_MANIFEST_TIME, "");
        if (OrderType.DISTRIBUTOR_ORDER.equals(orderDto.getOrderType())) {
            shipmentMetaMap.put(SHIPMENT_META_KEY_DO_PO_NUMBER, getPoNumberForDistributorOrder(orderDto.getIncrementId()));
        }

        return shipmentMetaMap;
    }

    private String getPoNumberForDistributorOrder(Long incrementId) {
        try {
            return distributorOrdersService.findByIncrementId(String.valueOf(incrementId)).getPoNumber();
        } catch (Exception e) {
            logger.error("Error while getting po number for DO order : " + incrementId, e);
            return null;
        }
    }

    private String getShipmentLensType(CreateOrderRequest orderPayload) {
        for (LineItemPayload lineItem : orderPayload.getLineItems()) {
            if (lineItem.getIsLocalFittingRequired()) {
                return "LOCAL_FITTING";
            }

            if (lineItem.getStoreInventory() != null && lineItem.getStoreInventory() != 0) {
                return "LP";
            }
        }

        return null;
    }

    public ProcessingType getProcessingType(OrderItemDto orderItemDto, OrderDto orderDto) {
        if (commonUtil.isDistributorOrder(orderDto)) {
            return ProcessingType.FR0;
        }
        if (FittingType.NOT_REQD == orderItemDto.getFittingType()) {
            return orderItemDto.getParentMagentoItemId() == null ? ProcessingType.FR0 : ProcessingType.PL;
        }
        switch (orderItemDto.getItemType()) {
            case EYEFRAME:
            case SMART_GLASSES:
                return ProcessingType.FR1;
            case SUNGLASS:
            case SUNGLASSES:
                return ProcessingType.FR2;

            case PACKAGE:
            case LEFT_LENS:
            case RIGHT_LENS:
                return ProcessingType.PL;

            default:
                return ProcessingType.FR0;
        }
    }
    public ShipmentType getShipmentType(ShipmentDto shipmentDto) {
        Optional<OrderItemDto> orderItemDtoOptional =  shipmentDto.getOrderItems().stream()
                .filter(oi -> FittingType.REQD.equals(oi.getFittingType()))
                .findFirst();
        if(!orderItemDtoOptional.isPresent()){
            return ShipmentType.FR0;
        }
        else if(ItemType.EYEFRAME.equals(orderItemDtoOptional.get().getItemType()) || ItemType.SMART_GLASSES.equals(orderItemDtoOptional.get().getItemType())) return ShipmentType.FR1;
        else if(ItemType.SUNGLASSES.equals(orderItemDtoOptional.get().getItemType()) || ItemType.SUNGLASS.equals(orderItemDtoOptional.get().getItemType())) return ShipmentType.FR2;

        return ShipmentType.FR0;
    }

    private String getPrescriptionType(List<LineItemPayload> orderItems) {
        String prescriptionType = "normal";
        for (LineItemPayload orderItemPayload : orderItems) {
            LineItemPropertiesPayload powerPayload = orderItemPayload.getLineItemProperties();

            if (powerPayload != null && powerPayload.getPowerType() != null && POWER_TYPE_PROGRESSIVE.equals(powerPayload.getPowerType())) {
                prescriptionType = "progressive";
                break;
            } else if (powerPayload != null && powerPayload.getPowerType() != null && POWER_TYPE_BIFOCAL.equals(powerPayload.getPowerType())) {
                prescriptionType = "bifocal";
            }
        }

        return prescriptionType;
    }

    private String getCustomerComments(List<NoteAttributesPayload> noteAttributes) {
        if (!CollectionUtils.isEmpty(noteAttributes)) {
            StringBuilder comments = new StringBuilder();
            for (NoteAttributesPayload notes : noteAttributes) {
                if (StringUtils.isNotBlank(notes.getValue())) {
                    comments.append(notes.getValue()).append(",");
                }
            }

            if (comments.length() > 0) {
                return comments.substring(0, comments.length() - 1);
            }
        }

        return StringUtils.EMPTY;
    }

    private void populateDiscountDetails(CreateOrderRequest orderPayload, HashMap<String, String> orderMetaMap) {
        if (CollectionUtils.isEmpty(orderPayload.getDiscounts())) {
            double giftVoucherDiscount = 0.0;
            double storeCredit = 0.0;
            double giftCardDiscount = 0.0;
            double prepaidDiscount = 0.0;
            double lenskartWallet = 0.0;
            double lenskartPlusWallet = 0.0;

            for (DiscountPayload discount : orderPayload.getDiscounts()) {
                if (discount.getAmount() != null) {
                    switch (DiscountType.valueOf(discount.getType())) {
                        case GIFTVOUCHER:
                            giftVoucherDiscount += discount.getAmount();
                            break;

                        case STORECREDIT:
                            storeCredit += discount.getAmount();
                            break;

                        case PREPAID:
                            prepaidDiscount += discount.getAmount();
                            break;

                        case LENSKART:
                            lenskartWallet += discount.getAmount();
                            break;

                        case LENSKARTPLUS:
                            lenskartPlusWallet += discount.getAmount();
                            break;

                        case GIFTCARD:
                            giftCardDiscount += discount.getAmount();
                            break;

                        default:
                            break;
                    }
                }
            }

            orderMetaMap.put(ORDER_META_KEY_LENSKART_DISCOUNT, String.valueOf(lenskartWallet));
            orderMetaMap.put(ORDER_META_KEY_LENSKART_PLUS_DISCOUNT, String.valueOf(lenskartPlusWallet));
            orderMetaMap.put(ORDER_META_KEY_GIFT_VOUCHER_DISCOUNT, String.valueOf(giftVoucherDiscount));
            orderMetaMap.put(ORDER_META_KEY_STORE_CREDIT, String.valueOf(storeCredit));
            orderMetaMap.put(ORDER_META_KEY_GIFT_CARD_DISCOUNT, String.valueOf(giftCardDiscount));
            orderMetaMap.put(ORDER_META_KEY_PREPAID_DISCOUNT, String.valueOf(prepaidDiscount));
            orderMetaMap.put(ORDER_META_KEY_TOTAL_DISCOUNT, String.valueOf(orderPayload.getTotalDiscount()));
        }
    }

    private String getChannel(CreateOrderRequest orderPayload, OrderDto orderDto) throws ApplicationException {
        String channelName = "";
        if(commonUtil.isDistributorJitOrder(orderDto)) {
            channelName =  Channel.INT_BULKTOVENDOR.name();
        } else if (commonUtil.isDistributorOrder(orderDto)) {
            channelName = Channel.BULKTOVENDOR.name();
        } else if (StringUtils.isNotBlank(orderPayload.getFacilityCode())
                && ojosFacilityCodes.contains(orderPayload.getFacilityCode().toUpperCase())) {
            channelName = Channel.OJOS.name();
        }else if(StringUtils.isNotBlank(orderPayload.getClientOrg()) && ApplicationConstants.OD_CLIENT_ORG.equalsIgnoreCase(orderPayload.getClientOrg())) {
            channelName = Channel.ODONLINE.name();
        } else {
            if (orderPayload.getExchangeFlag() == 1
                    && !((orderPayload.getStoreType() != null
                    && StoreType.COCO_JJ_STORE.name().equalsIgnoreCase(orderPayload.getStoreType()))
                    || (orderPayload.getStoreId() != null
                    && StoreId.JOHNJACOBS_STOREID.getValue().equals(orderPayload.getStoreId()))
                    || (orderPayload.getStoreId() != null
                    && StoreId.JJONLINE_STOREID.getValue().equals(orderPayload.getStoreId()))
            )) {
                channelName = Channel.EXCHANGE.name();
            } else {
                if (orderPayload.getCustomerPayload() != null && orderPayload.getCustomerPayload().getEmail() != null) {
                    channelName = getChannelFromEmail(orderPayload.getCustomerPayload().getEmail());
                }

                if ((orderPayload.getStoreType() != null
                        && StoreType.COCO_JJ_STORE.name().equalsIgnoreCase(orderPayload.getStoreType()))
                        || (orderPayload.getStoreId() != null
                        && StoreId.JOHNJACOBS_STOREID.getValue().equals(orderPayload.getStoreId()))
                ) {
                    channelName = Channel.JOHNJACOBS.name();
                } else if (orderPayload.getStoreId() != null && StoreId.LKSINGAPORE_STOREID.getValue().equals(orderPayload.getStoreId())) {
                    channelName = Channel.LKSINGAPORE.name();
                } else if (orderPayload.getStoreId() != null && StoreId.JJONLINE_STOREID.getValue().equals(orderPayload.getStoreId())) {
                    channelName = Channel.JJONLINE.name();
                }

                if (StringUtils.isEmpty(orderPayload.getStoreType())
                        && (orderPayload.getIsBulkOrder() == null || !orderPayload.getIsBulkOrder())
                        && (orderPayload.getFacilityCode() != null && !orderPayload.getFacilityCode().equalsIgnoreCase(ApplicationConstants.FACILITY_CODE_ONLINE))
                ) {
                    throw new ApplicationException("StoreType missing in OrderPayload", null);
                } else if (StringUtils.isNotEmpty(orderPayload.getStoreType())
                        && orderPayload.getIsBulkOrder() != null && !orderPayload.getIsBulkOrder()
                        && (orderPayload.getStoreType().equalsIgnoreCase(StoreType.COCO_LENSKART_STORE.name())
                        || orderPayload.getStoreType().equals(StoreType.HEC.name())
                        || orderPayload.getStoreType().equals(StoreType.COCO_INTERNATIONAL.name()))
                ) {
                    channelName = Channel.CUSTSTORE.name();
                } else if ((orderPayload.getIsBulkOrder() != null && orderPayload.getIsBulkOrder())
                        || ((orderPayload.getIsDualCompanyEnabled() != null
                        && orderPayload.getOffer3orfree() != null)
                        && !orderPayload.getIsDualCompanyEnabled()
                        && orderPayload.getOffer3orfree().substring(0, 1).equalsIgnoreCase(ApplicationConstants.THREE_OR_FREE_FLAG_TRUE))
                ) {
                    channelName = Channel.FRANCHISEBULK.name();
                } else if (orderPayload.getStoreType() != null
                        && (orderPayload.getStoreType().equalsIgnoreCase(StoreType.FOFO_WITH_SC.name())
                        || orderPayload.getStoreType().equals(StoreType.FOFO_WITHOUT_SC.name()))
                ) {
                    channelName = Channel.B2B.name();
                }

                if (StringUtils.isEmpty(channelName)) {
                    channelName = Channel.CUSTOM.name();
                }
            }
        }

        return channelName;
    }

    private String getChannelFromEmail(String email) {
        String channelName = "";

        switch (email) {
            case ApplicationConstants.SNAPDEALEYEGLASSES_LESNKART_IN:
                channelName = Channel.SNAPDEALEYE.name();
                break;

            case ApplicationConstants.SNAPDEAL_LENSKART_IN:
                channelName = Channel.SNAPDEAL_NEW.name();
                break;

            case ApplicationConstants.EASY_REWARDZ_DEALSKART_IN:
                channelName = Channel.EASY_REWARDZ.name();
                break;

            case ApplicationConstants.BENEFITPLUS_DEALSKART_IN:
                channelName = Channel.BENEFITPLUS.name();
                break;

            case ApplicationConstants.STOREKING_LENSKART_IN:
                channelName = Channel.STOREKING.name();
                break;

            case ApplicationConstants.FASHIONARA_LENSKART_IN:

            case ApplicationConstants.FASHIONARA_SUN_LENSKART_IN:
                channelName = Channel.FASHIONARA.name();
                break;

            case ApplicationConstants.PAYU_WITH_JUNGLEE_DEALSKART_IN:
                channelName = Channel.JUNGLEE.name();
                break;

            case ApplicationConstants.PAYTM_LENSKART_IN:
                channelName = Channel.PAYTM_MANUAL.name();
                break;

            case ApplicationConstants.ASKMEBAZAAR_LENSKART_IN:
                channelName = Channel.ASKMEBAZAAR.name();
                break;

            case ApplicationConstants.FLIPKART_WATCHES_DEALSKART_IN:
                channelName = Channel.FLIPKARTMANUAL.name();
                break;

            default:
                break;
        }

        if (email.indexOf(ApplicationConstants.OFFLINE_APP_LK) != -1) {
            channelName = Channel.OFFLINESMS.name();
        }

        return channelName;
    }

    private Date setDispatchDate(LineItemPayload orderItem) {
        if (orderItem.getDispatchDate() != null) {
            return orderItem.getDispatchDate();
        }

        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_YEAR, 2);
        return cal.getTime();
    }

    private Date setDeliveryDate(LineItemPayload orderItem) {
        if (orderItem.getDeliveryDate() != null) {
            return orderItem.getDeliveryDate();
        }

        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_YEAR, 2);
        return cal.getTime();
    }

    private OrderItemDto saveB2BItems(OrderItemDto orderItemDtoWarehouseCopy, OrderDto orderDto,Map<Long, String> magentoItemIdProductDeliveryTypeMapping,Map<Long, Double> priceDetails) throws Exception {

        OrderItemDto orderItemDtoCustomerCopy = new OrderItemDto();
        BeanUtils.copyProperties(orderItemDtoWarehouseCopy, orderItemDtoCustomerCopy);

        getB2BPriceMap(orderItemDtoWarehouseCopy, orderDto,magentoItemIdProductDeliveryTypeMapping,priceDetails);
        double multiplier = getMultiplier(orderItemDtoWarehouseCopy);
        if(!priceDetails.containsKey(orderItemDtoWarehouseCopy.getMagentoItemId())) {
            logger.error("[saveB2BItems] marginPrice is null for incrementId  {} magentoItemId {}", orderDto.getIncrementId(), orderItemDtoWarehouseCopy.getMagentoItemId());
        }
        double itemGrandTotal = priceDetails.get(orderItemDtoWarehouseCopy.getMagentoItemId()) * multiplier;
        logger.info("[saveB2BItems] order {} margin price {}", orderDto.getIncrementId(), itemGrandTotal);
        OrderItemPricesDto orderItemPricesDto = new OrderItemPricesDto();
        orderItemPricesDto.setItemTotal(itemGrandTotal);
        orderItemPricesDto.setCreatedBy(DEFAULT_OMS_USER);
        orderItemPricesDto.setUpdatedBy(DEFAULT_OMS_USER);
        orderItemDtoWarehouseCopy.setOrderItemPrice(orderItemPricesDto);

        if(!Channel.ODONLINE.equals(orderItemDtoWarehouseCopy.getChannel()))
            orderItemDtoCustomerCopy.setChannel(Channel.CUSTOM);

        orderItemDtoCustomerCopy.setB2bReferenceItemId(orderItemDtoWarehouseCopy.getMagentoItemId());

        return orderItemDtoCustomerCopy;
    }

    private double getMultiplier(OrderItemDto orderItemDto) throws Exception {
        HashMap<String, Product> productmap = new HashMap<>();
        List<PackageProductMapping> packageProductList = orderOpsConnector.getPackageProductList(orderItemDto.getProductId());
        logger.info("[CreateB2BOrderMapper][getMultiplier] packageProductList for orderId {} is {}", orderItemDto.getOrderId(), packageProductList);
        if (!CollectionUtils.isEmpty(packageProductList)) {
            List<Integer> productIdList = packageProductList.stream().map(PackageProductMapping::getProductId).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(productIdList)) {
                logger.info("[getMultiplier] productIdList is empty for orderItemId {}", orderItemDto.getId());
                throw new ApplicationException("Got Product list empty for b2b order orderId" + orderItemDto.getOrderId());
            }
            ProductListDto productListDto = new ProductListDto();
            productListDto.setProductList(productIdList);
            List<Product> productList = orderOpsConnector.getProductList(productListDto);
            buildProductMap(productList, productmap);
            double productGrandTotal = calculateProductGrandTotal(productList, productmap, packageProductList);
            Product product = productmap.get(String.valueOf(packageProductList.get(0).getProductId()));
            return product.getSellingPrice() / productGrandTotal;
        } else {
            return 1.0;
        }
    }

    private String getShippingDestination(List<LineItemPayload> orderItems) {
        if (!CollectionUtils.isEmpty(orderItems) && orderItems.get(0).getShipToStoreRequired()) {
            return SHIPPING_DESTINATION_STORE;
        }

        return SHIPPING_DESTINATION_CUSTOMER;
    }

    public OmsOrderEvent createDistributorOrderRequestToOmsOrderEvent(OrderEvent orderEvent) {
        OmsOrderEvent omsOrderEvent = new OmsOrderEvent();
        omsOrderEvent.setIncrementId(Long.valueOf(orderEvent.getOrderId()));
        omsOrderEvent.setEventType(OrderEventType.valueOf(orderEvent.getRequestType()));
        return omsOrderEvent;
    }

    private FittingType isFittingRequired(Product product, LineItemPayload lineItemPayload) {
        String productClassification = String.valueOf(product.getClassification());
        if (lineItemPayload.getLineItemProperties() != null && lineItemPayload.getLineItemProperties().getIsPower() == 1 &&
                (fittingClassifications.contains(productClassification) ||
                        zeroPowerClassifications.contains(productClassification)
                )
        )
            return FittingType.REQD;
        return FittingType.NOT_REQD;
    }

    public ItemType getItemType(String itemType) {
        if (ItemType.SUNGLASSES.name().equals(itemType)) {
            return ItemType.SUNGLASS;
        }

        if(ItemType.ACCESSORIES_REVENUE.name().equalsIgnoreCase(itemType)){
            return ItemType.ACCESSORIES;
        }
        return ItemType.valueOf(itemType);
    }

    private Map<Long, Double> getB2BPriceMap(OrderItemDto orderItem, OrderDto orderDto,Map<Long, String> magentoItemIdProductDeliveryTypeMapping,Map<Long, Double> priceDetails) throws Exception {
        if(priceDetails.isEmpty()) {
            if (StringUtils.equalsIgnoreCase(orderDto.getLkCountry(), "IN")) {
                getMarginPriceFromPos(orderDto.getIncrementId(), orderItem.getNavChannel().name(), priceDetails);
            } else {
                if (getMarginPriceFromJunoForIntl) {
                    logger.info("[getB2bMarginPrice] uwOrdersMap " + new Gson().toJson(magentoItemIdProductDeliveryTypeMapping));
                    JunoMarginPriceApiResponse junoMarginPriceApiResponse = junoConnector.getMarginPriceForB2bOrder(orderDto.getIncrementId());
                    Long key;
                    if (junoMarginPriceApiResponse != null && junoMarginPriceApiResponse.getItems() != null) {
                        for (JunoMarginPriceApiResponse.Item item : junoMarginPriceApiResponse.getItems()) {
                            key = (long) item.getItemId();
                            logger.info("[getB2bMarginPrice] uwOrdersMap item.getItemId() " + key);
                            if (magentoItemIdProductDeliveryTypeMapping.containsKey(key) && "B2B".equalsIgnoreCase(magentoItemIdProductDeliveryTypeMapping.get(key))) {
                                if (!Objects.isNull(item.getTotalMarginPrice()) && !Objects.isNull(item.getTotalMarginPrice().getPrice())) {
                                    priceDetails.put((long) item.getItemId(), item.getTotalMarginPrice().getPrice());
                                } else {
                                    logger.error("[getB2bMarginPrice] is null for incrementId  {} from juno {}", orderDto.getIncrementId(), item);
                                    throw new MarginPriceException(HttpStatus.INTERNAL_SERVER_ERROR.value(), "Margin price is null from juno");
                                }
                            }
                        }
                    }
                } else {
                    getMarginPriceFromPos(orderDto.getIncrementId(), orderItem.getNavChannel().name(), priceDetails);
                }
            }
        }
        return priceDetails;
    }

    private Map<Long, Double>  getMarginPriceFromPos(Long incrementId, String navChannel, Map<Long, Double> priceDetails)  throws Exception{
        B2bItemPriceDetailsResponseDto b2BItemPriceDetailsResponseDto = posConnector.getMarginPriceForB2bOrder(incrementId,navChannel);
        if (b2BItemPriceDetailsResponseDto != null && b2BItemPriceDetailsResponseDto.getItems() != null) {
            for (B2bItemPriceDetails b2bItemPriceDetails : b2BItemPriceDetailsResponseDto.getItems()) {
                priceDetails.put(Long.valueOf(b2bItemPriceDetails.getMagento_item_id()), b2bItemPriceDetails.getUnit_price());
            }
        }
        return priceDetails;
    }


    private CatalogOpsPowerWiseIdRequest populatePowerWiseIdRequest(OrderItemDto orderItemDto, OrderDto order, CreateOrderRequest orderPayload, LineItemPayload orderItem) throws ApplicationException {
        CatalogOpsPowerWiseIdRequest catalogOpsPowerWiseIdRequest = new CatalogOpsPowerWiseIdRequest();
        catalogOpsPowerWiseIdRequest.setIncrementId(order.getIncrementId());
        catalogOpsPowerWiseIdRequest.setOrderId(order.getJunoOrderId());
        catalogOpsPowerWiseIdRequest.setPowerRequestType(CONTACT_LENS_TYPE_POWER);
        catalogOpsPowerWiseIdRequest.setOrderStatus(
                OrderStatus.getNonProcessingStatus().contains(order.getOrderStatus().name())
                        ? "new"
                        : order.getOrderStatus().name()
        );
        catalogOpsPowerWiseIdRequest.setFacilityCode(orderPayload.getFacilityCode());
        List<PowerWisePidLineItemRequest> powerWisePidLineItemRequests = populateLineItems(orderItemDto, orderItem);
        catalogOpsPowerWiseIdRequest.setPowerWisePidRequestLineItem(powerWisePidLineItemRequests);

        return catalogOpsPowerWiseIdRequest;
    }

    private List<PowerWisePidLineItemRequest> populateLineItems(OrderItemDto orderItemDto, LineItemPayload orderItem) {
        List<PowerWisePidLineItemRequest> powerWisePidLineItemRequests = new ArrayList<>();


        PowerWisePidLineItemRequest lineItem = new PowerWisePidLineItemRequest();
        lineItem.setItemType(String.valueOf(orderItemDto.getItemType()));
        lineItem.setMagentoItemId(orderItem.getLineItemId());
        lineItem.setParentLineItemId(orderItem.getParentLineItemId());
        lineItem.setCoatingOId(orderItem.getOid());
        lineItem.setQuantity(orderItem.getQuantity());
        lineItem.setPowerType(orderItemDto.getItemPower().getPowerType());
        lineItem.setType(orderItem.getItemType());
        lineItem.setPatientName(orderItemDto.getItemPower().getPatientName());
        lineItem.setNotes(null);
        lineItem.setIsPower(1);
        lineItem.setPrescriptionUrl(orderItemDto.getItemPower().getPrescriptionUrl());
        lineItem.setIsLocalFittingOrder(orderItem.getIsLocalFittingRequired());
        lineItem.setProductId(Long.valueOf(orderItem.getSku()));
        lineItem.setPackageName(orderItem.getProductName());
        if (!ObjectUtils.isEmpty(orderItem.getLineItemProperties().getLeft())) {
            LineItemEyePowerRequest lineItemEyePowerRequest = getLineItemEyePowerRequest(orderItem, "LEFT");
            lineItem.setLeftEyePower(lineItemEyePowerRequest);
        }
        if (!ObjectUtils.isEmpty(orderItem.getLineItemProperties().getRight())) {
            LineItemEyePowerRequest lineItemEyePowerRequest = getLineItemEyePowerRequest(orderItem, "RIGHT");
            lineItem.setRightEyePower(lineItemEyePowerRequest);

        }
        powerWisePidLineItemRequests.add(lineItem);

        return powerWisePidLineItemRequests;
    }


    private static LineItemEyePowerRequest getLineItemEyePowerRequest(LineItemPayload orderItem, String powerType) {
        LineItemEyePowerRequest lineItemEyePowerRequest = new LineItemEyePowerRequest();
        lineItemEyePowerRequest.setSku(String.valueOf(orderItem.getSku()));
        if (powerType.equals("LEFT")) {
            lineItemEyePowerRequest.setSph(orderItem.getLineItemProperties().getLeft().getSph());
            lineItemEyePowerRequest.setAxis(orderItem.getLineItemProperties().getLeft().getAxis());
            lineItemEyePowerRequest.setBoxQty(orderItem.getLineItemProperties().getLeft().getBoxQty());
            lineItemEyePowerRequest.setCyl(orderItem.getLineItemProperties().getLeft().getCyl());
            lineItemEyePowerRequest.setAp(orderItem.getLineItemProperties().getLeft().getAp() != null ? orderItem.getLineItemProperties().getLeft().getAp().toUpperCase() : null);
        } else {
            lineItemEyePowerRequest.setSph(orderItem.getLineItemProperties().getRight().getSph());
            lineItemEyePowerRequest.setAxis(orderItem.getLineItemProperties().getRight().getAxis());
            lineItemEyePowerRequest.setBoxQty(orderItem.getLineItemProperties().getRight().getBoxQty());
            lineItemEyePowerRequest.setCyl(orderItem.getLineItemProperties().getRight().getCyl());
            lineItemEyePowerRequest.setAp(orderItem.getLineItemProperties().getRight().getAp() != null ? orderItem.getLineItemProperties().getRight().getAp().toUpperCase() : null);

        }
        return lineItemEyePowerRequest;
    }



    private void buildProductMap(List<Product> productList, HashMap<String, Product> productMap) {
        for (Product product : productList) {
            productMap.put(String.valueOf(product.getProductId()), product);
        }
    }

    public double calculateProductGrandTotal(List<Product> productList,HashMap<String,Product> productMap,List<PackageProductMapping> packageProductMappingList) throws Exception {
        logger.info("[CreateOrderMapper][calculateProductGrandTotal] Calculating Product Pricelist based on Selling Price Ratio [Parameter] => ProductGrandTotal : start ");

        double productGrandTotal = 0;
        for (PackageProductMapping packageProductMapping: packageProductMappingList){
            Product product =null;
            if(productMap.containsKey(String.valueOf(packageProductMapping.getProductId())))
                product =productMap.get(String.valueOf(packageProductMapping.getProductId()));
            else
                throw new Exception("Product Details not found ");

            productGrandTotal += product.getSellingPrice();
        }
        logger.info("[PackageProductMappingService][calculateProductGrandTotal] end ..product grand total  "+productGrandTotal);

        return productGrandTotal;
    }
}

