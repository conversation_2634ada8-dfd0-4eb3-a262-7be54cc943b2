package com.lenskart.oms.mapper;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.enums.ShipmentEvent;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.request.OrderOpsUpdateShipmentStatus;
import com.lenskart.oms.request.ShipmentItemUpdate;
import com.lenskart.oms.request.ShipmentUpdateEvent;
import com.lenskart.oms.service.OrderItemService;
import com.lenskart.oms.service.ShipmentService;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Component
@Setter(onMethod__ = {@Autowired})
public class UpdateShipmentStatusMapper {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    private ShipmentService shipmentService;
    private OrderItemService orderItemService;

    public List<OrderOpsUpdateShipmentStatus> transform(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        try {
            List<OrderOpsUpdateShipmentStatus> orderOpsUpdateShipmentStatusList = new ArrayList<>();
            OrderItemDto orderItemDto = new OrderItemDto();
            ShipmentDto shipmentDto = shipmentService.findBySearchTerms
                    ("wmsOrderCode.eq:" + shipmentUpdateEvent.getWmsOrderCode());
            for (ShipmentItemUpdate shipmentItemUpdate : shipmentUpdateEvent.getOrderItemList()) {
                if(!ObjectUtils.isEmpty(shipmentItemUpdate.getOrderItemId())) {
                     orderItemDto = orderItemService.findBySearchTerms
                            ("uwItemId.eq:" + shipmentItemUpdate.getOrderItemId());
                }
                OrderOpsUpdateShipmentStatus orderOpsUpdateShipmentStatus = new OrderOpsUpdateShipmentStatus();
                orderOpsUpdateShipmentStatus.setUnicomShipmentStatus(shipmentItemUpdate.getUnicomShipmentStatus());
                orderOpsUpdateShipmentStatus.setUnicomOrderCode(shipmentUpdateEvent.getWmsOrderCode());
                orderOpsUpdateShipmentStatus.setUwItemId(shipmentItemUpdate.getOrderItemId() != null ? Math.toIntExact(shipmentItemUpdate.getOrderItemId()) : null);
                orderOpsUpdateShipmentStatus.setShippingPackageId(shipmentDto.getWmsShippingPackageId());
                orderOpsUpdateShipmentStatus.setShipmentStatus(shipmentItemUpdate.getOrderOpsShipmentStatus());
                orderOpsUpdateShipmentStatus.setShipmentState(shipmentItemUpdate.getOrderOpsShipmentState());
                if (ShipmentEvent.MARK_SHIPMENT_PICKED.equals(shipmentUpdateEvent.getShipmentEvent())) {
                    orderOpsUpdateShipmentStatus.setBarcode(shipmentItemUpdate.getEntityId());
                } else if(StringUtils.hasLength(orderItemDto.getItemBarcode())) {
                    orderOpsUpdateShipmentStatus.setBarcode(orderItemDto.getItemBarcode());
                }
                orderOpsUpdateShipmentStatusList.add(orderOpsUpdateShipmentStatus);
            }
            logger.info("[UpdateShipmentStatusMapper] shipmentUpdateList {}", orderOpsUpdateShipmentStatusList);
            return orderOpsUpdateShipmentStatusList;
        } catch (Exception exception) {
            logger.error("UpdateShipmentStatusMapper.transform", exception);
            throw new ApplicationException("UpdateShipmentStatusMapper.transform" + exception.getMessage(), exception);
        }
    }
}
