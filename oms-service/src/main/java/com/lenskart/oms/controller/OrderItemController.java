package com.lenskart.oms.controller;

import com.lenskart.nexs.commonlogger.annotations.RestLogging;
import com.lenskart.nexs.commons.codes.CommonSuccessResponseCodes;
import com.lenskart.nexs.commons.controller.BaseController;
import com.lenskart.nexs.commons.dto.BaseResponseModel;
import com.lenskart.nexs.commons.response.CommonResponseBuilder;
import com.lenskart.nexs.commons.response.ResponseDTO;
import com.lenskart.oms.constants.ControllerConstants;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.entity.OrderItems;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.request.LensOnlyPidUpdateRequest;
import com.lenskart.oms.request.NavChannelRequest;
import com.lenskart.oms.request.OrderItemForOtcOrder;
import com.lenskart.oms.request.OtcOrderItemRequest;
import com.lenskart.oms.service.OrderItemService;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

@Timed
@RestController
@Tag(name = "Order Item Controller")
@Setter(onMethod__ = {@Autowired})
@RequestMapping(ControllerConstants.BASE_URL_V1 + ControllerConstants.ORDER_ITEM)
public class OrderItemController extends BaseController<ResponseDTO, OrderItemDto, OrderItems> {

    @RestLogging
    @PostMapping(ControllerConstants.GET_NAV_CHANNEL)
    public ResponseEntity<BaseResponseModel> getNavChannel(@RequestBody NavChannelRequest navChannelRequest) {
        String navChannel = ((OrderItemService)service).getNavChannel(navChannelRequest);
        return CommonResponseBuilder.successResponse(navChannel, CommonSuccessResponseCodes.RETRIEVED);
    }

    @RestLogging
    @PostMapping(ControllerConstants.MARK_OTC_SHIPMENT_INVOICE)
    public ResponseEntity<BaseResponseModel> markOtcShipmentInvoice(@RequestBody OtcOrderItemRequest orderItemRequest) throws ApplicationException {
        ((OrderItemService)service).markOtcShipmentInvoice(orderItemRequest);
        return CommonResponseBuilder.successResponse(CommonSuccessResponseCodes.PROCESSED);
    }

    @RestLogging
    @PostMapping("/updateLensOnlyFrameBarcode/{unicomOrderCode}")
    public void updateLensOnlyFrameBarcode(@RequestBody OrderItemDto orderItemDto, @PathVariable String unicomOrderCode){
        logger.info("[OrderItemController -> updateLensOnlyFrameBarcode] request :{} unicomOrderCode :{}",
                orderItemDto, unicomOrderCode);
        ((OrderItemService) service).updateLensOnlyFrameBarcode(orderItemDto, unicomOrderCode);
    }

    @RestLogging
    @PostMapping("/lensonly/pid/update")
    public ResponseEntity<OrderItemDto> updateLensOnlyFramePid(@RequestBody LensOnlyPidUpdateRequest lensOnlyPidUpdateRequest) {
        OrderItemDto orderItemDto = ((OrderItemService) service).updateLensOnlyFramePid(lensOnlyPidUpdateRequest);
        if(Objects.isNull(orderItemDto)) return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        return ResponseEntity.ok(orderItemDto);
    }

}
