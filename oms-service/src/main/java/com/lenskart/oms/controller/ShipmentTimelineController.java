package com.lenskart.oms.controller;

import com.lenskart.nexs.commons.controller.BaseController;
import com.lenskart.nexs.commons.response.ResponseDTO;
import com.lenskart.oms.constants.ControllerConstants;
import com.lenskart.oms.dto.ShipmentTimelineDto;
import com.lenskart.oms.entity.ShipmentTimeline;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Timed
@RestController
@Tag(name = "Shipment Timeline Controller")
@Setter(onMethod__ = {@Autowired})
@RequestMapping(ControllerConstants.BASE_URL_V1 + ControllerConstants.SHIPMENT_TIMELINE)
public class ShipmentTimelineController extends BaseController<ResponseDTO, ShipmentTimelineDto, ShipmentTimeline> {
}
