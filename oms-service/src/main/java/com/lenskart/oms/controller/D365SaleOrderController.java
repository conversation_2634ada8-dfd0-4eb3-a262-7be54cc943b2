package com.lenskart.oms.controller;

import com.lenskart.nexs.commonlogger.annotations.RestLogging;
import com.lenskart.nexs.commons.codes.CommonSuccessResponseCodes;
import com.lenskart.nexs.commons.constants.ResponseCodes;
import com.lenskart.nexs.commons.dto.BaseResponseModel;
import com.lenskart.nexs.commons.response.CommonResponseBuilder;
import com.lenskart.oms.constants.ControllerConstants;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.facade.D365Facade;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@Setter(onMethod__ = {@Autowired})
@RequestMapping(ControllerConstants.BASE_URL_V1 + ControllerConstants.D365_SALE_ORDER)
public class D365SaleOrderController {

    D365Facade d365Facade;

    @RestLogging
    @GetMapping("/saleOrder/{shippingPackageId}")
    public ResponseEntity<BaseResponseModel> getSaleOrder(@PathVariable("shippingPackageId") String shippingPackageId) throws ApplicationException {
        try {
            return CommonResponseBuilder.successResponse(d365Facade.generateSaleOrderPayload(shippingPackageId), CommonSuccessResponseCodes.PROCESSED);
        } catch (Exception exception) {
            log.error("ERROR in retrieving SaleOrder Details for shippingPackageId : " + shippingPackageId, exception);
            return CommonResponseBuilder.failureResponse(null, exception.getMessage(), ResponseCodes.INTERNAL_SERVER_ERROR_CODE);
        }
    }

    @RestLogging
    @GetMapping("/packingSlip/{shippingPackageId}")
    public ResponseEntity<BaseResponseModel> getPackingSlip(@PathVariable("shippingPackageId") String shippingPackageId) throws ApplicationException {
        try {
            return CommonResponseBuilder.successResponse(d365Facade.generatePackingSlipPayload(shippingPackageId), CommonSuccessResponseCodes.PROCESSED);
        } catch (Exception exception) {
            log.error("ERROR in retrieving PackingSlip Details for shippingPackageId : " + shippingPackageId, exception);
            return CommonResponseBuilder.failureResponse(null, exception.getMessage(), ResponseCodes.INTERNAL_SERVER_ERROR_CODE);
        }
    }
}
