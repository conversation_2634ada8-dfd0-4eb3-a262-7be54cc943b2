package com.lenskart.oms.controller;

import com.lenskart.nexs.commonlogger.annotations.RestLogging;
import com.lenskart.nexs.commons.controller.BaseController;
import com.lenskart.nexs.commons.dto.BaseResponseModel;
import com.lenskart.nexs.commons.response.CommonResponseBuilder;
import com.lenskart.nexs.commons.response.ResponseDTO;
import com.lenskart.oms.constants.ControllerConstants;
import com.lenskart.oms.dto.OnHoldMasterDto;
import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.entity.OnHoldMaster;
import com.lenskart.oms.entity.Order;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.facade.ReassignOmsFacade;
import com.lenskart.oms.producer.OrderEventOmsProducer;
import com.lenskart.oms.request.OrderEvent;
import com.lenskart.oms.utils.ObjectHelper;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

@Timed
@RestController
@Tag(name = "OnHold Master Controller")
@Setter(onMethod__ = {@Autowired})
@RequestMapping(ControllerConstants.BASE_URL_V1 + ControllerConstants.ON_HOLD_MASTER)
public class OnHoldMasterController extends BaseController<ResponseDTO, OnHoldMasterDto, OnHoldMaster> {

}
