package com.lenskart.oms.controller;

import com.lenskart.nexs.commonlogger.annotations.RestLogging;
import com.lenskart.nexs.commons.codes.CommonSuccessResponseCodes;
import com.lenskart.nexs.commons.controller.BaseController;
import com.lenskart.nexs.commons.dto.BaseResponseModel;
import com.lenskart.nexs.commons.response.CommonResponseBuilder;
import com.lenskart.nexs.commons.response.ResponseDTO;
import com.lenskart.oms.constants.ControllerConstants;
import com.lenskart.oms.dto.DistributorCustomerDetailsDto;
import com.lenskart.oms.entity.DistributorCustomerDetails;
import com.lenskart.oms.service.DistributorCustomerDetailsService;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Timed
@RestController
@Tag(name = "Customer Controller")
@Setter(onMethod__ = {@Autowired})
@RequestMapping(ControllerConstants.BASE_URL_V1 + ControllerConstants.CUSTOMER)
public class DistributorCustomerController extends BaseController<ResponseDTO, DistributorCustomerDetailsDto, DistributorCustomerDetails> {

    @Autowired
    DistributorCustomerDetailsService distributorCustomerDetailsService;

    @RestLogging
    @PutMapping(path = ControllerConstants.TOGGLE_CUSTOMER)
    public ResponseEntity<BaseResponseModel> toggleCustomer(@PathVariable Long customerId, @RequestParam Boolean enable) throws Exception {
        DistributorCustomerDetailsDto distributorCustomerDetailsDto = distributorCustomerDetailsService.toggleCustomer(customerId, enable);
        return CommonResponseBuilder.successResponse(distributorCustomerDetailsDto, CommonSuccessResponseCodes.UPDATED);
    }

}
