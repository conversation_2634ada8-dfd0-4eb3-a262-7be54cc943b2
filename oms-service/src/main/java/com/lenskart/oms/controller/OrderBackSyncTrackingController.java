package com.lenskart.oms.controller;

import com.lenskart.nexs.commonlogger.annotations.RestLogging;
import com.lenskart.nexs.commons.controller.BaseController;
import com.lenskart.nexs.commons.dto.BaseResponseModel;
import com.lenskart.nexs.commons.response.CommonResponseBuilder;
import com.lenskart.nexs.commons.response.ResponseDTO;
import com.lenskart.oms.constants.ControllerConstants;
import com.lenskart.oms.dto.OrderBackSyncTrackingDto;
import com.lenskart.oms.entity.OrderBackSyncTracking;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.facade.OrderBackSyncFacade;
import com.lenskart.oms.response.OmsSuccessResponseCodes;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Timed
@RestController
@Tag(name = "Order BackSync Controller")
@Setter(onMethod__ = {@Autowired})
@RequestMapping(ControllerConstants.BASE_URL_V1 + ControllerConstants.ORDER_BACK_SYNC)
public class OrderBackSyncTrackingController extends BaseController<ResponseDTO, OrderBackSyncTrackingDto, OrderBackSyncTracking> {

    private OrderBackSyncFacade orderBackSyncFacade;

    @RestLogging
    @PutMapping(ControllerConstants.BACK_SYNC_ACK)
    public ResponseEntity<BaseResponseModel> orderBackSyncAck(@RequestBody OrderBackSyncTrackingDto orderBackSyncTrackingDto) throws ApplicationException {
        orderBackSyncFacade.validateAndPushToTrackingQueue(orderBackSyncTrackingDto);
        return CommonResponseBuilder.successResponse(OmsSuccessResponseCodes.SUCCESSFULLY_ORDER_BACK_SYNC_REGISTERED.getMessage(), OmsSuccessResponseCodes.SUCCESSFULLY_ORDER_BACK_SYNC_REGISTERED);
    }
}
