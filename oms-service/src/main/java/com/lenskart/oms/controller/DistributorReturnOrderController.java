package com.lenskart.oms.controller;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.commonlogger.annotations.RestLogging;
import com.lenskart.nexs.commons.codes.CommonSuccessResponseCodes;
import com.lenskart.nexs.commons.constants.ResponseCodes;
import com.lenskart.nexs.commons.controller.BaseController;
import com.lenskart.nexs.commons.dto.BaseResponseModel;
import com.lenskart.nexs.commons.response.CommonResponseBuilder;
import com.lenskart.nexs.commons.response.ResponseDTO;
import com.lenskart.oms.constants.ControllerConstants;
import com.lenskart.oms.dto.DistributorReturnOrderDto;
import com.lenskart.oms.entity.DistributorReturnOrder;
import com.lenskart.oms.enums.DistributorReturnOrderStatus;
import com.lenskart.oms.facade.DistributorReturnOrderFacade;
import com.lenskart.oms.request.distributor.orderReturn.ReturnOrderRequest;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;

@Timed
@RestController
@Tag(name = "Distributor Orders Return Controller")
@Setter(onMethod__ = {@Autowired})
@RequestMapping(ControllerConstants.BASE_URL_V1 + ControllerConstants.DISTRIBUTOR_RETURN_ORDER.CONTROLLER_ENDPOINT)
public class DistributorReturnOrderController extends BaseController<ResponseDTO, DistributorReturnOrderDto, DistributorReturnOrder> {

    private DistributorReturnOrderFacade distributorReturnOrderFacade;

    @CustomLogger
    @Setter(AccessLevel.NONE)
    protected Logger logger;

    @RestLogging
    @GetMapping(value = ControllerConstants.DISTRIBUTOR_RETURN_ORDER.PRODUCT_DETAILS_WITH_BARCODE)
    public ResponseEntity<BaseResponseModel> productDetailsWithBarcode(@NotEmpty @PathVariable(value = "barcode") String barcode, @NotEmpty @RequestParam String facilityCode) {
        try {
            return CommonResponseBuilder.successResponse(distributorReturnOrderFacade.fetchProductDetailsWithBarcode(barcode, facilityCode), CommonSuccessResponseCodes.CREATED);
        } catch (Exception exception) {
            logger.error("[DistributorOrdersController][fetchProductDetailsWithBarcode]Exception for Barcode - " + barcode + " . ErrorMessage=" + exception.getMessage(), exception);
            return CommonResponseBuilder.failureResponse(null, exception.getMessage(), ResponseCodes.INTERNAL_SERVER_ERROR_CODE);
        }
    }

    @RestLogging
    @PostMapping(value = ControllerConstants.DISTRIBUTOR_RETURN_ORDER.RETURN_ORDER)
    public ResponseEntity<BaseResponseModel> returnOrder(@RequestBody ReturnOrderRequest returnOrderRequest) {
        try {
            return CommonResponseBuilder.successResponse(distributorReturnOrderFacade.returnOrder(returnOrderRequest), CommonSuccessResponseCodes.CREATED);
        } catch (Exception exception) {
            logger.error("[DistributorOrdersController][returnOrder]Exception for Barcode - " + returnOrderRequest.getScannedBarcode() + " . ErrorMessage=" + exception.getMessage(), exception);
            return CommonResponseBuilder.failureResponse(null, exception.getMessage(), ResponseCodes.INTERNAL_SERVER_ERROR_CODE);
        }
    }

    @RestLogging
    @PostMapping(value = ControllerConstants.DISTRIBUTOR_RETURN_ORDER.UPDATE_STATUS)
    public ResponseEntity<BaseResponseModel> updateStatus(@NotEmpty @PathVariable(value = "orderItemId") String orderItemId,
                                                                @NotEmpty @RequestHeader String userName,
                                                                @NotEmpty @RequestHeader DistributorReturnOrderStatus status) {
        try {
            return CommonResponseBuilder.successResponse(distributorReturnOrderFacade.updateStatus(orderItemId,userName, status), CommonSuccessResponseCodes.UPDATED);
        } catch (Exception exception) {
            logger.error("DistributorOrdersController.statusUpdate For OrderItemId - " + orderItemId, exception);
            return CommonResponseBuilder.failureResponse(null, exception.getMessage(), ResponseCodes.INTERNAL_SERVER_ERROR_CODE);
        }
    }
}
