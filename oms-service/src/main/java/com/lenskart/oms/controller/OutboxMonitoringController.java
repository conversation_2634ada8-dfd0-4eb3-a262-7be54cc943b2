package com.lenskart.oms.controller;

import com.lenskart.oms.constants.ControllerConstants;
import com.lenskart.oms.dto.OutboxEventDto;
import com.lenskart.oms.enums.OutboxEventStatus;
import com.lenskart.oms.service.OutboxEventService;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * REST controller for monitoring and managing outbox events
 */

@Slf4j
@RestController
@Setter(onMethod__ = {@Autowired})
@RequestMapping(ControllerConstants.BASE_URL_V1 + ControllerConstants.OUTBOX_EVENT)
public class OutboxMonitoringController {

    private OutboxEventService outboxEventService;

    /**
     * Get outbox event statistics
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<OutboxEventStatus, Long>> getStatistics() {
        try {
            Map<OutboxEventStatus, Long> statistics = outboxEventService.getEventStatistics();
            return ResponseEntity.ok(statistics);
        } catch (Exception e) {
            log.error("[getStatistics] Error getting outbox statistics", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get events by status and date range
     */
    @GetMapping("/events")
    public ResponseEntity<List<OutboxEventDto>> getEventsByStatusAndDateRange(
            @RequestParam OutboxEventStatus status,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {
        try {
            List<OutboxEventDto> events = outboxEventService.findByStatusAndDateRange(status, startDate, endDate);
            return ResponseEntity.ok(events);
        } catch (Exception e) {
            log.error("[getEventsByStatusAndDateRange] Error getting events by status and date range", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get events by aggregate ID
     */
    @GetMapping("/events/aggregate/{aggregateId}")
    public ResponseEntity<List<OutboxEventDto>> getEventsByAggregateId(@PathVariable String aggregateId) {
        try {
            // Get all events for the aggregate (you might want to add eventType parameter)
            List<OutboxEventDto> events = outboxEventService.search("aggregateId.eq:" + aggregateId);
            return ResponseEntity.ok(events);
        } catch (Exception e) {
            log.error("[getEventsByAggregateId] Error getting events for aggregate: {}", aggregateId, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get pending events count
     */
    @GetMapping("/pending/count")
    public ResponseEntity<Long> getPendingEventsCount() {
        try {
            long count = outboxEventService.countByEventStatus(OutboxEventStatus.PENDING);
            return ResponseEntity.ok(count);
        } catch (Exception e) {
            log.error("[getPendingEventsCount] Error getting pending events count", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get failed events count
     */
    @GetMapping("/failed/count")
    public ResponseEntity<Long> getFailedEventsCount() {
        try {
            long count = outboxEventService.countByEventStatus(OutboxEventStatus.FAILED);
            return ResponseEntity.ok(count);
        } catch (Exception e) {
            log.error("[getFailedEventsCount] Error getting failed events count", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get dead letter events count
     */
    @GetMapping("/dead-letter/count")
    public ResponseEntity<Long> getDeadLetterEventsCount() {
        try {
            long count = outboxEventService.countByEventStatus(OutboxEventStatus.DEAD_LETTER);
            return ResponseEntity.ok(count);
        } catch (Exception e) {
            log.error("[getDeadLetterEventsCount] Error getting dead letter events count", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Manually trigger processing of pending events
     */
    @PostMapping("/process/pending")
    public ResponseEntity<String> processPendingEvents() {
        try {
            outboxEventService.processPendingEvents();
            return ResponseEntity.ok("Pending events processing triggered successfully");
        } catch (Exception e) {
            log.error("[processPendingEvents] Error triggering pending events processing", e);
            return ResponseEntity.internalServerError().body("Error triggering pending events processing");
        }
    }

    /**
     * Manually trigger processing of retry events
     */
    @PostMapping("/process/retry")
    public ResponseEntity<String> processRetryEvents() {
        try {
            outboxEventService.processRetryEvents();
            return ResponseEntity.ok("Retry events processing triggered successfully");
        } catch (Exception e) {
            log.error("[processRetryEvents] Error triggering retry events processing", e);
            return ResponseEntity.internalServerError().body("Error triggering retry events processing");
        }
    }

    /**
     * Reset stale processing events
     */
    @PostMapping("/reset/stale")
    public ResponseEntity<String> resetStaleProcessingEvents() {
        try {
            outboxEventService.resetStaleProcessingEvents();
            return ResponseEntity.ok("Stale processing events reset successfully");
        } catch (Exception e) {
            log.error("[resetStaleProcessingEvents] Error resetting stale processing events", e);
            return ResponseEntity.internalServerError().body("Error resetting stale processing events");
        }
    }

    /**
     * Get event by ID
     */
    @GetMapping("/events/{eventId}")
    public ResponseEntity<OutboxEventDto> getEventById(@PathVariable Long eventId) {
        try {
            OutboxEventDto event = outboxEventService.findById(eventId);
            if (event != null) {
                return ResponseEntity.ok(event);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("[getEventById] Error getting event by ID: {}", eventId, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Health check endpoint for outbox publisher
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        try {
            Map<OutboxEventStatus, Long> statistics = outboxEventService.getEventStatistics();

            // Simple health check based on event counts
            long pendingCount = statistics.getOrDefault(OutboxEventStatus.PENDING, 0L);
            long failedCount = statistics.getOrDefault(OutboxEventStatus.FAILED, 0L);
            long deadLetterCount = statistics.getOrDefault(OutboxEventStatus.DEAD_LETTER, 0L);

            String status = "UP";
            if (pendingCount > 1000 || failedCount > 100 || deadLetterCount > 50) {
                status = "DEGRADED";
            }
            Map<String, Object> health = new HashMap<>();
            health.put("status", status);
            health.put("statistics", statistics);
            health.put("timestamp", new Date());

            return ResponseEntity.ok(health);
        } catch (Exception e) {
            log.error("[healthCheck] Error performing health check", e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
