package com.lenskart.oms.controller;

import com.lenskart.nexs.commonlogger.annotations.RestLogging;
import com.lenskart.nexs.commons.codes.BaseStatusCodesBase;
import com.lenskart.nexs.commons.codes.CommonSuccessResponseCodes;
import com.lenskart.nexs.commons.constants.ResponseCodes;
import com.lenskart.nexs.commons.controller.BaseController;
import com.lenskart.nexs.commons.dto.BaseResponseModel;
import com.lenskart.nexs.commons.response.CommonResponseBuilder;
import com.lenskart.nexs.commons.response.ResponseDTO;
import com.lenskart.oms.constants.ControllerConstants;
import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.entity.Order;
import com.lenskart.oms.enums.OrderSubStatus;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.facade.OrderEventsFacade;
import com.lenskart.oms.facade.OrderReconciliationFacade;
import com.lenskart.oms.facade.ReassignHelperFacade;
import com.lenskart.oms.facade.ReadyForWhStuckOrderFacade;
import com.lenskart.oms.request.OrderEvent;
import com.lenskart.oms.request.OrderReconciliationRequest;
import com.lenskart.oms.response.OmsSuccessResponseCodes;
import com.lenskart.oms.service.OrderService;

import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Slf4j
@Timed
@RestController
@Tag(name = "Order Controller")
@Setter(onMethod__ = {@Autowired})
@RequestMapping(ControllerConstants.BASE_URL_V1 + ControllerConstants.ORDER)
public class OrderController extends BaseController<ResponseDTO, OrderDto, Order> {

    private OrderEventsFacade orderEventsFacade;
    private ReassignHelperFacade reassignHelperFacade;
    private OrderReconciliationFacade orderReconciliationFacade;
    private ReadyForWhStuckOrderFacade readyForWhStuckOrderFacade;


    @RestLogging
    @PutMapping(path = ControllerConstants.CANCEL_ORDER_OMS)
    public ResponseEntity<BaseResponseModel> cancelOrder(@Valid @RequestBody OrderEvent orderEvent) throws Exception {
        orderEventsFacade.processInterceptorOrderEvent(orderEvent);
        return CommonResponseBuilder.successResponse(CommonSuccessResponseCodes.PROCESSED);
    }


    @RestLogging
    @PutMapping(ControllerConstants.REASSIGN_ORDER_OMS)
    public ResponseEntity<BaseResponseModel> reassignOrderOms(@PathVariable String wmsOrderCode) throws ApplicationException {
        reassignHelperFacade.reassignOrderOms(wmsOrderCode, OrderSubStatus.OMS_REASSIGNED.name());
        return CommonResponseBuilder.successResponse(OmsSuccessResponseCodes.SUCCESSFULLY_OMS_REASSIGNED);
    }

    @RestLogging
    @Transactional
    @PostMapping(ControllerConstants.PUBLISH_ORDER_EVENT)
    public ResponseEntity<BaseResponseModel> publishOrderEvent(@RequestBody OrderEvent orderEvent) throws ApplicationException {
        orderEventsFacade.publishOrderEvent(orderEvent);
        return CommonResponseBuilder.successResponse(CommonSuccessResponseCodes.PROCESSED);
    }

    @RestLogging
    @PutMapping(ControllerConstants.TRIGGER_PENDING_ORDERS_FOR_PROCESSING)
    public void syncPendingOmsOrdersToProcessKafka(@RequestBody OrderReconciliationRequest orderReconciliationRequest) {
        orderReconciliationFacade.syncPendingOrdersToProcessKafka(orderReconciliationRequest);
    }

    @RestLogging
    @PutMapping(ControllerConstants.TRIGGER_HANDLE_READY_FOR_WH_SHIPMENT)
    public void triggerHandleReadyForWhShipment(@RequestBody OrderReconciliationRequest orderReconciliationRequest ) {
        readyForWhStuckOrderFacade.handleReadyForWhStuckOrder(orderReconciliationRequest);
    }

    @RestLogging
    @GetMapping(ControllerConstants.TRIGGER_HANDLE_READY_FOR_WH_STUCK_ORDER)
    public void triggerHandleReadyForWhStuckOrder() {
        readyForWhStuckOrderFacade.handleReadyForWhStuckOrder();
    }
    @RestLogging
    @GetMapping(ControllerConstants.ORDER_SHIPMENT_TIMELINE_DETAILS)
    public ResponseEntity<BaseResponseModel> fetchOrderShipmentAndTimelineDetails(@PathVariable Long incrementId) {
        try {
            return CommonResponseBuilder.successResponse(orderService.findOrderShipmentAndShipmentTimelineDetails(incrementId), BaseStatusCodesBase.RETRIEVED);
        } catch (Exception exception) {
            log.error("ERROR in retrieving Shipment and Timeline Details for incrementId : " + incrementId, exception);
            return CommonResponseBuilder.failureResponse(null, exception.getMessage(), ResponseCodes.INTERNAL_SERVER_ERROR_CODE);
        }
    }

    @Autowired
    OrderService orderService;

}
