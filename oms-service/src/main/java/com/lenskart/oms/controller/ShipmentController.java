package com.lenskart.oms.controller;

import com.lenskart.nexs.commonlogger.annotations.RestLogging;
import com.lenskart.nexs.commons.codes.BaseStatusCodesBase;
import com.lenskart.nexs.commons.controller.BaseController;
import com.lenskart.nexs.commons.dto.BaseResponseModel;
import com.lenskart.nexs.commons.response.CommonResponseBuilder;
import com.lenskart.nexs.commons.response.ResponseDTO;
import com.lenskart.oms.constants.ControllerConstants;
import com.lenskart.oms.consumer.B2bOmsVirtualOrderEventConsumer;
import com.lenskart.oms.dto.ShipmentDetailsForB2bEvent;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.entity.Shipment;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.facade.OTCOrderFacade;
import com.lenskart.oms.facade.ReadyForWhStuckOrderFacade;
import com.lenskart.oms.facade.ShipmentEventsFacade;
import com.lenskart.oms.model.UwOrder;
import com.lenskart.oms.request.CreateInvoiceForLocalFitting;
import com.lenskart.oms.request.OtcShipmentEvent;
import com.lenskart.oms.request.ShipmentUpdateEvent;
import com.lenskart.oms.request.UpdateWmsOrderCodeRequest;
import com.lenskart.oms.response.OmsSuccessResponseCodes;
import com.lenskart.oms.service.OrderService;
import com.lenskart.order.interceptor.request.MarkLfOrderDispatchRequest;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotEmpty;
import java.util.List;


@Timed
@RestController
@Tag(name = "Shipment Controller")
@Setter(onMethod__ = {@Autowired})
@RequestMapping(ControllerConstants.BASE_URL_V1 + ControllerConstants.SHIPMENT)
public class ShipmentController extends BaseController<ResponseDTO, ShipmentDto, Shipment> {

    private ShipmentEventsFacade shipmentEventsFacade;
    private OrderService orderService;
    private OTCOrderFacade otcOrderFacade;
    private ReadyForWhStuckOrderFacade readyForWhStuckOrderFacade;
    private B2bOmsVirtualOrderEventConsumer b2bOmsVirtualOrderEventConsumer;

    @RestLogging
    @PostMapping("/backsyncStatusUpdate")
    public ResponseEntity<BaseResponseModel> backsyncStatusUpdate(@RequestBody ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        shipmentEventsFacade.processBackSyncUpdate(shipmentUpdateEvent);
        return CommonResponseBuilder.successResponse(OmsSuccessResponseCodes.SUCCESSFULLY_WMS_BACK_SYNC_REGISTERED);
    }

    @RestLogging
    @PostMapping("/markLfOrder/dispatch")
    public ResponseEntity<BaseResponseModel> markLfOrderDispatch(@RequestBody MarkLfOrderDispatchRequest markLfOrderDispatchRequest) throws Exception {
        shipmentEventsFacade.processLfDispatchUpdate(markLfOrderDispatchRequest);
        return CommonResponseBuilder.successResponse(OmsSuccessResponseCodes.SUCCESSFULLY_MARK_LF_DISPATCH_REGISTERED);
    }

    @PostMapping("/attachBarcode")
    public ResponseEntity<BaseResponseModel> attachBarcode(@RequestBody CreateInvoiceForLocalFitting createInvoiceForLocalFitting) throws Exception{
        orderService.attachBarcode(createInvoiceForLocalFitting);
        return CommonResponseBuilder.successResponse(OmsSuccessResponseCodes.SUCCESSFULLY_ATTACHED_BARCODE);
    }

    @PostMapping("/processOTCShipmentEvent")
    public ResponseEntity<BaseResponseModel> processOTCShipmentEvent(@RequestBody OtcShipmentEvent otcShipmentEvent) throws Exception{
        otcOrderFacade.processOTCShipmentEvent(otcShipmentEvent);
        return CommonResponseBuilder.successResponse(BaseStatusCodesBase.PROCESSED);
    }

    @RestLogging
    @PutMapping("/pushOtcOrderToWms")
    public ResponseEntity<BaseResponseModel> pushOtcOrderToWms(@RequestBody List<UwOrder> uwOrderList) {
        readyForWhStuckOrderFacade.pushWmsOrderEvent(uwOrderList);
        return CommonResponseBuilder.successResponse(BaseStatusCodesBase.PROCESSED);
    }

    @RestLogging
    @PostMapping("/triggerB2b")
    public ResponseEntity<BaseResponseModel> triggerB2b(@RequestBody ShipmentDetailsForB2bEvent shipmentDetailsForB2bEvent) throws Exception {
        b2bOmsVirtualOrderEventConsumer.trigger(shipmentDetailsForB2bEvent);
        return CommonResponseBuilder.successResponse(BaseStatusCodesBase.PROCESSED);
    }

    @PostMapping("/triggerB2bEmail")
    public ResponseEntity<BaseResponseModel> triggerB2bEmail(@NotEmpty @Email @RequestHeader String toEmail, @RequestBody OtcShipmentEvent otcShipmentEvent) throws Exception{
        shipmentEventsFacade.triggerB2bEmail(toEmail, otcShipmentEvent);
        return CommonResponseBuilder.successResponse(BaseStatusCodesBase.PROCESSED);
    }

    @RestLogging
    @PutMapping("/b2b/updateWmsOrderCode")
    public ResponseEntity<BaseResponseModel> updateWmsOrderCode(@RequestBody UpdateWmsOrderCodeRequest updateWmsOrderCode) throws Exception {
       shipmentEventsFacade.updateWmsOrderCode(updateWmsOrderCode);
        return CommonResponseBuilder.successResponse(BaseStatusCodesBase.PROCESSED);
    }

}
