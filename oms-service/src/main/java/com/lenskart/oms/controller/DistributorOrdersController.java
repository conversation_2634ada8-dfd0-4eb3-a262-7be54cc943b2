package com.lenskart.oms.controller;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.commonlogger.annotations.RestLogging;
import com.lenskart.nexs.commons.codes.CommonSuccessResponseCodes;
import com.lenskart.nexs.commons.constants.ResponseCodes;
import com.lenskart.nexs.commons.controller.BaseController;
import com.lenskart.nexs.commons.dto.BaseResponseModel;
import com.lenskart.nexs.commons.response.CommonResponseBuilder;
import com.lenskart.nexs.commons.response.ResponseDTO;
import com.lenskart.oms.constants.ControllerConstants;
import com.lenskart.oms.dto.DistributorOrdersDto;
import com.lenskart.oms.entity.DistributorOrders;
import com.lenskart.oms.enums.DoStatus;
import com.lenskart.oms.enums.DoType;
import com.lenskart.oms.enums.ShipmentEvent;
import com.lenskart.oms.enums.OutboxEventAggregateType;
import com.lenskart.oms.enums.OutboxEventType;
import com.lenskart.oms.facade.DistributorOrderFacade;
import com.lenskart.oms.producer.OrderEventWmsProducer;
import com.lenskart.oms.producer.OutboxKafkaProducer;
import com.lenskart.oms.request.distributor.DistributorOrderJITRequest;
import com.lenskart.oms.request.wms.WMSBulkOrderEvent;
import com.lenskart.oms.response.DoJitCreateOrderResponse;
import com.lenskart.oms.response.DoUploadOrderResponse;
import com.lenskart.oms.utils.ObjectHelper;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.lenskart.oms.constants.KafkaConstants.MESSAGE_IDEMPOTENCY_KEY;

@Timed
@RestController
@Tag(name = "Distributor Orders Controller")
@Setter(onMethod__ = {@Autowired})
@RequestMapping(ControllerConstants.BASE_URL_V1 + ControllerConstants.DISTRIBUTOR_ORDERS.CONTROLLER_ENDPOINT)
public class DistributorOrdersController extends BaseController<ResponseDTO, DistributorOrdersDto, DistributorOrders> {

    private DistributorOrderFacade distributorOrderFacade;
    private OutboxKafkaProducer outboxKafkaProducer;

    @CustomLogger
    @Setter(AccessLevel.NONE)
    protected Logger logger;

    @RestLogging
    @PostMapping(value = ControllerConstants.DISTRIBUTOR_ORDERS.ORDER_UPLOAD, consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public ResponseEntity<BaseResponseModel> upload(@NotEmpty @RequestPart(value = "csvFile", name = "csvFile") MultipartFile file,
                                                    @NotEmpty @RequestParam String poNumber, @NotEmpty @RequestParam String userName,
                                                    @NotEmpty @RequestParam String customerCode,
                                                    @NotEmpty @RequestParam String facilityCode,
                                                    @NotEmpty @RequestParam DoType doType) {
        DoUploadOrderResponse doUploadOrderResponse = null;
        try {
            doUploadOrderResponse = distributorOrderFacade.uploadOrder(file, poNumber, userName, customerCode, facilityCode, doType);
            if (doUploadOrderResponse != null && doUploadOrderResponse.getReferenceOrderId() != null)
                return CommonResponseBuilder.successResponse(doUploadOrderResponse, CommonSuccessResponseCodes.CREATED);
        } catch (Exception exception) {
            logger.error("DistributorOrdersController.upload For Po - " + poNumber, exception);
            return CommonResponseBuilder.failureResponse(null, exception.getMessage(), ResponseCodes.INTERNAL_SERVER_ERROR_CODE);
        }
        return CommonResponseBuilder.failureResponse(doUploadOrderResponse, "uploadOrderFailed", ResponseCodes.INTERNAL_SERVER_ERROR_CODE);
    }

    @RestLogging
    @PostMapping(value = ControllerConstants.DISTRIBUTOR_ORDERS.ORDER_CREATE)
    public ResponseEntity<BaseResponseModel> create(@NotEmpty @PathVariable(value = "refDoOrderId") String refDoOrderId,
                                                    @NotEmpty @RequestHeader String userName) {
        try {
            return CommonResponseBuilder.successResponse(distributorOrderFacade.createOrder(userName, refDoOrderId), CommonSuccessResponseCodes.CREATED);
        } catch (Exception exception) {
            logger.error("DistributorOrdersController.create For ReferenceOrderId - " + refDoOrderId, exception);
            return CommonResponseBuilder.failureResponse(null, exception.getMessage(), ResponseCodes.INTERNAL_SERVER_ERROR_CODE);
        }
    }

    @RestLogging
    @PostMapping(value = ControllerConstants.DISTRIBUTOR_ORDERS.ORDER_APPROVE)
    public ResponseEntity<BaseResponseModel> approve(@NotEmpty @PathVariable(value = "refDoOrderId") String refDoOrderId,
                                                     @NotEmpty @RequestHeader String userName) {
        try {
            return CommonResponseBuilder.successResponse(distributorOrderFacade.approveOrder(userName, refDoOrderId), CommonSuccessResponseCodes.CREATED);
        } catch (Exception exception) {
            logger.error("DistributorOrdersController.approve For ReferenceOrderId - " + refDoOrderId, exception);
            return CommonResponseBuilder.failureResponse(null, exception.getMessage(), ResponseCodes.INTERNAL_SERVER_ERROR_CODE);
        }
    }

    @RestLogging
    @PostMapping(value = ControllerConstants.DISTRIBUTOR_ORDERS.ORDER_REJECT)
    public ResponseEntity<BaseResponseModel> reject(@NotEmpty @PathVariable(value = "refDoOrderId") String refDoOrderId,
                                                    @NotEmpty @RequestHeader String userName,
                                                    @NotEmpty @RequestHeader String status,
                                                    @RequestHeader String cancellationReason) {
        try {
            return CommonResponseBuilder.successResponse(distributorOrderFacade.rejectOrder(userName, refDoOrderId, status, cancellationReason), CommonSuccessResponseCodes.CREATED);
        } catch (Exception exception) {
            logger.error("DistributorOrdersController.reject For ReferenceOrderId - " + refDoOrderId, exception);
            return CommonResponseBuilder.failureResponse(null, exception.getMessage(), ResponseCodes.INTERNAL_SERVER_ERROR_CODE);
        }
    }

    @RestLogging
    @PostMapping(value = ControllerConstants.DISTRIBUTOR_ORDERS.ORDER_DETAILS)
    public ResponseEntity<BaseResponseModel> details(@NotEmpty @PathVariable(value = "incrementId") String incrementId,
                                                    @NotEmpty @RequestHeader String userName) {
        try {
            return CommonResponseBuilder.successResponse(distributorOrderFacade.orderDetails(userName, incrementId), CommonSuccessResponseCodes.CREATED);
        } catch (Exception exception) {
            logger.error("DistributorOrdersController.details For ReferenceOrderId - " + incrementId, exception);
            return CommonResponseBuilder.failureResponse(null, exception.getMessage(), ResponseCodes.INTERNAL_SERVER_ERROR_CODE);
        }
    }

    @RestLogging
    @Transactional(rollbackFor = Exception.class)
    @PostMapping(value = "/test/pushToKafka/{shipmentId}")
    public boolean pushToKafka(@NotEmpty @PathVariable(value = "shipmentId") String shipmentId,
                               @NotEmpty @RequestHeader String userName) throws Exception {
        try {
            WMSBulkOrderEvent wmsBulkOrderEvent = new WMSBulkOrderEvent();
            wmsBulkOrderEvent.setShipmentEvent(ShipmentEvent.CREATE_ORDER);
            wmsBulkOrderEvent.setShipmentId(String.valueOf(shipmentId));

            String messageIdempotencyKey = wmsBulkOrderEvent.getShipmentEvent().name() + "_" + wmsBulkOrderEvent.getShipmentId();

            Map<String, String> headers = new HashMap<>();
            headers.put(MESSAGE_IDEMPOTENCY_KEY, messageIdempotencyKey);

            outboxKafkaProducer.publishOrderEvent(
                    String.valueOf(wmsBulkOrderEvent.getShipmentId()),
                    OutboxEventAggregateType.ORDER_ID,
                    wmsBulkOrdersEventTopic,
                    ObjectHelper.writeValue(wmsBulkOrderEvent),
                    OutboxEventType.WMS_ORDER_EVENT,
                    headers,
                    new Date()
            );

            return true;
        } catch (Exception exception) {
            logger.error("controller DistributorOrdersController.approve For ReferenceOrderId - " + shipmentId, exception);
            throw new Exception("controller pushToKafka failed " + exception.getMessage());
        }
    }


    @RestLogging
    @PostMapping(value = ControllerConstants.DISTRIBUTOR_ORDERS.CREATE_JIT_AS_DO)
    public ResponseEntity<BaseResponseModel> createDoJitOrder(@RequestBody DistributorOrderJITRequest distributorOrderJITRequest) {
        DoJitCreateOrderResponse doJitCreateOrderResponse = null;
        try {
            doJitCreateOrderResponse = distributorOrderFacade.createJitDitributorOrder(distributorOrderJITRequest);
            if (doJitCreateOrderResponse != null && doJitCreateOrderResponse.isSuccess())
                return CommonResponseBuilder.successResponse(doJitCreateOrderResponse, CommonSuccessResponseCodes.CREATED);
        } catch (Exception exception) {
            logger.error("DistributorOrdersController.upload For Po - " + distributorOrderJITRequest.getPoNumber(), exception);
            return CommonResponseBuilder.failureResponse(null, exception.getMessage(), ResponseCodes.INTERNAL_SERVER_ERROR_CODE);
        }
        return CommonResponseBuilder.failureResponse(doJitCreateOrderResponse, "create order failed", ResponseCodes.INTERNAL_SERVER_ERROR_CODE);
    }

    @Autowired
    OrderEventWmsProducer orderEventWmsProducer;

    @Setter(AccessLevel.NONE)
    @Value("${wms.orders.event.topic}")
    private String wmsOrdersEventTopic;

    @Setter(AccessLevel.NONE)
    @Value("${wms.bulk.orders.event.topic:wms-bulk-order-events-topic}")
    private String wmsBulkOrdersEventTopic;

}
