package com.lenskart.oms.producer;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.enums.OutboxEventAggregateType;
import com.lenskart.oms.enums.OutboxEventType;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.request.OmsOrderEvent;
import com.lenskart.oms.utils.ObjectHelper;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.lenskart.oms.constants.KafkaConstants.MESSAGE_IDEMPOTENCY_KEY;

@Component
@Setter(onMethod__ = {@Autowired})
public class ProcessOrderEventOmsProducer {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    @Setter(AccessLevel.NONE)
    @Value("${oms.orders.event.process.topic}")
    private String omsOrdersEventProcessTopic;

    private OutboxKafkaProducer outboxKafkaProducer;

    @Transactional(rollbackFor = Exception.class)
    public void sendMessage(OmsOrderEvent omsOrderEvent) throws ApplicationException {
        try {
            String messageIdempotencyKey = omsOrderEvent.getEventType().name() + "_" + omsOrderEvent.getOrderDto().getId();

            Map<String, String> headers = new HashMap<>();
            headers.put(MESSAGE_IDEMPOTENCY_KEY, messageIdempotencyKey);

            outboxKafkaProducer.publishOrderEvent(
                    String.valueOf(omsOrderEvent.getIncrementId()),
                    OutboxEventAggregateType.ORDER_ID,
                    omsOrdersEventProcessTopic,
                    ObjectHelper.writeValue(omsOrderEvent),
                    OutboxEventType.POST_ORDER_CREATE,
                    headers,
                    new Date()
            );

            logger.info("Successfully queued process order event for incrementId: {} with eventType: {}",
                    omsOrderEvent.getIncrementId(), omsOrderEvent.getEventType());
        } catch (Exception exception) {
            String message = String.format("error while publishing message to topic: %s , payload: %s Error: %s",
                    omsOrdersEventProcessTopic, omsOrderEvent, exception.getMessage());
            logger.error(message, exception);
            throw new ApplicationException(message, exception);
        }
    }
}
