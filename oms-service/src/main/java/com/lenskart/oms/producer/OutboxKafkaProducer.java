package com.lenskart.oms.producer;

import com.lenskart.oms.dto.OutboxEventDto;
import com.lenskart.oms.enums.OutboxEventAggregateType;
import com.lenskart.oms.enums.OutboxEventType;
import com.lenskart.oms.service.OutboxEventService;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Outbox-based Kafka producer that ensures reliable event publishing
 * Uses the transactional outbox pattern to guarantee event delivery
 */
@Slf4j
@Component
@Setter(onMethod__ = {@Autowired})
public class OutboxKafkaProducer {

    private OutboxEventService outboxEventService;

    /**
     * Publish order event with headers to outbox for reliable Kafka delivery
     */
    @Transactional(rollbackFor = Exception.class)
    public void publishOrderEvent(String incrementId,
                                  OutboxEventAggregateType aggregateType,
                                  String topicName,
                                  String eventPayload,
                                  OutboxEventType eventType,
                                  Map<String, String> headers,
                                  Date scheduledAt) {
        try {
            log.info("[publishOrderEvent] Publishing {} event for order: {} to topic: {}",
                    eventType, incrementId, topicName);

            OutboxEventDto outboxEvent = outboxEventService.createOutboxEvent(
                    incrementId,                   // aggregateId
                    aggregateType,                 // aggregateType
                    eventType,                     // eventType
                    topicName,                     // topicName
                    incrementId,                   // partitionKey
                    eventPayload,                  // eventPayload
                    headers,                       // headers
                    scheduledAt                    // scheduledAt
            );

            log.info("[publishOrderEvent] Created outbox event: {} for order: {}",
                    outboxEvent.getId(), incrementId);

        } catch (Exception e) {
            log.error("[publishOrderEvent] Failed to publish {} event for order: {}",
                    eventType, incrementId, e);
            throw e;
        }
    }

}
