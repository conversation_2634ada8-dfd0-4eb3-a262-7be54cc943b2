package com.lenskart.oms.producer;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.enums.OutboxEventAggregateType;
import com.lenskart.oms.enums.OutboxEventType;
import com.lenskart.oms.exception.ApplicationException;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.lenskart.oms.constants.KafkaConstants.MESSAGE_IDEMPOTENCY_KEY;

@Component
@Setter(onMethod__ = {@Autowired})
public class FdsCreditNoteProducer {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    @Value("${fds.create.creditNote.topic}")
    @Setter(AccessLevel.NONE)
    private String fdsCreateCreditNote;

    private OutboxKafkaProducer outboxKafkaProducer;

    @Transactional(rollbackFor = Exception.class)
    public void sendMessage(String itemId) throws ApplicationException {
        try {
            Map<String, String> headers = new HashMap<>();
            headers.put(MESSAGE_IDEMPOTENCY_KEY, "CREATE_CREDIT_NOTE" + "_" + itemId);

            outboxKafkaProducer.publishOrderEvent(
                    itemId,
                    OutboxEventAggregateType.ORDER_ID,
                    fdsCreateCreditNote,
                    itemId,
                    OutboxEventType.ORDER_EVENT,
                    headers,
                    new Date()
            );

            logger.info("Successfully queued FDS credit note event for itemId: {}", itemId);
        } catch (Exception exception) {
            String message = String.format("error while publishing message to topic: %s , payload: %s Error: %s",
                    fdsCreateCreditNote, itemId, exception.getMessage());
            logger.error(message, exception);
            throw new ApplicationException(message, exception);
        }
    }

}