package com.lenskart.oms.producer;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.enums.OutboxEventAggregateType;
import com.lenskart.oms.enums.OutboxEventType;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.request.OrderOpsOrderEvent;
import com.lenskart.oms.utils.ObjectHelper;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.lenskart.oms.constants.KafkaConstants.MESSAGE_IDEMPOTENCY_KEY;

@Component
@Setter(onMethod__ = {@Autowired})
public class OrderOpsBackSyncProducer {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    @Setter(AccessLevel.NONE)
    @Value("${orderops.backsync.update.topic}")
    private String orderOpsBackSyncUpdateTopic;

    private OutboxKafkaProducer outboxKafkaProducer;

    @Transactional(rollbackFor = Exception.class)
    public void sendMessage(OrderOpsOrderEvent orderOpsOrderEvent) throws ApplicationException {
        try {
            String messageIdempotencyKey = orderOpsOrderEvent.getEventName() + "_" + orderOpsOrderEvent.getOrderId();

            Map<String, String> headers = new HashMap<>();
            headers.put(MESSAGE_IDEMPOTENCY_KEY, messageIdempotencyKey);

            outboxKafkaProducer.publishOrderEvent(
                    String.valueOf(orderOpsOrderEvent.getOrderId()),
                    OutboxEventAggregateType.ORDER_ID,
                    orderOpsBackSyncUpdateTopic,
                    ObjectHelper.writeValue(orderOpsOrderEvent),
                    OutboxEventType.ORDER_BACK_SYNC,
                    headers,
                    new Date()
            );

            logger.info("Successfully queued order ops back sync event for orderId: {}", orderOpsOrderEvent.getOrderId());
        } catch (Exception exception) {
            String message = String.format("error while publishing message to topic: %s , payload: %s Error: %s",
                    orderOpsBackSyncUpdateTopic, orderOpsOrderEvent, exception.getMessage());
            logger.error(message, exception);
            throw new ApplicationException(message, exception);
        }
    }
}
