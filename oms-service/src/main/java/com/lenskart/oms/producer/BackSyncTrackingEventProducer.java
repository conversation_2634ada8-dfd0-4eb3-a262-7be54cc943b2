package com.lenskart.oms.producer;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.dto.OrderBackSyncTrackingDto;
import com.lenskart.oms.enums.OutboxEventAggregateType;
import com.lenskart.oms.enums.OutboxEventType;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.utils.ObjectHelper;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.lenskart.oms.constants.KafkaConstants.MESSAGE_IDEMPOTENCY_KEY;

@Component
@Setter(onMethod__ = {@Autowired})
public class BackSyncTrackingEventProducer {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    @Setter(AccessLevel.NONE)
    @Value("${oms.backsync.tracking.topic}")
    private String omsBackSyncTrackingTopic;

    private OutboxKafkaProducer outboxKafkaProducer;

    @Transactional(rollbackFor = Exception.class)
    public void sendMessage(OrderBackSyncTrackingDto orderBackSyncTrackingDto) throws ApplicationException {
        try {
            String messageIdempotencyKey = orderBackSyncTrackingDto.getEventName() + "_" + orderBackSyncTrackingDto.getEntityId();

            Map<String, String> headers = new HashMap<>();
            headers.put(MESSAGE_IDEMPOTENCY_KEY, messageIdempotencyKey);

            outboxKafkaProducer.publishOrderEvent(
                    String.valueOf(orderBackSyncTrackingDto.getEntityId()),
                    OutboxEventAggregateType.ORDER_ID,
                    omsBackSyncTrackingTopic,
                    ObjectHelper.writeValue(orderBackSyncTrackingDto),
                    OutboxEventType.ORDER_BACK_SYNC,
                    headers,
                    new Date()
            );

            logger.info("Successfully queued back sync tracking event for entityId: {}", orderBackSyncTrackingDto.getEntityId());
        } catch (Exception exception) {
            String message = String.format("error while publishing message to topic: %s , payload: %s Error: %s",
                    omsBackSyncTrackingTopic, orderBackSyncTrackingDto, exception.getMessage());
            logger.error(message, exception);
            throw new ApplicationException(message, exception);
        }
    }
}
