package com.lenskart.oms.producer;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.enums.OutboxEventAggregateType;
import com.lenskart.oms.enums.OutboxEventType;
import com.lenskart.oms.exception.ApplicationException;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.lenskart.oms.constants.KafkaConstants.MESSAGE_IDEMPOTENCY_KEY;

@Component
@Setter(onMethod__ = {@Autowired})
public class OrderEventOmsProducer {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    @Setter(AccessLevel.NONE)
    @Value("${oms.orders.event.topic}")
    private String omsOrdersEventTopic;

    private OutboxKafkaProducer outboxKafkaProducer;

    @Transactional(rollbackFor = Exception.class)
    public void sendMessage(String kafkaPayload, Long incrementId, String eventType) throws ApplicationException {
        try {
            Map<String, String> headers = new HashMap<>();
            headers.put(MESSAGE_IDEMPOTENCY_KEY, eventType + "_" + incrementId);

            outboxKafkaProducer.publishOrderEvent(
                    String.valueOf(incrementId),
                    OutboxEventAggregateType.ORDER_ID,
                    omsOrdersEventTopic,
                    kafkaPayload,
                    OutboxEventType.ORDER_EVENT,
                    headers,
                    new Date()
            );

            logger.info("Successfully queued order event for incrementId: {} with eventType: {}", incrementId, eventType);
        } catch (Exception exception) {
            String message = String.format("error while publishing message to topic: %s , payload: %s Error: %s",
                    omsOrdersEventTopic, kafkaPayload, exception.getMessage());
            logger.error(message, exception);
            throw new ApplicationException(message, exception);
        }
    }
}
