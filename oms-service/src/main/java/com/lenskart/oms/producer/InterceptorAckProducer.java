package com.lenskart.oms.producer;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.utils.ObjectHelper;
import com.lenskart.order.interceptor.dto.OrderEventAcknowledgementDTO;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;

import static com.lenskart.oms.constants.KafkaConstants.MESSAGE_IDEMPOTENCY_KEY;

@Component
@Setter(onMethod__ = {@Autowired})
public class InterceptorAckProducer {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    @Setter(AccessLevel.NONE)
    @Value("${orders.event.ack.topic}")
    private String ordersEventAckTopic;

    private KafkaTemplate<String, String> kafkaTemplate;

    public void sendMessage(OrderEventAcknowledgementDTO orderEventAcknowledgementDTO) throws ApplicationException {
        try {
            ProducerRecord<String, String> producerRecord = new ProducerRecord<>(
                    ordersEventAckTopic,
                    String.valueOf(orderEventAcknowledgementDTO.getIncrementId()),
                    ObjectHelper.writeValue(orderEventAcknowledgementDTO)
            );
            String messageIdempotencyKey  = "interceptor_order_event_ack_" + orderEventAcknowledgementDTO.getIncrementId();
            producerRecord.headers()
                    .add(MESSAGE_IDEMPOTENCY_KEY, messageIdempotencyKey.getBytes());
            kafkaTemplate.send(producerRecord)
                    .get();
            logger.info("[sendEventAckToOrderInterceptor][sendMessage] message pushed to kafka successfully for orderEventAcknowledgementDTO payload: {}", orderEventAcknowledgementDTO);
        } catch (Exception exception) {
            String message = MessageFormat.format("error while publishing message to topic: {0} , payload: {1} Error: {2}",
                    ordersEventAckTopic, orderEventAcknowledgementDTO, exception.getMessage());
            logger.error(message, exception);
            throw new ApplicationException(message, exception);
        }
    }
}

