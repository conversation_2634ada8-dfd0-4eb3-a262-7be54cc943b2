package com.lenskart.oms.producer;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.enums.OutboxEventAggregateType;
import com.lenskart.oms.enums.OutboxEventType;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.utils.ObjectHelper;
import com.lenskart.order.interceptor.dto.OrderEventAcknowledgementDTO;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.lenskart.oms.constants.KafkaConstants.MESSAGE_IDEMPOTENCY_KEY;

@Component
@Setter(onMethod__ = {@Autowired})
public class InterceptorAckProducer {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    @Setter(AccessLevel.NONE)
    @Value("${orders.event.ack.topic}")
    private String ordersEventAckTopic;

    private OutboxKafkaProducer outboxKafkaProducer;

    @Transactional(rollbackFor = Exception.class)
    public void sendMessage(OrderEventAcknowledgementDTO orderEventAcknowledgementDTO) throws ApplicationException {
        try {
            String messageIdempotencyKey = "interceptor_order_event_ack_" + orderEventAcknowledgementDTO.getIncrementId();

            Map<String, String> headers = new HashMap<>();
            headers.put(MESSAGE_IDEMPOTENCY_KEY, messageIdempotencyKey);

            outboxKafkaProducer.publishOrderEvent(
                    String.valueOf(orderEventAcknowledgementDTO.getIncrementId()),
                    OutboxEventAggregateType.ORDER_ID,
                    ordersEventAckTopic,
                    ObjectHelper.writeValue(orderEventAcknowledgementDTO),
                    OutboxEventType.ORDER_EVENT,
                    headers,
                    new Date()
            );

            logger.info("[sendEventAckToOrderInterceptor][sendMessage] message pushed to outbox successfully for orderEventAcknowledgementDTO payload: {}", orderEventAcknowledgementDTO);
        } catch (Exception exception) {
            String message = String.format("error while publishing message to topic: %s , payload: %s Error: %s",
                    ordersEventAckTopic, orderEventAcknowledgementDTO, exception.getMessage());
            logger.error(message, exception);
            throw new ApplicationException(message, exception);
        }
    }
}

