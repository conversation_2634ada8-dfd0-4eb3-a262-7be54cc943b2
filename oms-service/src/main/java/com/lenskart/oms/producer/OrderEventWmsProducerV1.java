package com.lenskart.oms.producer;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.request.WmsOrderEvent;
import com.lenskart.oms.request.wms.WMSBulkOrderEvent;
import com.lenskart.oms.utils.ObjectHelper;
import com.lenskart.oms.utils.OmsCommonUtil;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.text.MessageFormat;

import static com.lenskart.oms.constants.KafkaConstants.MESSAGE_IDEMPOTENCY_KEY;

@Component
@Setter(onMethod__ = {@Autowired})
public class OrderEventWmsProducerV1 {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    @Setter(AccessLevel.NONE)
    @Value("${wms.orders.event.topic}")
    private String wmsOrdersEventTopic;

    @Setter(AccessLevel.NONE)
    @Value("${wms.bulk.orders.event.topic:wms-bulk-order-events-topic}")
    private String wmsBulkOrdersEventTopic;

    private KafkaTemplate<String, String> kafkaTemplate;
    private OmsCommonUtil omsCommonUtil;

    @Transactional(rollbackFor = Exception.class)
    public void sendMessage(WmsOrderEvent wmsOrderEvent) throws ApplicationException {
        try {
            logger.info("OrderEventWmsProducer sendMessage " + wmsOrderEvent.getShipmentDto().getWmsOrderCode());
            ProducerRecord<String, String> producerRecord;
            if (omsCommonUtil.isBulkOrder(wmsOrderEvent.getOrderDto())) {
                producerRecord = setProducerRecordForBulkOrder(wmsOrderEvent);
            } else {
                producerRecord = new ProducerRecord<>(wmsOrdersEventTopic, String.valueOf(wmsOrderEvent.getOrderDto().getIncrementId()), ObjectHelper.writeValue(wmsOrderEvent));

                String messageIdempotencyKey = wmsOrderEvent.getShipmentEvent().name() + "_" + wmsOrderEvent.getShipmentDto().getId();
                producerRecord.headers().add(MESSAGE_IDEMPOTENCY_KEY, messageIdempotencyKey.getBytes());
            }
            kafkaTemplate.send(producerRecord).get();
            logger.info("OrderEventWmsProducer sendMessage success " + wmsOrderEvent.getOrderDto().getJunoOrderId());
        } catch (Exception exception) {
            String message = MessageFormat.format("error while publishing message to topic: {0} , payload: {1} Error: {2}", wmsOrdersEventTopic, wmsOrderEvent, exception.getMessage());
            logger.error(message, exception);
            logger.error("OrderEventWmsProducer sendMessage order {} error {}", wmsOrderEvent.getOrderDto().getJunoOrderId(), exception.getMessage(), exception);
            throw new ApplicationException(message, exception);
        }
    }

    public ProducerRecord<String, String> setProducerRecordForBulkOrder(WmsOrderEvent wmsOrderEvent) throws ApplicationException {
        logger.info("OrderEventWmsProducer setProducerRecordForBulkOrder entered " + wmsOrderEvent.getOrderDto().getJunoOrderId());
        WMSBulkOrderEvent wmsBulkOrderEvent = new WMSBulkOrderEvent();
        wmsBulkOrderEvent.setShipmentEvent(wmsOrderEvent.getShipmentEvent());
        wmsBulkOrderEvent.setShipmentId(String.valueOf(wmsOrderEvent.getShipmentDto().getId()));
        ProducerRecord<String, String> producerRecord = new ProducerRecord<>(wmsBulkOrdersEventTopic, String.valueOf(wmsBulkOrderEvent.getShipmentId()), ObjectHelper.writeValue(wmsBulkOrderEvent));
        String messageIdempotencyKey = wmsBulkOrderEvent.getShipmentEvent().name() + "_" + wmsBulkOrderEvent.getShipmentId();
        producerRecord.headers().add(MESSAGE_IDEMPOTENCY_KEY, messageIdempotencyKey.getBytes());
        logger.info("OrderEventWmsProducer setProducerRecordForBulkOrder exited " + wmsOrderEvent.getOrderDto().getJunoOrderId());
        return producerRecord;
    }
}
