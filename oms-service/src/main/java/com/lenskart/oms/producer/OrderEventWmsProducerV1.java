package com.lenskart.oms.producer;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.enums.OutboxEventAggregateType;
import com.lenskart.oms.enums.OutboxEventType;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.request.WmsOrderEvent;
import com.lenskart.oms.request.wms.WMSBulkOrderEvent;
import com.lenskart.oms.utils.ObjectHelper;
import com.lenskart.oms.utils.OmsCommonUtil;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.lenskart.oms.constants.KafkaConstants.MESSAGE_IDEMPOTENCY_KEY;

@Component
@Setter(onMethod__ = {@Autowired})
public class OrderEventWmsProducerV1 {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    @Setter(AccessLevel.NONE)
    @Value("${wms.orders.event.topic}")
    private String wmsOrdersEventTopic;

    @Setter(AccessLevel.NONE)
    @Value("${wms.bulk.orders.event.topic:wms-bulk-order-events-topic}")
    private String wmsBulkOrdersEventTopic;

    private OutboxKafkaProducer outboxKafkaProducer;
    private OmsCommonUtil omsCommonUtil;

    @Transactional(rollbackFor = Exception.class)
    public void sendMessage(WmsOrderEvent wmsOrderEvent) throws ApplicationException {
        try {
            logger.info("OrderEventWmsProducer sendMessage " + wmsOrderEvent.getShipmentDto().getWmsOrderCode());

            if (omsCommonUtil.isBulkOrder(wmsOrderEvent.getOrderDto())) {
                sendBulkOrderMessage(wmsOrderEvent);
            } else {
                sendRegularOrderMessage(wmsOrderEvent);
            }

            logger.info("OrderEventWmsProducer sendMessage success " + wmsOrderEvent.getOrderDto().getJunoOrderId());
        } catch (Exception exception) {
            String message = String.format("error while publishing message to topic: %s , payload: %s Error: %s",
                    wmsOrdersEventTopic, wmsOrderEvent, exception.getMessage());
            logger.error(message, exception);
            logger.error("OrderEventWmsProducer sendMessage order {} error {}", wmsOrderEvent.getOrderDto().getJunoOrderId(), exception.getMessage(), exception);
            throw new ApplicationException(message, exception);
        }
    }

    private void sendRegularOrderMessage(WmsOrderEvent wmsOrderEvent) {
        String messageIdempotencyKey = wmsOrderEvent.getShipmentEvent().name() + "_" + wmsOrderEvent.getShipmentDto().getId();

        Map<String, String> headers = new HashMap<>();
        headers.put(MESSAGE_IDEMPOTENCY_KEY, messageIdempotencyKey);

        outboxKafkaProducer.publishOrderEvent(
                String.valueOf(wmsOrderEvent.getOrderDto().getIncrementId()),
                OutboxEventAggregateType.ORDER_ID,
                wmsOrdersEventTopic,
                ObjectHelper.writeValue(wmsOrderEvent),
                OutboxEventType.WMS_ORDER_EVENT,
                headers,
                new Date()
        );
    }

    private void sendBulkOrderMessage(WmsOrderEvent wmsOrderEvent) {
        WMSBulkOrderEvent wmsBulkOrderEvent = new WMSBulkOrderEvent();
        wmsBulkOrderEvent.setShipmentEvent(wmsOrderEvent.getShipmentEvent());
        wmsBulkOrderEvent.setShipmentId(String.valueOf(wmsOrderEvent.getShipmentDto().getId()));

        String messageIdempotencyKey = wmsBulkOrderEvent.getShipmentEvent().name() + "_" + wmsBulkOrderEvent.getShipmentId();

        Map<String, String> headers = new HashMap<>();
        headers.put(MESSAGE_IDEMPOTENCY_KEY, messageIdempotencyKey);

        outboxKafkaProducer.publishOrderEvent(
                String.valueOf(wmsBulkOrderEvent.getShipmentId()),
                OutboxEventAggregateType.ORDER_ID,
                wmsBulkOrdersEventTopic,
                ObjectHelper.writeValue(wmsBulkOrderEvent),
                OutboxEventType.WMS_ORDER_EVENT,
                headers,
                new Date()
        );
    }


}
