package com.lenskart.oms.producer;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.exception.ApplicationException;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;

import static com.lenskart.oms.constants.KafkaConstants.MESSAGE_IDEMPOTENCY_KEY;

@Component
@Setter(onMethod__ = {@Autowired})
public class CommonKafkaProducer {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    private KafkaTemplate<String, String> kafkaTemplate;

    public void sendMessage(String topicName, String key, String message, String messageIdempotencyKey) throws ApplicationException {
        try {
            logger.info("sending kafka message. topic: {} messageIdempotencyKey: {}", topicName, messageIdempotencyKey);
            ProducerRecord<String, String> producerRecord = new ProducerRecord<>(
                    topicName,
                    key,
                    message
            );
            producerRecord.headers().add(MESSAGE_IDEMPOTENCY_KEY, messageIdempotencyKey.getBytes());
            kafkaTemplate.send(producerRecord).get();
        } catch (Exception e) {
            String error = MessageFormat.format("error while publishing message to topic: {0} , payload: {1} Error: {2}",
                    topicName, message, e.getMessage()
            );
            logger.error(error, e);
            throw new ApplicationException(message, e);
        }
    }
}
