package com.lenskart.oms.producer;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.enums.OutboxEventAggregateType;
import com.lenskart.oms.enums.OutboxEventType;
import com.lenskart.oms.exception.ApplicationException;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.lenskart.oms.constants.KafkaConstants.MESSAGE_IDEMPOTENCY_KEY;

@Component
@Setter(onMethod__ = {@Autowired})
public class CommonKafkaProducer {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    private OutboxKafkaProducer outboxKafkaProducer;

    @Transactional(rollbackFor = Exception.class)
    public void sendMessage(String topicName, String key, String message, String messageIdempotencyKey) throws ApplicationException {
        try {
            logger.info("sending kafka message. topic: {} messageIdempotencyKey: {}", topicName, messageIdempotencyKey);

            Map<String, String> headers = new HashMap<>();
            headers.put(MESSAGE_IDEMPOTENCY_KEY, messageIdempotencyKey);

            outboxKafkaProducer.publishOrderEvent(
                    key,
                    OutboxEventAggregateType.ORDER_ID,
                    topicName,
                    message,
                    OutboxEventType.ORDER_EVENT,
                    headers,
                    new Date()
            );

            logger.info("Successfully queued common kafka event for key: {} to topic: {}", key, topicName);
        } catch (Exception e) {
            String error = String.format("error while publishing message to topic: %s , payload: %s Error: %s",
                    topicName, message, e.getMessage()
            );
            logger.error(error, e);
            throw new ApplicationException(error, e);
        }
    }
}
