package com.lenskart.oms.producer;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.enums.OutboxEventAggregateType;
import com.lenskart.oms.enums.OutboxEventType;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.model.MarkFoundAsyncPayload;
import com.lenskart.oms.utils.ObjectHelper;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.lenskart.oms.constants.KafkaConstants.MESSAGE_IDEMPOTENCY_KEY;

@Component
@Setter(onMethod__ = {@Autowired})
public class MarkOrderFoundProducer {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    @Setter(AccessLevel.NONE)
    @Value("${mark.order.found.topic:mark-order-found}")
    private String markOrderFoundTopic;

    private OutboxKafkaProducer outboxKafkaProducer;

    @Transactional(rollbackFor = Exception.class)
    public void sendMessage(MarkFoundAsyncPayload markFoundAsyncPayload, Long uwItemId) throws ApplicationException {
        try {
            String messageIdempotencyKey = "MarkOrderFound_" + uwItemId;

            Map<String, String> headers = new HashMap<>();
            headers.put(MESSAGE_IDEMPOTENCY_KEY, messageIdempotencyKey);

            outboxKafkaProducer.publishOrderEvent(
                    String.valueOf(uwItemId),
                    OutboxEventAggregateType.ORDER_ID,
                    markOrderFoundTopic,
                    ObjectHelper.writeValue(markFoundAsyncPayload),
                    OutboxEventType.ORDER_EVENT,
                    headers,
                    new Date()
            );

            logger.info("Successfully queued mark order found event for uwItemId: {}", uwItemId);
        } catch (Exception exception) {
            String message = String.format("error while publishing message to topic: %s , payload: %s Error: %s",
                    markOrderFoundTopic, markFoundAsyncPayload, exception.getMessage());
            logger.error(message, exception);
            throw new ApplicationException(message, exception);
        }
    }
}
