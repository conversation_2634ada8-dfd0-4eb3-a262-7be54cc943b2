package com.lenskart.oms.producer;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.model.MarkFoundAsyncPayload;
import com.lenskart.oms.utils.ObjectHelper;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.util.Map;

import static com.lenskart.oms.constants.KafkaConstants.MESSAGE_IDEMPOTENCY_KEY;

@Component
@Setter(onMethod__ = {@Autowired})
public class MarkOrderFoundProducer {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    @Setter(AccessLevel.NONE)
    @Value("${mark.order.found.topic:mark-order-found}")
    private String markOrderFoundTopic;

    private KafkaTemplate<String, String> kafkaTemplate;

    public void sendMessage(MarkFoundAsyncPayload markFoundAsyncPayload, Long uwItemId) throws ApplicationException {
        try {
            ProducerRecord<String, String> producerRecord = new ProducerRecord<>(
                    markOrderFoundTopic,
                    String.valueOf(uwItemId),
                    ObjectHelper.writeValue(markFoundAsyncPayload)
            );

            String messageIdempotencyKey  =  "MarkOrderFound_" + uwItemId;
            producerRecord.headers()
                    .add(MESSAGE_IDEMPOTENCY_KEY, messageIdempotencyKey.getBytes());
            kafkaTemplate.send(producerRecord)
                    .get();
        } catch (Exception exception) {
            String message = MessageFormat.format("error while publishing message to topic: {0} , payload: {1} Error: {2}",
                    markOrderFoundTopic, markFoundAsyncPayload, exception.getMessage());
            logger.error(message, exception);
            throw new ApplicationException(message, exception);
        }
    }
}
