package com.lenskart.oms.producer;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.enums.OutboxEventAggregateType;
import com.lenskart.oms.enums.OutboxEventType;
import com.lenskart.oms.exception.ApplicationException;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.lenskart.oms.constants.KafkaConstants.MESSAGE_IDEMPOTENCY_KEY;

@Component
@Setter(onMethod__ = {@Autowired})
public class DistributorOrderEventOmsProducer {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    @Setter(AccessLevel.NONE)
    @Value("${oms.do.orders.event.topic:oms-do-order-events-topic}")
    private String omsDistributorOrdersEventTopic;

    private OutboxKafkaProducer outboxKafkaProducer;

    @Transactional(rollbackFor = Exception.class)
    public void sendMessage(String kafkaPayload, String incrementId, String eventType) throws ApplicationException {
        try {
            Map<String, String> headers = new HashMap<>();
            headers.put(MESSAGE_IDEMPOTENCY_KEY, eventType + "_" + incrementId);

            outboxKafkaProducer.publishOrderEvent(
                    incrementId,
                    OutboxEventAggregateType.ORDER_ID,
                    omsDistributorOrdersEventTopic,
                    kafkaPayload,
                    OutboxEventType.DISTRIBUTOR_ORDER_EVENT,
                    headers,
                    new Date()
            );

            logger.info("Successfully queued distributor order event for incrementId: {} with eventType: {}", incrementId, eventType);
        } catch (Exception exception) {
            String message = String.format("error while publishing message to topic: %s , payload: %s Error: %s",
                    omsDistributorOrdersEventTopic, kafkaPayload, exception.getMessage());
            logger.error(message, exception);
            throw new ApplicationException(message, exception);
        }
    }
}
