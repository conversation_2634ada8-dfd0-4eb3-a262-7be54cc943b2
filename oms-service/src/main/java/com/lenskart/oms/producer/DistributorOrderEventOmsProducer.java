package com.lenskart.oms.producer;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.exception.ApplicationException;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;

import static com.lenskart.oms.constants.KafkaConstants.MESSAGE_IDEMPOTENCY_KEY;

@Component
@Setter(onMethod__ = {@Autowired})
public class DistributorOrderEventOmsProducer {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    @Setter(AccessLevel.NONE)
    @Value("${oms.do.orders.event.topic:oms-do-order-events-topic}")
    private String omsDistributorOrdersEventTopic;

    private KafkaTemplate<String, String> kafkaTemplate;

    public void sendMessage(String kafkaPayload, String incrementId, String eventType) throws ApplicationException {
        try {
            ProducerRecord<String, String> producerRecord = new ProducerRecord<>(
                    omsDistributorOrdersEventTopic,
                    incrementId,
                    kafkaPayload
            );
            producerRecord.headers()
                    .add(MESSAGE_IDEMPOTENCY_KEY, (eventType + "_" + incrementId).getBytes());
            kafkaTemplate.send(producerRecord)
                    .get();
        } catch (Exception exception) {
            String message = MessageFormat.format("error while publishing message to topic: {0} , payload: {1} Error: {2}",
                    omsDistributorOrdersEventTopic, kafkaPayload, exception.getMessage());
            logger.error(message, exception);
            throw new ApplicationException(message, exception);
        }
    }
}
