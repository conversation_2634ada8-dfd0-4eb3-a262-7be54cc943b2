package com.lenskart.oms.producer;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.enums.OutboxEventAggregateType;
import com.lenskart.oms.enums.OutboxEventType;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.request.ShipmentUpdateEvent;
import com.lenskart.oms.utils.ObjectHelper;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Component
@Setter(onMethod__ = {@Autowired})
public class ShipmentEventOmsProducer {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    private OutboxKafkaProducer outboxKafkaProducer;

    @Transactional(rollbackFor = Exception.class)
    public void sendMessage(ShipmentUpdateEvent shipmentUpdateEvent, String topicName, Map<String, String> additionalHeaders, String hashKey) throws ApplicationException {
        try {
            Map<String, String> headers = new HashMap<>();
            if (additionalHeaders != null) {
                headers.putAll(additionalHeaders);
            }

            outboxKafkaProducer.publishOrderEvent(
                    hashKey,
                    OutboxEventAggregateType.ORDER_ID,
                    topicName,
                    ObjectHelper.writeValue(shipmentUpdateEvent),
                    OutboxEventType.SHIPMENT_EVENT,
                    headers,
                    new Date()
            );

            logger.info("Successfully queued shipment event for hashKey: {} to topic: {}", hashKey, topicName);
        } catch (Exception exception) {
            String message = String.format("error while publishing message to topic: %s , payload: %s Error: %s",
                    topicName, shipmentUpdateEvent, exception.getMessage());
            logger.error(message, exception);
            throw new ApplicationException(message, exception);
        }
    }
}
