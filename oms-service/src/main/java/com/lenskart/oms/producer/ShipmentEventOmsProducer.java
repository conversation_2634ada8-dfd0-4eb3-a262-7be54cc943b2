package com.lenskart.oms.producer;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.request.ShipmentUpdateEvent;
import com.lenskart.oms.utils.ObjectHelper;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.util.Map;

@Component
@Setter(onMethod__ = {@Autowired})
public class ShipmentEventOmsProducer {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    private KafkaTemplate<String, String> kafkaTemplate;

    public void sendMessage(ShipmentUpdateEvent shipmentUpdateEvent, String topicName, Map<String, String> additionalHeaders, String hashKey) throws ApplicationException {
        try {
            ProducerRecord<String, String> producerRecord = new ProducerRecord<>(
                    topicName,
                    hashKey,
                    ObjectHelper.writeValue(shipmentUpdateEvent)
            );
            if (additionalHeaders != null) {
                for (Map.Entry<String, String> item : additionalHeaders.entrySet()) {
                    producerRecord.headers().add(item.getKey(), item.getValue().getBytes());
                }
            }
            kafkaTemplate.send(producerRecord)
                    .get();
        } catch (Exception exception) {
            String message = MessageFormat.format("error while publishing message to topic: {0} , payload: {1} Error: {2}",
                    topicName, shipmentUpdateEvent, exception.getMessage());
            logger.error(message, exception);
            throw new ApplicationException(message, exception);
        }
    }
}
