package com.lenskart.oms.configs;

import com.lenskart.oms.request.InvoiceMailItem;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Getter
@Setter
@NoArgsConstructor
@ConfigurationProperties(
        prefix = "invoice.mails"
)
public class InvoiceMailConfig {
    private Map<String, InvoiceMailItem> country = new HashMap<>();
}
