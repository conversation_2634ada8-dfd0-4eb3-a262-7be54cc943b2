package com.lenskart.oms.configs;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Getter
@Setter
@NoArgsConstructor
@ConfigurationProperties(
        prefix = "callback"
)
public class CallbackProperties {
    private Map<String, String> juno = new HashMap();
    private Map<String, String> jj = new HashMap();
    private Map<String, String> lkWalletReversal = new HashMap();
    private Map<String, String> lkWalletCredit = new HashMap();
}
