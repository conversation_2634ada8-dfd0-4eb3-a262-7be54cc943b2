package com.lenskart.oms.configs;

import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.listener.DeadLetterPublishingRecoverer;
import org.springframework.kafka.listener.DefaultErrorHandler;
import org.springframework.util.backoff.FixedBackOff;

import java.util.HashMap;
import java.util.Map;

import static com.lenskart.oms.constants.KafkaConstants.OMS_BACKSYNC_EVENT_GROUP_ID;
import static com.lenskart.oms.constants.KafkaConstants.OMS_BACKSYNC_TRACKING_EVENT_GROUP_ID;

@Configuration
@EnableKafka
public class KafkaConsumerConfig {

    @Autowired
    private Environment environment;

    private static final FixedBackOff BACK_SYNC_TRACKING_BACK_OFF = new FixedBackOff(1000L, 4L);

    @Bean
    public ConcurrentKafkaListenerContainerFactory<String, String> kafkaListenerContainerFactory(final ConsumerFactory<String, String> consumerFactory,
                                                                                                 final KafkaTemplate<String, String> template) {
        final ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(consumerFactory);
        factory.setConcurrency(environment.containsProperty("spring.kafka.consumer.concurrency") ? environment.getProperty("spring.kafka.consumer.concurrency", Integer.class) : 1);
        factory.setCommonErrorHandler(new DefaultErrorHandler(new DeadLetterPublishingRecoverer(template), new FixedBackOff(environment.getProperty("spring.kafka.consumer.backoff.interval", Integer.class), environment.getProperty("spring.kafka.consumer.backoff.retryCount", Integer.class))));
        return factory;
    }

    private ConsumerFactory<String, String> backsyncConsumerFactory() {
        Map<String, Object> config = new HashMap<>();
        config.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, environment.getProperty("spring.kafka.bootstrap-servers"));
        config.put(ConsumerConfig.GROUP_ID_CONFIG, OMS_BACKSYNC_EVENT_GROUP_ID);
        config.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        config.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        config.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, environment.containsProperty("backsync.consumer.max.polls.records") ? environment.getProperty("backsync.consumer.max.polls.records", Integer.class) : 50);
        config.put(ConsumerConfig.ISOLATION_LEVEL_CONFIG, "read_committed");
        config.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, "false");
        return new DefaultKafkaConsumerFactory<>(config);
    }

    @Bean("backsyncKafkaListenerContainerFactory")
    public ConcurrentKafkaListenerContainerFactory<String, String> orderAdaptorKafkaListenerContainerFactory(final KafkaTemplate<String, String> template) {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(backsyncConsumerFactory());
        factory.setConcurrency(environment.containsProperty("spring.kafka.consumer.concurrency") ? environment.getProperty("spring.kafka.consumer.concurrency", Integer.class) : 1);
        factory.setCommonErrorHandler(new DefaultErrorHandler(new DeadLetterPublishingRecoverer(template), new FixedBackOff(environment.getProperty("spring.kafka.consumer.backoff.interval", Integer.class), environment.getProperty("spring.kafka.consumer.backoff.retryCount", Integer.class))));
        return factory;
    }

    @Bean("backsyncTrackingKafkaListenerContainerFactory")
    public ConcurrentKafkaListenerContainerFactory<String, String> backSyncTrackingKafkaListenerContainerFactory(final KafkaTemplate<String, String> template) {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(backsyncTrackingConsumerFactory());
        factory.setConcurrency(environment.containsProperty("spring.kafka.consumer.concurrency") ? environment.getProperty("spring.kafka.consumer.concurrency", Integer.class) : 1);
        factory.setCommonErrorHandler(new DefaultErrorHandler(new DeadLetterPublishingRecoverer(template), new FixedBackOff(environment.getProperty("spring.kafka.consumer.backoff.interval", Integer.class), environment.getProperty("spring.kafka.consumer.backoff.retryCount", Integer.class))));
        return factory;
    }

    private ConsumerFactory<String, String> backsyncTrackingConsumerFactory() {
        Map<String, Object> config = new HashMap<>();
        config.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, environment.getProperty("spring.kafka.bootstrap-servers"));
        config.put(ConsumerConfig.GROUP_ID_CONFIG, OMS_BACKSYNC_TRACKING_EVENT_GROUP_ID);
        config.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        config.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        config.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, environment.containsProperty("backsync.consumer.max.polls.records") ? environment.getProperty("backsync.consumer.max.polls.records", Integer.class) : 50);
        config.put(ConsumerConfig.ISOLATION_LEVEL_CONFIG, "read_committed");
        config.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, "false");
        return new DefaultKafkaConsumerFactory<>(config);
    }
}
