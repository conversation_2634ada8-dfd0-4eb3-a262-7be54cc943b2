package com.lenskart.oms.configs;

import org.springframework.context.annotation.Configuration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.vault.config.SecretBackendConfigurer;
import org.springframework.cloud.vault.config.VaultConfigurer;

@Configuration
@Slf4j
public class VaultConfigManager implements VaultConfigurer {

    @Value("${VAULT_BACKEND:lenskart/c2d/c2d-config}")
    String vault_backend;

    @Value("${VAULT_DEFAULT_CONTEXT:scm-order-sensei}")
    String appName;

    @Override
    public void addSecretBackends(SecretBackendConfigurer configurer) {
        configurer.add(vault_backend + "/" + appName);
        configurer.registerDefaultDiscoveredSecretBackends(false);
    }
}
