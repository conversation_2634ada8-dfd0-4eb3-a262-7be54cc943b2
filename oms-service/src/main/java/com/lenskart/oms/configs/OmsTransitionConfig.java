package com.lenskart.oms.configs;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.model.OmsEvents;
import com.lenskart.oms.model.OrderItemOperations;
import com.lenskart.oms.model.OrderItemTransitions;
import com.lenskart.oms.utils.ObjectHelper;
import com.lenskart.oms.utils.OmsTransitionsKeyDeserializer;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.stream.Collectors;

@Configuration
public class OmsTransitionConfig {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    @Bean(name = "omsEvents")
    public OmsEvents getEventsResource() throws JsonProcessingException, ApplicationException {
        String eventString = getEvents("/omsEvents.json");
        return ObjectHelper.getObjectMapper().readValue(eventString, OmsEvents.class);
    }

    private String getEvents(String filePath) throws ApplicationException {
        String eventString;
        try {
            try (InputStream inputStream = getClass().getResourceAsStream(filePath)) {
                assert inputStream != null;
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
                    eventString = reader.lines()
                            .collect(Collectors.joining(System.lineSeparator()));
                }
            }
        } catch (IOException e) {
            logger.error("error while initializing events bean from json: {}", filePath);
            throw new ApplicationException("error while initializing events bean with message " + e.getMessage(), null);
        }
        SimpleModule simpleModule = new SimpleModule();
        simpleModule.addKeyDeserializer(OrderItemOperations.class, new OmsTransitionsKeyDeserializer());
        ObjectHelper.getObjectMapper().registerModule(simpleModule);
        return eventString;
    }
}
