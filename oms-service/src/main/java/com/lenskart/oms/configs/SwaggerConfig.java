package com.lenskart.oms.configs;

import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.ExternalDocumentation;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.Operation;
import io.swagger.v3.oas.models.headers.Header;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.media.StringSchema;
import io.swagger.v3.oas.models.parameters.Parameter;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import org.elasticsearch.core.List;
import org.springdoc.core.customizers.OperationCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;

@Configuration
public class SwaggerConfig {

    @Bean
    public OpenAPI openAPI() {
        final String securitySchemeName = "bearerAuth";
        return new OpenAPI()
                .components(
                        new Components()
                                .addSecuritySchemes(securitySchemeName,
                                        new SecurityScheme()
                                                .type(SecurityScheme.Type.HTTP)
                                                .scheme("basic")
                        )
                        .addHeaders("x-client-key",
                                new Header()
                                        .description("x-client-key")
                                        .schema(new StringSchema())
                        )
                )
                .security(List.of(
                        new SecurityRequirement()
                                .addList(securitySchemeName)
                        )
                )
                .info(
                        new Info()
                                .title("OMS API")
                                .description("Order Management System")
                                .version("v0.0.1")
                                .license(
                                        new License()
                                                .name("Lenskart")
                                                .url("https://lenskart.in")
                                )
                )
                .externalDocs(
                        new ExternalDocumentation()
                                .description("OMS Documentation")
                );
    }

    @Component
    public class globalHeaderOperationCustomizer implements OperationCustomizer {

        @Override
        public Operation customize(Operation operation, HandlerMethod handlerMethod) {
            Parameter xClientKey =
                    new Parameter()
                            .in(ParameterIn.HEADER.toString())
                            .name("x-client-key")
                            .schema(new StringSchema())
                            .required(true);

            operation.addParametersItem(xClientKey);
            return operation;
        }
    }
}