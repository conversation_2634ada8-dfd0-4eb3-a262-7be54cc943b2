package com.lenskart.oms.repository;

import com.lenskart.nexs.commons.repository.BaseRepository;
import com.lenskart.oms.entity.Order;
import com.lenskart.oms.enums.OrderStatus;
import com.lenskart.oms.enums.OrderSubStatus;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Set;

import javax.persistence.Tuple;

@Repository
public interface OrderRepository extends BaseRepository<Order> {
    List<Order> findByOrderStatusAndOrderSubStatusAndUpdatedAtBetween(OrderStatus orderStatus, OrderSubStatus orderSubStatus, Date startHour, Date endHour);
    List<Order> findByOrderStatusAndUpdatedAtBetween(OrderStatus orderStatus, Date startHour, Date endHour);
    List<Order> findByOrderStatusInAndCreatedAtBetween(Set<OrderStatus> orderStatus, Date startHour, Date endHour);
    List<Order> findByOrderStatusInAndUpdatedAtBetween(Set<OrderStatus> orderStatus, Date startHour, Date endHour);

    @Query(value = "select increment_id from order_sensei.orders where order_status in ?2 and updated_at < ?1", nativeQuery = true)
    List<Long> findByStatusInAndUpdatedAt(Date endDate, Set<String> nonProcessingStatus);

    @Modifying
    @Query(value = "UPDATE Order e set e.orderStatus = :status, e.orderSubStatus = :subStatus WHERE e.id = :id")
    void updateStatus(@Param("id") Long id, @Param("status") OrderStatus status, @Param("subStatus") OrderSubStatus subStatus);

    @Query(
            value = "select o.juno_order_id, o.order_status, o.order_sub_status, "
                    + "s.wms_order_code, s.wms_shipping_package_id, s.shipment_type, s.shipment_sub_type, "
                    + "s.shipment_status, s.shipment_sub_status, st.invoice_time, st.manifest_time, "
                    + "st.dispatch_time, st.delivered_time, st.created_at as shipment_timeline_created_at, "
                    + "o.created_at as order_created_at "
                    + "from orders o "
                    + "inner join shipment_timeline st on st.order_id = o.id "
                    + "inner join shipment s on s.id = st.shipment_id "
                    + "where o.increment_id = :incrementId "
                    + "group by s.id",
            nativeQuery = true)
    List<Tuple> findOrderShipmentAndShipmentTimelineDetails(Long incrementId);

    @Query(value = "SELECT id FROM orders WHERE increment_id = :incrementId", nativeQuery = true)
    Long findOrderIdByIncrementId(@Param("incrementId") Long incrementId);

    @Modifying
    @Query(value = "UPDATE orders SET order_status = :status, order_sub_status = :subStatus WHERE id = :orderId", nativeQuery = true)
    void updateOrderStatus(@Param("orderId") Long orderId, @Param("status") String status, @Param("subStatus") String subStatus);
}
