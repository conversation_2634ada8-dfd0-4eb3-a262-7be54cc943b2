package com.lenskart.oms.repository;

import com.lenskart.oms.entity.UnicomMpSaleOrderItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class UnicomMpSaleOrderItemRepository {

    private final JdbcTemplate jdbcTemplate;

    @Autowired
    public UnicomMpSaleOrderItemRepository(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    public List<UnicomMpSaleOrderItem> findByBarcode(String barcode) {
        String sql = "SELECT barcode, sku_code, display_order_code, sale_order_item_code, created FROM unicom_mp_sale_order_item WHERE barcode = ?";

        return jdbcTemplate.query(sql, new Object[]{barcode},
                (rs, rowNum) -> new UnicomMpSaleOrderItem(
                        rs.getString("barcode"),
                        rs.getString("sku_code"),
                        rs.getString("display_order_code"),
                        rs.getString("sale_order_item_code"),
                        rs.getDate("created")));
    }

}
