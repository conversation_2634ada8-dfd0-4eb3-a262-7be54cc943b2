package com.lenskart.oms.repository;

import com.lenskart.nexs.commons.repository.BaseRepository;
import com.lenskart.oms.entity.OrderItems;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import javax.persistence.Tuple;
import java.util.List;

@Repository
public interface OrderItemRepository extends BaseRepository<OrderItems> {
    @Query(value = "select count(distinct shipment_id) from order_items where order_id=?1", nativeQuery = true)
    Integer getDistinctShipmentCount(Long orderId);
    List<OrderItems> findByMagentoItemId(Long magentoItemId);
    @Query(value = "update order_items set fitting_id = ?1 where id = ?2", nativeQuery = true)
    void updateFittingId(Long fittingId, Long orderItemId);

    @Modifying
    @Query(value = "update order_items set b2b_reference_item_id=?2 where id=?1", nativeQuery = true)
    void updateB2BRefId(Long orderItemId, Long b2bRefId);

    @Query(value = "select oi.id as id, oi.uw_item_id as uwItemId, oi.item_status as itemStatus, oi.order_id as orderId from order_items oi inner JOIN shipment s on s.id=oi.shipment_id where s.wms_order_code=?1", nativeQuery = true)
    List<Tuple> getOrderItems(String wmsOrderCode);

    @Modifying
    @Query(value = "update order_items oi set oi.item_status=?2 , oi.item_sub_status=?3 , oi.shipment_id=?4 , oi.order_id=?5  where oi.id in ?1", nativeQuery = true)
    void updateSkippedItems(List<Long> orderItemIds, String itemStatus, String itemSubStatus, Long shipmentId, Long orderId);

    @Query(value = "SELECT id FROM order_items WHERE order_id = :orderId", nativeQuery = true)
    List<Long> findIdsByOrderId(@Param("orderId") Long orderId);

    @Query(value = "SELECT item_status FROM order_items WHERE id = :id", nativeQuery = true)
    String findStatusById(@Param("id") Long id);

    @Modifying
    @Query(value = "UPDATE order_items SET item_status = :status, item_sub_status = :subStatus WHERE id IN :ids", nativeQuery = true)
    void updateItemStatuses(@Param("ids") List<Long> ids, @Param("status") String status, @Param("subStatus") String subStatus);

    @Query(value = "SELECT shipment_id FROM order_items WHERE id = ?1", nativeQuery = true)
    Long findDoShipmentIdByOrderItemId(Long id);
}
