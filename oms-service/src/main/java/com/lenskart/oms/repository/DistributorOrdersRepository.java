package com.lenskart.oms.repository;

import com.lenskart.nexs.commons.repository.BaseRepository;
import com.lenskart.oms.entity.DistributorOrders;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Set;

@Repository
public interface DistributorOrdersRepository extends BaseRepository<DistributorOrders> {
    DistributorOrders findByJunoOrderId(String junoOrderId);
    DistributorOrders findByIncrementId(String incrementId);

    @Query(value = "select do1.id as doId from distributor_orders do1 LEFT JOIN orders o1 on do1.increment_id = o1.increment_id where o1.increment_id is null and do1.increment_id is not null and do1.juno_order_id is not null and do1.status = 'APPROVED';", nativeQuery = true)
    List<Long> findAllUnsyncedApprovedDO();
}
