package com.lenskart.oms.repository;

import com.lenskart.nexs.commons.repository.BaseRepository;
import com.lenskart.oms.entity.ShipmentMetaData;
import io.lettuce.core.dynamic.annotation.Param;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Repository
public interface ShipmentMetaRepository extends BaseRepository<ShipmentMetaData> {
    Optional<ShipmentMetaData> findByShipmentIdAndEntityKey(Long shipmentId, String entityKey);

    @Query(value = "SELECT shipment_id FROM shipment_meta_data WHERE entity_key = ?1 AND entity_value = ?2 AND created_at BETWEEN ?3 AND ?4", nativeQuery = true)
    List<Long> findShipmentByEntityKeyAndEntityValueAndCreatedAtBetween(String entityKey, String entityValue, Date startDate, Date endDate);
}
