package com.lenskart.oms.repository;

import com.lenskart.nexs.commons.repository.BaseRepository;
import com.lenskart.oms.entity.OutboxEvent;
import com.lenskart.oms.enums.OutboxEventStatus;
import com.lenskart.oms.enums.OutboxEventType;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Repository
public interface OutboxEventRepository extends BaseRepository<OutboxEvent> {

    /**
     * Find pending events that are ready to be processed
     */
    @Query("SELECT e FROM OutboxEvent e WHERE e.eventStatus IN :statuses " +
           "AND e.scheduledAt <= :currentTime " +
           "ORDER BY e.createdAt ASC")
    List<OutboxEvent> findPendingEvents(@Param("statuses") List<OutboxEventStatus> statuses, 
                                       @Param("currentTime") Date currentTime);

    /**
     * Find events by aggregate ID and type
     */
    List<OutboxEvent> findByAggregateIdAndEventTypeOrderByCreatedAtDesc(String aggregateId, OutboxEventType eventType);

    /**
     * Find event by idempotency key to prevent duplicates
     */
    Optional<OutboxEvent> findByIdempotencyKey(String idempotencyKey);

    /**
     * Find events that need to be retried
     */
    @Query("SELECT e FROM OutboxEvent e WHERE e.eventStatus = :status " +
           "AND e.retryCount < e.maxRetryCount " +
           "AND e.scheduledAt <= :currentTime " +
           "ORDER BY e.createdAt ASC")
    List<OutboxEvent> findRetryableEvents(@Param("status") OutboxEventStatus status, 
                                         @Param("currentTime") Date currentTime);

    /**
     * Update event status
     */
    @Modifying
    @Query("UPDATE OutboxEvent e SET e.eventStatus = :status, e.processedAt = :processedAt, " +
           "e.errorMessage = :errorMessage WHERE e.id = :id")
    void updateEventStatus(@Param("id") Long id, 
                          @Param("status") OutboxEventStatus status,
                          @Param("processedAt") Date processedAt,
                          @Param("errorMessage") String errorMessage);

    /**
     * Increment retry count
     */
    @Modifying
    @Query("UPDATE OutboxEvent e SET e.retryCount = e.retryCount + 1, " +
           "e.eventStatus = :status, e.scheduledAt = :nextRetryTime WHERE e.id = :id")
    void incrementRetryCount(@Param("id") Long id, 
                            @Param("status") OutboxEventStatus status,
                            @Param("nextRetryTime") Date nextRetryTime);

    /**
     * Find old processed events for cleanup
     */
    @Query("SELECT e FROM OutboxEvent e WHERE e.eventStatus = :status " +
           "AND e.processedAt < :cutoffDate")
    List<OutboxEvent> findOldProcessedEvents(@Param("status") OutboxEventStatus status, 
                                           @Param("cutoffDate") Date cutoffDate);

    /**
     * Delete old processed events
     */
    @Modifying
    @Query("DELETE FROM OutboxEvent e WHERE e.eventStatus = :status " +
           "AND e.processedAt < :cutoffDate")
    void deleteOldProcessedEvents(@Param("status") OutboxEventStatus status, 
                                 @Param("cutoffDate") Date cutoffDate);

    /**
     * Count events by status
     */
    long countByEventStatus(OutboxEventStatus status);

    /**
     * Find events by status and date range for monitoring
     */
    @Query("SELECT e FROM OutboxEvent e WHERE e.eventStatus = :status " +
           "AND e.createdAt BETWEEN :startDate AND :endDate " +
           "ORDER BY e.createdAt DESC")
    List<OutboxEvent> findByStatusAndDateRange(@Param("status") OutboxEventStatus status,
                                              @Param("startDate") Date startDate,
                                              @Param("endDate") Date endDate);

    /**
     * Find events that are stuck in processing state for too long
     */
    @Query("SELECT e FROM OutboxEvent e WHERE e.eventStatus = :status " +
           "AND e.updatedAt < :staleTime")
    List<OutboxEvent> findStaleProcessingEvents(@Param("status") OutboxEventStatus status,
                                               @Param("staleTime") Date staleTime);
}
