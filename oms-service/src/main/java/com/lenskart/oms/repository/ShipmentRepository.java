package com.lenskart.oms.repository;

import com.lenskart.nexs.commons.repository.BaseRepository;
import com.lenskart.oms.entity.Order;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.entity.Shipment;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import com.lenskart.oms.enums.OrderStatus;
import org.springframework.stereotype.Repository;

import javax.persistence.Tuple;
import java.util.List;

import java.util.Date;
import java.util.List;
import java.util.Set;

@Repository
public interface ShipmentRepository extends BaseRepository<Shipment> {
    List<Shipment> findByShipmentStatusAndUpdatedAtBetween(String orderStatus, Date startHour, Date endHour);

    @Query(value = "select s.* from orders o inner join order_items oi on o.id = oi.order_id inner join shipment s on s.id = oi.shipment_id where o.increment_id = ?1 group by s.id", nativeQuery = true)
    List<Tuple> findDoShipmentsByIncrementId(String incrementId);
    Shipment findByWmsOrderCode(String unicomOrderCode);

    @Modifying
    @Query(value = "update shipment set facility = ?1, shipment_sub_status = 'FACILITY_ASSIGNED', updated_at = now(), updated_by = 'create order process strategy' where id = ?2", nativeQuery = true)
    void updateAssignedFacility(String facility, Long shipmentId);

    @Modifying
    @Query(value = "UPDATE shipment SET shipment_status = ?2, shipment_sub_status = ?3 WHERE id = ?1", nativeQuery = true)
    void updateStatusAndSubStatusById(Long id, String status, String subStatus);

    @Query(value = "SELECT wms_order_code from shipment WHERE id = ?1", nativeQuery = true)
    String findWmsOrderCodeById(Long id);


}
