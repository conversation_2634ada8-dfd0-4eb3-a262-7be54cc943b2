package com.lenskart.oms.exception;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class D365PublishDispatchException extends Exception {

    private final Integer code;
    private final String errorMessage;
    private final Throwable cause;

    public D365PublishDispatchException(String errorMessage, Throwable cause) {
        super(errorMessage, cause);
        this.code = 500;
        this.errorMessage = errorMessage;
        this.cause = cause;
    }
}