package com.lenskart.oms.exception;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class DistributorReturnOrderException extends RuntimeException {

    private final Integer code;
    private final String errorMessage;
    private final Throwable cause;

    public DistributorReturnOrderException(String errorMessage) {
        super(errorMessage);
        this.code = 500;
        this.errorMessage = errorMessage;
        this.cause = null;
    }

    public DistributorReturnOrderException(Integer code, String errorMessage) {
        super(errorMessage);
        this.code = code;
        this.errorMessage = errorMessage;
        this.cause = null;
    }

    public DistributorReturnOrderException(Integer code, String errorMessage, Throwable cause) {
        super(errorMessage, cause);
        this.code = code;
        this.errorMessage = errorMessage;
        this.cause = cause;
    }

    public DistributorReturnOrderException(String errorMessage, Throwable cause) {
        super(errorMessage, cause);
        this.code = 500;
        this.errorMessage = errorMessage;
        this.cause = cause;
    }
}
