package com.lenskart.oms.exception;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class TerminatedStateException extends Exception {

    private final Integer code;
    private final String errorMessage;
    private final Throwable cause;

    public TerminatedStateException(String errorMessage) {
        super(errorMessage);
        this.code = 500;
        this.errorMessage = errorMessage;
        this.cause = null;
    }

    public TerminatedStateException(Integer code, String errorMessage) {
        super(errorMessage);
        this.code = code;
        this.errorMessage = errorMessage;
        this.cause = null;
    }

    public TerminatedStateException(Integer code, String errorMessage, Throwable cause) {
        super(errorMessage, cause);
        this.code = code;
        this.errorMessage = errorMessage;
        this.cause = cause;
    }

    public TerminatedStateException(String errorMessage, Throwable cause) {
        super(errorMessage, cause);
        this.code = 500;
        this.errorMessage = errorMessage;
        this.cause = cause;
    }
}
