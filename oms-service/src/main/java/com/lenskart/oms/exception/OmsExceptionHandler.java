package com.lenskart.oms.exception;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.commons.codes.Status;
import com.lenskart.nexs.commons.dto.BaseMetaModel;
import com.lenskart.nexs.commons.dto.BaseResponseModel;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;


@ControllerAdvice
public class OmsExceptionHandler {

    private static final String EXCEPTION_OCCURRED_ERROR_MESSAGE = "error message: {}";

    @CustomLogger
    private Logger logger;

    @ResponseBody
    @ExceptionHandler(value = {ApplicationException.class})
    public ResponseEntity<BaseResponseModel> handleException(ApplicationException exception) {
        logger.error(EXCEPTION_OCCURRED_ERROR_MESSAGE, exception.getMessage(), exception);
        return failureResponse(exception.getMessage(), exception.getCode());
    }

    @ResponseBody
    @ExceptionHandler(value = {RuntimeException.class})
    public ResponseEntity<BaseResponseModel> handleException(RuntimeException exception) {
        logger.error(EXCEPTION_OCCURRED_ERROR_MESSAGE, exception.getMessage(), exception);
        return failureResponse(exception.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR.value());
    }

    private ResponseEntity<BaseResponseModel> failureResponse(String message, Integer code) {
        BaseResponseModel baseResponseModel = createResponseModel(message, code);
        return new ResponseEntity<>(baseResponseModel, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    private BaseResponseModel createResponseModel(String message, Integer code) {
        BaseResponseModel baseResponseModel = new BaseResponseModel();
        BaseMetaModel baseMetaModel = new BaseMetaModel();
        baseMetaModel.setDisplayMessage(message);
        baseMetaModel.setCode(String.valueOf(code));
        baseMetaModel.setMessage(Status.ERROR.name());
        baseResponseModel.setMeta(baseMetaModel);
        return baseResponseModel;
    }
}
