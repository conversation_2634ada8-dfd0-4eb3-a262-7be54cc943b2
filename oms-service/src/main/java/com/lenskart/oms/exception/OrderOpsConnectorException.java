package com.lenskart.oms.exception;

public class OrderOpsConnectorException extends RuntimeException{
    private final Integer code;
    private final String errorMessage;
    private final Throwable cause;

    public OrderOpsConnectorException(Integer code, String errorMessage, Throwable cause) {
        this.code = code;
        this.errorMessage = errorMessage;
        this.cause = cause;
    }

    public OrderOpsConnectorException(String message, String errorMessage, Throwable cause) {
        super(message);
        this.code = 500;
        this.errorMessage = errorMessage;
        this.cause = cause;
    }

    public OrderOpsConnectorException(String message, String errorMessage) {
        super(message);
        this.code = 500;
        this.errorMessage = errorMessage;
        this.cause = null;
    }
}
