package com.lenskart.oms.exception;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class FdsException extends RuntimeException {

    private final Integer code;
    private final String errorMessage;
    private final Throwable cause;

    public FdsException(String errorMessage) {
        super(errorMessage);
        this.code = 500;
        this.errorMessage = errorMessage;
        this.cause = null;
    }

    public FdsException(Integer code, String errorMessage) {
        super(errorMessage);
        this.code = code;
        this.errorMessage = errorMessage;
        this.cause = null;
    }

    public FdsException(Integer code, String errorMessage, Throwable cause) {
        super(errorMessage, cause);
        this.code = code;
        this.errorMessage = errorMessage;
        this.cause = cause;
    }

    public FdsException(String errorMessage, Throwable cause) {
        super(errorMessage, cause);
        this.code = 500;
        this.errorMessage = errorMessage;
        this.cause = cause;
    }
}
