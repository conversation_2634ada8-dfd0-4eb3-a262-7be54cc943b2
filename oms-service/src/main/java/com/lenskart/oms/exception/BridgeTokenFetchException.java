package com.lenskart.oms.exception;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class BridgeTokenFetchException extends RuntimeException {
    private final Integer code;
    private final String errorMessage;

    public BridgeTokenFetchException(Integer code, String errorMessage) {
        this.code = code;
        this.errorMessage = errorMessage;
    }
}
