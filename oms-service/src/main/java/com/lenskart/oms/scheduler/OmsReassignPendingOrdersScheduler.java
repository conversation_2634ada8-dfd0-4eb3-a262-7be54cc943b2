package com.lenskart.oms.scheduler;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.facade.ReassignHelperFacade;
import io.micrometer.core.annotation.Timed;
import lombok.AccessLevel;
import lombok.Setter;
import net.javacrumbs.shedlock.core.SchedulerLock;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Setter(onMethod__ = {@Autowired})
@ConditionalOnProperty(name = "sensei.schedular.enabled", havingValue = "true")
public class OmsReassignPendingOrdersScheduler {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    protected Logger logger;

    private ReassignHelperFacade reassignHelperFacade;

    /***
     * This scheduler will fetch all the pending and created OMS orders where created_at is less than currentTime minus N hours
     * and oms-reassign the whole order if shipments of the order are in CREATED and PENDING state at the same time
     * i.e. if one or more shipment is in CREATED state and one or more shipment is in PENDING state
     * @throws Exception in case any 4** and 5**.
     */
    @Timed
//    @Scheduled(cron = "0 0 */6 * * *")
//    @SchedulerLock(name = "OMS_REASSIGN_PENDING_ORDERS_SCHEDULER")
    public void omsReassignPendingOrders() {
        logger.info("[omsReassignPendingOrders] Starting scheduler job to re-sync pending OMS orders to OMS process kafka");
        reassignHelperFacade.omsReassignPendingOrders(false);
    }
}
