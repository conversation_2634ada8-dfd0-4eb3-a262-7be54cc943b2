package com.lenskart.oms.scheduler;

import com.lenskart.oms.facade.ReadyForWhStuckOrderFacade;
import io.micrometer.core.annotation.Timed;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.core.SchedulerLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@Setter(onMethod__ = {@Autowired})
@ConditionalOnProperty(name = "sensei.schedular.enabled", havingValue = "true")
public class ReadyForWhStuckOrderScheduler {

    private ReadyForWhStuckOrderFacade readyForWhStuckOrderFacade;

    @Timed
    @Scheduled(cron = "${oms.readyForWhStuckOrder.scheduler.time}")
    @SchedulerLock(name = "ReadyForWhStuckOrderScheduler", lockAtMostForString = "600000", lockAtLeastForString = "600000")
    public void execute() {
        try {
            readyForWhStuckOrderFacade.handleReadyForWhStuckOrder();
        } catch (Exception exception) {
            log.error("[ReadyForWhStuckOrderScheduler][execute]", exception);
        }
    }
}
