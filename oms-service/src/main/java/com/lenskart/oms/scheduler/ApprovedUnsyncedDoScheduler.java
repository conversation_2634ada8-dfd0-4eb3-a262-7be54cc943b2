package com.lenskart.oms.scheduler;

import com.lenskart.oms.facade.DistributorOrderFacade;
import io.micrometer.core.annotation.Timed;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.core.SchedulerLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@Setter(onMethod__ = {@Autowired})
@ConditionalOnProperty(name = "sensei.approved.do.sync.schedular.enabled", havingValue = "true")
public class ApprovedUnsyncedDoScheduler {

    private DistributorOrderFacade distributorOrderFacade;

    @Timed
    @Scheduled(cron = "${oms.approvedUnsyncedDoScheduler.scheduler.time}")
    @SchedulerLock(name = "ApprovedUnsyncedDoScheduler", lockAtMostForString = "600000", lockAtLeastForString = "600000")
    public void execute() {
        try {
            distributorOrderFacade.triggerUnsyncedApprovedDO();
        } catch (Exception exception) {
            log.error("[ApprovedUnsyncedDoScheduler][execute]", exception);
        }
    }
}
