package com.lenskart.oms.scheduler;

import com.lenskart.oms.facade.OrderOpsSenseiStatusReconciliationFacade;
import io.micrometer.core.annotation.Timed;
import lombok.Setter;
import net.javacrumbs.shedlock.core.SchedulerLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Setter(onMethod__ = {@Autowired})
@ConditionalOnProperty(name = "sensei.schedular.enabled", havingValue = "true")
public class OrderOpsAndOmsStatusDiscrepancyScheduler {

    private OrderOpsSenseiStatusReconciliationFacade orderOpsSenseiStatusReconciliationFacade;

    @Timed
    @Scheduled(cron = "${oms.discrepancy.scheduler.time}")
    @SchedulerLock(name = "OrderOps_And_Oms_Status_Discrepancy_Scheduler")
    public void alertOrderOpsAndOmsStatusDiscrepancy() {
       orderOpsSenseiStatusReconciliationFacade.reconcile();
    }
}
