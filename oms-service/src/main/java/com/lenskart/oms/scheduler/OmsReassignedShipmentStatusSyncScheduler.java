package com.lenskart.oms.scheduler;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.facade.ReassignHelperFacade;
import io.micrometer.core.annotation.Timed;
import lombok.AccessLevel;
import lombok.Setter;
import net.javacrumbs.shedlock.core.SchedulerLock;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.scheduling.annotation.Scheduled;

@Component
@Setter(onMethod__ = {@Autowired})
@ConditionalOnProperty(name = "sensei.schedular.reassignment.shipmentSync.enabled", havingValue = "true")
public class OmsReassignedShipmentStatusSyncScheduler {
    @CustomLogger
    @Setter(AccessLevel.NONE)
    protected Logger logger;
    private ReassignHelperFacade reassignHelperFacade;

    @Timed
    @Scheduled(cron = "0 0 */6 * * *")
    @SchedulerLock(name = "OMS_REASSIGN_SHIPMENT_SYNC_SCHEDULER", lockAtMostForString = "600000", lockAtLeastForString = "600000")
    public void omsReassignPendingOrders() throws ApplicationException {
        logger.info("[omsReassignPendingOrders] Starting scheduler job to re-sync OMS reassigned synced shipments to OMS process kafka");
        reassignHelperFacade.syncReassignedShipment();
    }

}
