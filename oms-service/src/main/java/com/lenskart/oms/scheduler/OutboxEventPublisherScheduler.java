package com.lenskart.oms.scheduler;

import com.lenskart.oms.service.OutboxEventService;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.core.SchedulerLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@Setter(onMethod__ = {@Autowired})
public class OutboxEventPublisherScheduler {

    private OutboxEventService outboxEventService;

    /**
     * Process pending outbox events and publish to Kafka
     * Runs every 30 seconds
     */
    @Scheduled(fixedDelayString = "${outbox.publisher.schedule.delay:30000}")
    @SchedulerLock(name = "processPendingOutboxEvents", lockAtMostFor = 60000, lockAtLeastFor = 30000)
    public void processPendingEvents() {
        try {
            outboxEventService.processPendingEvents();
        } catch (Exception e) {
            log.error("[processPendingEvents] Error processing pending events", e);
        }
    }

    /**
     * Process retry events
     * Runs every 5 minutes
     */
    @Scheduled(fixedDelayString = "${outbox.publisher.retry.schedule.delay:300000}")
    @SchedulerLock(name = "processRetryOutboxEvents", lockAtMostFor = 600000, lockAtLeastFor = 300000)
    public void processRetryEvents() {
        try {
            outboxEventService.processRetryEvents();
        } catch (Exception e) {
            log.error("[processRetryEvents] Error processing retry events", e);
        }
    }

    /**
     * Reset stale processing events
     * Runs every hour
     */
    @Scheduled(fixedDelayString = "${outbox.publisher.stale.reset.delay:3600000}")
    @SchedulerLock(name = "resetStaleProcessingOutboxEvents", lockAtMostFor = 7200000, lockAtLeastFor = 3600000)
    public void resetStaleProcessingEvents() {
        try {
            outboxEventService.resetStaleProcessingEvents();
        } catch (Exception e) {
            log.error("[resetStaleProcessingEvents] Error resetting stale events", e);
        }
    }
    
}
