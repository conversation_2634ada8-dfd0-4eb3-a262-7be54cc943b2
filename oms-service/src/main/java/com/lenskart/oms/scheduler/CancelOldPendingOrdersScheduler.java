package com.lenskart.oms.scheduler;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.facade.CancelOldPendingOrdersFacade;
import io.micrometer.core.annotation.Timed;
import lombok.AccessLevel;
import lombok.Setter;
import net.javacrumbs.shedlock.core.SchedulerLock;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Setter(onMethod__ = {@Autowired})
@ConditionalOnProperty(name = "sensei.schedular.enabled", havingValue = "true")
public class CancelOldPendingOrdersScheduler {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    private CancelOldPendingOrdersFacade cancelOldPendingOrdersFacade;

    @Timed
    @Scheduled(cron = "${oms.cancelOldOrders.scheduler.time}")
    @SchedulerLock(name = "Oms_Cancel_Old_Pending_Orders_Scheduler", lockAtLeastForString = "PT15M", lockAtMostForString = "PT30M")
    public void cancelOldPendingOrdersScheduler() {
        try {
            cancelOldPendingOrdersFacade.cancelOldOrders();
        }catch (Exception e){
            logger.error("[cancelOldPendingOrdersScheduler] scheduler failed with exception: {}", e.getMessage(), e);
        }
    }
}
