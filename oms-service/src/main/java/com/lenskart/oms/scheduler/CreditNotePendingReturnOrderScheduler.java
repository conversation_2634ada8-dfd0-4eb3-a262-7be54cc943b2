package com.lenskart.oms.scheduler;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.dto.DistributorReturnOrderDto;
import com.lenskart.oms.enums.DistributorReturnOrderStatus;
import com.lenskart.oms.facade.DistributorReturnOrderFacade;
import com.lenskart.oms.service.DistributorReturnOrderService;
import io.micrometer.core.annotation.Timed;
import lombok.AccessLevel;
import lombok.Setter;
import net.javacrumbs.shedlock.core.SchedulerLock;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.text.Format;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Component
@Setter(onMethod__ = {@Autowired})
public class CreditNotePendingReturnOrderScheduler {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    private DistributorReturnOrderFacade distributorReturnOrderFacade;

    private DistributorReturnOrderService distributorReturnOrderService;

    @Value("${oms.cancelOldOrders.scheduler.timePeriodInDays:3}")
    @Setter(AccessLevel.NONE)
    private Integer timePeriodInDays;

    @Timed
    @Scheduled(cron = "${oms.creditNotePendingOrders.scheduler.time}")
    @SchedulerLock(name = "oms_credit_note_pending_orders_scheduler", lockAtLeastForString = "PT15M", lockAtMostForString = "PT30M")
    public void creditNotePendingReturnOrderScheduler() {
        long currentTime = System.currentTimeMillis();
        Format formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
        Date endTimeStamp = new Date(currentTime - 24 * 3600 * 1000L);
        Date startTimeStamp = new Date(endTimeStamp.getTime() - timePeriodInDays * 24 * 3600 * 1000L);
        String startDate = formatter.format(startTimeStamp);
        String endDate = formatter.format(endTimeStamp);
        logger.info("[creditNotePendingReturnOrderScheduler] fetching from distributor return orders table of sensei DB for start date: {}, end date: {} for the scheduler that started at: {}", startDate, endDate, currentTime);
        List<DistributorReturnOrderDto> distributorReturnOrderDtoList = distributorReturnOrderService.search("updatedAt.gte:" + startDate + "___updatedAt.lte:" + endDate + "___status.eq:" + DistributorReturnOrderStatus.PUTAWAY_CREATED.name());
        logger.info("[creditNotePendingReturnOrderScheduler] total distributor return order list size fetched for last {} days is {} i.e. {} to {} for the scheduler that started at: {}. order dto list: {}", timePeriodInDays, distributorReturnOrderDtoList.size(), startDate, endDate, currentTime, distributorReturnOrderDtoList);
        if (CollectionUtils.isEmpty(distributorReturnOrderDtoList)) {
            logger.info("[creditNotePendingReturnOrderScheduler] distributorReturnOrderDtoList is empty hence no discrepancy since last {} days. Therefore terminating the scheduler that started at: {}", timePeriodInDays, currentTime);
            return;
        }
        for (DistributorReturnOrderDto distributorReturnOrderDto : distributorReturnOrderDtoList) {
            try {
                String itemId = distributorReturnOrderDto.getItemId().toString();
                logger.info("[creditNotePendingReturnOrderScheduler]Trigerring CrediNoteEvent for {}", itemId);
                distributorReturnOrderFacade.triggerCreditNoteEvent(itemId);
            } catch (Exception e) {
                logger.error("[creditNotePendingReturnOrderScheduler] failed with exception: {}", e.getMessage(), e);
            }
        }
        logger.info("[creditNotePendingReturnOrderScheduler]Ended at {}", new Date());
    }
}
