package com.lenskart.oms.scheduler;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.enums.OrderStatus;
import com.lenskart.oms.enums.OrderSubStatus;
import com.lenskart.oms.facade.OrderReconciliationFacade;
import com.lenskart.oms.request.OrderReconciliationRequest;
import io.micrometer.core.annotation.Timed;
import lombok.AccessLevel;
import lombok.Setter;
import net.javacrumbs.shedlock.core.SchedulerLock;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Setter(onMethod__ = {@Autowired})
@ConditionalOnProperty(name = "sensei.schedular.enabled", havingValue = "true")
public class SyncPendingOmsOrdersToProcessKafkaScheduler {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    protected Logger logger;

    private OrderReconciliationFacade orderFacade;

    /***
     * This scheduler will fetch all the created status OMS orders, and trigger validateAndSync method of OMS
     * then order will eventually synced to process Kafka if every check is valid.
     * @throws Exception in case any 4** and 5**.
     */
    @Timed
    @Scheduled(cron = "0 0 */3 * * *")
    @SchedulerLock(name = "SYNC_PENDING_OMS_ORDERS_TO_PROCESS_KAFKA_SCHEDULER")
    public void syncPendingOmsOrdersToProcessKafka() {
        logger.info("[syncPendingOrderToProcessKafka] Starting scheduler job to re-sync pending OMS orders to OMS process kafka");
        OrderReconciliationRequest orderReconciliationRequest = populateOrderReconciliationRequest();
        orderFacade.syncPendingOrdersToProcessKafka(orderReconciliationRequest);
    }

    private OrderReconciliationRequest populateOrderReconciliationRequest() {
        OrderReconciliationRequest orderReconciliationRequest = new OrderReconciliationRequest();
        orderReconciliationRequest.setOrderStatus(OrderStatus.CREATED);
        orderReconciliationRequest.setIsFullSync(false);

        return orderReconciliationRequest;
    }
}
