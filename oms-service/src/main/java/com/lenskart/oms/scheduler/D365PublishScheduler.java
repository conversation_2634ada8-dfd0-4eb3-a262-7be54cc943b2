package com.lenskart.oms.scheduler;

import com.lenskart.oms.connector.D365Connector;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.dto.ShipmentMetaDataDto;
import com.lenskart.oms.entity.OrderItems;
import com.lenskart.oms.entity.Shipment;
import com.lenskart.oms.entity.ShipmentMetaData;
import com.lenskart.oms.enums.NavChannel;
import com.lenskart.oms.repository.ShipmentMetaRepository;
import com.lenskart.oms.repository.ShipmentRepository;
import com.lenskart.oms.request.D365PublishDispatchRequest;
import com.lenskart.oms.response.D365PublishDispatchResponse;
import com.lenskart.oms.service.ShipmentMetaService;
import com.lenskart.oms.utils.LegalOwnerUtil;
import io.micrometer.core.annotation.Timed;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@Slf4j
@ConditionalOnProperty(name = "sensei.schedular.enabled", havingValue = "true")
public class D365PublishScheduler {

    @Autowired
    private ShipmentMetaRepository shipmentMetaRepository;

    @Autowired
    private ShipmentRepository shipmentRepository;

    @Autowired
    private D365Connector d365Connector;

    @Autowired
    private ShipmentMetaService shipmentMetaService;

    @Autowired
    private LegalOwnerUtil legalOwnerUtil;

    @Value("${d365.publish.dispatch.scheduler.startTimeInMinutes}")
    private Integer startTime;

    @Value("${d365.publish.dispatch.scheduler.endTimeInMinutes}")
    private Integer endTime;

    @Timed
    @Scheduled(cron = "${d365.publish.dispatch.scheduler.cron}")
    public void retryFailedD365Publish() {
        try {
            Date schedulerStartTime = new Date();
            Date startDate = DateUtils.addMinutes(new Date(), -startTime);
            Date endDate = DateUtils.addMinutes(new Date(), -endTime);
            log.info("[D365PublishScheduler] started at: {} with startTime= {} and endTime= {}", schedulerStartTime, startDate, endDate);
            List<Long> failedShipmentIds = shipmentMetaRepository.findShipmentByEntityKeyAndEntityValueAndCreatedAtBetween(ApplicationConstants.D365_DISPATCH_SHIPMENT_KEY, "FALSE", startDate, endDate);
            List<Shipment> shipments = shipmentRepository.findAllById(failedShipmentIds);
            log.info("[D365PublishScheduler] Total no. failed D365 publish dispatch shipments are {}", shipments.size());
            if (shipments.isEmpty()) return;

            for (Shipment shipment : shipments) {
                D365PublishDispatchRequest d365PublishDispatchRequest = new D365PublishDispatchRequest();
                d365PublishDispatchRequest.setEventTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
                d365PublishDispatchRequest.setFacilityCode(shipment.getFacility());
                d365PublishDispatchRequest.setShippingPackageId(shipment.getWmsShippingPackageId());
                d365PublishDispatchRequest.setOrderId(shipment.getOrderItems().get(0).getOrderId());
                d365PublishDispatchRequest.setLegalEntity(legalOwnerUtil.getLegalOwner(shipment.getFacility(), null, shipment.getLegalOwnerCountry()));
                d365PublishDispatchRequest.setNavChannel(shipment.getOrderItems().get(0).getNavChannel().name());
                d365PublishDispatchRequest.setSource("NEXS");
                d365PublishDispatchRequest.setEntityType(NavChannel.DODTC.equals(shipment.getOrderItems().get(0).getNavChannel()) ? "DISTRIBUTED_SALE_ORDER" : "SALE_ORDER");
                List<Long> uwItemIds = new ArrayList<>();
                for (OrderItems orderItem : shipment.getOrderItems()) {
                    uwItemIds.add(orderItem.getUwItemId());
                }
                d365PublishDispatchRequest.setUwItemIds(uwItemIds);

                boolean isPublished = false;
                D365PublishDispatchResponse d365PublishDispatchResponse = d365Connector.publishDispatchOrderToD365(d365PublishDispatchRequest);
                if (d365PublishDispatchResponse == null)
                    log.error("[D365PublishScheduler] NULL response from D365 API");
                else if (d365PublishDispatchResponse.getData().getStatus().equals("SUCCESS")) {
                    isPublished = true;
                    Optional<ShipmentMetaData> shipmentMetaDataOptional = shipmentMetaRepository.findByShipmentIdAndEntityKey(shipment.getId(), ApplicationConstants.D365_DISPATCH_SHIPMENT_KEY);
                    shipmentMetaDataOptional.ifPresent(shipmentMetaData -> shipmentMetaService.save(convertToShipmentMetaDataDto(shipmentMetaData)));
                }
                log.info("[DispatchShipmentStrategy] Updated KEY= {} with VALUE= {} for Shipment: {}", ApplicationConstants.D365_DISPATCH_SHIPMENT_KEY, isPublished, shipment.getId());
            }
        } catch (Exception e) {
            log.error("[D365PublishScheduler] Exception Occurred: ", e);
        }
    }

    private ShipmentMetaDataDto convertToShipmentMetaDataDto(ShipmentMetaData shipmentMetaData) {
        ShipmentMetaDataDto shipmentMetaDataDto = new ShipmentMetaDataDto();
        shipmentMetaDataDto.setShipmentId(shipmentMetaData.getShipmentId());
        shipmentMetaDataDto.setEntityKey(shipmentMetaData.getEntityKey());
        shipmentMetaDataDto.setEntityValue("TRUE");
        shipmentMetaDataDto.setCreatedAt(shipmentMetaData.getCreatedAt());
        shipmentMetaDataDto.setUpdatedAt(new Date());
        shipmentMetaDataDto.setCreatedBy(shipmentMetaData.getCreatedBy());
        shipmentMetaDataDto.setUpdatedBy(shipmentMetaData.getUpdatedBy());
        shipmentMetaDataDto.setId(shipmentMetaData.getId());
        shipmentMetaDataDto.setVersion(shipmentMetaData.getVersion());
        return shipmentMetaDataDto;
    }
}
