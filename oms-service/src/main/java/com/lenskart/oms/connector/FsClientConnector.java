package com.lenskart.oms.connector;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.constants.ApplicationConstants;
import io.micrometer.core.annotation.Timed;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.Collections;

@Component
public class FsClientConnector {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    @Value("${fs.client.base.url}")
    private String fsClientBaseUrl;

    private final RestTemplate restTemplate;

    public FsClientConnector(RestTemplateBuilder restTemplateBuilder,
                        @Value("${fs.client.rest.timeout}") Integer connectionTimeout
    ) {
        this.restTemplate = restTemplateBuilder
                .setConnectTimeout(connectionTimeout == null ? Duration.ofMillis(ApplicationConstants.DEFAULT_CONNECTION_TIMEOUT) : Duration.ofMillis(connectionTimeout))
                .setReadTimeout(connectionTimeout == null ? Duration.ofMillis(ApplicationConstants.DEFAULT_READ_TIMEOUT) : Duration.ofMillis(connectionTimeout))
                .build();
    }

    @Timed
    public Integer isPartPaymentOrder(Long incrementId) {
        try {
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            HttpEntity<Object> httpEntity = new HttpEntity(httpHeaders);
            String pathUrl = String.format("/order/paymentCheck/part_payment_order/%s", incrementId);

            logger.info("[isPartPaymentOrder] Calling fsClient to fetch part payment for the order {}", incrementId);
            ResponseEntity<Integer> responseEntity = restTemplate.exchange(fsClientBaseUrl + pathUrl, HttpMethod.GET, httpEntity, Integer.class, (Object) null);
            if (responseEntity.getStatusCode() != HttpStatus.OK && responseEntity.getBody() == null) {
                logger.error("[isPartPaymentOrder] error while fetching part payment for the order {}", incrementId);
                return Integer.valueOf(1);
            }

            logger.info("[isPartPaymentOrder] faClient call to fetch part payment for the order {} successful with response {}", incrementId, responseEntity.getBody());
            return Integer.valueOf(responseEntity.getBody());
        } catch (Exception e) {
            logger.error("[isPartPaymentOrder] error while fetching part payment for the order {} with exception {}", incrementId, e);
            return Integer.valueOf(1);
        }
    }

    @Timed
    public BigDecimal isStoreCreditUsed(Long incrementId) {
        try {
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            HttpEntity<Object> httpEntity = new HttpEntity(httpHeaders);
            String pathUrl = String.format("/order/paymentCheck/store_credit_used/%s", incrementId);

            logger.info("[isStoreCreditUsed] Calling fsClient to fetch store credit details for the order {}", incrementId);
            ResponseEntity<Integer> responseEntity = restTemplate.exchange(fsClientBaseUrl + pathUrl, HttpMethod.GET, httpEntity, Integer.class, (Object) null);
            if (responseEntity.getStatusCode() != HttpStatus.OK && responseEntity.getBody() == null) {
                logger.error("[isPartPaymentOrder] error while fetching store credit details for the order {}", incrementId);
                return new BigDecimal("1.0");
            }

            logger.info("[isStoreCreditUsed] fsClient call to fetch store credit details for the order {} successful with response {}", incrementId, responseEntity.getBody());
            return BigDecimal.valueOf(responseEntity.getBody());
        } catch (Exception e) {
            logger.error("[isStoreCreditUsed] error while fetching store credit details for the order {} with exception {}", incrementId, e);
            return new BigDecimal("1.0");
        }
    }

    @Timed
    public Integer isFranchiseCreditUsed(Long incrementId) {
        try {
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            HttpEntity<Object> httpEntity = new HttpEntity(httpHeaders);
            String pathUrl = String.format("/order/paymentCheck/franchise_credit_used/%s", incrementId);

            logger.info("[isFranchiseCreditUsed] Calling fsClient to fetch franchise credit details for the order {}", incrementId);
            ResponseEntity<Integer> responseEntity = restTemplate.exchange(fsClientBaseUrl + pathUrl, HttpMethod.GET, httpEntity, Integer.class, (Object) null);
            if (responseEntity.getStatusCode() != HttpStatus.OK && responseEntity.getBody() == null) {
                logger.error("[isFranchiseCreditUsed] error while fetching franchise credit details for the order {}", incrementId);
                return Integer.valueOf(1);
            }

            logger.info("[isFranchiseCreditUsed] faClient call to fetch franchise credit details for the order {} successful with response {}", incrementId, responseEntity.getBody());
            return Integer.valueOf(responseEntity.getBody());
        } catch (Exception e) {
            logger.error("[isFranchiseCreditUsed] error while fetching franchise credit details for the order {} with exception {}", incrementId, e);
            return Integer.valueOf(1);
        }
    }

    @Timed
    public BigDecimal isEntryInLkWallet(Long incrementId) {
        try {
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            HttpEntity<Object> httpEntity = new HttpEntity(httpHeaders);
            String pathUrl = String.format("/order/paymentCheck/lenskart_wallet/%s", incrementId);

            logger.info("[isEntryInLkWallet] Calling fsClient to check entry in LK Wallet for the order {}", incrementId);
            ResponseEntity<Integer> responseEntity = restTemplate.exchange(fsClientBaseUrl + pathUrl, HttpMethod.GET, httpEntity, Integer.class, (Object) null);
            if (responseEntity.getStatusCode() != HttpStatus.OK && responseEntity.getBody() == null) {
                logger.error("[isEntryInLkWallet] error while checking entry in LK Wallet for the order {}", incrementId);
                return new BigDecimal("1.0");
            }

            logger.info("[isEntryInLkWallet] faClient call to check entry in LK Wallet for the order {} successful with response {}", incrementId, responseEntity.getBody());
            return BigDecimal.valueOf(responseEntity.getBody());
        } catch (Exception e) {
            logger.error("[isEntryInLkWallet] error while checking entry in LK Wallet for the order {} with exception {}", incrementId, e);
            return new BigDecimal("1.0");
        }
    }
}
