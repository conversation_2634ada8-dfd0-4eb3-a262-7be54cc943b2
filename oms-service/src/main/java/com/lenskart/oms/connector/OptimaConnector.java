
package  com.lenskart.oms.connector;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.oms.exception.OptimaFailureException;
import com.lenskart.optima.request.AssignShipmentFullfillerRequest;
import com.lenskart.optima.response.AssignShipmentFullfillerResponse;
import io.micrometer.core.annotation.Timed;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.*;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.util.Objects;

import static com.lenskart.oms.constants.ApplicationConstants.OPTIMA_DEFAULT_CONNECTION_TIMEOUT;
import static com.lenskart.oms.constants.ApplicationConstants.OPTIMA_DEFAULT_READ_TIMEOUT;

@Slf4j
@Component
public class OptimaConnector {

    private final RestTemplate restTemplate;

    @Value("${optima.base.url}")
    private String optimaBaseUrl;

    @Value("${optima.authorization.token}")
    private String authorizationToken;

    @Autowired
    private ObjectMapper objectMapper;

    public static final String BASE_URL_V1 = "/optima/api/v1";
    public static final String SHIPMENT = "/shipment";
    public static final String ASSIGN_FULFILLER = "/assignFullfiller";

    public OptimaConnector(RestTemplateBuilder restTemplateBuilder, @Value("${optima.rest.timeout}") Integer connectionTimeout) {
        this.restTemplate = restTemplateBuilder
                .setConnectTimeout(connectionTimeout == null ? Duration.ofMillis(OPTIMA_DEFAULT_CONNECTION_TIMEOUT) : Duration.ofMillis(connectionTimeout))
                .setReadTimeout(connectionTimeout == null ? Duration.ofMillis(OPTIMA_DEFAULT_READ_TIMEOUT) : Duration.ofMillis(connectionTimeout))
                .build();
    }

    @Timed
    @Retryable(value = {OptimaFailureException.class}, maxAttemptsExpression = "${optima.api.retry.maxAttempts}")
    public AssignShipmentFullfillerResponse getFulfillerFacility(AssignShipmentFullfillerRequest assignShipmentFullfillerRequest) {
        String wmsOrderCode = assignShipmentFullfillerRequest.getWmsOrderCode();
        String url = optimaBaseUrl.concat(BASE_URL_V1).concat(SHIPMENT).concat(ASSIGN_FULFILLER);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set("Authorization", "Basic " + authorizationToken);
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity httpEntity = new HttpEntity(assignShipmentFullfillerRequest, httpHeaders);
        try {
            ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
            log.info("OptimaConnector.getFulfillerFacility : URL - {}, Data - {}, Response - {}", url, assignShipmentFullfillerRequest, responseEntity);
            if (ObjectUtils.isEmpty(responseEntity.getBody()))
                throw new OptimaFailureException("Response recieved from optima is null/empty for wmsOrderCode:" + wmsOrderCode);
            JsonNode jsonNode = objectMapper.readTree(responseEntity.getBody());
            if(Objects.nonNull(jsonNode) && jsonNode.get("meta").get("message").asText().equals("Success")) {
                AssignShipmentFullfillerResponse assignShipmentFullfillerResponse = objectMapper.readValue(jsonNode.get("data").toString(), AssignShipmentFullfillerResponse.class);
                return assignShipmentFullfillerResponse;
            } else
                throw new OptimaFailureException("Jsonnode is empty, Exception occured while fetching facility from optima for wmsOrderCode:" + wmsOrderCode);
        } catch (Exception e) {
            log.error("OptimaConnector.getFulfillerFacility.Exception ", e);
            throw new OptimaFailureException("Exception occured while fetching facility from optima for wmsOrderCode:" + wmsOrderCode, e);
        }
    }



}