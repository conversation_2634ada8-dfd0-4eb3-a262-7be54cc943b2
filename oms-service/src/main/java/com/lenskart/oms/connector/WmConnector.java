package com.lenskart.oms.connector;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.model.OrderStockCheckResponse;
import com.lenskart.oms.request.MarkOrderCompleteRequest;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.server.ResponseStatusException;

import java.time.Duration;
import java.util.Arrays;

@Component
public class WmConnector {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    @Value("${wm.base.url}")
    private String wmBaseUrl;

    private final RestTemplate restTemplate;

    public WmConnector(RestTemplateBuilder restTemplateBuilder, @Value("${wm.rest.timeout}") Integer connectionTimeout) {
        this.restTemplate = restTemplateBuilder
                .setConnectTimeout(connectionTimeout == null ? Duration.ofMillis(ApplicationConstants.DEFAULT_CONNECTION_TIMEOUT) : Duration.ofMillis(connectionTimeout))
                .setReadTimeout(connectionTimeout == null ? Duration.ofMillis(ApplicationConstants.DEFAULT_READ_TIMEOUT) : Duration.ofMillis(connectionTimeout))
                .build();
    }

    public void markOrderComplete(MarkOrderCompleteRequest markOrderCompleteRequest, String trackingNo) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));
            HttpEntity<MarkOrderCompleteRequest> requestHttpEntity = new HttpEntity<>(markOrderCompleteRequest, headers);
            String finalUrl = wmBaseUrl + "/manifests/mark_order_complete/" + trackingNo;
            ResponseEntity<OrderStockCheckResponse> responseEntity = restTemplate.exchange(finalUrl, HttpMethod.POST, requestHttpEntity, OrderStockCheckResponse.class);
            logger.info("WmConnector.markOrderComplete : URL - {}, OrderId - {}, Request - {}, Response - {}",
                    finalUrl, markOrderCompleteRequest.getOrderId(), markOrderCompleteRequest, responseEntity);
            if (!responseEntity.getStatusCode().is2xxSuccessful()) {
                logger.error("WmConnector.markOrderComplete.Exception For Payload: {} status code: {} response: {}",
                        markOrderCompleteRequest, responseEntity.getStatusCodeValue(), responseEntity.getBody());
                //throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "error while markOrderComplete in wm for markOrderCompleteRequest: " + markOrderCompleteRequest);
            }
        } catch (Exception exception) {
            logger.error("WmConnector.markOrderComplete.Exception For Payload - " + markOrderCompleteRequest, exception);
            //throw exception;
        }
    }
}
