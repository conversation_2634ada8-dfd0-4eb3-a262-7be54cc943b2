package com.lenskart.oms.connector;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.request.SmsRequest;
import com.lenskart.oms.response.SmsResponse;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import java.time.Duration;

@Component
public class CommunicationSmsConnector {

    @Value("${communication.base.url}")
    private String url;

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    private final RestTemplate restTemplate;

    public CommunicationSmsConnector(RestTemplateBuilder restTemplateBuilder,
                                     @Value("${catalog.ops.timeout}") Integer connectionTimeout
    ) {
        this.restTemplate = restTemplateBuilder
                .setConnectTimeout(connectionTimeout == null ? Duration.ofMillis(ApplicationConstants.DEFAULT_CONNECTION_TIMEOUT) : Duration.ofMillis(connectionTimeout))
                .setReadTimeout(connectionTimeout == null ? Duration.ofMillis(ApplicationConstants.DEFAULT_READ_TIMEOUT) : Duration.ofMillis(connectionTimeout))
                .build();
    }

    public SmsResponse sendPostRequest(SmsRequest request, Long incrementId) throws ApplicationException {
        try {
            SmsResponse response = new SmsResponse();
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Object> httpEntity = new HttpEntity(request.getRequestPayload(),httpHeaders);
            logger.info("[sendPostRequest] going to call sms service to send sms with request body: {} and url: {}",request, url+"/api/sms");
            ResponseEntity<String> responseEntity = restTemplate.exchange(url + request.getUrl(), HttpMethod.POST, httpEntity, String.class, (Object) null);
            if (responseEntity.getStatusCode() == HttpStatus.OK && responseEntity.getBody() != null) {
                logger.info("[sendPostRequest] response form sms service: {}",responseEntity.getBody());
                response.setResponseCode(responseEntity.getStatusCode().value());
                response.setResponseContent(responseEntity.getBody());
            }else{
                logger.error("[sendPostRequest] Send Sms returned response null for increment id: {} and request body: {}, response code: {}",incrementId, request, responseEntity.getStatusCode());
                throw new ApplicationException("Send Sms returned response null for increment id: "+incrementId);
            }
            return response;
        } catch (Exception exception) {
            logger.error("[sendPostRequest] Exception while sending Sms for increment id: {} with message: {}",incrementId, exception.getMessage());
            throw new ApplicationException("Send sms failed for increment id: "+incrementId+" with exception: "+exception.getMessage(),exception);
        }
    }
}
