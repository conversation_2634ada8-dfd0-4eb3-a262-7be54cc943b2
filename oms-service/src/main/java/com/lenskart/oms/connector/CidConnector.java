package com.lenskart.oms.connector;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.google.gson.Gson;
import com.lenskart.nexs.cid.request.StockBlockOrderRequest;
import com.lenskart.nexs.cid.response.StockBlockOrderResponse;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.commons.dto.BaseResponseModel;
import com.lenskart.nexs.ims.request.OrderStockCheckRequest;
import com.lenskart.nexs.ims.request.StockCheck;
import com.lenskart.nexs.ims.response.OrderItemStockCheckResponse;
import com.lenskart.nexs.ims.response.StockCheckResponse;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.model.OrderStockCheckResponse;
import com.lenskart.oms.request.distributor.InventoryAvailabilityCountRR;
import com.lenskart.oms.utils.ObjectHelper;
import io.micrometer.core.annotation.Timed;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.*;
import org.springframework.orm.ObjectOptimisticLockingFailureException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.server.ResponseStatusException;

import java.io.IOException;
import java.time.Duration;
import java.util.*;

@Component
public class CidConnector {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    @Value("${cid.base.url}")
    private String cidBaseUrl;

    private final RestTemplate restTemplate;
    private static final String CID_STOCK_CHECK_ENDPOINT = "/nexs/cid/api/v1/pendingOrder/inventoryCheckAndBlock";
    private static final String CID_INVENTORY_AVAILABILITY_COUNT = "/nexs/cid/api/v1/warehouseInventory/getInventoryAvailabilityCount";
    private static final String CID_BULK_STOCK_CHECK_ENDPOINT = "/nexs/cid/api/v1/pendingOrder/stockBlockAvailableInventory";

    public CidConnector(RestTemplateBuilder restTemplateBuilder,
                        @Value("${cid.rest.timeout}") Integer connectionTimeout
    ) {
        this.restTemplate = restTemplateBuilder
                .setConnectTimeout(connectionTimeout == null ? Duration.ofMillis(ApplicationConstants.DEFAULT_CONNECTION_TIMEOUT) : Duration.ofMillis(connectionTimeout))
                .setReadTimeout(connectionTimeout == null ? Duration.ofMillis(ApplicationConstants.DEFAULT_READ_TIMEOUT) : Duration.ofMillis(connectionTimeout))
                .build();
    }

    @Timed
    @Retryable(value = {HttpServerErrorException.InternalServerError.class,
            HttpServerErrorException.ServiceUnavailable.class,
            HttpServerErrorException.GatewayTimeout.class,
            HttpServerErrorException.BadGateway.class,
            ObjectOptimisticLockingFailureException.class,
            ResourceAccessException.class},
            maxAttemptsExpression = "${cid.stock.check.api.retry.maxAttempts}",
            backoff = @Backoff(delayExpression = "${cid.stock.check.api.retry.maxDelay}"))
    public boolean triggerStockCheck(OrderStockCheckRequest stockCheckRequestForCID) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        HttpEntity<Object> httpEntity = new HttpEntity(stockCheckRequestForCID, httpHeaders);
        ResponseEntity<String> responseEntity =
                restTemplate.exchange(cidBaseUrl + CID_STOCK_CHECK_ENDPOINT, HttpMethod.POST, httpEntity, String.class, (Object) null);
        logger.info("CidConnector.triggerStockCheck : URL - {}, Request - {}, Response - {}", cidBaseUrl + CID_STOCK_CHECK_ENDPOINT, stockCheckRequestForCID, responseEntity);
        boolean stockCheckResponse = checkStockCheckResponse(responseEntity, stockCheckRequestForCID.getOrderItemStockCheckList().get(0).getStockCheckList());
        logger.info("stockCheckResponse: {} for stockCheckRequestForCID: {}", stockCheckResponse, stockCheckRequestForCID);
        return stockCheckResponse;
    }

    private boolean checkStockCheckResponse(ResponseEntity<String> responseEntity, List<StockCheck> stockCheckRequest) {
        logger.info("[CidConnector -> checkStockCheckResponse] stock check response is {}", responseEntity);
        if (responseEntity == null || responseEntity.getBody() == null) {
            return false;
        }
        if (!responseEntity.getStatusCode().equals(HttpStatus.OK)) {
            return false;
        }
        List<OrderItemStockCheckResponse> orderItemStockCheckResponses = getOrderItemStockCheckResponseObject(responseEntity);
        if (orderItemStockCheckResponses == null || orderItemStockCheckResponses.isEmpty()) {
            return false;
        }
        for (OrderItemStockCheckResponse orderItemStockCheckResponse : orderItemStockCheckResponses) {
            if (orderItemStockCheckResponse == null) {
                return false;
            }
            List<StockCheckResponse> stockCheckList = orderItemStockCheckResponse.getStockCheckList();
            if (stockCheckList == null || stockCheckList.isEmpty()) {
                return false;
            }
            /*if (stockCheckList.size() != stockCheckRequest.size()) {
                return false;
            }*/

            for (StockCheckResponse stockCheckResponse : stockCheckList) {
                if (!stockCheckResponse.isFulfillable()) {
                    return false;
                }
            }
        }
        return true;
    }

    private List<OrderItemStockCheckResponse> getOrderItemStockCheckResponseObject(ResponseEntity<String> responseEntity) {
        try {
            List<OrderItemStockCheckResponse> orderItemStockCheckResponses = new ArrayList<>();
            Map<?, ?> responseMap = (Map) getDataObjFromResponse(responseEntity.getBody());
            if (responseMap != null) {
                orderItemStockCheckResponses =
                        ObjectHelper.getObjectMapper().readValue(ObjectHelper.getObjectMapper().writeValueAsString(responseMap.get("data")),
                                new TypeReference<List<OrderItemStockCheckResponse>>() {
                                });
            }
            return orderItemStockCheckResponses;
        } catch (IOException e) {
            logger.error("Exception occurred while converting the response", e);
            return Collections.emptyList();
        }
    }

    private Object getDataObjFromResponse(String responseBody) {
        try {
            return ObjectHelper.getObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                    .readValue(responseBody, (Class<?>) Map.class);
        } catch (IOException e) {
            logger.error("IOException occurred while converting the response to class", e);
            return null;
        }
    }

    @Timed
    public InventoryAvailabilityCountRR triggerInventoryAvailabilityCount(InventoryAvailabilityCountRR inventoryAvailabilityCountRequest) {
        InventoryAvailabilityCountRR inventoryAvailabilityCountResponse = null;
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        HttpEntity<Object> httpEntity = new HttpEntity(inventoryAvailabilityCountRequest, httpHeaders);
        ResponseEntity<BaseResponseModel> cidResponse =
                restTemplate.exchange(cidBaseUrl + CID_INVENTORY_AVAILABILITY_COUNT, HttpMethod.POST, httpEntity, BaseResponseModel.class, (Object) null);
        logger.info("[CidConnector.triggerInventoryAvailabilityCount] Called {} with request {} and response {}", CID_INVENTORY_AVAILABILITY_COUNT, httpEntity, cidResponse);
        if (cidResponse != null && cidResponse.getStatusCode().is2xxSuccessful())
            inventoryAvailabilityCountResponse = new Gson().fromJson(cidResponse.getBody().getData().toString(), InventoryAvailabilityCountRR.class);
        return inventoryAvailabilityCountResponse;
    }

    @Timed
    @Retryable(value = {HttpServerErrorException.InternalServerError.class,
            HttpServerErrorException.ServiceUnavailable.class,
            HttpServerErrorException.GatewayTimeout.class,
            HttpServerErrorException.BadGateway.class,
            ObjectOptimisticLockingFailureException.class,
            ResourceAccessException.class},
            maxAttemptsExpression = "${cid.stock.check.api.retry.maxAttempts}",
            backoff = @Backoff(delayExpression = "${cid.stock.check.api.retry.maxDelay}"))
    public StockBlockOrderResponse triggerBulkStockCheck(StockBlockOrderRequest orderStockCheckRequest) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));
            HttpEntity<StockBlockOrderRequest> requestHttpEntity = new HttpEntity<>(orderStockCheckRequest, headers);
            String finalUrl = cidBaseUrl + CID_BULK_STOCK_CHECK_ENDPOINT;
            ResponseEntity<OrderStockCheckResponse> responseEntity = restTemplate.exchange(finalUrl, HttpMethod.POST, requestHttpEntity, OrderStockCheckResponse.class);
            logger.info("CidConnector.triggerBulkStockCheck : URL - {}, OrderId - {}, Request - {}, Response - {}", finalUrl, orderStockCheckRequest.getOrderId(), orderStockCheckRequest, responseEntity);
            if (responseEntity != null && responseEntity.getStatusCode().is2xxSuccessful() && responseEntity.getBody() != null && responseEntity.getBody().getData() != null) {
                return responseEntity.getBody().getData();
            } else {
                throw new ResponseStatusException(responseEntity.getStatusCode(), responseEntity.getBody().toString());
            }
        } catch (Exception exception) {
            logger.error("CidConnector.triggerBulkStockCheck.Exception For Payload - " + orderStockCheckRequest, exception);
            throw exception;
        }
    }

}
