package com.lenskart.oms.connector;

import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.exception.ApplicationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.boot.web.client.RestTemplateBuilder;

import java.time.Duration;
import java.util.Collections;

@Component
@Slf4j
public class OrderManagementServiceConnector {

    private final RestTemplate restTemplate;

    @Value("${order.management.service.base.url}")
    private String baseUrl;

    @Value("${order.management.service.is.sbrt.endpoint}")
    private String sbrtEndpoint;

    public OrderManagementServiceConnector(RestTemplateBuilder restTemplateBuilder,
                                     @Value("${order.interceptor.connection.timeout}") Integer connectionTimeout,
                                     @Value("${order.interceptor.read.timeout}") Integer readTimeout) {
        this.restTemplate = restTemplateBuilder
                .setConnectTimeout(connectionTimeout == null ? Duration.ofMillis(ApplicationConstants.DEFAULT_CONNECTION_TIMEOUT) : Duration.ofMillis(connectionTimeout))
                .setReadTimeout(readTimeout == null ? Duration.ofMillis(ApplicationConstants.DEFAULT_READ_TIMEOUT) : Duration.ofMillis(readTimeout))
                .build();
    }

    public boolean fetchSbrtFlagForUnicomOrderCode(String unicomOrderCode) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        HttpEntity<Object> httpEntity = new HttpEntity(httpHeaders);
        String path = baseUrl + sbrtEndpoint + unicomOrderCode;
        try {
            ResponseEntity<Boolean> responseEntity = restTemplate.exchange(path, HttpMethod.GET, httpEntity, Boolean.class, (Object) null);
            return Boolean.TRUE.equals(responseEntity.getBody());
        } catch (Exception ex) {
            log.error("[OrderManagementServiceConnector][fetchSbrtFlagForUnicomOrderCode] Error Occurred for " + unicomOrderCode, ex);
            throw new ApplicationException("Error while fetching SBRT details from order management service for " + unicomOrderCode, ex);
        }
    }
}
