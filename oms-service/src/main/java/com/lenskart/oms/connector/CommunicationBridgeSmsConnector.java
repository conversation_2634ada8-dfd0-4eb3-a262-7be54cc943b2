package com.lenskart.oms.connector;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.constants.RedisOps;
import com.lenskart.nexs.service.RedisHandler;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.dto.BridgeTokenResponseDto;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.exception.BridgeCommunicationException;
import com.lenskart.oms.exception.BridgeTokenFetchException;
import com.lenskart.oms.exception.InvalidBridgeAuthTokenException;
import com.lenskart.oms.request.SmsRequest;
import com.lenskart.oms.response.SmsResponse;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.*;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Component
public class CommunicationBridgeSmsConnector {

    @Value("${communication.bridge.base.url}")
    private String bridgeV4BaseUrl;

    @Value("${communication.bridge.auth.token.ttl}")
    private Long authTokenRedisTtl;

    @Value("${bridge.auth.seed.token}")
    private String bridgeAuthSeedToken;

    @Value("${bridge.create.auth.token.url}")
    private String bridgeCreateAuthTokenUrl;

    @Value("${bridge.template.id}")
    private String bridgeTemplateId;

    @CustomLogger
    private Logger logger;

    private final RestTemplate restTemplate;

    public CommunicationBridgeSmsConnector(RestTemplateBuilder restTemplateBuilder,
                                           @Value("${communication.bridge.timeout}") Integer connectionTimeout
    ) {
        this.restTemplate = restTemplateBuilder
                .setConnectTimeout(connectionTimeout == null ? Duration.ofMillis(ApplicationConstants.DEFAULT_CONNECTION_TIMEOUT) : Duration.ofMillis(connectionTimeout))
                .setReadTimeout(connectionTimeout == null ? Duration.ofMillis(ApplicationConstants.DEFAULT_READ_TIMEOUT) : Duration.ofMillis(connectionTimeout))
                .build();
    }

    @Retryable(value = {BridgeCommunicationException.class, InvalidBridgeAuthTokenException.class, ApplicationException.class},
                maxAttemptsExpression = "${bridge.sms.max.attempts:3}")
    public SmsResponse sendCommunication(SmsRequest request, Long incrementId) {
        try {
            String authToken = getBridgeAuthToken();
            SmsResponse response = new SmsResponse();
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.set("token", authToken);
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            Map<String, Object> payload = new HashMap<>();
            payload.put("payload", request.getRequestPayload());
            HttpEntity<Object> httpEntity = new HttpEntity(payload, httpHeaders);
            ObjectMapper objectMapper = new ObjectMapper();
            logger.info("[sendCommunication] going to call sms service to send sms with request body: {} and url: {} and auth_token: {} and httpEntity : {} AND httpHeaders: {}", request, bridgeV4BaseUrl, authToken, httpEntity, httpHeaders);
            String url = bridgeV4BaseUrl + "/communication-api/" + bridgeTemplateId + "/IN/en";
            ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class, (Object) null);
            response.setResponseCode(responseEntity.getStatusCode().value());
            response.setResponseContent(responseEntity.getBody());
            //203
            if (responseEntity.getStatusCode() == HttpStatus.NON_AUTHORITATIVE_INFORMATION) {
                logger.info("[sendCommunication] Exception triggered: 203 : Communication Sent Failed for increment id: {} and request body: {}, response code: {}",incrementId, request, responseEntity.getStatusCode());
                throw new BridgeCommunicationException(HttpStatus.NON_AUTHORITATIVE_INFORMATION.value(), "Exception triggered: 203 : Communication Sent Failed");
            } else if(responseEntity.getStatusCode().is2xxSuccessful()) { //200 201 202
                logger.info("[sendCommunication] response form sms service: {}", responseEntity.getBody());
                response.setResponseCode(responseEntity.getStatusCode().value());
                response.setResponseContent(responseEntity.getBody());
            }
            else if (responseEntity.getStatusCode() == HttpStatus.UNAUTHORIZED){ // 401
                RedisHandler.redisOps(RedisOps.DEL, "auth_token");
                logger.error("[sendCommunication] Auth Key invalid || Send Sms returned response null for increment id: {} and request body: {}, response code: {}", incrementId, request, responseEntity.getStatusCode());
                throw new BridgeCommunicationException(HttpStatus.UNAUTHORIZED.value(), "Auth Key Invalid");
            } else { //500
                logger.error("[sendCommunication] Send Sms returned response null for increment id: {} and request body: {}, response code: {}", incrementId, request, responseEntity.getStatusCode());
                throw new InvalidBridgeAuthTokenException(HttpStatus.INTERNAL_SERVER_ERROR.value(), "Send Sms returned response null for increment id: " + incrementId);
            }
            return response;
        } catch (Exception exception) {
            logger.error("[sendCommunication] Exception while sending SMS for increment id: {} with message: {}", incrementId, exception.getMessage());
            throw new BridgeCommunicationException(HttpStatus.INTERNAL_SERVER_ERROR.value(), "Send SMS v4 failed for increment id: " + incrementId + " with exception: " + exception);
        }
    }

    public String getBridgeAuthToken() throws Exception {
        String authToken = (String) RedisHandler.redisOps(RedisOps.GET, "auth_token");
        if (!Objects.isNull(authToken)) return authToken;

        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        httpHeaders.set("seed_token", bridgeAuthSeedToken);
        HttpEntity httpEntity = new HttpEntity(httpHeaders);
        try {
            ResponseEntity<BridgeTokenResponseDto> tokenResponse = restTemplate.
                    exchange(bridgeCreateAuthTokenUrl, HttpMethod.GET, httpEntity, BridgeTokenResponseDto.class, (Object) null);
            logger.info("[getBridgeAuthToken] Response : {} and Status : {}", tokenResponse.getBody(), tokenResponse.getStatusCode());
            authToken = Objects.requireNonNull(tokenResponse.getBody()).getToken();
            RedisHandler.redisOps(RedisOps.SETVALUETTL, "auth_token", authToken, authTokenRedisTtl, TimeUnit.HOURS);
            return authToken;
        } catch (Exception e) {
            logger.error("[BridgeTokenFetchException] Error in fetching Auth Token", e);
            throw new BridgeTokenFetchException(HttpStatus.NOT_FOUND.value(), "Error in fetching Auth Token." + e);
        }
    }
}