package com.lenskart.oms.connector;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.exception.D365PublishDispatchException;
import com.lenskart.oms.request.D365PublishDispatchRequest;
import com.lenskart.oms.response.D365PublishDispatchResponse;
import com.lenskart.oms.service.ShipmentMetaService;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;

@Component
public class D365Connector {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    private final RestTemplate restTemplate;

    @Value("${d365.base.url}")
    private String d365BaseUrl;


    @Autowired
    ShipmentMetaService shipmentMetaService;

    public D365Connector(RestTemplateBuilder restTemplateBuilder,
                         @Value("${d365.read.timeout}") Integer readTimeout,
                         @Value("${d365.connection.timeout}") Integer connectionTimeout) {
        this.restTemplate = restTemplateBuilder
                .setConnectTimeout(connectionTimeout == null ? Duration.ofMillis(ApplicationConstants.DEFAULT_CONNECTION_TIMEOUT) : Duration.ofMillis(connectionTimeout))
                .setReadTimeout(readTimeout == null ? Duration.ofMillis(ApplicationConstants.DEFAULT_READ_TIMEOUT) : Duration.ofMillis(readTimeout))
                .build();
    }

    public D365PublishDispatchResponse publishDispatchOrderToD365(D365PublishDispatchRequest d365PublishDispatchRequest) throws D365PublishDispatchException {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<D365PublishDispatchRequest> httpEntity = new HttpEntity<>(d365PublishDispatchRequest, httpHeaders);
        try {
            ResponseEntity<D365PublishDispatchResponse> responseEntity = restTemplate.exchange(
                    d365BaseUrl,
                    HttpMethod.POST,
                    httpEntity,
                    D365PublishDispatchResponse.class
            );
            logger.info("[D365Connector] Request: {} ||  Response from D365 Shipment Creation API || Response: {}", d365PublishDispatchRequest, responseEntity);
            if(responseEntity.getStatusCode().is2xxSuccessful())
                return responseEntity.getBody();
        } catch(Exception e) {
            logger.error("[D365Connector] Error in calling d365 API for shippingPackageId: " + d365PublishDispatchRequest.getShippingPackageId());
            throw new D365PublishDispatchException("Error in calling d365 API: " + e.getMessage(), e);
        }
        return null;
    }
}
