package com.lenskart.oms.connector;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.enums.OmsSystem;
import com.lenskart.oms.enums.OrderType;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.request.WmsOrderActionRequest;
import com.lenskart.oms.response.WmsOrderItemHeaderBaseResponse;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.util.Collections;
import java.util.concurrent.CompletableFuture;

@Component
public class NexsWmsConnector {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    private static final String WMS_CANCEL_ORDER = "/nexs/wms/api/v1/order/returnOrCancel";
    private static final String WMS_RETURN_ORDER = "/nexs/wms/api/v1/order/return";
    private static final String WMS_CREATED_ORDER = "/nexs/wms/api/v1/order/getOrderByWmsOrderCode/";

    private final RestTemplate restTemplate;
    private final RestTemplate doRestTemplate;

    @Value("${nexs.wms.base.url}")
    private String nexsWmsBaseUrl;

    @CustomLogger
    private Logger log;

    public NexsWmsConnector(RestTemplateBuilder restTemplateBuilder,
                            @Value("${nexs.wms.read.timeout}") Integer readTimeout,
                            @Value("${nexs.wms.connection.timeout}") Integer connectionTimeout,
                            @Value("${nexs.do.wms.read.timeout:30000}") Integer doReadTimeout,
                            @Value("${nexs.do.wms.connection.timeout:30000}") Integer doConnectionTimeout) {
        this.restTemplate = restTemplateBuilder
                .setConnectTimeout(connectionTimeout == null ? Duration.ofMillis(ApplicationConstants.DEFAULT_CONNECTION_TIMEOUT) : Duration.ofMillis(connectionTimeout))
                .setReadTimeout(readTimeout == null ? Duration.ofMillis(ApplicationConstants.DEFAULT_READ_TIMEOUT) : Duration.ofMillis(readTimeout))
                .build();
        this.doRestTemplate = restTemplateBuilder
                .setConnectTimeout(Duration.ofMillis(doConnectionTimeout))
                .setReadTimeout(Duration.ofMillis(doReadTimeout))
                .build();
    }

    public void triggerCancelOrderInNexsWms(WmsOrderActionRequest wmsOrderActionRequest, OrderType orderType) throws ApplicationException {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        HttpEntity<Object> httpEntity = new HttpEntity(wmsOrderActionRequest, httpHeaders);
        ResponseEntity<String> responseEntity = null;

        log.info("[NexsCreateOrderStrategy -> triggerWMSCancelOrder] Going to call WMS with cancel order request {} on URL {}{}", wmsOrderActionRequest, nexsWmsBaseUrl, WMS_CANCEL_ORDER);

        if(OrderType.DISTRIBUTOR_ORDER.equals(orderType))
            responseEntity = doRestTemplate.exchange(nexsWmsBaseUrl + WMS_CANCEL_ORDER, HttpMethod.PUT, httpEntity, String.class, (Object) null);
        else
            responseEntity = restTemplate.exchange(nexsWmsBaseUrl + WMS_CANCEL_ORDER, HttpMethod.PUT, httpEntity, String.class, (Object) null);
        log.info("triggerCancelOrderInNexsWms - IncrementId {}, Response {} ", wmsOrderActionRequest.getIncrementId(), responseEntity);

        if (HttpStatus.OK.equals(responseEntity.getStatusCode()) && responseEntity.getBody() != null) {
            log.info("[NexsCancelOrderStrategy -> triggerWMSCancelOrder] Cancel Order call to WMS successful with response code {} and message {}", responseEntity.getStatusCode(), responseEntity.getBody());
        } else {
            log.error("[NexsCancelOrderStrategy -> triggerWMSCancelOrder] Cancel Order call to WMS failed with response code {} and message {}", responseEntity.getStatusCode(), responseEntity.getBody());
            throw new ApplicationException("returnOrCancel call to WMS failed with response code " + responseEntity.getStatusCode() + " and message " + responseEntity.getBody());
        }
    }

    @Async
    public CompletableFuture<Object> reassignOrderOms(Long incrementId, OmsSystem omsSystem, String wmsOrderCode) {
        AsyncResult<Object> result = new AsyncResult<>("success");
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        HttpEntity<Object> httpEntity = new HttpEntity(httpHeaders);
        String pathUrl = String.format("%s/nexs/wms/api/v1/order/%s/updateOmsSystem?wmsOrderCode=%s&updatedBy=%s&omsSystem=%s", nexsWmsBaseUrl, incrementId, wmsOrderCode, ApplicationConstants.OMS_ORDER_SYSTEM, omsSystem);
        logger.info("[NexsWmsConnector -> reassignOrderOms] going to reassign OMS for incrementId {} with URL {}", incrementId, pathUrl);
        ResponseEntity<String> responseEntity = restTemplate.exchange( pathUrl, HttpMethod.PUT, httpEntity, String.class, (Object) null);
        if (responseEntity.getStatusCode() != HttpStatus.OK && responseEntity.getBody() == null) {
            return AsyncResult.forExecutionException(
                    new ApplicationException("error while reassignOrderOmsToOrderOps in nexs-wms for incrementId: " + incrementId, null)
            ).completable();
        }
        return result.completable();
    }

    public boolean orderExistsInWms(String wmsOrderCode) {
        String pathUrl = nexsWmsBaseUrl + WMS_CREATED_ORDER + wmsOrderCode;
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        HttpEntity<Object> httpEntity = new HttpEntity<>(httpHeaders);
        try {
            ResponseEntity<WmsOrderItemHeaderBaseResponse> responseEntity = restTemplate.exchange(pathUrl, HttpMethod.GET, httpEntity, WmsOrderItemHeaderBaseResponse.class);
            log.info("[NexsWmsConnector][orderExistsInWms] wmsOrderCode: {} response code: {} and message: {}", wmsOrderCode, responseEntity.getStatusCode(), responseEntity.getBody());
            if (responseEntity.getStatusCode() == HttpStatus.OK && responseEntity.getBody() != null && responseEntity.getBody().isData()) {
                return responseEntity.getBody().isData();
            }
            return false;
        } catch (Exception e) {
            log.error("[NexsWmsConnector][orderExistsInWms] Exception while calling WMS order exists API", e);
            return false;
        }
    }

    public void triggerReturnOrderInNexsWms(WmsOrderActionRequest wmsOrderActionRequest) throws ApplicationException {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        HttpEntity<Object> httpEntity = new HttpEntity(wmsOrderActionRequest, httpHeaders);

        log.info("[NexsCreateOrderStrategy -> triggerReturnOrderInNexsWms] Going to call WMS with return order request {} on URL {}{}",
                wmsOrderActionRequest, nexsWmsBaseUrl, WMS_RETURN_ORDER
        );
        ResponseEntity<String> responseEntity =
                restTemplate.exchange(nexsWmsBaseUrl + WMS_RETURN_ORDER, HttpMethod.PUT, httpEntity, String.class, (Object) null);

        if (HttpStatus.OK.equals(responseEntity.getStatusCode()) && responseEntity.getBody() != null) {
            log.info("[NexsCancelOrderStrategy -> triggerReturnOrderInNexsWms] Return Order call to WMS successful with response code {} and message {}", responseEntity.getStatusCode(), responseEntity.getBody());
        } else {
            log.error("[NexsCancelOrderStrategy -> triggerReturnOrderInNexsWms] Return Order call to WMS failed with response code {} and message {}", responseEntity.getStatusCode(), responseEntity.getBody());
            throw new ApplicationException("return call to WMS failed with response code " + responseEntity.getStatusCode() + " and message " + responseEntity.getBody());
        }
    }
}
