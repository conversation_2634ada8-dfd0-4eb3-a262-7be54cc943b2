package com.lenskart.oms.connector;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.enums.OmsSystem;
import com.lenskart.oms.exception.ApplicationException;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.util.Collections;
import java.util.concurrent.CompletableFuture;

@Component
public class OrderInterceptorConnector {

    @CustomLogger
    private Logger logger;

    @Value("${order.interceptor.base.url}")
    private String orderInterceptorBaseUrl;

    private final RestTemplate restTemplate;

    public OrderInterceptorConnector(RestTemplateBuilder restTemplateBuilder,
                                     @Value("${order.interceptor.connection.timeout}") Integer connectionTimeout,
                                     @Value("${order.interceptor.read.timeout}") Integer readTimeout) {
        this.restTemplate = restTemplateBuilder
                .setConnectTimeout(connectionTimeout == null ? Duration.ofMillis(ApplicationConstants.DEFAULT_CONNECTION_TIMEOUT) : Duration.ofMillis(connectionTimeout))
                .setReadTimeout(readTimeout == null ? Duration.ofMillis(ApplicationConstants.DEFAULT_READ_TIMEOUT) : Duration.ofMillis(readTimeout))
                .build();
    }

    @Async
    public CompletableFuture<Object> reassignOrderOms(Long incrementId, OmsSystem omsSystem) {
        AsyncResult<Object> result = new AsyncResult<>("success");
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        HttpEntity<Object> httpEntity = new HttpEntity(httpHeaders);
        String pathUrl = String.format("%s/scm/interceptor/api/v1/order/%s/updateOmsSystem?updatedBy=%s&omsSystem=%s", orderInterceptorBaseUrl, incrementId, ApplicationConstants.OMS_ORDER_SYSTEM, omsSystem);
        logger.info("[OrderInterceptorConnector -> reassignOrderOms] going to reassign OMS for incrementId {} with URL {}", incrementId, pathUrl);
        ResponseEntity<String> responseEntity = restTemplate.exchange( pathUrl, HttpMethod.PUT, httpEntity, String.class, (Object) null);
        if (responseEntity.getStatusCode() != HttpStatus.OK && responseEntity.getBody() == null) {
            return AsyncResult.forExecutionException(
                    new ApplicationException("error while reassignOrderOmsToOrderOps in order-interceptor for incrementId: " + incrementId, null)
            ).completable();
        }
        return result.completable();
    }
}
