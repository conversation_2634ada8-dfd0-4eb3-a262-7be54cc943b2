package com.lenskart.oms.connector;

import com.lenskart.inventoryadapter.request.GetMarkFoundRequest;
import com.lenskart.inventoryadapter.request.GetMarkNotFoundRequest;
import com.lenskart.inventoryadapter.request.GetSearchRequest;
import com.lenskart.inventoryadapter.response.CustomResponse;
import com.lenskart.inventoryadapter.response.GetMarkFoundResponse;
import com.lenskart.inventoryadapter.response.GetMarkNotFoundResponse;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.optima.response.AvailableInventoryResponse;
import io.micrometer.core.annotation.Timed;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.util.Collections;

@Component
public class InventoryAdaptorConnector {

    private static final String SKU_SEARCH = "/searchV2";
    private static final String MARK_FOUND = "/getMarkFound";
    private static final String MARK_NOT_FOUND = "/getMarkNotFound";
    private static final String FETCH_ITEM_DATA = "/inventory/fetchItemData";

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    @Value("${ia.base.url}")
    private String iaBaseUrl;

    private final RestTemplate restTemplate;

    public InventoryAdaptorConnector(RestTemplateBuilder restTemplateBuilder,
                                     @Value("${ia.read.timeout}") Integer readTimeout,
                                     @Value("${ia.connection.timeout}") Integer connectionTimeout) {
        this.restTemplate = restTemplateBuilder
                .setConnectTimeout(connectionTimeout == null ? Duration.ofMillis(ApplicationConstants.DEFAULT_CONNECTION_TIMEOUT) : Duration.ofMillis(connectionTimeout))
                .setReadTimeout(readTimeout == null ? Duration.ofMillis(ApplicationConstants.DEFAULT_READ_TIMEOUT) : Duration.ofMillis(readTimeout))
                .build();
    }

    @Timed
    public AvailableInventoryResponse triggerInventoryAvailabilityCount(GetSearchRequest getSearchRequest) {
        try {
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            HttpEntity<GetSearchRequest> httpEntity = new HttpEntity<>(getSearchRequest, httpHeaders);
            ResponseEntity<AvailableInventoryResponse> iaResponse =
                    restTemplate.exchange(iaBaseUrl + SKU_SEARCH, HttpMethod.POST, httpEntity, AvailableInventoryResponse.class);
            logger.info("[InventoryAdaptorConnector.triggerInventoryAvailabilityCount] Called {} with request {} and response {}", iaBaseUrl + SKU_SEARCH, httpEntity, iaResponse);
            if (iaResponse.getStatusCode().is2xxSuccessful())
                return iaResponse.getBody();
        } catch (Exception exception) {
            logger.error("InventoryAdaptorConnector.triggerInventoryAvailabilityCount For Request - " + getSearchRequest, exception);
        }

        return null;
    }

    @Timed
    public GetMarkNotFoundResponse triggerInventoryMarkNotFound(GetMarkNotFoundRequest getMarkNotFoundRequest) {
        try {
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            HttpEntity<GetMarkNotFoundRequest> httpEntity = new HttpEntity<>(getMarkNotFoundRequest, httpHeaders);
            ResponseEntity<GetMarkNotFoundResponse> iaResponse =
                    restTemplate.exchange(iaBaseUrl + MARK_NOT_FOUND, HttpMethod.POST, httpEntity, GetMarkNotFoundResponse.class);
            logger.info("[InventoryAdaptorConnector.triggerInventoryMarkNotFound] Called {} with request {} and response {}", iaBaseUrl + MARK_NOT_FOUND, httpEntity, iaResponse);
            if (iaResponse.getStatusCode().is2xxSuccessful())
                return iaResponse.getBody();
        } catch (Exception exception) {
            logger.error("InventoryAdaptorConnector.triggerInventoryMarkNotFound For Request - " + getMarkNotFoundRequest, exception);
        }
        return null;
    }

    @Timed
    public GetMarkFoundResponse triggerInventoryMarkFound(GetMarkFoundRequest getMarkFoundRequest) {
        try {
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            HttpEntity<GetMarkFoundRequest> httpEntity = new HttpEntity<>(getMarkFoundRequest, httpHeaders);
            ResponseEntity<GetMarkFoundResponse> iaResponse =
                    restTemplate.exchange(iaBaseUrl + MARK_FOUND, HttpMethod.POST, httpEntity, GetMarkFoundResponse.class);
            logger.info("[InventoryAdaptorConnector.triggerInventoryMarkFound] Called {} with request {} and response {}", iaBaseUrl + MARK_FOUND, httpEntity, iaResponse);
            if (iaResponse.getStatusCode().is2xxSuccessful())
                return iaResponse.getBody();
        } catch (Exception exception) {
            logger.error("InventoryAdaptorConnector.triggerInventoryMarkFound For Request - " + getMarkFoundRequest, exception);
        }
        return null;
    }

    @Timed
    public CustomResponse fetchItemData(String barcode, String facilityCode) throws ApplicationException {
        logger.info("[fetchItemData] barcode: {} , facilityCode: {}", barcode, facilityCode);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        HttpEntity<Object> httpEntity = new HttpEntity(httpHeaders);
        String pathUrl = iaBaseUrl + FETCH_ITEM_DATA + "?barcode=" + barcode + "&facility=" + facilityCode;
        logger.info("[fetchItemData] inventory adaptor fetch item data url {} and barcode {}", pathUrl, barcode);
        ResponseEntity<CustomResponse> responseEntity = restTemplate.exchange(pathUrl, HttpMethod.GET, httpEntity, CustomResponse.class, (Object) null);
        if (responseEntity.getStatusCode() != HttpStatus.OK && responseEntity.getBody() == null) {
            logger.error("[fetchItemData] error while fetching item data from inventory adaptor for barcode {}", barcode);
            throw new ApplicationException("Error while fetching item data for barcode:" + barcode, null);
        }
        logger.info("[fetchItemData] inventory adaptor call to fetch item data for barcode {} successful with response {}", barcode, responseEntity.getBody());
        return responseEntity.getBody();
    }
}
