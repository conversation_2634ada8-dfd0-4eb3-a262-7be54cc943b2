package com.lenskart.oms.connector;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.reflect.TypeToken;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonObject;
import com.lenskart.fds.dto.DocumentDetailsDto;
import com.lenskart.fds.request.CreateDocumentRequest;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.commons.dto.BaseResponseModel;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.utils.ObjectHelper;
import com.lenskart.fds.request.CreateDocumentRequest;

import com.lenskart.nexs.commons.dto.BaseResponseModel;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.request.GetDocumentRequest;
import com.lenskart.oms.utils.ObjectHelper;
import io.micrometer.core.annotation.Timed;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.time.Duration;
import java.util.Arrays;
import java.io.InputStream;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class FdsConnector {
    @Value("${fds.timeout}")
    private int timeout;

    @Value("${fds.host}")
    private String fdsBaseUrl;

    @Value("${fds.createInvoice.url}")
    private String createInvoiceUrl;


    @Value("${fds.getInvoice.url}")
    private String getInvoiceUrl;
    @Autowired
    private RestTemplate restTemplate;

    private static final String FETCH_INVOICE_BY_SHIPMENT_IDS = "/fds/api/v1/document/search?searchTerms=documentSourceRefId.in:";

    public String createDocument(CreateDocumentRequest createDocumentRequest) throws ApplicationException {
        log.info("create invoice  request: {}", createDocumentRequest);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));
        HttpEntity<String> httpEntity = new HttpEntity(createDocumentRequest, httpHeaders);
        try {
            ResponseEntity<String> responseEntity = restTemplate.exchange(fdsBaseUrl + createInvoiceUrl, HttpMethod.POST, httpEntity, String.class, new Object[]{null});
            log.info(" [FdsConnector: create invoice], response body:{}", responseEntity.getBody());
            if (responseEntity.getStatusCode().is2xxSuccessful() && responseEntity.getBody() != null) {
                BaseResponseModel response = ObjectHelper.getObjectMapper().readValue(responseEntity.getBody(), new TypeReference<BaseResponseModel>() {});
                return (String) response.getData();
            } else {
                log.error("Unable to create invoice response  from FDS for request from  failed ,response is {}", responseEntity);
                throw new ApplicationException("error while create invoice for shipment: " + createDocumentRequest.getDocumentSourceReferenceId() + " response: " + responseEntity.getBody());
            }
        } catch (Exception e) {
            log.error("error while create invoice error: " + e.getMessage(), e);
            throw new ApplicationException("error while create invoice error: " + e.getMessage(), e);
        }
    }

    @Timed
    public List<DocumentDetailsDto> fetchInvoices(List<String> shippingPackageIds) {
        List<DocumentDetailsDto> documentDetailsDtos = null;
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        HttpEntity<Object> httpEntity = new HttpEntity(httpHeaders);
        String finalUrl = fdsBaseUrl + FETCH_INVOICE_BY_SHIPMENT_IDS + String.join(",", shippingPackageIds);
        ResponseEntity<String> responseEntity = restTemplate.exchange(finalUrl, HttpMethod.GET, httpEntity, String.class, (Object) null);
        log.info("[FdsConnector -> fetchInvoices] URL - {}, Response - {}", finalUrl, responseEntity);
        if (responseEntity != null && responseEntity.getStatusCode().is2xxSuccessful()) {
            JsonObject results = new Gson().fromJson(responseEntity.getBody(), JsonObject.class);
            if (results != null && results.has("data") && results.getAsJsonObject("data").has("content")) {
                documentDetailsDtos = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create().fromJson(results.getAsJsonObject("data").getAsJsonArray("content"), new TypeToken<List<DocumentDetailsDto>>() {
                }.getType());
            }
        }
        return documentDetailsDtos;
    }


    public InputStream getDocument(GetDocumentRequest getDocumentRequest) throws Exception {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.add("templateType",getDocumentRequest.getTemplateType());
            HttpEntity<Object> entity = new HttpEntity<>(headers);
            String getDocumentUrl = UriComponentsBuilder.fromHttpUrl(fdsBaseUrl + getInvoiceUrl)
                    .path(getDocumentRequest.getDocumentSourceReferenceId())
                    .queryParam("documentProvider", getDocumentRequest.getDocumentProvider())
                    .queryParam("facilityCode", getDocumentRequest.getFacility())
                    .queryParam("documentSource",getDocumentRequest.getDocumentSource())
                    .toUriString();
            log.info("[FdsConnector][getDocument] getDocumentRequest= {}, URL= {}", getDocumentRequest, getDocumentUrl);
            ResponseEntity<org.springframework.core.io.Resource> response = restTemplate.exchange(getDocumentUrl, HttpMethod.GET, entity, org.springframework.core.io.Resource.class);
            if (Objects.nonNull(response) && HttpStatus.OK.equals(response.getStatusCode())) {
                log.info("[FdsConnector][getDocument]-Success Response from FDS for {}", getDocumentRequest);
                return response.getBody().getInputStream();
            } else {
                log.info("[FdsConnector][getDocument] Unable to get Document for {} errorMessage={}", getDocumentRequest, response.getBody().getDescription());
                throw new Exception("Unable to get Invoice From FDS: " + response.getBody().getDescription());
            }
        } catch (Exception e) {
            log.error("[FdsConnector][getDocument] Exception while getting document at FDS for =" + getDocumentRequest + e.getMessage(), e);
            throw new Exception(e.getMessage());
        }
    }



}