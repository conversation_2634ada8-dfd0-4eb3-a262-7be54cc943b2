package com.lenskart.oms.connector;

import com.fasterxml.jackson.core.type.TypeReference;
import com.lenskart.core.model.Product;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.constants.RedisOps;
import com.lenskart.nexs.service.RedisHandler;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.request.CatalogOpsLensOnlyItemRequest;
import com.lenskart.oms.request.CatalogOpsPowerWiseIdRequest;
import com.lenskart.oms.response.AssignPowerResponseWrapper;
import com.lenskart.oms.utils.ObjectHelper;
import io.micrometer.core.annotation.Timed;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.time.Duration;
import java.util.concurrent.TimeUnit;

@Component
public class CatalogOpsConnector {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    @Value("${catalog.ops.redis.ttl}")
    private String redisTtl;

    @Value("${catalog.ops.host}")
    private String catalogOpsHost;

    @Value("${catalog.ops.fetch.byProductId.url}")
    private String fetchProductDetailsByProductId;

    @Value("${catalog.ops.fetch.framePIdByPackage.url}")
    private String fetchProductIdsByPackageId;

    @Value("${catalog.ops.fetch.classification.map:/v2/classificationMap/getAll}")
    private String fetchClassificationMap;



    private final String fetchPowerWisePid = "/product/fetchPowerWiseProductIdDetails";

    private final String fetchProductDetailsByProductIdAndClassification = "/product/findByClassificationIdAndPids";

    private static final String CATALOG_PRODUCT_DETAILS_BY_PRODUCT_ID_CACHE_KEY = "CATALOG_PRODUCT_DETAILS_BY_PRODUCT_ID";

    private static final String CATALOG_PRODUCT_IDS_BY_PACKAGE_ID_CACHE_KEY = "CATALOG_PRODUCT_IDS_BY_PACKAGE_ID_CACHE_KEY_";
    private final RestTemplate restTemplate;
    private static final String CATALOG_PRODUCT_ID_FOR_LENS_ONLY_TYPE_URL = "/new-lens-only/product-id/";

    public CatalogOpsConnector(RestTemplateBuilder restTemplateBuilder,
                        @Value("${catalog.ops.timeout}") Integer connectionTimeout
    ) {
        this.restTemplate = restTemplateBuilder
                .setConnectTimeout(connectionTimeout == null ? Duration.ofMillis(ApplicationConstants.DEFAULT_CONNECTION_TIMEOUT) : Duration.ofMillis(connectionTimeout))
                .setReadTimeout(connectionTimeout == null ? Duration.ofMillis(ApplicationConstants.DEFAULT_READ_TIMEOUT) : Duration.ofMillis(connectionTimeout))
                .build();
    }

    @Timed
    public Product findProductDetailsByProductId(Long productId) throws Exception {
        Product product = getProductDetailsFromRedis(productId);
        if (!Objects.isNull(product)) {
            return product;
        }

        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        HttpEntity<Object> httpEntity = new HttpEntity(httpHeaders);

        logger.info("[findProductDetailsByProductId] going to call catalog ops to get product {} details with URL {}{}", productId, catalogOpsHost, fetchProductDetailsByProductId);
        ResponseEntity<String> responseEntity =
                restTemplate.exchange(catalogOpsHost + fetchProductDetailsByProductId + productId, HttpMethod.GET, httpEntity, String.class, (Object) null);
        logger.info("[findProductDetailsByProductId] response from catalogOps is {}", responseEntity);

        if (responseEntity.getStatusCode() == HttpStatus.OK && responseEntity.getBody() != null) {
            product = ObjectHelper.getObjectMapper().readValue(responseEntity.getBody(), new TypeReference<Product>() {});
            RedisHandler.redisOps(RedisOps.SETVALUETTL, CATALOG_PRODUCT_DETAILS_BY_PRODUCT_ID_CACHE_KEY + productId, ObjectHelper.convertToString(product), Long.valueOf(redisTtl), TimeUnit.HOURS);
            logger.info("[findProductDetailsByProductId] product details by product id {} stored in redis with key {}{}", productId, CATALOG_PRODUCT_DETAILS_BY_PRODUCT_ID_CACHE_KEY, productId);

            return product;
        } else {
            logger.error("[findProductDetailsByProductId] Fetch products details by product id from catalog failed with status {}", responseEntity.getStatusCode());
            throw new ApplicationException("Fetch product details by product id from catalog failed, response is " + responseEntity.getStatusCodeValue(), null);
        }
    }

    private Product getProductDetailsFromRedis(Long productId) throws Exception {
        Object productListObj = RedisHandler.redisOps(RedisOps.GET, CATALOG_PRODUCT_DETAILS_BY_PRODUCT_ID_CACHE_KEY + productId);
        if (Objects.nonNull(productListObj)) {
            logger.info("[findProductDetailsByProductId] Product details found in cache for product id {}", productId);
            return ObjectHelper.getObjectMapper().readValue(String.valueOf(productListObj), new TypeReference<Product>() {});
        }
        return null;
    }

    public AssignPowerResponseWrapper fetchPowerWisePidsFromCatalogOps(CatalogOpsPowerWiseIdRequest catalogOpsPowerWiseIdRequest) throws Exception {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> httpEntity = new HttpEntity(catalogOpsPowerWiseIdRequest,httpHeaders);
        logger.info("[fetchPowerWisePidsFromCatalogOps] going to call catalog ops to get power wise pid details with URL {}{} and with request body: {}", catalogOpsHost, fetchPowerWisePid, catalogOpsPowerWiseIdRequest);
        ResponseEntity<AssignPowerResponseWrapper> responseEntity = restTemplate.exchange(catalogOpsHost + fetchPowerWisePid, HttpMethod.POST, httpEntity, AssignPowerResponseWrapper.class, (Object) null);
        if (responseEntity.getStatusCode() == HttpStatus.OK && responseEntity.getBody() != null) {
            logger.info("[fetchPowerWisePidsFromCatalogOps] response from catalog ops : {}",responseEntity.getBody());
            return responseEntity.getBody();
        } else {
            logger.error("[fetchPowerWisePidsFromCatalogOps] unexpected/null response from catalog ops : {} with status code: {}",responseEntity.getBody(), responseEntity.getStatusCode());
            throw new ApplicationException("Fetch Power wise PId's from catalog failed, response is " + responseEntity.getStatusCodeValue());
        }
    }

    public List<Product> getProductDetailsByPIdsAndClassification(Set<Long> productId, Integer classification, String lensType) throws ApplicationException {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> httpEntity = new HttpEntity(productId,httpHeaders);
        logger.info("[getProductDetailsByPIdsAndClassification] going to call catalog ops to get product details by pid and classification with URL {}{} ,requested pids: {} , classification: {}, request/lens type: {}", catalogOpsHost, fetchProductDetailsByProductIdAndClassification, productId, classification, lensType);
        ResponseEntity<String> responseEntity;
        try {
            responseEntity = restTemplate.exchange(catalogOpsHost + fetchProductDetailsByProductIdAndClassification + "/" + classification + "/" + lensType, HttpMethod.POST, httpEntity, String.class, (Object) null);
            if (responseEntity.getStatusCode() == HttpStatus.OK && responseEntity.getBody() != null) {
                logger.info("[getProductDetailsByPIdsAndClassification] response from catalog ops : {}", responseEntity.getBody());
                return ObjectHelper.getObjectMapper().readValue(responseEntity.getBody(), new TypeReference<List<Product>>() {
                });
            } else {
                logger.error("[getProductDetailsByPIdsAndClassification] unexpected/null response from catalog ops : {} with status code: {}", responseEntity.getBody(), responseEntity.getStatusCode());
                throw new ApplicationException("[getProductDetailsByPIdsAndClassification] Fetch Product details from catalog failed, response is " + responseEntity.getStatusCodeValue());
            }
        } catch (Exception e){
            logger.error("[getProductDetailsByPIdsAndClassification] catalog ops call failed with exception: {}", e.getMessage());
            throw new ApplicationException("[getProductDetailsByPIdsAndClassification] catalog ops cal failed with exception: " + e.getMessage(),e);
        }
    }

    public List<Integer> getFramePIdByPackage(Long packageId) throws ApplicationException {
        try {
            List<Integer> productIds = getProductIdsFromRedis(packageId);
            if (!CollectionUtils.isEmpty(productIds)) {
                return productIds;
            }
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            HttpEntity<Object> httpEntity = new HttpEntity(httpHeaders);

            logger.info("[getFramePIdByPackage] going to call catalog ops to get productIds for packageId: {} details with URL {}{}", packageId, catalogOpsHost, fetchProductIdsByPackageId);
            ResponseEntity<String> responseEntity =
                    restTemplate.exchange(catalogOpsHost + fetchProductIdsByPackageId + packageId, HttpMethod.GET, httpEntity, String.class, (Object) null);
            logger.info("[getFramePIdByPackage] response from catalogOps is {}", responseEntity);

            if (responseEntity.getStatusCode() == HttpStatus.OK && responseEntity.getBody() != null) {
                productIds = ObjectHelper.getObjectMapper().readValue(responseEntity.getBody(), new TypeReference<List<Integer>>() {
                });
                RedisHandler.redisOps(RedisOps.SETVALUETTL, CATALOG_PRODUCT_IDS_BY_PACKAGE_ID_CACHE_KEY + packageId, ObjectHelper.convertToString(productIds), Long.valueOf(redisTtl), TimeUnit.HOURS);
                return productIds;
            } else {
                logger.error("[getFramePIdByPackage] Fetch productIds by packageId : {} from catalog failed with status {}", packageId, responseEntity.getStatusCode());
                throw new ApplicationException("Fetch productIds by packageId : "+packageId+" from catalog failed, response is " + responseEntity.getStatusCodeValue(), null);
            }
        } catch (Exception e){
            logger.error("[getFramePIdByPackage] Fetch productIds by packageId : {} from catalog failed with exception {}", packageId, e.getMessage());
            throw new ApplicationException("Fetch productIds by packageId : "+packageId+" from catalog failed, exception is " + e.getMessage(), null);
        }
    }

    private List<Integer> getProductIdsFromRedis(Long packageId) throws Exception {
        Object productIdListObj = RedisHandler.redisOps(RedisOps.GET, CATALOG_PRODUCT_IDS_BY_PACKAGE_ID_CACHE_KEY + packageId);
        if (Objects.nonNull(productIdListObj)) {
            logger.info("[getProductIdsFromRedis] Product Ids found in cache for package id {}", packageId);
            return ObjectHelper.getObjectMapper().readValue(String.valueOf(productIdListObj), new TypeReference<List<Integer>>() {});
        }
        return null;
    }

    public Integer getProductIdForLensOnlyItem(Integer productId, Integer magentoItemId, String frameBarcode) throws ApplicationException {
        logger.info("[getProductIdForLensOnlyItem] Fetching lens only pid for sku {} and magentoItemId {}", productId, magentoItemId);
        CatalogOpsLensOnlyItemRequest lensOnlyProductIdRequest = new CatalogOpsLensOnlyItemRequest();
        lensOnlyProductIdRequest.setMagentoItemId(magentoItemId);
        lensOnlyProductIdRequest.setFrameBarcode(frameBarcode);
        lensOnlyProductIdRequest.setSource("WEB");
        String url= catalogOpsHost + CATALOG_PRODUCT_ID_FOR_LENS_ONLY_TYPE_URL + productId.toString();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<?> entity = new HttpEntity<Object>(lensOnlyProductIdRequest, headers);

        try {
            logger.info("[getProductIdForLensOnlyItem] Calling Catalog service with url: {} and request: {}", url, lensOnlyProductIdRequest);
            ResponseEntity<String> catalogResponse = restTemplate.exchange(url, HttpMethod.POST, entity,
                    String.class);
            logger.info("[getProductIdForLensOnlyItem] Response from Catalog ops for sku: {} is :{}", productId, ObjectHelper.convertToString(catalogResponse));
            if (catalogResponse != null && catalogResponse.getStatusCode() == HttpStatus.OK && catalogResponse.getBody() != null) {
                String responseString=catalogResponse.getBody();
                JSONObject jsonObject = new JSONObject(responseString);
                if (jsonObject != null && jsonObject.get("lensOnlyProductId") != null) {
                    logger.info("[getProductIdForLensOnlyItem] lensOnlyProductId value is: {} for sku: {}", jsonObject.get("lensOnlyProductId"), productId);
                    return (int)jsonObject.get("lensOnlyProductId");
                } else {
                    logger.error("[getProductIdForLensOnlyItem] response body or lensOnlyProductId in the body is null for sku request: {}", productId);
                    throw new ApplicationException("Response body or lensOnlyProductId in the body is null for sku request: "+productId);
                }
            } else {
                logger.error("[getProductIdForLensOnlyItem] complete response from catalog-ops is null or response status is not OK for sku request: {}", productId);
                throw new ApplicationException("Complete response from catalog-ops is null or response status is not OK for sku request: "+productId);
            }
        } catch (Exception e) {
            logger.error("[getProductIdForLensOnlyItem] Fetching lens only pid from catalog-ops for sku : "+productId+" and magentoItemId : "+magentoItemId+" failed with exception: "+e.getMessage());
            throw new ApplicationException("Fetching lens only pid from catalog-ops for sku : "+productId+" and magentoItemId : "+magentoItemId+" failed with exception: "+e.getMessage());
        }
    }

    public Map<Integer, String> getClassificationMap() {
        String url = catalogOpsHost + fetchClassificationMap;
        ResponseEntity<Map<Integer, String>> responseEntity = restTemplate.exchange(
                url,
                HttpMethod.GET,
                null,
                new ParameterizedTypeReference<Map<Integer, String>>() {}
        );

        return responseEntity.getBody();
    }
}
