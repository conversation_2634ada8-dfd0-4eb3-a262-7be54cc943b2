package com.lenskart.oms.connector;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.dto.B2bItemPriceDetailsResponseDto;
import com.lenskart.oms.exception.ApplicationException;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;

@Component
public class POSConnector {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    private final RestTemplate restTemplate;

    @Value("${psclient.baseurl}")
    private String baseUrl;

    @Value("${pos.baseUrl}")
    private String posBaseUrl;

    @Value("${pos.validation.flag}")
    private Boolean posValidationFlag;

    public POSConnector(RestTemplateBuilder restTemplateBuilder,
                        @Value("${pos.connector.timeout}") Integer connectionTimeout
    ) {
        this.restTemplate = restTemplateBuilder
                .setConnectTimeout(connectionTimeout == null ? Duration.ofMillis(ApplicationConstants.DEFAULT_CONNECTION_TIMEOUT) : Duration.ofMillis(connectionTimeout))
                .setReadTimeout(connectionTimeout == null ? Duration.ofMillis(ApplicationConstants.DEFAULT_READ_TIMEOUT) : Duration.ofMillis(connectionTimeout))
                .build();
    }

    public Boolean getPowerStatusFromPOS(Integer incrementId) throws ApplicationException {
        boolean prescriptionStatusFlag = true;
        if (!posValidationFlag) {
            try {
                logger.info("getPowerStatusFromPOS incrementId : {}, url : {}/franchise/getPrescriptionStatus/{}", incrementId, baseUrl, incrementId);
                ResponseEntity<String> response = restTemplate.exchange(baseUrl + "/franchise/getPrescriptionStatus/" + incrementId, HttpMethod.GET, null, String.class);
                logger.info("[getPowerStatusFromPOS] Response from ps service is {} for incrementId : {}",response,incrementId);
                if (null != response && !StringUtils.isBlank(response.getBody()) && HttpStatus.OK.equals(response.getStatusCode())) {
                    if (StringUtils.equalsIgnoreCase(response.getBody().trim(), "false")) {
                        logger.info("[getPowerStatusFromPOS] prescriptionStatusFlag is false for incrementId : {}", incrementId);
                        prescriptionStatusFlag = false;
                    } else {
                        logger.info("[getPowerStatusFromPOS] prescriptionStatusFlag is true for incrementId : {}", incrementId);
                        prescriptionStatusFlag = true;
                    }
                } else {
                    throw new ApplicationException("Response from POS is null or empty for increment id: "+incrementId);
                }
            } catch (Exception e) {
                logger.error("[getPowerStatusFromPOS] Exception occurred : ", e);
                throw new ApplicationException("[getPowerStatusFromPOS] Exception occurred for incrementId: "+ incrementId +" with message: "+e.getMessage(), e);
            }
        }
        return prescriptionStatusFlag;
    }

    public B2bItemPriceDetailsResponseDto getMarginPriceForB2bOrder(Long incrementId, String channel) throws Exception {
        try {
            logger.info("[POSConnector] : getMarginPriceForB2bOrder incrementId: " + incrementId+" channel: "+channel);
            String url = posBaseUrl + "/v1/po/"+incrementId+"?channel="+channel.toUpperCase();
            logger.info("[PSServiceImpl : getMarginPriceForB2bOrder ] calling pos api url : "+url);
            HttpHeaders headers =new HttpHeaders();
            headers.set("X-Lenskart-App-Id", "connect");
            headers.set("X-Lenskart-API-Key", "valyoo123");
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity request = new HttpEntity(headers);
            logger.info("[POSConnector : getMarginPriceForB2bOrder ] headers : "+headers);
            logger.info("[POSConnector : getMarginPriceForB2bOrder ] request : "+request);
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, request,
                    String.class);
            logger.info("[POSConnector : getMarginPriceForB2bOrder ] response : "+response);
            if (null != response && !StringUtils.isBlank(response.getBody())) {
                logger.info("[POSConnector : getMarginPriceForB2bOrder ] response body : "+response.getBody());
                logger.info("POSConnector : getMarginPriceForB2bOrder : " + response.getBody());
                ObjectMapper mapper = new ObjectMapper();
                B2bItemPriceDetailsResponseDto b2BItemPriceDetailsResponseDto = mapper.readValue(response.getBody(), B2bItemPriceDetailsResponseDto.class);
                return b2BItemPriceDetailsResponseDto;
            }
        } catch (Exception e) {
            logger.error("getMarginPriceForB2bOrder", e);
            throw new Exception( "Margin price not found for b2b order");
        }
        return null;
    }
}
