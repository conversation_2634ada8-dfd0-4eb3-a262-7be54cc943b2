package com.lenskart.oms.connector;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.commons.dto.BaseResponseModel;
import com.lenskart.nexs.fms.model.response.FacilityDetailsResponse;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.utils.ObjectHelper;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.time.Duration;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Component
public class FmsConnector {

    @Value("${fms.base.url}")
    private String fmsBaseUrl;

    @Value("${fms.facility.details.url}")
    private String fmsFetchDetailsUrl;

    @Value("${fms.redis.ttl}")
    private String redisTtl;

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;


    @Autowired
    private ObjectMapper objectMapper;
    private static final String FACILITIES = "facilities";
    private static final String ALL_FACILITIES_FROM_FMS = "/facilities";


    @Value("${fms.nexs.facility.page.size}")
    private Integer pageSize;

    private final RestTemplate restTemplate;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    private static final String FMS_RESPONSE_CACHE_KEY = "NEXS_INVOICING_FMS_";

    public FmsConnector(RestTemplateBuilder restTemplateBuilder,
                        @Value("${fms.connection.timeout}") Integer connectionTimeout,
                        @Value("${fms.read.timeout}") Integer readTimeout) {
        this.restTemplate = restTemplateBuilder
                .setConnectTimeout(connectionTimeout == null ? Duration.ofMillis(ApplicationConstants.DEFAULT_CONNECTION_TIMEOUT) : Duration.ofMillis(connectionTimeout))
                .setReadTimeout(readTimeout == null ? Duration.ofMillis(ApplicationConstants.DEFAULT_READ_TIMEOUT) : Duration.ofMillis(readTimeout))
                .build();
    }

    public Boolean fetchFacilityDetails(String facilityCode) throws Exception {
        Object facilityDetails = redisTemplate.opsForValue().get(FMS_RESPONSE_CACHE_KEY + facilityCode);
        if (Objects.nonNull(facilityDetails))
            return true;
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> httpEntity = new HttpEntity(httpHeaders);
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(fmsBaseUrl + fmsFetchDetailsUrl);
        logger.info("Request for FMS for facility details  : " + facilityCode);
        ResponseEntity<String> responseEntity = restTemplate.exchange(builder.toUriString() + facilityCode, HttpMethod.GET, httpEntity, String.class, (Object) new Object());
        if (responseEntity.getStatusCode() == HttpStatus.OK && responseEntity.getBody() != null) {
            BaseResponseModel baseResponseModel = objectMapper.readValue(responseEntity.getBody(), new TypeReference<BaseResponseModel>() {
            });
            logger.info("[fetchFacilityDetails] response from fms {}", baseResponseModel.getData().toString());
            FacilityDetailsResponse facilityDetailsResponse = objectMapper.convertValue(baseResponseModel.getData(), new TypeReference<FacilityDetailsResponse>() {
            });
            logger.info("Response from FMS for fetch facility details  : " + facilityDetailsResponse);
            if (Objects.isNull(facilityDetailsResponse)) {
                logger.error("Invalid facility code" + facilityCode);
                return false;            }
            redisTemplate.opsForValue().set(FMS_RESPONSE_CACHE_KEY + facilityCode, facilityCode, Long.valueOf(redisTtl), TimeUnit.HOURS);
            return true;
        } else {
            logger.error("Fetch facility details from fms failed ,response is " + responseEntity.getStatusCodeValue() + "response is" + responseEntity.getBody());
            return false;
        }
    }
}
