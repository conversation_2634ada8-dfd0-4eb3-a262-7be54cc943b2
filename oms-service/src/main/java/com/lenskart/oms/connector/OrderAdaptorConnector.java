package com.lenskart.oms.connector;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.response.DailyOrderCounterResponse;
import com.lenskart.oms.response.UwOrderResponse;
import io.micrometer.core.annotation.Timed;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.*;
import org.springframework.orm.ObjectOptimisticLockingFailureException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.util.Collections;

@Component
public class OrderAdaptorConnector {

    private static final String DAILY_COUNT_FROM_ORDER_ADAPTOR = "/order/checkDailyOrderCounter";

    @CustomLogger
    private Logger logger;

    @Value("${order.adaptor.base.url}")
    private String orderAdaptorBaseUrl;

    private final RestTemplate restTemplate;

    public OrderAdaptorConnector(RestTemplateBuilder restTemplateBuilder,
                             @Value("${order.adaptor.connection.timeout}") Integer connectionTimeout,
                             @Value("${order.adaptor.read.timeout}") Integer readTimeout) {
        this.restTemplate = restTemplateBuilder
                .setConnectTimeout(connectionTimeout == null ? Duration.ofMillis(ApplicationConstants.DEFAULT_CONNECTION_TIMEOUT) : Duration.ofMillis(connectionTimeout))
                .setReadTimeout(readTimeout == null ? Duration.ofMillis(ApplicationConstants.DEFAULT_READ_TIMEOUT) : Duration.ofMillis(readTimeout))
                .build();
    }

    @Timed
    @Retryable(value = {HttpServerErrorException.InternalServerError.class,
            HttpServerErrorException.ServiceUnavailable.class,
            HttpServerErrorException.GatewayTimeout.class,
            HttpServerErrorException.BadGateway.class,
            ObjectOptimisticLockingFailureException.class},
            maxAttemptsExpression = "${order.adaptor.api.retry.maxAttempts}",
            backoff = @Backoff(delayExpression = "${order.adaptor.api.retry.maxDelay}"))
    public DailyOrderCounterResponse getDailyOrderLimitCount(String facilityCode, boolean fr0Order) throws ApplicationException {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        HttpEntity<Object> httpEntity = new HttpEntity(httpHeaders);
        String pathUrl = orderAdaptorBaseUrl + DAILY_COUNT_FROM_ORDER_ADAPTOR + "?fr0Order=" + fr0Order + "&facilityCode=" + facilityCode;
        logger.info("[getDailyOrderLimitCount] order adaptor daily order count url {}", pathUrl);
        ResponseEntity<DailyOrderCounterResponse> responseEntity = restTemplate.exchange(pathUrl, HttpMethod.GET, httpEntity, DailyOrderCounterResponse.class, (Object) null);
        if (responseEntity.getStatusCode() != HttpStatus.OK && responseEntity.getBody() == null) {
            logger.error("[getDailyOrderLimitCount] error while fetching daily order limit from order adaptor for facility {}", facilityCode);
            throw new ApplicationException("Error while fetching daily order count for facility code:" + facilityCode, null);
        }
        logger.info("[getDailyOrderLimitCount] order adaptor call to fetch daily order count for facilityCode {} successful with response {}", facilityCode, responseEntity.getBody());
        return responseEntity.getBody();
    }
}
