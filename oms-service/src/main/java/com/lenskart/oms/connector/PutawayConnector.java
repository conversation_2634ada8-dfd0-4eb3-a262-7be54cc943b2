package com.lenskart.oms.connector;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.exception.FdsException;
import com.lenskart.oms.exception.PutawayServiceException;
import com.lenskart.oms.model.PutawayResponse;
import com.lenskart.putawayservice.dto.PutawayCreationRequest;
import com.lenskart.putawayservice.dto.PutawayCreationResponse;
import io.micrometer.core.annotation.Timed;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.orm.ObjectOptimisticLockingFailureException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Component
public class PutawayConnector {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    @Value("${putaway.base.url}")
    private String putawayBaseUrl;
    @Value("${create.putaway.url}")
    private String createPutawayUrl;

    private final RestTemplate restTemplate;

    public PutawayConnector(RestTemplateBuilder restTemplateBuilder, @Value("${putaway.rest.timeout}") Integer connectionTimeout) {
        this.restTemplate = restTemplateBuilder.setConnectTimeout(connectionTimeout == null ? Duration.ofMillis(ApplicationConstants.DEFAULT_CONNECTION_TIMEOUT) : Duration.ofMillis(connectionTimeout)).setReadTimeout(connectionTimeout == null ? Duration.ofMillis(ApplicationConstants.DEFAULT_READ_TIMEOUT) : Duration.ofMillis(connectionTimeout)).build();
    }

    @Timed
    @Retryable(value = {HttpServerErrorException.InternalServerError.class, HttpServerErrorException.ServiceUnavailable.class, HttpServerErrorException.GatewayTimeout.class, HttpServerErrorException.BadGateway.class, ObjectOptimisticLockingFailureException.class, ResourceAccessException.class}, backoff = @Backoff(100))
    public String createPutaway(PutawayCreationRequest putawayCreationRequest) {
        try {
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            HttpEntity<Object> httpEntity = new HttpEntity<>(putawayCreationRequest, httpHeaders);
            String url = putawayBaseUrl.concat(createPutawayUrl);
            logger.info("[PutawayConnector][createPutaway] URL={} , request: {}", url, putawayCreationRequest);
            ResponseEntity<List<PutawayResponse>> response = restTemplate.exchange(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<List<PutawayResponse>>() {});
            logger.info("[PutawayConnector][createPutaway] response: " + response.getBody());
            if (Objects.nonNull(response.getBody()) && response.getBody().get(0).getStatusCode() == 200 && response.getBody().get(0).getSuccess() != null
                    && response.getBody().get(0).getSuccess().equals(true) && StringUtils.isNotBlank(response.getBody().get(0).getBarcodeInfo().get(0).getNewPutawayId()))
                //todo revalidate the API response and handle it accordingly
                return response.getBody().get(0).getBarcodeInfo().get(0).getNewPutawayId();
            else {
                throw new PutawayServiceException("Error while creating putaway");
            }
        } catch (Exception e) {
            logger.error("[PutawayConnector][createPutaway] Exception for: " + putawayCreationRequest + "ErrorMessage=" + e.getMessage(), e);
            throw new PutawayServiceException("Exception while creating putaway " + e.getMessage(), e);
        }
    }
}
