package com.lenskart.oms.connector;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.response.OmsResponse;
import com.lenskart.oms.response.Serviceability;
import io.micrometer.core.annotation.Timed;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.*;
import org.springframework.orm.ObjectOptimisticLockingFailureException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.time.Duration;
import java.util.Collections;
import java.util.List;

@Component
public class LsmServiceabilityConnector {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    @Value("${lsm.serviceability.base.url}")
    private String lsmServiceabilityBaseUrl;

    private final RestTemplate restTemplate;

    public LsmServiceabilityConnector(RestTemplateBuilder restTemplateBuilder,
                               @Value("${lsm.serviceability.rest.timeout}") Integer connectionTimeout
    ) {
        this.restTemplate = restTemplateBuilder
                .setConnectTimeout(connectionTimeout == null ? Duration.ofMillis(ApplicationConstants.DEFAULT_CONNECTION_TIMEOUT) : Duration.ofMillis(connectionTimeout))
                .setReadTimeout(connectionTimeout == null ? Duration.ofMillis(ApplicationConstants.DEFAULT_READ_TIMEOUT) : Duration.ofMillis(connectionTimeout))
                .build();
    }

    @Timed
    @Retryable(value = {HttpServerErrorException.InternalServerError.class,
            HttpServerErrorException.ServiceUnavailable.class,
            HttpServerErrorException.GatewayTimeout.class,
            HttpServerErrorException.BadGateway.class,
            ObjectOptimisticLockingFailureException.class,
            ResourceAccessException.class},
            maxAttemptsExpression = "${lsm.serviceability.api.retry.maxAttempts}",
            backoff = @Backoff(delayExpression = "${lsm.serviceability.api.retry.maxDelay}"))
    public OmsResponse<List<Serviceability>> fetchServiceabilityByPincodeAndFacility(String lkCountry, String pincode, String facilityCode) throws ApplicationException {
        OmsResponse<List<Serviceability>> serviceableCourierResponseList;
        try {
            if (StringUtils.isEmpty(facilityCode) || StringUtils.isEmpty(pincode)) {
                throw new ApplicationException("[fetchServiceabilityByPincodeAndFacility] facilityCode or pincode is NULL", null);
            }
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(lsmServiceabilityBaseUrl + "/pincode" + "/parameter-serviceability");
            if(!"IN".equalsIgnoreCase(lkCountry)) {
                 builder = builder
                        .queryParam("facility_code", facilityCode)
                        .queryParam("country_code", lkCountry);
            } else {
                 builder = builder
                        .queryParam("facility_code", facilityCode)
                        .queryParam("pincode", pincode);
            }

            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            HttpEntity<Object> httpEntity = new HttpEntity(httpHeaders);

            logger.info("[isPinCodeServiceableForFacility -> fetchServiceabilityByPincodeAndFacility] going to call serviceability for facility {} and pincode {} on {}", facilityCode, pincode, builder.toUriString());
            ResponseEntity<String> responseEntity =
                    restTemplate.exchange(builder.toUriString(), HttpMethod.GET, httpEntity, String.class, (Object) null);
            logger.info("[isPinCodeServiceableForFacility -> fetchServiceabilityByPincodeAndFacility] serviceability response for facility {} and pincode {} is {}", facilityCode, pincode, responseEntity);

            if (responseEntity.getStatusCode() == HttpStatus.OK && responseEntity.getBody() != null) {
                ObjectMapper mapper = new ObjectMapper();
                serviceableCourierResponseList = mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                        .readValue(responseEntity.getBody(), new TypeReference<OmsResponse<List<Serviceability>>>() {
                        });

                return serviceableCourierResponseList;
            }
        } catch (HttpServerErrorException.InternalServerError
                | HttpServerErrorException.ServiceUnavailable
                | HttpServerErrorException.GatewayTimeout
                | HttpServerErrorException.BadGateway
                | ObjectOptimisticLockingFailureException
                | ResourceAccessException exception) {
            logger.error("[isPinCodeServiceableForFacility -> fetchServiceabilityByPincodeAndFacility] serviceability check failed for pincode {} and facility {} with exception {}. Going to retry", pincode, facilityCode, exception);
            throw exception;
        } catch(Exception exception) {
            logger.error("[isPinCodeServiceableForFacility -> fetchServiceabilityByPincodeAndFacility] serviceability check failed for pincode {} and facility {} with exception {}. Not Going to retry", pincode, facilityCode, exception);
            throw new ApplicationException("serviceability check failed with exception " + exception, null);
        }
        return null;
    }
}
