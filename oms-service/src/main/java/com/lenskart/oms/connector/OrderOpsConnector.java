package com.lenskart.oms.connector;

import com.lenskart.core.model.Order;
import com.lenskart.core.model.Product;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.dto.ProductListDto;
import com.lenskart.oms.enums.OmsSystem;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.model.PackageProductMapping;
import com.lenskart.oms.exception.OrderOpsConnectorException;
import com.lenskart.oms.model.UwOrder;
import com.lenskart.oms.request.*;
import com.lenskart.oms.request.distributor.JitDoItemStatusUpdateRequest;
import com.lenskart.oms.response.NonWareHouseOrderShipmentUpdateResponse;
import com.lenskart.oms.response.OrderStatusUpdateDetails;
import com.lenskart.oms.response.UwOrderResponse;
import com.lenskart.oms.utils.ObjectHelper;
import io.micrometer.core.annotation.Timed;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

@Component
public class OrderOpsConnector {

    private static final String UPDATE_ORDER_TO_ORDER_OPS = "/backsync/updateOmsOrder";
    private static final String UPDATE_SHIPMENT_STATUS = "/nexs/update/unicomShipmentStatus";
    private static final String BACKSYNC_JUNO_TO_ORDER_OPS = "/backSyncUpdate";

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    @Value("${order.ops.base.url}")
    private String orderOpsBaseUrl;

    @Value("${APPLICATION_NAME:default}")
    String applicationName;

    @Value("${vault.test.key:default_key}")
    String vaultTestValue;

    @Value("${order.ops.consumer.base.url}")
    private String orderOpsConsumerBaseUrl;

    private final RestTemplate restTemplate;

    public OrderOpsConnector(RestTemplateBuilder restTemplateBuilder,
                             @Value("${order.ops.connection.timeout}") Integer connectionTimeout,
                             @Value("${order.ops.read.timeout}") Integer readTimeout) {
        this.restTemplate = restTemplateBuilder
                .setConnectTimeout(connectionTimeout == null ? Duration.ofMillis(ApplicationConstants.DEFAULT_CONNECTION_TIMEOUT) : Duration.ofMillis(connectionTimeout))
                .setReadTimeout(readTimeout == null ? Duration.ofMillis(ApplicationConstants.DEFAULT_READ_TIMEOUT) : Duration.ofMillis(readTimeout))
                .build();
    }

    @PostConstruct
    private void postConstruct() {
        logger.info("Vault configuration for order-sensei. Getting APPLICATION_NAME : {} and test key : {}  from vault." , applicationName, vaultTestValue);
    }

    @Async
    public CompletableFuture<Object> reassignOrderOms(Long incrementId, OmsSystem omsSystem, String wmsOrderCode) {
        AsyncResult<Object> result = new AsyncResult<>("success");
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        HttpEntity<Object> httpEntity = new HttpEntity(httpHeaders);
        String pathUrl = String.format("%s/order/%s/updateOmsSystem?unicomOrderCode=%s&updatedBy=%s&omsSystem=%s", orderOpsBaseUrl, incrementId, wmsOrderCode , ApplicationConstants.OMS_ORDER_SYSTEM, omsSystem);
        logger.info("[OrderOpsConnector -> reassignOrderOms] going to reassign OMS for incrementId {} with URL {}", incrementId, pathUrl);
        ResponseEntity<String> responseEntity = restTemplate.exchange(pathUrl, HttpMethod.PUT, httpEntity, String.class, (Object) null);
        if (responseEntity.getStatusCode() != HttpStatus.OK && responseEntity.getBody() == null) {
            return AsyncResult.forExecutionException(
                    new ApplicationException("error while reassignOrderOmsToOrderOps in order-ops for incrementId: " + incrementId, null)
            ).completable();
        }
        return result.completable();
    }

    @Timed
    public UwOrderResponse getUwOrderDetails(Long incrementId) throws ApplicationException {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        HttpEntity<Object> httpEntity = new HttpEntity(httpHeaders);
        String pathUrl = String.format("/uwOrders/%s", incrementId);
        ResponseEntity<UwOrderResponse> responseEntity = restTemplate.exchange(orderOpsBaseUrl + pathUrl, HttpMethod.GET, httpEntity, UwOrderResponse.class, (Object) null);
        logger.info("[getUwOrderDetails] order {} and response {}", incrementId, responseEntity);
        if (responseEntity.getStatusCode() != HttpStatus.OK && responseEntity.getBody() == null) {
            throw new ApplicationException("error while fetching uwOrder details from order-ops for incrementId: " + incrementId, null);
        }
        return responseEntity.getBody();
    }

    @Timed
    public UwOrderResponse getUwOrderWithItemType(Long incrementId) throws ApplicationException {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        HttpEntity<Object> httpEntity = new HttpEntity(httpHeaders);
        String pathUrl = String.format("/uwOrdersWithItemType/%s", incrementId);

        logger.info("[getUwOrderWithItemType] Calling OrderOps to fetch uwOrder details with Item type for incrementId {}", incrementId);
        ResponseEntity<UwOrderResponse> responseEntity = restTemplate.exchange(orderOpsBaseUrl + pathUrl, HttpMethod.GET, httpEntity, UwOrderResponse.class, (Object) null);
        if (responseEntity.getStatusCode() != HttpStatus.OK && responseEntity.getBody() == null) {
            logger.error("[getUwOrderWithItemType] error while fetching uwOrder details with Item type from order-ops for incrementId {}", incrementId);
            throw new ApplicationException("[getUwOrderWithItemType] error while fetching uwOrder details with Item type from order-ops for incrementId: " + incrementId, null);
        }

        logger.info("[getUwOrderWithItemType] OrderOps call to fetch uwOrder details with Item type for incrementId {} successful with response {}", incrementId, responseEntity.getBody());
        return responseEntity.getBody();
    }

    @Timed
    public ResponseEntity<String> updateOmsOrder(OrderOpsOrderEvent orderOpsOrderEvent) throws ApplicationException {
        ResponseEntity<String> responseEntity = null;
        try {
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            HttpEntity<String> httpEntity = new HttpEntity(orderOpsOrderEvent, httpHeaders);
            logger.info("[updateOmsOrder] Calling OrderOps to update oms order for incrementId {}, eventName {}, URL {}{} with request {}",
                    orderOpsOrderEvent.getOrderId(), orderOpsOrderEvent.getEventName(), orderOpsBaseUrl, UPDATE_ORDER_TO_ORDER_OPS, orderOpsOrderEvent);
            responseEntity = restTemplate.exchange(orderOpsBaseUrl + UPDATE_ORDER_TO_ORDER_OPS, HttpMethod.PUT, httpEntity, String.class, new Object[]{null});
            logger.info("updateOmsOrder - Response : {}", responseEntity);
        } catch (Exception exception) {
            String errorMessage = String.format("error while update OMS order to order-ops for : %s", orderOpsOrderEvent);
            logger.error(errorMessage, exception);
            throw new ApplicationException(errorMessage, null);
        }
        return responseEntity;
    }

    @Timed
    public String updateOrderOpsShipmentStatus(List<OrderOpsUpdateShipmentStatus> orderOpsUpdateShipmentStatus) throws ApplicationException {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        HttpEntity<String> httpEntity = new HttpEntity(orderOpsUpdateShipmentStatus, httpHeaders);
        logger.info("Request URL - {}, Request Method - {}, Request Body - {}", orderOpsBaseUrl + UPDATE_SHIPMENT_STATUS, HttpMethod.POST.name(), httpEntity);
        ResponseEntity<String> responseEntity = restTemplate.exchange(orderOpsBaseUrl + UPDATE_SHIPMENT_STATUS, HttpMethod.POST, httpEntity, String.class, (Object) null);
        logger.info("Response From OrderOps - {}", responseEntity);
        if (responseEntity == null || (responseEntity.getStatusCode() != null && (responseEntity.getStatusCode().is2xxSuccessful() && StringUtils.isNotBlank(responseEntity.getBody()))))
            return responseEntity.getBody();
        else return null;
    }

    @Timed
    public List<Integer> countStoreCreditOrdersByCustomerId(Long customerId) throws ApplicationException {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        HttpEntity<Object> httpEntity = new HttpEntity(httpHeaders);
        String pathUrl = String.format("/customer/countStoreCreditOrder/%s", customerId);

        logger.info("[countStoreCreditOrdersByCustomerId] Calling OrderOps to fetch store credit orders for customerId {}", customerId);
        ResponseEntity<List<Integer>> responseEntity = restTemplate.exchange(orderOpsBaseUrl + pathUrl, HttpMethod.GET, httpEntity, new ParameterizedTypeReference<List<Integer>>() {
        });
        if (responseEntity.getStatusCode() != HttpStatus.OK && responseEntity.getBody() == null) {
            logger.error("[countStoreCreditOrdersByCustomerId] error while fetching store credit orders for customerId {}", customerId);
            throw new ApplicationException("[countStoreCreditOrdersByCustomerId] error while fetching store credit orders for customerId: " + customerId, null);
        }

        logger.info("[countStoreCreditOrdersByCustomerId] OrderOps call to fetch store credit orders for customerId {} successful with response {}", customerId, responseEntity.getBody());
        return responseEntity.getBody();
    }

    @Timed
    public Integer countCustomerBlacklistFlag(Long customerId) throws ApplicationException {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        HttpEntity<Object> httpEntity = new HttpEntity(httpHeaders);
        String pathUrl = String.format("/customer/countBlacklistFlag/%s", customerId);

        logger.info("[countCustomerBlacklistFlag] Calling OrderOps to fetch blacklist customer info for customerId {}", customerId);
        ResponseEntity<Integer> responseEntity = restTemplate.exchange(orderOpsBaseUrl + pathUrl, HttpMethod.GET, httpEntity, Integer.class, (Object) null);
        if (responseEntity.getStatusCode() != HttpStatus.OK && responseEntity.getBody() == null) {
            logger.error("[countCustomerBlacklistFlag] error while fetching customer blacklist info for incrementId {}", customerId);
            throw new ApplicationException("[countCustomerBlacklistFlag] error while fetching customer blacklist info for customerId: " + customerId, null);
        }

        logger.info("[countCustomerBlacklistFlag] OrderOps call to fetch customer blacklist info for customerId {} successful with response {}", customerId, responseEntity.getBody());
        return responseEntity.getBody();
    }

    @Timed
    public Integer countTokaiItemsForOrder(Long orderId) throws ApplicationException {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        HttpEntity<Object> httpEntity = new HttpEntity(httpHeaders);
        String pathUrl = String.format("/countTokaiItemsForOrder/%s", orderId);

        logger.info("[countTokaiItemsForOrder] Calling OrderOps to fetch tokai items for the order {}", orderId);
        ResponseEntity<Integer> responseEntity = restTemplate.exchange(orderOpsBaseUrl + pathUrl, HttpMethod.GET, httpEntity, Integer.class, (Object) null);
        if (responseEntity.getStatusCode() != HttpStatus.OK && responseEntity.getBody() == null) {
            logger.error("[countTokaiItemsForOrder] error while fetching tokai items for the order {}", orderId);
            throw new ApplicationException("[countTokaiItemsForOrder] error while fetching tokai items for the order " + orderId, null);
        }

        logger.info("[countTokaiItemsForOrder] OrderOps call to fetch tokai items for the order {} successful with response {}", orderId, responseEntity.getBody());
        return responseEntity.getBody();
    }

    @Timed
    public Integer countOrderByCustomerIdAndStateAndOffer_3OrFree(Long customerId) throws ApplicationException {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        HttpEntity<Object> httpEntity = new HttpEntity(httpHeaders);
        String pathUrl = String.format("/countOrderByCustomerIdAnd3OrFreeOffer/%s", customerId);

        logger.info("[countOrderByCustomerIdAndStateAndOffer_3OrFree] Calling OrderOps to fetch order count by customer id {} and offer 3OrFree", customerId);
        ResponseEntity<Integer> responseEntity = restTemplate.exchange(orderOpsBaseUrl + pathUrl, HttpMethod.GET, httpEntity, Integer.class, (Object) null);
        if (responseEntity.getStatusCode() != HttpStatus.OK && responseEntity.getBody() == null) {
            logger.error("[countOrderByCustomerIdAndStateAndOffer_3OrFree] error while calling OrderOps to fetch order count by customer id {} and offer 3OrFree", customerId);
            throw new ApplicationException("[countOrderByCustomerIdAndStateAndOffer_3OrFree] error while calling OrderOps to fetch order count by customer id " + customerId + " and offer 3OrFree", null);
        }

        logger.info("[countOrderByCustomerIdAndStateAndOffer_3OrFree] OrderOps call to fetch order count by customer id {} and offer 3OrFree, successful with response {}", customerId, responseEntity.getBody());
        return responseEntity.getBody();
    }

    @Timed
    public ResponseEntity<String> processJunoBackSyncViaOO(Long incrementId, BackSyncRequest backSyncRequest) throws ApplicationException {
        ResponseEntity<String> responseEntity = null;
        try {
            logger.info("processJunoBackSyncViaOO - Request : {}, IncrementId : {}", ObjectHelper.writeNonNullValue(backSyncRequest), incrementId);
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            HttpEntity<String> httpEntity = new HttpEntity(backSyncRequest, httpHeaders);
            String orderOpsBackSyncURL = orderOpsBaseUrl.concat("/").concat(incrementId.toString()).concat(BACKSYNC_JUNO_TO_ORDER_OPS);
            responseEntity = restTemplate.exchange(orderOpsBackSyncURL, HttpMethod.PUT, httpEntity, String.class, new Object[]{null});
            logger.info("processJunoBackSyncViaOO - Response : {}", responseEntity);
        } catch (Exception exception) {
            logger.error("OrderOpsConnector.processJunoBackSyncViaOO", exception);
            throw new ApplicationException(exception.getMessage(), exception);
        }
        return responseEntity;
    }

    @Timed
    public Order findLastOrderByCustomerId(Long customerId) throws ApplicationException {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        HttpEntity<Object> httpEntity = new HttpEntity(httpHeaders);
        String pathUrl = String.format("/findLastOrderByCustomerId/%s", customerId);

        logger.info("[findLastOrderByCustomerId] Calling OrderOps to fetch order last order for customer {}", customerId);
        ResponseEntity<Order> responseEntity = restTemplate.exchange(orderOpsBaseUrl + pathUrl, HttpMethod.GET, httpEntity, Order.class, (Object) null);
        if (responseEntity.getStatusCode() != HttpStatus.OK && responseEntity.getBody() == null) {
            logger.error("[findLastOrderByCustomerId] error while calling OrderOps to fetch last order for customer {}", customerId);
            throw new ApplicationException("[findLastOrderByCustomerId] error while calling OrderOps to fetch last order for customer " + customerId, null);
        }

        logger.info("[findLastOrderByCustomerId] OrderOps call to fetch last order for customer {}, successful with response {}", customerId, responseEntity.getBody());
        return responseEntity.getBody();
    }

    @Timed
    public List<UwOrder> getUwOrderDetailsByIncrementIdList(List<Long> incrementIdList) throws ApplicationException {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        HttpEntity<Object> httpEntity = new HttpEntity(incrementIdList, httpHeaders);
        String pathUrl = "/uwOrdersByIncrementIdList";
        ResponseEntity<List<UwOrder>> responseEntity = restTemplate.exchange(orderOpsBaseUrl + pathUrl, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<List<UwOrder>>() {
        });
        if (responseEntity.getStatusCode() != HttpStatus.OK && responseEntity.getBody() == null) {
            logger.error("[getUwOrderDetailsByIncrementIdList] - error while fetching uwOrder details from order-ops for incrementId list : {}", incrementIdList);
            throw new ApplicationException("[getUwOrderDetailsByIncrementIdList] - error while fetching uwOrder details from order-ops for provided incrementId list", null);
        }
        return responseEntity.getBody();
    }

    @Timed
    public Map<Long, String> verifyAndCancelOnOrderOps(Set<Long> incrementIdList) throws ApplicationException {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        HttpEntity<Object> httpEntity = new HttpEntity(incrementIdList, httpHeaders);
        String pathUrl = "/nexs/oms/verifyAndCancelByIncrementIdList";
        ResponseEntity<Map<Long, String>> responseEntity = restTemplate.exchange(orderOpsBaseUrl + pathUrl, HttpMethod.PUT, httpEntity, new ParameterizedTypeReference<Map<Long,String>>() {
        });
        if (responseEntity.getStatusCode() != HttpStatus.OK && responseEntity.getBody() == null) {
            logger.error("[verifyAndCancelOnOrderOps] - error while cancelling on order-ops for incrementId list : {}", incrementIdList);
            throw new ApplicationException("[verifyAndCancelOnOrderOps] - error while cancelling on order-ops for provided incrementId list", null);
        }
        return responseEntity.getBody();
    }

    public List<PackageProductMapping> getPackageProductList(Long productId) throws ApplicationException {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        HttpEntity<Object> httpEntity = new HttpEntity(httpHeaders);
        String pathUrl = String.format("/packageProductList/%s", productId);
        ResponseEntity<List<PackageProductMapping>> responseEntity = restTemplate.exchange(orderOpsBaseUrl + pathUrl, HttpMethod.GET, httpEntity, new ParameterizedTypeReference<List<PackageProductMapping>>() {});
        if (responseEntity.getStatusCode() != HttpStatus.OK && responseEntity.getBody() == null) {
            logger.error("[getPackageProductList] - error while fetching packageProductList  from order-ops for productId: {}", productId);
            throw new ApplicationException("[getPackageProductList] - error while fetching packageProductList  from order-ops for productId", null);
        }
        return responseEntity.getBody();
    }

    public List<Product> getProductList(ProductListDto productIdList) throws ApplicationException {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        HttpEntity<Object> httpEntity = new HttpEntity(productIdList,httpHeaders);
        String pathUrl = String.format("/productList");
        logger.info("[getProductList] httpEntity {}", httpEntity);
        ResponseEntity<List<Product>> responseEntity = restTemplate.exchange(orderOpsBaseUrl + pathUrl, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<List<Product>>() {});
        if (responseEntity.getStatusCode() != HttpStatus.OK && responseEntity.getBody() == null) {
            logger.error("[getPackageProductList] - error while fetching ProductList  from order-ops for productIdList: {}", productIdList);
            throw new ApplicationException("[getPackageProductList] - error while fetching productList  from order-ops for productId", null);
        }
        return responseEntity.getBody();
    }


    @Timed
    public OrderStatusUpdateDetails getOrderStatus(String unicomOrderCode) throws ApplicationException {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        HttpEntity<Object> httpEntity = new HttpEntity(httpHeaders);
        String pathUrl = String.format("/getOrderStatus/UnicomOrderCode/%s", unicomOrderCode);
        ResponseEntity<OrderStatusUpdateDetails> responseEntity = restTemplate.exchange(orderOpsBaseUrl + pathUrl, HttpMethod.GET, httpEntity, OrderStatusUpdateDetails.class, (Object) null);
        if (responseEntity.getStatusCode() != HttpStatus.OK && responseEntity.getBody() == null) {
            throw new OrderOpsConnectorException(responseEntity.getStatusCode().toString() ,"error while fetching orderStatusUpdateDetails  from order-ops for unicomOrderCode" + unicomOrderCode);
        }
        return responseEntity.getBody();
    }



    public void updateOrderOpsForBarcode(List<OmsOrderOpsLFBarcodeUpdate> omsOrderOpsLFBarcodeUpdateList) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        HttpEntity<Object> httpEntity = new HttpEntity(omsOrderOpsLFBarcodeUpdateList, httpHeaders);
        String pathUrl = "/v1/update/lf/barcode/";
        ResponseEntity<String> responseEntity = restTemplate.exchange(orderOpsBaseUrl + pathUrl, HttpMethod.POST, httpEntity, String.class);
        if (responseEntity.getStatusCode() != HttpStatus.OK && responseEntity.getBody() == null) {
            logger.error("[updateOrderOpsForBarcode] - error while updating local fitting orders barcode on order-ops for unicom order code : {}",
                    omsOrderOpsLFBarcodeUpdateList.get(0).getUnicomOrderCode());
            throw new ApplicationException("error while updating local fitting order barcodes in order-ops for unicom order code" + omsOrderOpsLFBarcodeUpdateList.get(0).getUnicomOrderCode());
        }
    }

    public NonWareHouseOrderShipmentUpdateResponse updateShipmentLevelStatusForNonWarehouseOrder(NonWareHouseOrderShipmentUpdateRequest nonWareHouseOrderShipmentUpdateRequest) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        HttpEntity<Object> httpEntity = new HttpEntity<>(nonWareHouseOrderShipmentUpdateRequest, httpHeaders);
        String pathUrl = "/oms/updateNonWarehouseOrderStatus/";
        ResponseEntity<NonWareHouseOrderShipmentUpdateResponse> responseEntity = restTemplate.exchange(orderOpsBaseUrl + pathUrl, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<NonWareHouseOrderShipmentUpdateResponse>() {
        });
        logger.error("updateShipmentLevelStatusForNonWarehouseOrder Response From OO {} for wnsOrderCode : {}", responseEntity, nonWareHouseOrderShipmentUpdateRequest.getWmsOrderCode());
        if (responseEntity.getStatusCode() != HttpStatus.OK && responseEntity.getBody() == null) {
            logger.error("[updateShipmentLevelStatusForNonWarehouseOrder] - error while updating uwOrder details at order-ops for wnsOrderCode : {}", nonWareHouseOrderShipmentUpdateRequest.getWmsOrderCode());
            throw new ApplicationException("[updateShipmentLevelStatusForNonWarehouseOrder] - error while updating uwOrder details at order-ops for wnsOrderCode", null);
        }
        return responseEntity.getBody();
    }

    public String markDOItemSkipped(JitDoItemStatusUpdateRequest jitDoItemStatusUpdateRequest) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        HttpEntity<Object> httpEntity = new HttpEntity<>(jitDoItemStatusUpdateRequest, httpHeaders);
        String pathUrl = "/jitDo/itemStatusUpdate";
        ResponseEntity<String> responseEntity = restTemplate.exchange(orderOpsConsumerBaseUrl + pathUrl, HttpMethod.POST,httpEntity,  String.class, (Object) null);
        if (responseEntity.getStatusCode() != HttpStatus.OK && responseEntity.getBody() == null) {
            logger.error("[markDOItemSkipped] - error while updating skip details at order-ops for po Id : {}", jitDoItemStatusUpdateRequest.getJitPoId());
            throw new ApplicationException("[markDOItemSkipped] - error while updating skip details at order-ops for po Id", null);
        }
        return responseEntity.getBody();
    }
}
