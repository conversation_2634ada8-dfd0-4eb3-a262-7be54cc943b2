package com.lenskart.oms.connector;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.commons.dto.BaseResponseModel;
import com.lenskart.nexs.ims.request.UpdateAndReleaseStockRequest;
import com.lenskart.nexs.ims.response.OrderItemStockReleaseResponse;
import com.lenskart.nexs.ims.response.StockReleaseResponse;
import com.lenskart.nexs.ims.response.UpdateAndReleaseStockResponse;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.utils.ObjectHelper;
import io.micrometer.core.annotation.Timed;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;

@Component
public class NexsImsConnector {

    private static final String RELEASE_INVENTORY_URL = "/nexs/api/ims/stockInOutAndRelease";

    @CustomLogger
    private Logger logger;

    @Value("${ims.base.url}")
    private String imsBaseUrl;
    private final RestTemplate restTemplate;

    public NexsImsConnector(RestTemplateBuilder restTemplateBuilder,
                            @Value("${nexs.ims.read.timeout}") Integer readTimeout,
                            @Value("${nexs.ims.connection.timeout}") Integer connectionTimeout) {
        this.restTemplate = restTemplateBuilder
                .setConnectTimeout(connectionTimeout == null ? Duration.ofMillis(ApplicationConstants.DEFAULT_CONNECTION_TIMEOUT) : Duration.ofMillis(connectionTimeout))
                .setReadTimeout(readTimeout == null ? Duration.ofMillis(ApplicationConstants.DEFAULT_READ_TIMEOUT) : Duration.ofMillis(readTimeout))
                .build();
    }

    @Timed
    public void releaseStock(UpdateAndReleaseStockRequest updateAndReleaseStockRequest) throws ApplicationException {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> httpEntity = new HttpEntity(updateAndReleaseStockRequest, httpHeaders);
        ResponseEntity<String> responseEntity = restTemplate.exchange(imsBaseUrl + RELEASE_INVENTORY_URL, HttpMethod.POST, httpEntity, String.class, new Object[]{null});
        if (responseEntity.getStatusCode() != HttpStatus.OK && responseEntity.getBody() == null) {
            String errorMessage = String.format("error while releasing inventory in ims for : %s", updateAndReleaseStockRequest);
            logger.error(errorMessage);
            throw new ApplicationException(errorMessage, null);
        }
        BaseResponseModel baseResponseModel = ObjectHelper.readValue(responseEntity.getBody(), BaseResponseModel.class);
        UpdateAndReleaseStockResponse response = ObjectHelper.convertNonNullValue(baseResponseModel.getData(), UpdateAndReleaseStockResponse.class);
        validateReleaseStockResponse(updateAndReleaseStockRequest, response);
    }

    private void validateReleaseStockResponse(UpdateAndReleaseStockRequest updateAndReleaseStockRequest, UpdateAndReleaseStockResponse response) throws ApplicationException {
        if (response == null || CollectionUtils.isEmpty(response.getStockReleaseResponseList())) {
            String errorMessage = String.format("error while releasing inventory in ims for : %s", updateAndReleaseStockRequest);
            logger.error(errorMessage);
            throw new ApplicationException(errorMessage, null);
        }
        for (OrderItemStockReleaseResponse stockReleaseResponse : response.getStockReleaseResponseList()) {
            if (stockReleaseResponse != null && stockReleaseResponse.getStockCheckList() != null && !stockReleaseResponse.getStockCheckList().isEmpty()) {
                for (StockReleaseResponse releaseResponse : stockReleaseResponse.getStockCheckList()) {
                    if (!releaseResponse.isSuccess() && !releaseResponse.getStatusCode().equals(HttpStatus.CREATED.toString())) {
                        throw new ApplicationException("IMS stock release operation failed with response " + response + "Ims update Failed", null);
                    }
                }
            }
        }
    }
}
