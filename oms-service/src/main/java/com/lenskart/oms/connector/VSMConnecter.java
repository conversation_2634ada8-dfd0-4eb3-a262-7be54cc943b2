package com.lenskart.oms.connector;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.wms.request.vsmprecomplete.PreCompleteCheck;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.dto.ShipmentTimelineDto;
import com.lenskart.oms.exception.ApplicationException;
import com.lenskart.oms.request.ShipmentItemUpdate;
import com.lenskart.oms.request.ShipmentUpdateEvent;
import com.lenskart.oms.service.OrderItemService;
import com.lenskart.oms.service.ShipmentService;
import com.lenskart.oms.service.ShipmentTimelineService;
import com.lenskart.vsm.soap.api.markitemfulfillable.Item;
import com.lenskart.vsm.soap.api.markitemfulfillable.MarkItemFulfillable;
import com.lenskart.vsm.soap.api.markitemfulfillable.UwItems;
import com.lenskart.vsm.soap.api.markordercomplete.MarkOrderComplete;
import com.lenskart.vsm.soap.api.markorderdispatched.MarkOrderDispatched;
import com.lenskart.vsm.soap.api.markorderdispatched.OrderId;
import com.lenskart.vsm.soap.api.markorderqc.MarkOrderQc;
import com.lenskart.vsm.soap.api.markorderqc.UwItemIdArr;
import com.lenskart.vsm.soap.api.markorderstockout.MarkOrderStockout;
import com.lenskart.vsm.soap.api.markupdateshippingpackage.MarkUpdateShippingPackage;
import io.micrometer.core.annotation.Timed;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.*;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import java.io.StringWriter;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class VSMConnecter {

    @Value("${vsm.base.url}")
    private String vsmBaseUrl;

    private ShipmentService shipmentService;
    private OrderItemService orderItemService;
    private ShipmentTimelineService shipmentTimelineService;

    private final RestTemplate restClient;

    public VSMConnecter(RestTemplateBuilder restTemplateBuilder,
                        @Value("${vsm.read.timeout}") Integer readTimeout,
                        @Value("${vsm.connection.timeout}") Integer connectionTimeout,
                        OrderItemService orderItemService,
                        ShipmentService shipmentService,
                        ShipmentTimelineService shipmentTimelineService
    ) {
        this.orderItemService = orderItemService;
        this.shipmentService = shipmentService;
        this.shipmentTimelineService = shipmentTimelineService;
        this.restClient = restTemplateBuilder
                .setConnectTimeout(connectionTimeout == null ? Duration.ofMillis(ApplicationConstants.DEFAULT_CONNECTION_TIMEOUT) : Duration.ofMillis(connectionTimeout))
                .setReadTimeout(readTimeout == null ? Duration.ofMillis(ApplicationConstants.DEFAULT_READ_TIMEOUT) : Duration.ofMillis(readTimeout))
                .build();
    }

    private static final String SOAP_ENVELOPE_BODY_PREFIX = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\">\n" +
            "    <soapenv:Body>\n";
    private static final String SOAP_ENVELOPE_BODY_SUFFIX = "</soapenv:Body>\n" +
            "</soapenv:Envelope>";
    private static final String XML_TAG = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>";
    private static final String UW_ITEMS_ARR_TAG = "<uwItems>";
    private static final String UW_ITEMS_ARR_WITH_ARRAY_TYPE = "<uwItems xsi:type=\"SOAP-ENC:Array\" SOAP-ENC:arrayType=\"unnamed_struct_use_soapval[2]\">";
    private static final String UW_ITEM_ID_ARRAY_ARR_TAG = "<UwItemIdArr>";
    private static final String UW_ITEM_ARR_WITH_ARRAY_TYPE = "<UwItemIdArr xsi:type=\"SOAP-ENC:Array\" SOAP-ENC:arrayType=\"unnamed_struct_use_soapval[2]\">";
    private static final String ORDER_ID = "<OrderId>";
    private static final String ORDER_ID_ARRAY = "<OrderId xsi:type=\"SOAP-ENC:Array\" SOAP-ENC:arrayType=\"unnamed_struct_use_soapval[2]\">";


    @CustomLogger
    private static Logger log;

    public String createShippingPackageId(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        try {
            if (!CollectionUtils.isEmpty(shipmentUpdateEvent.getOrderItemList())) {
                String uwItemIds = StringUtils.join(shipmentUpdateEvent.getOrderItemList().stream().map(ShipmentItemUpdate::getOrderItemId).collect(Collectors.toList()), ",");
                MarkUpdateShippingPackage shippingPackageRequest = new MarkUpdateShippingPackage();
                shippingPackageRequest.setShippingPackageId(shipmentUpdateEvent.getEntityId());
                shippingPackageRequest.setUwItems(uwItemIds);
                return makeVsmCallWithRetry(convertRequestXml(shippingPackageRequest, ApplicationConstants.SHIPPING_PACKAGE_REQUEST), ApplicationConstants.SHIPPING_PACKAGE_REQUEST);
            } else
                throw new ApplicationException(ApplicationConstants.SHIPPING_PACKAGE_NOT_UPDATED_ON_VSM + " ->  Empty OrderItems");
        } catch (Exception exception) {
            log.error("VSMConnector.createShippingPackageId - Request : " + shipmentUpdateEvent, exception);
            throw new ApplicationException("VSMConnector.createShippingPackageId", exception);
        }
    }

    public String markItemFulfillable(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        try {
            log.info("[{}, markItemFulfillable] The shipmentUpdate Request is {}",this.getClass().getSimpleName(),
                    shipmentUpdateEvent);
            MarkItemFulfillable markItemFulfillable = new MarkItemFulfillable();
            UwItems uwItems = new UwItems();
            markItemFulfillable.setUwItems(uwItems);
            for (ShipmentItemUpdate lineItem : shipmentUpdateEvent.getOrderItemList()) {
                Item item = new Item();
                item.setIsFulfillable(lineItem.getEntityId());
                item.setUwItemId(String.valueOf(lineItem.getOrderItemId()));
                uwItems.getItem().add(item);
            }
            String result = convertRequestXml(markItemFulfillable, ApplicationConstants.ITEM_FULFILLABLE_REQUEST);
            result = result.replaceFirst(UW_ITEMS_ARR_TAG, UW_ITEMS_ARR_WITH_ARRAY_TYPE);
            return makeVsmCallWithRetry(result, ApplicationConstants.ITEM_FULFILLABLE_REQUEST);
        } catch (Exception exception) {
            log.error("VSMConnector.markItemFulfillable - Request : " + shipmentUpdateEvent, exception);
            throw new ApplicationException("VSMConnector.markItemFulfillable", exception);
        }
    }

    public String markShipmentQc(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        try {
            String wmsOrderCode = shipmentUpdateEvent.getWmsOrderCode();
            MarkOrderQc markOrderQc = new MarkOrderQc();
            UwItemIdArr uwItemIdArr = new UwItemIdArr();
            for (ShipmentItemUpdate orderItems : shipmentUpdateEvent.getOrderItemList()) {
                com.lenskart.vsm.soap.api.markorderqc.Item item = new com.lenskart.vsm.soap.api.markorderqc.Item();
                if ("CUSTOMIZATION_COMPLETE".equalsIgnoreCase(orderItems.getUnicomShipmentStatus()))
                    item.setQcStatus("Pass");
                else
                    item.setQcStatus("Fail");
                item.setQcTime(String.valueOf(orderItems.getEventTime().toInstant().getEpochSecond()));
                item.setUwItemId(orderItems.getOrderItemId().toString());
                uwItemIdArr.getItem().add(item);
            }
            markOrderQc.setUwItemIdArr(uwItemIdArr);
            markOrderQc.setOrderId(wmsOrderCode);
            String result = convertRequestXml(markOrderQc, ApplicationConstants.MARK_ORDER_QC);
            result = result.replaceFirst(UW_ITEM_ID_ARRAY_ARR_TAG, UW_ITEM_ARR_WITH_ARRAY_TYPE);
            return makeVsmCallWithRetry(result, ApplicationConstants.MARK_ORDER_QC);
        } catch (Exception exception) {
            log.error("VSMConnector.markShipmentQc - Request : " + shipmentUpdateEvent, exception);
            throw new ApplicationException("VSMConnector.markShipmentQc", exception);
        }
    }

    public String markShipmentDispatch(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        try {
            MarkOrderDispatched markOrderDispatched = new MarkOrderDispatched();
            OrderId orderIds = new OrderId();
            markOrderDispatched.setOrderId(orderIds);
            com.lenskart.vsm.soap.api.markorderdispatched.Item orderId = new com.lenskart.vsm.soap.api.markorderdispatched.Item();
            orderId.setOrderId(shipmentUpdateEvent.getWmsOrderCode());
            markOrderDispatched.getOrderId().getItem().add(orderId);
            String result = convertRequestXml(markOrderDispatched, ApplicationConstants.MARK_ORDER_DISPATCHED);
            result = result.replaceFirst(ORDER_ID, ORDER_ID_ARRAY);
            return makeVsmCallWithRetry(result, ApplicationConstants.MARK_ORDER_DISPATCHED);
        } catch (Exception exception) {
            log.error("VSMConnector.markShipmentDispatch - Request : " + shipmentUpdateEvent, exception);
            throw new ApplicationException("VSMConnector.markShipmentDispatch", exception);
        }
    }

    public String markShipmentManifest(ShipmentUpdateEvent shipmentUpdateEvent, ShipmentDto shipmentDto, ShipmentTimelineDto shipmentTimelineDto) throws ApplicationException {
        try {
            MarkOrderComplete markOrderComplete = new MarkOrderComplete();
            markOrderComplete.setCourier(shipmentDto.getCourierCode());
            markOrderComplete.setOrderId(shipmentUpdateEvent.getWmsOrderCode());
            markOrderComplete.setTrackingNo(shipmentDto.getAwbNumber());
            markOrderComplete.setManifestNumber(shipmentDto.getManifestNumber());
            markOrderComplete.setDocketNumber(shipmentDto.getAwbNumber());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date dateWithoutTime = sdf.parse(sdf.format(shipmentUpdateEvent.getEventTime()));
            markOrderComplete.setManifestDate(String.valueOf(dateWithoutTime));
            markOrderComplete.setINVDate(String.valueOf(sdf.parse(sdf.format(shipmentTimelineDto.getInvoiceTime()))));
            SimpleDateFormat timeFormat = new SimpleDateFormat("hh:mm:s");
            markOrderComplete.setManifestTime("" + timeFormat.parse(timeFormat.format(shipmentUpdateEvent.getEventTime())));
            markOrderComplete.setINVTime("" + timeFormat.parse(timeFormat.format(shipmentTimelineDto.getInvoiceTime())));
            String result = convertRequestXml(markOrderComplete, ApplicationConstants.MARK_ORDER_COMPLETE);
            return makeVsmCallWithRetry(result, ApplicationConstants.MARK_ORDER_COMPLETE);
        } catch (Exception exception) {
            log.info("ShipmentUpdateEvent - {}, ShipmentDto - {}, ShipmentTimelineDto - {}", shipmentUpdateEvent, shipmentDto, shipmentTimelineDto);
            log.error("VSMConnector.markShipmentManifest", exception);
            throw new ApplicationException("VSMConnector.markShipmentManifest", exception);
        }
    }

    @Timed
    @Retryable(value = Exception.class, maxAttemptsExpression = "${vsm.soap.api.retry.maxAttempts}",
            backoff = @Backoff(delayExpression = "${vsm.soap.api.retry.maxDelay}"))
    public String makeVsmCallWithRetry(String request, String requestType) throws ApplicationException {
        try {
            return makeVsmCall(request, requestType);
        } catch (Exception exception) {
            log.error("VSMConnector.makeVsmCallWithRetry", exception);
            throw new ApplicationException("VSMConnector.makeVsmCallWithRetry", exception);
        }
    }

    private String makeVsmCall(String request, String requestType) throws Exception {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.TEXT_XML);
        HttpEntity<String> httpEntity = new HttpEntity(request, httpHeaders);
        log.info("Request URL - {}, Request Method - {}, Request Body - {}", vsmBaseUrl, HttpMethod.POST.name(), httpEntity);
        ResponseEntity<String> responseEntity = restClient.exchange(vsmBaseUrl, HttpMethod.POST, httpEntity, String.class, (Object) null);
        log.info("Response From VSM - {}", responseEntity);
        if ((responseEntity.getStatusCode().is2xxSuccessful() && StringUtils.isNotBlank(responseEntity.getBody())))
            return responseEntity.getBody();
        else return null;
    }

    public static String convertRequestXml(Object source, String requestType) throws ApplicationException {
        String result;
        StringWriter sw = new StringWriter();
        try {
            JAXBContext context = null;
            if (requestType.equalsIgnoreCase(ApplicationConstants.SHIPPING_PACKAGE_REQUEST))
                context = JAXBContext.newInstance(MarkUpdateShippingPackage.class);
            else if (requestType.equalsIgnoreCase(ApplicationConstants.ITEM_FULFILLABLE_REQUEST))
                context = JAXBContext.newInstance(MarkItemFulfillable.class);
            else if (requestType.equalsIgnoreCase(ApplicationConstants.MARK_ORDER_QC))
                context = JAXBContext.newInstance(MarkOrderQc.class);
            else if (requestType.equalsIgnoreCase(ApplicationConstants.MARK_ORDER_COMPLETE))
                context = JAXBContext.newInstance(MarkOrderComplete.class);
            else if (requestType.equalsIgnoreCase(ApplicationConstants.MARK_ORDER_DISPATCHED))
                context = JAXBContext.newInstance(MarkOrderDispatched.class);
            else if (requestType.equalsIgnoreCase(ApplicationConstants.MARK_ORDER_PRE_COMPLETE))
                context = JAXBContext.newInstance(PreCompleteCheck.class);
            else if (requestType.equalsIgnoreCase(ApplicationConstants.MARK_ORDER_STOCK_OUT)) {
                context = JAXBContext.newInstance(MarkOrderStockout.class);
            }
            Marshaller marshaller = context.createMarshaller();
            marshaller.marshal(source, sw);
            result = sw.toString();
        } catch (JAXBException exception) {
            log.error("VSMConnector.convertRequestXml For RequestType - " + requestType, exception);
            throw new ApplicationException("VSMConnector.convertRequestXml For RequestType - " + requestType, exception);
        }
        result = result.replace(XML_TAG, "");
        result = SOAP_ENVELOPE_BODY_PREFIX + result + SOAP_ENVELOPE_BODY_SUFFIX;
        return result;
    }

    public String markShipmentPicked(ShipmentUpdateEvent shipmentUpdateEvent) throws ApplicationException {
        try{
            List<ShipmentItemUpdate> updatedItemList = new ArrayList<>();
            if (!allItemPickedForShipment(shipmentUpdateEvent.getWmsOrderCode(), updatedItemList)) {
                log.info("[VSMConnector.markShipmentPicked] all items are not PICKED for event {}", shipmentUpdateEvent);
                return null;
            }
            MarkOrderStockout markOrderStockout = new MarkOrderStockout();
            com.lenskart.vsm.soap.api.markorderstockout.UwItemIdArr uwItemIdArr = new com.lenskart.vsm.soap.api.markorderstockout.UwItemIdArr();
            for (ShipmentItemUpdate lineItem : updatedItemList) {
                com.lenskart.vsm.soap.api.markorderstockout.Item item = new com.lenskart.vsm.soap.api.markorderstockout.Item();
                item.setStockoutTime(String.valueOf(lineItem.getEventTime() != null ? lineItem.getEventTime().toInstant().getEpochSecond() : new Date().toInstant().getEpochSecond()));                item.setUwItemId(String.valueOf(lineItem.getOrderItemId()));
                uwItemIdArr.getItem().add(item);
            }
            markOrderStockout.setUwItemIdArr(uwItemIdArr);
            markOrderStockout.setOrderId(shipmentUpdateEvent.getWmsOrderCode());
            String result = convertRequestXml(markOrderStockout, ApplicationConstants.MARK_ORDER_STOCK_OUT);
            result = result.replaceFirst(UW_ITEM_ID_ARRAY_ARR_TAG,UW_ITEM_ARR_WITH_ARRAY_TYPE);
            return makeVsmCallWithRetry(result, ApplicationConstants.MARK_ORDER_DISPATCHED);
        }catch (Exception exception) {
            log.error("VSMConnector.markShipmentPicked - Request : " + shipmentUpdateEvent, exception);
            throw new ApplicationException("VSMConnector.markShipmentPicked", exception);
        }
    }

    private boolean allItemPickedForShipment(String wmsOrderCode, List<ShipmentItemUpdate> updatedItemList) {
        ShipmentDto shipmentDto = shipmentService.findBySearchTerms("wmsOrderCode.eq:" + wmsOrderCode);
        List<ShipmentTimelineDto> timelineDtoList = shipmentTimelineService.findByShipmentId(shipmentDto.getId());
        for (OrderItemDto orderItemDto : shipmentDto.getOrderItems()) {
            log.info("[allItemPickedForShipment] barcode for orderItem {} is {}", orderItemDto.getId(), orderItemDto.getItemBarcode());
            if (orderItemDto != null && StringUtils.isEmpty(orderItemDto.getItemBarcode())) {
                return false;
            }
            for(ShipmentTimelineDto timelineDto : timelineDtoList){
                if(timelineDto.getOrderItemId().equals(orderItemDto.getId())){
                    ShipmentItemUpdate newItem = new ShipmentItemUpdate();
                    newItem.setOrderItemId(orderItemDto.getUwItemId());
                    newItem.setEventTime(timelineDto.getPickedTime());
                    newItem.setEntityId(orderItemDto.getItemBarcode());
                    newItem.setUnicomShipmentStatus("PICKED");
                    updatedItemList.add(newItem);
                }
            }
        }
        return true;
    }
}
