package com.lenskart.oms.connector;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.oms.constants.ApplicationConstants;
import com.lenskart.oms.dto.JunoMarginPriceApiResponse;
import com.lenskart.oms.dto.JunoMarginPriceApiResult;
import com.lenskart.oms.enums.OrderCounterEntity;
import com.lenskart.oms.exception.D365PublishDispatchException;
import com.lenskart.oms.exception.MarginPriceException;
import io.micrometer.core.annotation.Timed;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;

@Component
public class JunoConnector {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;

    @Value("${juno.base.url}")
    private String junoBaseUrl;

    @Value("${juno.access.token}")
    private String junoAccessToken;

    @Value("${juno.margin.price.api:/v2/orders/legal/margin/breakup}")
    private String junoMarginPriceUrl;

    @Value("${juno.session.token}")
    private String junoSessionToken;

    private final RestTemplate restTemplate;

    private static final String VERSION = "/v2";
    private static final String VSM_API_CLIENT = "VSM";
    private static final String SCM_API_CLIENT = "scm";
    private static final String X_AUTH_TOKEN_KEY = "X-Auth-Token";
    private static final String X_API_CLIENT_KEY = "X-Api-Client";
    private static final String X_SESSION_TOKEN = "X-Session-Token";

    public JunoConnector(RestTemplateBuilder restTemplateBuilder,
                         @Value("${juno.rest.timeout}") Integer connectionTimeout
    ) {
        this.restTemplate = restTemplateBuilder
                .setConnectTimeout(connectionTimeout == null ? Duration.ofMillis(ApplicationConstants.DEFAULT_CONNECTION_TIMEOUT) : Duration.ofMillis(connectionTimeout))
                .setReadTimeout(connectionTimeout == null ? Duration.ofMillis(ApplicationConstants.DEFAULT_READ_TIMEOUT) : Duration.ofMillis(connectionTimeout))
                .build();
    }

    @Timed
    public String fetchLatestOrderCounter(OrderCounterEntity operation) {
        try {
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            httpHeaders.add(X_AUTH_TOKEN_KEY, junoAccessToken);
            httpHeaders.add(X_API_CLIENT_KEY, VSM_API_CLIENT);
            httpHeaders.add(X_SESSION_TOKEN, junoSessionToken);
            HttpEntity<String> httpEntity = new HttpEntity(httpHeaders);
            String orderCounterUrl = junoBaseUrl + "/v2/orders/counter-service/" + operation;
            ResponseEntity<String> responseEntity = restTemplate.exchange(orderCounterUrl, HttpMethod.GET, httpEntity, String.class);
            logger.info("[fetchLatestOrderCounter] Response - {} for URL - {}", responseEntity, orderCounterUrl);
            if (responseEntity != null && responseEntity.getStatusCode().is2xxSuccessful()) {
                JsonObject results = new Gson().fromJson(responseEntity.getBody(), JsonObject.class);
                return (results != null && results.has("result")) ? results.get("result").getAsString() : null;
            }
        } catch (Exception e) {
            logger.error("JunoConnector.fetchLatestOrderCounter.Exception For Operation - " + operation, e);
        }
        return null;
    }

    @Timed
    public JunoMarginPriceApiResponse getMarginPriceForB2bOrder(Long incrementId) throws MarginPriceException {
        try {
            logger.info("[JunoServiceImpl : getMarginPriceForB2bOrder ] incrementId: {}",incrementId);
            String url = junoBaseUrl + junoMarginPriceUrl+"?orderId="+incrementId;
            HttpHeaders headers =new HttpHeaders();
            headers.add(X_AUTH_TOKEN_KEY, junoAccessToken);
            headers.add(X_API_CLIENT_KEY, VSM_API_CLIENT);
            headers.add(X_SESSION_TOKEN, junoSessionToken);
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity request = new HttpEntity(headers);
            logger.debug("[JunoConnector : getMarginPriceForB2bOrder ] calling juno api url : {}, request {},",url,request);
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<JunoMarginPriceApiResult> response = restTemplate.exchange(url, HttpMethod.GET, request, JunoMarginPriceApiResult.class);
            logger.info("[JunoConnector : getMarginPriceForB2bOrder ] url {} ,response {}",url,response);
            if (response != null && response.getBody() != null) {
                return response.getBody().getResult();
            } else {
                logger.info("[JunoConnector : getMarginPriceForB2bOrder ] Did not get expected response from Juno for incrementId {} response {}",incrementId,response);
                throw new MarginPriceException(HttpStatus.INTERNAL_SERVER_ERROR.value(), "Margin price is null from juno");
            }
        } catch (Exception e) {
            logger.error("[JunoConnector : getMarginPriceForB2bOrder ] incrementId "+incrementId +" error "+ e.getMessage(),e);
            throw new MarginPriceException(HttpStatus.INTERNAL_SERVER_ERROR.value(), "Margin price is null from juno");
        }
    }
}
