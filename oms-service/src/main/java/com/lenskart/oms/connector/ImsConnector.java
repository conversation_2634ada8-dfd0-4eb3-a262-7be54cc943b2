package com.lenskart.oms.connector;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.commons.dto.BaseResponseModel;
import com.lenskart.nexs.ims.request.UpdateStocksRequestV2;
import com.lenskart.nexs.ims.response.UpdateStocksResponseV2;
import com.lenskart.oms.utils.ObjectHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.apache.logging.log4j.Logger;



@Component
@Slf4j
public class ImsConnector{

    @Value("${nexs.ims.connection.timeout}")
    private int timeout;

    @Value("${ims.base.url}")
    private String imsHost;

    @Value("${ims.stockRequestV2.url}")
    private String imsUpdateBarcodeStatus;

    @Autowired
    private RestTemplate restTemplate;

    public UpdateStocksResponseV2 updateBarcodeStatus(UpdateStocksRequestV2 updateStocksRequestV2) throws Exception {
        String url = imsHost + imsUpdateBarcodeStatus;
        log.info("updateStocksRequestV2 updating barcode  status at IMS for barcode request: " +
                updateStocksRequestV2.toString() + " " + url);

        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> httpEntity = new HttpEntity(updateStocksRequestV2, httpHeaders);
        log.info(" [IMSConnectorImpl: checkAvailableBarcodeLocationToIms], request:{}", updateStocksRequestV2);
        try {
            ResponseEntity<String> responseEntity = restTemplate.exchange(url,
                    HttpMethod.POST, httpEntity, String.class, new Object[]{null});
            log.info(" [IMSConnectorImpl: updateBarcodeStatus], responseEntity:{}", responseEntity);
            if (responseEntity.getStatusCode().is2xxSuccessful() && responseEntity.getBody() != null) {
                BaseResponseModel baseResponseModel = ObjectHelper.getObjectMapper().readValue(responseEntity.getBody(), new TypeReference<BaseResponseModel>() {
                });
                UpdateStocksResponseV2 response = ObjectHelper.getObjectMapper().convertValue(baseResponseModel.getData(), new TypeReference<UpdateStocksResponseV2>() {});
                log.info(" [IMSConnectorImpl: updateBarcodeStatus], UpdateStocksResponseV2:{}", response);
                return response;
            } else {
                log.error("Unable to get details from IMS for request from ims failed ,response is " + responseEntity);
                throw new Exception("Unable to update  details for " + updateStocksRequestV2 +
                        " from IMS for request from ims failed ,response is " + responseEntity
                        + " for request " + updateStocksRequestV2);
            }
        } catch (Exception e) {
            log.error("Error in update barcode request: " + e.getMessage());
            throw new Exception(e.getMessage());
        }
    }

}
