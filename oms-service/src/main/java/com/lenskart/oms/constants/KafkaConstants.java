package com.lenskart.oms.constants;

public final class KafkaConstants {

    private KafkaConstants() {
    }

    public static final String MESSAGE_IDEMPOTENCY_KEY = "message-idempotency-key";
    public static final String EVENT_IDENTIFIER_KEY = "event-identifier-key";
    public static final String OMS_ORDER_EVENT_GROUP_ID = "oms";
    public static final String OMS_ORDER_EVENT_PROCESS_CONSUMER = "oms_order_event_process_consumer";
    public static final String OMS_DISTRIBUTO_ORDER_EVENT_CONSUMER = "oms_distributo_order_event_consumer";
    public static final String OMS_OTC_ORDER_EVENT_CONSUMER = "oms_otc_order_event_consumer";
    public static final String OMS_B2B_ORDER_EVENT_CONSUMER = "oms_b2b_order_event_consumer";

    public static final String WMS_SHIPMENT_EVENT_GROUP_ID = "wms_shipment_backsync";
    public static final String WMS_DO_SHIPMENT_EVENT_GROUP_ID = "wms_do_shipment_backsync";
    public static final String OMS_BACKSYNC_EVENT_GROUP_ID = "oms-backsync";
    public static final String OMS_BACKSYNC_TRACKING_EVENT_GROUP_ID = "oms-backsync-tracking";
    public static final String OMS_OTC_ORDER_EVENTS_PROCESS_TOPIC = "oms-otc-order-events-process-topic";
    public static final String B2B_VIRTUAL_ORDER_EVENT_TOPIC = "oms-b2b-order-process-topic";

}
