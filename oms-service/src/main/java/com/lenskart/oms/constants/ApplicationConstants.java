package com.lenskart.oms.constants;

import org.apache.kafka.common.protocol.types.Field;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public final class ApplicationConstants {

    public static final int ENTITY_VALUE_MAX_LENGTH = 255;

    private ApplicationConstants() {
    }
    public static final String D365_DISPATCH_SHIPMENT_KEY = "D365_PUBLISHED_DISPATCH_SHIPMENT";
    public static final Integer DEFAULT_CONNECTION_TIMEOUT = 3000;
    public static final Integer DEFAULT_READ_TIMEOUT = 3000;
    public static final String CLIENT = "client";
    public static final String USER_ID = "userId";
    public static final String USER_EMAIL = "userEmail";
    public static final String OMS_ORDER_SYSTEM = "OMS";
    public static final String SNAPDEALEYEGLASSES_LESNKART_IN = "<EMAIL>";
    public static final String SNAPDEAL_LENSKART_IN = "<EMAIL>";
    public static final String EASY_REWARDZ_DEALSKART_IN = "<EMAIL>";
    public static final String BENEFITPLUS_DEALSKART_IN = "<EMAIL>";
    public static final String STOREKING_LENSKART_IN = "<EMAIL>";
    public static final String FASHIONARA_LENSKART_IN = "<EMAIL>";
    public static final String FASHIONARA_SUN_LENSKART_IN = "<EMAIL>";
    public static final String PAYU_WITH_JUNGLEE_DEALSKART_IN = "<EMAIL>";
    public static final String PAYTM_LENSKART_IN = "<EMAIL>";
    public static final String ASKMEBAZAAR_LENSKART_IN = "<EMAIL>";
    public static final String FLIPKART_WATCHES_DEALSKART_IN = "<EMAIL>";
    public static final String OFFLINE_APP_LK = "offlineapplk.in";

    public static final String THREE_OR_FREE_FLAG_TRUE = "1";
    public static final String FACILITY_CODE_ONLINE = "0";
    public static final String JJ_STORE_TYPE = "0";

    public static final String SHIPPING_DESTINATION_STORE = "SHIP_TO_STORE";
    public static final String SHIPPING_DESTINATION_CUSTOMER = "SHIP_TO_CUSTOMER";


    public static final String JJ = "jj";

    public static final String JUNO = "juno";
    public static final Integer SUNGLASSES_CLASSIFICATION_ID = 11357;
    public static final Integer EYEFRAME_CLASSIFICATION_ID = 11355;
    public static final Integer ZERO_POWER_CLASSIFICATION_ID = 17775;
    public static final Integer CONTACT_LENS_CLASSIFICATION_ID = 11354;
    public static final Integer CONTACT_LENS_SOLUTION_CLASSIFICATION_ID = 19153;
    public static final String SUNGLASS_CLASSIFICATIONS = "21567";
    public static final String SUNGLASS_AND_SOLUTION_CLASSIFICATIONS = "11357,21567,11355,17775,18585";
    public static final String SHIPPING_ACCESSORIES_CLASSIFICATIONS = "11365,13349,13287,15667,17492,19278,21566";
    public static final Integer TEMP_LEFT_LENS_PID = 19872;
    public static final Integer TEMP_RIGHT_LENS_PID = 19873;

    public static final String FITTING_NOT_REQUIRED = "not_reqd";

    public static final Integer GOLD_MEMBERSHIP_PRODUCT_ID = 128269;

    public static final String VALID_PAYMENT_METHODS = "cashondelivery, paytbyb, payzero, offlinecash, offlinecard, storecredit,giftcard, giftvoucher, franchisecredit, offlinepart, checkmo, ccavenuepay, seamless, payucheckout_shared, citrus, payu_shared, emi_shared, payback, payzippy, payumoney, wallet, mswipe, paytm_cc, pogo, olamoney,purchaseorder,storecard,storecash, storemisc, storemomoe,exchangep,offlinepaytm,offlineairtel, storepaytm,lenskartwallet,storeairtel,storephonepe, paypal,aqualensrazorpay,lkusstripe,juspay,ezetapcard,paylater,marketplace,medibuddy,primer";
    public static final String BLACKLIST_PASS_PAYMENT_METHODS = "payu_shared,ccavenuepay,paytm_cc,paytm,paypal,aqualensrazorpay,lkusstripe,juspay,medibuddy,ezetapcard,primer";
    public static final String BLACKLIST_PRODUCT_ID = "129927,115456";
    public static final String FRANCHISE_PAYMENT_METHODS = "storecard,storecash,storemisc,storemomoe,franchisecredit,offlinemomoe,storepaytm,lenskartwallet,storeairtel,storephonepe";
    public static final String SKIP_PAYMENT_METHOD = "franchisecredit,lenskartwallet,offlineairtel,offlinecard,offlinecash,offlinepart,offlinepaytm";
    public static final String COCO_FOFO_STORES = "COCO_LENSKART_STORE,FOFO_WITH_SC,FOFO_WITHOUT_SC,COCO_JJ_STORE,COCO_INTERNATIONAL";
    public static final String ACCESSORIES = "accessories";
    public static final String STORE_PHONE_PE = "storephonepe";

    public static final String DEFAULT_OMS_USER = "oms";

    public static final String ORDER_META_KEY_CART_ID = "CART_ID";
    public static final String ORDER_META_KEY_MALL = "MALL";
    public static final String ORDER_META_KEY_TOTAL_TAX = "TOTAL_TAX";
    public static final String ORDER_META_KEY_SHIPPING_CHARGE = "SHIPPING_CHARGE";
    public static final String ORDER_META_KEY_SUB_TOTAL = "SUB_TOTAL";
    public static final String ORDER_META_KEY_GRAND_TOTAL = "GRAND_TOTAL";
    public static final String ORDER_META_KEY_GIFT_MESSAGE = "GIFT_MESSAGE";
    public static final String ORDER_META_KEY_GIFT_MESSAGE_FROM = "GIFT_MESSAGE_FROM";
    public static final String ORDER_META_KEY_GIFT_MESSAGE_TO = "GIFT_MESSAGE_TO";
    public static final String ORDER_META_KEY_STORE_TYPE = "STORE_TYPE";
    public static final String ORDER_META_KEY_SALESMAN_NAME = "SALESMAN_NAME";
    public static final String ORDER_META_KEY_SALESMAN_EMAIL = "SALESMAN_EMAIL";
    public static final String ORDER_META_KEY_SALESMAN_PHONE = "SALESMAN_PHONE";
    public static final String ORDER_META_KEY_QUOTE_ID = "QUOTE_ID";
    public static final String ORDER_META_KEY_SALES_CODE = "SALES_CODE";
    public static final String ORDER_META_KEY_PROMISED_SHIP_DATE = "PROMISED_SHIP_DATE";
    public static final String ORDER_META_KEY_PROMISED_DELIVERY_DATE = "PROMISED_DELIVERY_DATE";
    public static final String ORDER_META_KEY_OFFER_3ORFREE = "OFFER_3ORFREE";
    public static final String ORDER_META_KEY_PAYMENT_GATEWAY = "PAYMENT_GATEWAY";
    public static final String ORDER_META_KEY_PARTIAL_PAYMENT = "PARTIAL_PAYMENT";
    public static final String ORDER_META_KEY_TRANSACTION_ID = "TRANSACTION_ID";
    public static final String ORDER_META_KEY_FACILITY_CODE = "FACILITY_CODE";
    public static final String ORDER_META_KEY_CUSTOMER_COMMENTS = "CUSTOMER_COMMENTS";
    public static final String ORDER_META_KEY_PRESCRIPTION_TYPE = "PRESCRIPTION_TYPE";
    public static final String ORDER_META_KEY_PRESCRIPTION_METHOD = "PRESCRIPTION_METHOD";
    public static final String ORDER_META_KEY_JIT_FLAG = "JIT_FLAG";
    public static final String ORDER_META_KEY_LENSKART_DISCOUNT = "LENSKART_DISCOUNT";
    public static final String ORDER_META_KEY_LENSKART_PLUS_DISCOUNT = "LENSKART_PLUS_DISCOUNT";
    public static final String ORDER_META_KEY_GIFT_VOUCHER_DISCOUNT = "GIFT_VOUCHER_DISCOUNT";
    public static final String ORDER_META_KEY_STORE_CREDIT = "STORE_CREDIT";
    public static final String ORDER_META_KEY_GIFT_CARD_DISCOUNT = "GIFT_CARD_DISCOUNT";
    public static final String ORDER_META_KEY_PREPAID_DISCOUNT = "PREPAID_DISCOUNT";
    public static final String ORDER_META_KEY_TOTAL_DISCOUNT = "TOTAL_DISCOUNT";
    public static final String ORDER_META_KEY_CUSTOMER_ID = "CUSTOMER_ID";
    public static final String ORDER_META_KEY_CUSTOMER_NAME = "CUSTOMER_NAME";
    public static final String ORDER_META_KEY_CUSTOMER_EMAIL = "CUSTOMER_EMAIL";
    public static final String ORDER_META_KEY_CUSTOMER_NUMBER = "CUSTOMER_NUMBER";
    public static final String ORDER_META_KEY_JUNO_INITIAL_STATE = "JUNO_INITIAL_STATE";
    public static final String ORDER_META_KEY_JUNO_INITIAL_STATUS = "JUNO_INITIAL_STATUS";
    public static final String ORDER_META_KEY_BULK_ORDER = "BULK_ORDER";
    public static final String ORDER_META_KEY_DUAL_COMPANY_ENABLED = "IS_DUAL_COMPANY_ENABLED";
    public static final String IS_INSURANCE_BENEFIT_ORDER = "is_insurance_benefit_order";
    public static final String IS_INSURANCE_ORDER = "is_insurance_order";
    public static final String INSURANCE_PROVIDER = "insurance_provider";
    public static final String FRAME_BROKEN_GV_FLOW = "frame_broken_gv_flow";
    public static final String ORDER_ITEM_META_KEY_PRODUCT_NAME = "PRODUCT_NAME";
    public static final String ORDER_ITEM_META_KEY_IMAGE_URL = "IMAGE_URL";
    public static final String ORDER_ITEM_META_KEY_LOCAL_FITTING_FACILITY = "LOCAL_FITTING_FACILITY";
    public static final String ORDER_ITEM_META_KEY_VIRTUAL_FACILITY_CODE = "VIRTUAL_FACILITY_CODE";
    public static final String ORDER_ITEM_META_KEY_CONTACT_LENS_ID = "CONTACT_LENS_ID";
    public static final String ORDER_ITEM_META_KEY_CART_ITEM_ID = "CART_ITEM_ID";
    public static final String ORDER_ITEM_META_KEY_HUB_CODE = "HUB_CODE";
    public static final String ORDER_ITEM_META_KEY_HUB_COUNTRY = "HUB_COUNTRY";
    public static final String ORDER_ITEM_META_KEY_PROCESSING_TYPE = "PROCESSING_TYPE";
    public static final String ORDER_ITEM_META_KEY_3ORFREE = "3ORFREE";
    public static final String ORDER_ITEM_META_KEY_IS_POWER = "IS_POWER";
    public static final String OREDER_ITEM_META_KEY_CENTRAL_FACILITY_CODE = "CENTRAL_FACILITY_CODE";
    public static final String ORDER_TYPE_RIMLESS = "rimless";
    public static final String ORDER_ITEM_META_KEY_RIMLESS_BAR_PID = "RIMLESS_BAR_PID";
    public static final String SHIPMENT_META_KEY_IS_SHIPMENT_SHIPPED = "IS_SHIPMENT_SHIPPED";
    public static final String SHIPMENT_META_KEY_SHIPPING_TIME = "SHIPPING_TIME";
    public static final String SHIPMENT_META_KEY_COMPLETE_TIME = "COMPLETE_TIME";
    public static final String SHIPMENT_META_KEY_UPDATED_CRM = "UPDATED_CRM";
    public static final String SHIPMENT_META_KEY_UPDATED_MAGENTO = "UPDATED_MAGENTO";
    public static final String SHIPMENT_META_KEY_IS_SHIPMENT_PACKED = "IS_SHIPMENT_PACKED";
    public static final String SHIPMENT_META_KEY_DELIVERED_TIME = "DELIVERED_TIME";
    public static final String SHIPMENT_META_KEY_PICK_TIME = "PICK_TIME";
    public static final String SHIPMENT_META_KEY_PICKED_BY = "PICKED_BY";
    public static final String SHIPMENT_META_KEY_AWB_ASSIGNED_AT = "AWB_ASSIGNED_AT";
    public static final String SHIPMENT_META_KEY_FOLLOWED_UP_DATE = "FOLLOWED_UP_DATE";
    public static final String SHIPMENT_META_KEY_LAST_FOLLOWED_UP_DATE = "LAST_FOLLOWED_UP_DATE";
    public static final String SHIPMENT_META_KEY_MANIFEST_TIME = "MANIFEST_TIME";
    public static final String SHIPMENT_META_KEY_DO_PO_NUMBER = "PO_NUMBER";

    public static final String CHATBOT = "chatbot";
    public static final String OTC = "OTC";
    public static final String PAYMENT_METHOD_JUSPAY = "juspay";
    public static final String PAYMENT_METHOD_PRIMER = "primer";
    public static final String PAY_LATER = "paylater";
    public static final String STORE_TYPE_HEC = "HEC";
    public static final String PAYMENT_METHOD_CASHONDELIVERY = "cashondelivery";
    public static final String PAYMENT_TRANSACTION_ID = "paymentTransactionId";
    public static final String PAYMENT_PAY_U_ID = "payuId";
    public static final String TOTAL_PREPAID = "totalPrepaid";
    public static final String TOTAL_COD = "totalCod";
    public static final String PARTIAL_PAYMENT = "partialPayment";
    public static final String PAYMENT_GATEWAY = "paymentGateway";
    public static final int JUNO_STORE_ID = 4;
    public static final Long LENS_PACKAGE_PID = Long.valueOf(-1);
    public static final Long LENS_COATING_PID = Long.valueOf(0);
    public static final String JJ_NAV_CHANNELS = "JJBULK,JJOTC,JJONLINEB2B,JOHNJACOBS,JJOnlineDTC,JJB2B,JJDTC";
    public static final ArrayList<String> OWN_STORE_OR_JJ_FRANCHISE = new ArrayList<>(Arrays.asList("COCO_LENSKART_STORE", "HEC", "COCO_JJ_STORE", "COCO_INTERNATIONAL"));
    public static final Long HOME_EYE_CHECK_UP_PROGRAM = Long.valueOf(47552);
    public static final String AND_OPERATION = "___";
    public static final String VSM_PARTIAL = "VSM_PARTIAL";

    public static final String SHIPPING_PACKAGE_REQUEST = "shippingPackageRequest";
    public static final String ITEM_FULFILLABLE_REQUEST = "itemFulfillableRequest";
    public static final String IS_FULLFILLABLE = "IS_FULLFILLABLE";
    public static final String MARK_ORDER_QC = "markOrderQc";
    public static final String MARK_ORDER_COMPLETE = "markOrderComplete";
    public static final String MARK_ORDER_DISPATCHED = "markOrderDisPatched";
    public static final String MARK_ORDER_PRE_COMPLETE = "preCompleteCheck";
    public static final String MARK_ORDER_STOCK_OUT = "markOrderStockOut";
    public static final String SHIPPING_PACKAGE_NOT_UPDATED_ON_VSM = "Validation error: ShippingPackageId not Updated On Vsm";
    public static final String ON_HOLD_REASON_FRAUD_ORDER = "FRAUD_ORDER";
    public static final String JUNO_CALLBACK_CHECKPOINT = "DISPATCHED";
    public static final String DATE_PATTERN = "yyyy-MM-dd HH:mm:ss";
    public static final String SUCCESS = "SUCCESS";
    public static final String FAILED = "FAILED";
    public static final String STATIC_CALL_ME_FOR_POWER = "Call Me";
    public static final String UW_ITEM_ID_UPDATE_EVENT_TYPE_CANCELLATION = "cancellation";
    public static final String UW_ITEM_ID_UPDATE_EVENT_TYPE_CREATE_ORDER = "order_create";

    public static final String POWER_TYPE_SINGLE_VISION = "SINGLE_VISION";
    public static final String POWER_TYPE_BIFOCAL = "BIFOCAL";
    public static final String POWER_TYPE_PROGRESSIVE = "PROGRESSIVE";
    public static final String POWER_TYPE_ZERO_POWER = "ZERO_POWER";
    public static final String POWER_TYPE_NORMAL = "NORMAL";
    public static final String CONTACT_LENS_TYPE_POWER = "CONTACT_LENS";
    public static final String POWER_TYPE_LENS_ONLY_ZERO_POWER = "LENS_ONLY_ZERO_POWER";

    public static final String LENS_PACKAGE_TYPE_SUNGLASS = "sunglasses";
    public static final String LENS_PACKAGE_TYPE_NORMAL = "normal";
    public static final String LENS_PACKAGE_TYPE_BIFOCAL = "bifocal";
    public static final String LENS_PACKAGE_TYPE_PROGRESSIVE = "progressive";
    public static final String LENS_PACKAGE_TYPE_ZERO_POWER = "zero_power";
    public static final String IMS_OTC_INVOICED_OPERATION =  "OTC_INVOICED";
    public static final String IMS_OTC_DISPATCHED_OPERATION =  "OTC_DISPATCHED";
    public static final String IMS_OTC_PICKED_OPERATION = "OTC_ALLOCATED";
    public static final String IMS_OTC_LOYALTY_DISPATCHED_BARCODE_CREATION = "LOYALTY_OTC_DISPATCHED";

    public static final String UAE_FACILITY_CODE = "AE";
    public static final String LKAE = "LKAE";
    public static final String ORDER_OPS_STATUS_PROCESSING = "processing";
    public static final String LOYALTY = "LOYALTY";
    public static final Integer LOYALTY_CLASSIFICATION = 21566;
    public static final Integer OPTIMA_DEFAULT_CONNECTION_TIMEOUT = 3000;
    public static final Integer OPTIMA_DEFAULT_READ_TIMEOUT = 9000;
    public static final String DISTRIBUTOR_ORDER_SOURCE_NAME = "Mail";
    public static final String DISTRIBUTOR_ORDER = "DISTRIBUTOR_ORDER";
    public static final String DEFAULT_SHELF = "DEFAULT";
    public static final String OD_CLIENT_ORG = "owndays";
    public static final String ALMADALLAH_INVOICE = "ALMADALLAH_INVOICE";

    public interface CSV_ERRORS {
        public static final String DUPLICATE_PRODUCT = "ProductId is repeated.";
        public static final String UNFULFILLABLE_PRODUCT = " pieces. Inventory not available.";
        public static final String NEGATIVE_QTY = "Product quantity cannot be negative.";
        public static final String NEGATIVE_PRICE = "Product price cannot be negative.";
        public static final String MAX_QTY_EXCEED = "Total product count cannot exceed ";
        public static final String UNSUPPORTED_ITEM_TYPE = "This product item type is not supported.";
        public static final String INTERNAL_ERROR = "Unable to find product details.";
    }

    public interface CHILD_ENTITIES {
        String ORDER_ITEM_META_DATA = "orderItemMetaData";
        String ORDER_ITEM_PRICE = "orderItemPrice";
        String ITEM_POWER = "itemPower";
        String ORDER_ITEMS = "orderItems";
        String SHIPMENT_META_DATA = "shipmentMetaData";
    }

}
