package com.lenskart.oms.constants;

public final class ControllerConstants {

    private ControllerConstants() {
    }

    public static final String BASE_URL_V1 = "/oms/api/v1";
    public static final String ORDER = "/order";
    public static final String SHIPMENT = "/shipment";
    public static final String ORDER_ITEM = "/orderItem";
    public static final String ORDER_BACK_SYNC = "/orderBackSync";
    public static final String ON_HOLD_MASTER = "/onHoldMaster";
    public static final String SHIPMENT_TIMELINE = "/shipmentTimeline";
    public static final String OUTBOX_EVENT = "/outboxEvent";
    public static final String REASSIGN_ORDER_OMS = "/{wmsOrderCode}/reassignOrderOms";
    public static final String GET_NAV_CHANNEL = "/getNavChannel";
    public static final String BACK_SYNC_ACK = "/ack";
    public static final String TRIGGER_PENDING_ORDERS_FOR_PROCESSING = "/triggerPendingOrderForProcessing";
    public static final String PUBLISH_ORDER_EVENT = "/publishOrderEvent";

    public static final String CUSTOMER = "/customer";
    public static final String SAVE_CUSTOMER = "/saveWithAddress";
    public static final String TOGGLE_CUSTOMER = "toggle/{customerId}";

    public static final String CANCEL_ORDER_OMS = "/cancel/{incrementId}";
    public static final String TRIGGER_HANDLE_READY_FOR_WH_STUCK_ORDER = "/triggerHandleReadyForWhStuckOrder";
    public static final String TRIGGER_HANDLE_READY_FOR_WH_SHIPMENT = "/triggerHandleReadyForWhShipment";
    public static final String ORDER_SHIPMENT_TIMELINE_DETAILS = "shipmentDetails/{incrementId}";

    public static final String D365_SALE_ORDER = "/d365SaleOrder";

    public interface DISTRIBUTOR_ORDERS {
        String CONTROLLER_ENDPOINT = "/distributorOrder";
        String ORDER_UPLOAD = "/upload";
        String ORDER_CREATE = "/create/{refDoOrderId}";
        String ORDER_APPROVE = "/approve/{refDoOrderId}";
        String ORDER_REJECT = "/reject/{refDoOrderId}";
        String ORDER_DETAILS = "/details/{incrementId}";
        String CREATE_JIT_AS_DO = "/create/jitAsDo";
    }

    public interface DISTRIBUTOR_RETURN_ORDER {
        String CONTROLLER_ENDPOINT = "/distributorReturnOrder";
        String PRODUCT_DETAILS_WITH_BARCODE = "/productDetails/{barcode}";
        String RETURN_ORDER = "/returnOrder";
        String UPDATE_STATUS = "/updateStatus/{orderItemId}";
    }
    public static final String MARK_OTC_SHIPMENT_INVOICE = "/markOtcShipmentInvoice";
}
