package com.lenskart.oms.request.distributor.orderReturn;

import com.lenskart.oms.enums.QcStatus;
import com.lenskart.oms.enums.ReturnType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@ToString
public class ReturnOrderRequest {
    private String scannedBarcode;
    private String returnFacility;
    private QcStatus qcStatus;
    private String qcFailReason;
    private String returnReason;
    private ReturnType returnType;
    private String userName;
}
