package com.lenskart.oms.request.distributor;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class InventoryAvailabilityCountRR {

    @NotEmpty
    private List<InventoryItemCountRR> items;

    @NotBlank
    private String facility;

    private String legalOwner = "LKIN";

    private String requestedBy;

}