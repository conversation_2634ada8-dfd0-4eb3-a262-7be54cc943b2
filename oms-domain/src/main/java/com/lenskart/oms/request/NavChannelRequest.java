package com.lenskart.oms.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;

@Getter
@Setter
@ToString
public class NavChannelRequest {
    @NotEmpty(message = "productDeliveryType can not be empty")
    private String productDeliveryType;
    private String facilityCode;
    @NotEmpty(message = "storeType can not be empty")
    private String storeType;
    @NotEmpty(message = "storeId can not be null")
    private Integer storeId;
    @NotEmpty(message = "isBulkOrder can not be null")
    private Boolean isBulkOrder;
    private Boolean isDistributorOrder = false;
    private Boolean getIsDistributorJitOrder = false;
    private String clientOrg;
}
