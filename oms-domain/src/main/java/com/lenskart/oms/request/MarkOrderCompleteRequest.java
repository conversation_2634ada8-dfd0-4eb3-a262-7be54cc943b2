package com.lenskart.oms.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class MarkOrderCompleteRequest {
    @JsonProperty("increment_id")
    private String incrementId;

    @JsonProperty("order_id")
    private String orderId;

    @JsonProperty("shipping_package_id")
    private String shippingPackageId;
    @JsonProperty("courier")
    private String courier;

    @JsonProperty("docket_number")
    private String docketNumber;

    @JsonProperty("inv_date")
    private String invDate;

    @JsonProperty("inv_time")
    private String invTime;

    @JsonProperty("manifest_date")
    private String manifestDate;

    @JsonProperty("manifest_time")
    private String manifestTime;

    @JsonProperty("manifest_number")
    private String manifestNumber;
}
