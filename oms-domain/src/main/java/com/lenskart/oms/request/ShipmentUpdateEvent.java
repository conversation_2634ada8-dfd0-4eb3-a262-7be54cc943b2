package com.lenskart.oms.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.lenskart.oms.enums.ShipmentEvent;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ShipmentUpdateEvent {

    @NotNull(message = "shipmentEvent cant be null")
    private ShipmentEvent shipmentEvent;
    private String wmsOrderCode;
    private String entityId;
    private String courierCode;
    private Date eventTime;
    private List<ShipmentItemUpdate> orderItemList;

}
