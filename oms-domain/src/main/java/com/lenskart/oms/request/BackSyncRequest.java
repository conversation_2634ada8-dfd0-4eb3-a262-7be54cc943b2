package com.lenskart.oms.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class BackSyncRequest {

    String checkPoint;
    List<Integer> uwItemIdList;
    String invoiceUrl;
    String invoiceDate;

}
