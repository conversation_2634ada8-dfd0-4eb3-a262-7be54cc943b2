package com.lenskart.oms.request.distributor;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class JitDoItemStatusUpdateRequest {
    @NotNull(message = "Po Number is null")
    Long jitPoId;
    @NotNull(message = "fitting Id is null")
    Long fittingId;
    @NotNull(message = "Item status is null")
    String doItemStatus;
    @NotNull(message = "Event name is null")
    String eventName;
    String reason;
}
