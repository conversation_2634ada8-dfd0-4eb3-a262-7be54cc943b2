package com.lenskart.oms.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class PowerWisePidLineItemRequest {
    private String itemType;
    private Long magentoItemId;
    private Long parentLineItemId;
    private Long productId;
    private String coatingOId;
    private String packageName;
    private Integer quantity;
    private String powerType;
    private String type;
    private String patientName;
    private String notes;
    private int isPower;
    private String prescriptionUrl;
    private Boolean isLocalFittingOrder;
    private LineItemEyePowerRequest leftEyePower;
    private LineItemEyePowerRequest rightEyePower;
    private LineItemRelationShipRequest relationship;
}
