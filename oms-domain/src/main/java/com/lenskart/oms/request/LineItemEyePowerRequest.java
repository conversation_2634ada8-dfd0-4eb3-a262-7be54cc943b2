package com.lenskart.oms.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class LineItemEyePowerRequest {
    private String sku;
    private String sph;
    private String cyl;
    private String axis;
    private String ap;
    private String pd;
    private String lensHeight;
    private String lensWidth;
    private String effectiveDia;
    private String nearPD;
    private String edgeDistance;
    private String topDistance;
    private String bottomDistance;
    private String tint;
    private String color;
    private String baseCurve;
    private Integer boxQty;
    private String spvd;
}
