package com.lenskart.oms.request;

import com.lenskart.oms.enums.OrderStatus;
import com.lenskart.oms.enums.OrderSubStatus;
import lombok.*;

import java.util.List;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class OrderReconciliationRequest {
    private List<Long> incrementIdList;
    private OrderStatus orderStatus;
    private OrderSubStatus orderSubStatus;
    private Boolean isFullSync = false;
}
