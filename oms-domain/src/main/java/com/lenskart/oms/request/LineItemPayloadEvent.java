package com.lenskart.oms.request;

import com.lenskart.oms.dto.OrderItemPowerDto;
import com.lenskart.oms.enums.ItemType;
import lombok.*;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class LineItemPayloadEvent {

    @NotNull
    private Long lineItemId;

    private Long parentLineItemId;

    @NotNull
    private Integer sku;

    private String oid;

    @NotNull
    private ItemType itemType;

    @NotNull
    private Integer quantity;

    private String productName;

    private String powerType = "normal";

    private String type;

    private String patientName;

    private String notes;

    private OrderItemPowerDto left;

    private OrderItemPowerDto right;

    private int isPower;

    private String prescriptionUrl;

    private Date updatedAt;

    private String source;

    private String salesCode;

    private String productDeliveryType;

    private Boolean shipToStoreRequired = false;

    private Boolean isLocalFittingRequired = false;

    private String localFittingFacility;

    private String virtualFacilityCode;

    private Integer contactLensId;

    private Double itemTotal;

    private String relationType;

    private String relationName;

    private String relationTelephone;

    private String relationPhoneCode;

    private String relationGender;

    private String relationYearOfBirth;

    private boolean isLineItemPropertiesNull = true;

    private boolean isRelationShipNull = true;

    private boolean isPowerFollowUpOrder = false;
}
