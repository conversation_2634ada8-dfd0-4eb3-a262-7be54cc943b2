package com.lenskart.oms.request.distributor;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class InventoryItemCountRR {

    @NotBlank
    private Integer productId;

    @NotBlank
    private Integer requestedQty;

    private Long totalAvailableQty;

    private Long fulfillableQty;

    private Long unFulfillableQty;

    public InventoryItemCountRR(Integer productId, Integer requestedQty){
        this.productId = productId;
        this.requestedQty = requestedQty;
    }

}
