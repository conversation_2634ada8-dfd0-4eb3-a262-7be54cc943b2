package com.lenskart.oms.request;


import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

@Getter
@Setter
@ToString
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class ShipmentItemUpdate {

    @EqualsAndHashCode.Include
    private Long orderItemId;
    private String entityId;
    private Date eventTime;
    private String fullfillableType;
    private String unicomShipmentStatus;
    private String orderOpsShipmentState;
    private String orderOpsShipmentStatus;
}
