package com.lenskart.oms.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class Item {
    public String barcode;
    public String product_id;
    public String putaway_code;
    public String error;
    public boolean success;
    public String state;
    public Object expiry_date;
    @JsonProperty("ims_operation")
    private String imsOperation;
    @JsonProperty("order_item_code")
    private Long orderItemCode;
}