package com.lenskart.oms.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.util.Date;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class OrderEvent {

    @JsonProperty("order_id")
    private String orderId;

    @JsonProperty("client_id")
    private String clientId;

    @JsonProperty("request_data")
    private String requestData;

    @JsonProperty("request_type")
    private String requestType;

    @JsonProperty("requested_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date requestedAt;

    @JsonProperty("lk_country")
    private String lkCountry;

    @JsonProperty("nav_channel")
    private String navChannel;

    @JsonProperty("is_oms_order")
    private Boolean isOmsOrder;

    @JsonProperty("cancellation_sub_type")
    private String cancellationSubType;
}
