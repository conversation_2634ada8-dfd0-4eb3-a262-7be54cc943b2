package com.lenskart.oms.request;

import com.lenskart.oms.dto.OrderDto;
import com.lenskart.oms.dto.ShipmentDto;
import com.lenskart.oms.enums.OrderEventType;
import lombok.*;

import java.util.List;
import java.util.Map;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class OmsOrderEvent {
    private Long incrementId;
    private OrderEventType eventType;
    private OrderDto orderDto;
    private String clientId;
    private List<ShipmentDto> shipmentDtoList;
    private CancelOrderRequest cancelOrderRequest;
    private List<LineItemPayloadEvent> lineItemPayloadEventList;
    private String prescriptionType = "NORMAL";
    private boolean needToPublishWhReady = false;
    private Map<Long, String> shipmentLegalOwnerMap;
    private Map<Long,String> shipmentHubCodeMap;
}
