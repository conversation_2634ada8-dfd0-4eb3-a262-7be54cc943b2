package com.lenskart.oms.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.util.Date;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class PowerReceivedEvent {
    @JsonProperty("order_id")
    private String orderId;
    @JsonProperty("client_id")
    private String clientId;
    @JsonProperty("request_data")
    private String requestData;
    @JsonProperty("request_type")
    private String requestType;
    @JsonProperty("requested_at")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date requestedAt;
}
