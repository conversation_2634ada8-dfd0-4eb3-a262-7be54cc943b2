package com.lenskart.oms.request;

import lombok.*;

import java.util.List;

@Getter
@Setter
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NonWareHouseOrderShipmentUpdateRequest {
    private String wmsOrderCode;
    private String shippingPackageId;
    private String facilityCode;
    private String unicomShipmentStatus;
    private String shipmentState;
    private String shipmentStatus;
    private String unicomSyncStatus;
    private List<NonWareHouseOrderItem> items;
    private String invoiceUrl;
}
