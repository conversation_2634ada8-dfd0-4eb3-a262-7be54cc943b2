package com.lenskart.oms.request;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateInvoiceForLocalFitting {
    @JsonProperty("po_num")
    public String poNum;
    public String vendor_invoice_number;
    public String invoice_date;
    public String facility_code;
    public String created_by;
    public String legal_owner;
    public String grn_code;
    public List<Item> items;
}
