package com.lenskart.oms.request.distributor;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DistributorOrderJITRequest {
   private List<DistributorJitLineItems> lineItemsList;
   private String poNumber;
   private String userName;
   private String customerCode;
   private String facilityCode;
}
