package com.lenskart.oms.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class OmsOrderOpsLFBarcodeUpdate {
    private String unicomOrderCode;
    private String frameBarcode;
    private String leftLensBarcode;
    private String rightLensBarcode;
}
