package com.lenskart.oms.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@ToString
public class D365PublishDispatchRequest {
    private String eventTime;
    private String facilityCode;
    private Long orderId;
    private String legalEntity;
    private String navChannel;
    private String shippingPackageId;
    private String source;
    private String entityType;
    private List<Long> uwItemIds;
}
