package com.lenskart.oms.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.oms.dto.OrderItemDto;
import lombok.*;

import javax.validation.constraints.NotNull;
import java.util.List;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class UpdatePowerRequest {

    @NotNull
    @JsonProperty("order_id")
    private Integer orderId;

    @JsonProperty("line_items")
    private List<OrderItemDto> orderItemList;
}
