package com.lenskart.oms.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class InvoiceMailItem {
    private String facebookUrl;
    private String instaUrl;
    private String twitterUrl;
    private String tiktokUrl;
    private String sunglassesUrl;
    private String visitNearByUrl;
    private String emailUrl;
    private String callUs;
    private String supportUrl;
    private String fromUrl;
}
