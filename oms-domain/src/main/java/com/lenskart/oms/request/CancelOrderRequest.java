package com.lenskart.oms.request;

import com.lenskart.oms.dto.OrderItemDto;
import com.lenskart.oms.enums.CancellationType;
import com.lenskart.oms.enums.LKCountry;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class CancelOrderRequest {
    private String paymentMethod;
    private Integer reasonId;
    private String reasonDetail;
    private BankDetails bankDetail;
    private String source;
    private String initiatedBy;
    private String unicomCode;
    private CancellationType cancellationType;
    private String cancellationSubType;
    private List<OrderItemDto> orderItems;
    private boolean IsWalletRefundEligible;
    private boolean cancelRefund;
    private LKCountry lkCountry;
    private String csOrderId;
    private Integer magentoItemId;
}
