package com.lenskart.oms.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class Serviceability {

    private String id;
    private String pincode;
    private String courier_code;
    private String country_code;
    private Boolean is_enabled;
    private Boolean is_reverse_pickup;
    private Integer priority;
    private String description;
    private Boolean is_prepaid;
    private Boolean is_cod;
    private Integer amount_limit;
    private Integer delivery_eta;
    private Integer delivery_eta_buffer;
    private Boolean is_express;
    private Integer express_delivery_eta;
    private Integer express_delivery_eta_buffer;
    private String facility_code;
    private String routingCode;
    private Boolean is_liquid;
}
