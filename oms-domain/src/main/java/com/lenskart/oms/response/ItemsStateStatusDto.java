package com.lenskart.oms.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

public class ItemsStateStatusDto {

    @JsonProperty("itemId")
    private Long itemId;

    @JsonProperty("status")
    private String status;

    @JsonProperty("state")
    private String state;

    @JsonIgnore
    @JsonProperty("uwItemId")
    private Integer uwItemId;

    @JsonProperty("oldStatus")
    private String oldStatus;

    @JsonProperty("oldState")
    private String oldState;
}
