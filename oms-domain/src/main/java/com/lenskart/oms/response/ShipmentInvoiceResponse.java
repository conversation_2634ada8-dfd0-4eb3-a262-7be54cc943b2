package com.lenskart.oms.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.lenskart.oms.dto.ShipmentDto;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ShipmentInvoiceResponse extends ShipmentDto {
    private String documentNo;
    private Double totalPrice;
    private String documentLink;
    private String shippingPackageId;
}
