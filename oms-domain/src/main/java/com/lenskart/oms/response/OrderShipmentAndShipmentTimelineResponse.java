package com.lenskart.oms.response;

import java.math.BigInteger;
import java.util.Date;

import com.lenskart.oms.enums.OrderStatus;
import com.lenskart.oms.enums.OrderSubStatus;
import com.lenskart.oms.enums.ShipmentStatus;
import com.lenskart.oms.enums.ShipmentSubStatus;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class OrderShipmentAndShipmentTimelineResponse {

    private BigInteger junoOrderId;
    private OrderStatus orderStatus;
    private OrderSubStatus orderSubStatus;
    private String wmsOrderCode;
    private String wmsShippingPackageId;
    private String shipmentType;
    private String shipmentSubType;
    private ShipmentStatus shipmentStatus;
    private ShipmentSubStatus shipmentSubStatus;
    private Date invoiceTime;
    private Date manifestTime;
    private Date dispatchTime;
    private Date deliveredTime;
    private Date orderCreatedAt;
    private Date shipmentTimeLineCreatedAt;
}
