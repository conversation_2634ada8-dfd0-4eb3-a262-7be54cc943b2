package com.lenskart.oms.response;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class OmsResponse<T> implements Serializable {
    private static final long serialVersionUID = 3899837650484970219L;
    private String errorCode;
    private String errorMessage;
    private T result;
}