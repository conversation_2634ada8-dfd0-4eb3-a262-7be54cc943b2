package com.lenskart.oms.response;

import com.lenskart.nexs.commons.codes.BaseResponseCodesMethods;
import lombok.Getter;

@Getter
public enum OmsSuccessResponseCodes implements BaseResponseCodesMethods {

    SUCCESSFULLY_OMS_REASSIGNED(1005, "Order OMS Successfully Reassigned To OrderOps"),
    SUCCESSFULLY_WMS_BACK_SYNC_REGISTERED(1006, "Backsync update has been registered successfully"),
    SUCCESSFULLY_ORDER_BACK_SYNC_REGISTERED(1007, "Order back sync acknowledgement successful"),
    SUCCESSFULLY_ATTACHED_BARCODE(1008,"barcode attached successfully"),
    SUCCESSFULLY_MARK_LF_DISPATCH_REGISTERED(1008,"LF Order dispatch request acknowledgement successfully");


    private final int code;
    private final String message;

    private OmsSuccessResponseCodes(int code, String message) {
        this.code = code;
        this.message = message;
    }
}