package com.lenskart.oms.response;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class AssignPowerDetails {
    private Long magentoItemId;
    private Boolean isPowerFollowUpRequired;
    private String powerFollowUpType;
    private Long rightPowerWisePID;
    private Long rightLensShellId;
    private String rightLensIndex;
    private Long leftPowerWisePID;
    private Long leftLensShellId;
    private String leftLensIndex;
    private Long frameShellId;
    private String coatingOid;
    private String packageName;
}
