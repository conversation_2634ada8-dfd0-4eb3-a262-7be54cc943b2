package com.lenskart.oms.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.lenskart.oms.dto.DistributorCustomerDetailsDto;
import com.lenskart.oms.request.distributor.CsvErrors;
import com.lenskart.oms.request.distributor.DistributorCsvLineItems;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DoUploadOrderResponse {

    private Long referenceOrderId;
    private List<DistributorCsvLineItems> pidListing;
    private DistributorCustomerDetailsDto customer;
    private String facilityCode;
    private Double doTotalPrice;
    private List<CsvErrors> errors = new ArrayList<>();

}
