package com.lenskart.oms.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderStatusUpdateDetails {

    @JsonProperty("increment_id")
    private Integer incrementId;
    @JsonProperty("unicom_order_code")
    private String unicomOrderCode;

    @JsonProperty("status")
    private String status;
    @JsonProperty("state")
    private String state;
    @JsonProperty("source")
    private String source;
    @JsonProperty("shipment_package_id")
    private String shipmentPackageId;
    @JsonProperty("tracking_no")
    private String trackingNo;

    @JsonProperty("carrier_code")
    private String carrierCode;

    @JsonProperty("partial_cancellation_flag")
    private Boolean partialFlag;

    @JsonProperty("magentoItemsList")
    private List<ItemsStateStatusDto> itemsStateStatusDto;
}