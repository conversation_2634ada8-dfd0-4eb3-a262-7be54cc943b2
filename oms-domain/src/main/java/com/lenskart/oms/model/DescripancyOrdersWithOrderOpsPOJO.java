package com.lenskart.oms.model;

import lombok.*;

import java.util.Date;

@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class DescripancyOrdersWithOrderOpsPOJO {
    private Integer incrementId;
    private Long uwItemId;
    private String orderOpsUnicomShipmentStatus;
    private String orderOpsShipmentState;
    private String orderOpsShipmentStatus;
    private String senseiOrderStatus;
    private String senseiShipmentStatus;
    private String senseiOrderItemStatus;
    private Date orderOpsCreatedAt;
    private String unicomSynStatus;
}
