package com.lenskart.oms.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

@Getter
@Setter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class UwOrder {
    private Integer uwItemId;
    private String unicomOrderCode;
    private Integer incrementId;
    private String shipmentState;
    private String shipmentStatus;
    private String unicomShipmentStatus;
    private Date createdAt;
    private Integer productId;
    private String itemType;
    private Long magentoItemId;
    private String unicomSynStatus;
    private String barcode;
}
