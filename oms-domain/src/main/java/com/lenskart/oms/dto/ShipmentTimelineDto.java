package com.lenskart.oms.dto;

import com.lenskart.nexs.commons.dto.BaseDto;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

@Getter
@Setter
@ToString(callSuper = true)
public class ShipmentTimelineDto extends BaseDto {

    private Long orderId;
    private Long shipmentId;
    private Long orderItemId;
    private Date fulfilledTime;
    private Date assignedTime;
    private Date pickedTime;
    private Date qcCompleteTime;
    private Date qcFailTime;
    private Date packedTime;
    private Date invoiceTime;
    private Date manifestTime;
    private Date dispatchTime;
    private Date deliveredTime;
}
