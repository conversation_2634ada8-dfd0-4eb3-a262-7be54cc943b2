package com.lenskart.oms.dto;

import com.lenskart.core.model.Product;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class ProductItemDetailDTO implements Serializable {

    private Product product;
    private Map<String, Object> powerDetails;
    private double itemPrice;
}
