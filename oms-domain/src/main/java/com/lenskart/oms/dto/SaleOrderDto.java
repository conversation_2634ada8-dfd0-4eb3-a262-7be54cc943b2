package com.lenskart.oms.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class SaleOrderDto {
    @JsonProperty("IsUpdateSalesOrder")
    private int isUpdateSalesOrder;
    @JsonProperty("SalesOrderNumber")
    private String salesOrderNumber;
    @JsonProperty("RefInvoiceNo")
    private String refInvoiceNo;
    @JsonProperty("OrgInvoiceNo")
    private String orgInvoiceNo;
    @JsonProperty("Stores")
    private String stores;
    @JsonProperty("SalesChannel")
    private String salesChannel;
    @JsonProperty("SubChannel")
    private String subChannel;
    @JsonProperty("CustomerAccount")
    private String customerAccount;
    @JsonProperty("CustomerGroup")
    private String customerGroup;
    @JsonProperty("FirstName")
    private String firstName;
    @JsonProperty("MiddleName")
    private String middleName;
    @JsonProperty("LastName")
    private String lastName;
    @JsonProperty("Gender")
    private String gender;
    @JsonProperty("InvoiceAccount")
    private String invoiceAccount;
    @JsonProperty("InventLocationId")
    private String inventLocationId;
    @JsonProperty("CurrencyCode")
    private String currencyCode;
    @JsonProperty("OrderType")
    private String orderType;
    @JsonProperty("ExportReason")
    private String exportReason;
    @JsonProperty("IsWithTax")
    private String isWithTax;
    @JsonProperty("TCSGroup")
    private String tCSGroup;
    @JsonProperty("TDSGroup")
    private String tDSGroup;
    @JsonProperty("ModeOfPayment")
    private String modeOfPayment;
    @JsonProperty("DeliveryName")
    private String deliveryName;
    @JsonProperty("AddressStreet")
    private String addressStreet;
    @JsonProperty("AddressCity")
    private String addressCity;
    @JsonProperty("AddressState")
    private String addressState;
    @JsonProperty("AddressZipCode")
    private String addressZipCode;
    @JsonProperty("AddressCountryRegionId")
    private String addressCountryRegionId;
    @JsonProperty("ContactPersonId")
    private String contactPersonId;
    @JsonProperty("Email")
    private String email;
    @JsonProperty("Phone")
    private String phone;
    @JsonProperty("OrderCreationDate")
    private String orderCreationDate;
    @JsonProperty("MethodOfPayment")
    private String methodOfPayment;
    @JsonProperty("CourierCode")
    private String courierCode;
    @JsonProperty("LegalEntity")
    private String LegalEntity;
    @JsonProperty("TermsOfPayment")
    private String termsOfPayment;
    @JsonProperty("ModeOfDelivery")
    private String modeOfDelivery;
    @JsonProperty("WebOrderNo")
    private String webOrderNo;
    @JsonProperty("SoLines")
    private List<SaleOrderItemDto> soLines;
    @JsonProperty("MiscChargesLines")
    private List<String> miscChargesLines;
    @JsonProperty("MiscChargesHeader")
    private List<String> miscChargesHeader;
    @JsonProperty("RMANumber")
    private String rmaNumber;
    @JsonProperty("isIntegrated")
    private Integer isIntegrated;
    @JsonProperty("Fulfillment_WH")
    private String fulfillmentWH;
}
