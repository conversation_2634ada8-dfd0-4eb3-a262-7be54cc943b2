package com.lenskart.oms.dto;

import lombok.Data;

@Data
public class PackageProductPriceDetails {
    private Double grandTotal;
    private Double discount;
    private Double rewardPoints;
    private Double taxCollected;
    private Double shippingCharges;
    private Double scDiscount;
    private Double gvDiscount;
    private Double implicitDiscounts;
    private Double walletDiscount;
    private Double walletPlusDiscount;
    private Double exchangeDiscount;
    private Double couponDiscount;
    private Double itemTotalAfterDiscount;
    private Double prePaidDiscount;
    private Double fcDiscount;
}
