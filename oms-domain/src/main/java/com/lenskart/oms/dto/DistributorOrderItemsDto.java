package com.lenskart.oms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.lenskart.nexs.commons.dto.BaseDto;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class DistributorOrderItemsDto extends BaseDto {

    private Long orderId;
    private Integer productId;
    private String productName;
    private String productType;
    private Integer quantity;
    private Double price;

}
