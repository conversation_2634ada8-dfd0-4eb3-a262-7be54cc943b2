package com.lenskart.oms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.lenskart.nexs.commons.dto.BaseDto;
import com.lenskart.oms.enums.AddressType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Objects;

@Getter
@Setter
@ToString(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class DistributorCustomerAddressDetailsDto extends BaseDto {
    private Long customerDetailsId;
    private AddressType addressType;
    private String addressLine1;
    private String addressLine2;
    private String city;
    private String country;
    private Integer pincode;
    private String state;
    private String phone;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof DistributorCustomerAddressDetailsDto)) return false;
        DistributorCustomerAddressDetailsDto that = (DistributorCustomerAddressDetailsDto) o;
        return addressType == that.addressType && addressLine1.equals(that.addressLine1) && addressLine2.equals(that.addressLine2) && city.equals(that.city) && country.equals(that.country) && pincode.equals(that.pincode) && state.equals(that.state) && phone.equals(that.phone);
    }

    @Override
    public int hashCode() {
        return Objects.hash(addressType, addressLine1, addressLine2, city, country, pincode, state, phone);
    }
}
