package com.lenskart.oms.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class SaleOrderItemDto {
    @JsonProperty("LineNumber")
    private Long lineNumber;
    @JsonProperty("ItemNumber")
    private String itemNumber;
    @JsonProperty("QtyOrdered")
    private Integer qtyOrdered;
    @JsonProperty("SalesPrice")
    private String salesPrice;
    @JsonProperty("LineDiscountAmount")
    private String lineDiscountAmount;
    @JsonProperty("DeliveryType")
    private String deliveryType;
    @JsonProperty("SourcingVendor")
    private String sourcingVendor;
    @JsonProperty("DeliveryModeCode")
    private String deliveryModeCode;
    @JsonProperty("InventLocationId")
    private String inventLocationId;
    @JsonProperty("InventSiteId")
    private String inventSiteId;
    @JsonProperty("ConfirmedReceiptDate")
    private String confirmedReceiptDate;
    @JsonProperty("ConfirmedShipDate")
    private String confirmedShipDate;

    @JsonProperty("Units")
    private String units;
    @JsonProperty("CostCenter")
    private String costCenter;
    @JsonProperty("Stores")
    private String stores;
    @JsonProperty("SalesChannel")
    private String salesChannel;
    @JsonProperty("SubChannel")
    private String subChannel;
    @JsonProperty("PartnerType")
    private String partnerType;
    @JsonProperty("ItemClassification")
    private String itemClassification;
    @JsonProperty("Brand")
    private String brand;
    @JsonProperty("Employee")
    private String employee;
    @JsonProperty("MagentoItemId")
    private Integer magentoItemId;
    @JsonProperty("PurchPrice")
    private String purchPrice;
    @JsonProperty("HSNCode")
    private String hsnCode;
    @JsonProperty("SalesOrderItemCode")
    private String salesOrderItemCode;
    @JsonProperty("TaxRateType")
    private String taxRateType;
    @JsonProperty("ItemSalesTaxGrp")
    private String itemSalesTaxGrp;
    @JsonProperty("SalesTaxGrp")
    private String salesTaxGrp;
    @JsonProperty("ItemTemplateName")
    private String itemTemplateName;
    @JsonProperty("SACCode")
    private String sacCode;
    @JsonProperty("SalesPool")
    private String salesPool;
    @JsonProperty("ReturnedOrderNo")
    private String returnedOrderNo;
    @JsonProperty("OriginalSaleOrderNo")
    private Integer originalSaleOrderNo;
    @JsonProperty("OriginalSaleOrderLineNo")
    private Integer originalSaleOrderLineNo;
    @JsonProperty("ReturnCostPrice")
    private Double returnCostPrice;
    @JsonProperty("LK_Refrence_WH")
    private String lkReferenceWh;
    @JsonProperty("Barcode")
    private String barcode;
    @JsonProperty("LkPurchasePrice")
    private Double lkPurchasePrice;
    @JsonProperty("Exempt")
    private String exempt;
}
