package com.lenskart.oms.dto;

import com.lenskart.nexs.commons.dto.BaseDto;
import com.lenskart.oms.enums.ShipmentSubStatus;
import com.lenskart.oms.enums.ShipmentStatus;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@ToString(callSuper = true)
public class ShipmentDto extends BaseDto {

    private String wmsOrderCode;
    private String wmsShippingPackageId;
    private ShipmentStatus shipmentStatus;
    private ShipmentSubStatus shipmentSubStatus;
    private String facility;
    private String legalOwner;
    private String legalOwnerCountry;
    private String shipmentType;
    private String shipmentSubType;
    private String courierCode;
    private String awbNumber;
    private String invoiceNumber;
    private String manifestNumber;
    private Date actualShipDate;
    private Date actualDeliveryDate;
    private Date expectedShipDate;
    private Date expectedDeliveryDate;
    private OrderAddressDto shippingAddress = new OrderAddressDto();
    private OrderAddressDto billingAddress = new OrderAddressDto();
    private List<OrderItemDto> orderItems = new ArrayList<>();
    private List<ShipmentMetaDataDto> shipmentMetaData = new ArrayList<>();

}
