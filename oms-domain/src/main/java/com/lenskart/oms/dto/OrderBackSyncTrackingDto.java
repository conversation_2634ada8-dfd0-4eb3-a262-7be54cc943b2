package com.lenskart.oms.dto;

import com.lenskart.nexs.commons.dto.BaseDto;
import com.lenskart.oms.enums.BackSyncEntityType;
import com.lenskart.oms.enums.BackSyncEventName;
import com.lenskart.oms.enums.BackSyncEventStatus;
import com.lenskart.oms.enums.BackSyncSystem;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
public class OrderBackSyncTrackingDto extends BaseDto {

    private BackSyncEventName eventName;
    private BackSyncEntityType entityType;
    private String entityId;
    private BackSyncEventStatus eventStatus;
    private BackSyncSystem backSyncSystem;
    private String message;
}
