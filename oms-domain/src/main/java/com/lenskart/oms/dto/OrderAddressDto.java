package com.lenskart.oms.dto;

import com.lenskart.nexs.commons.dto.BaseDto;
import com.lenskart.oms.enums.AddressType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
public class OrderAddressDto extends BaseDto {

    private AddressType addressType;
    private String street;
    private String region;
    private String city;
    private String country;
    private String postcode;
    private String stateCode;
    private String countryCode;
    private String firstName;
    private String lastName;
    private String email;
    private String telephone;
    private String fax;
}
