package com.lenskart.oms.dto;

import com.lenskart.nexs.commons.dto.BaseDto;
import com.lenskart.oms.enums.OrderStatus;
import com.lenskart.oms.enums.OrderSubStatus;
import com.lenskart.oms.enums.OrderSubType;
import com.lenskart.oms.enums.OrderType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@ToString(callSuper = true)
public class OrderDto extends BaseDto {

    private Long incrementId;
    private Long junoOrderId;
    private OrderStatus orderStatus;
    private OrderSubStatus orderSubStatus;
    private Date orderDate;
    private String lkCountry;
    private String currencyCode;
    private Long customerId;
    private String merchantId;
    private String orderSource;
    private Long storeId;
    private Boolean exchangeFlag;
    private Long exchangeItemId;
    private Boolean paymentCaptured;
    private String paymentMethod;
    private Boolean isOnHold;
    private Long onHoldReasonId;
    private OrderSubType orderSubType;
    private OrderType orderType;
    private List<OrderItemDto> orderItems = new ArrayList<>();
    private List<OrderMetaDataDto> orderMetaData = new ArrayList<>();
}
