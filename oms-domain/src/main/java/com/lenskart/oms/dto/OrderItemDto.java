package com.lenskart.oms.dto;

import com.lenskart.nexs.commons.dto.BaseDto;
import com.lenskart.oms.enums.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@ToString(callSuper = true)
public class OrderItemDto extends BaseDto {

    private Long shipmentId;
    private Long orderId;
    private Long magentoItemId;
    private Long parentMagentoItemId;
    private OrderItemStatus itemStatus;
    private OrderItemSubStatus itemSubStatus;
    private Long productId;
    private String itemBarcode;
    private Long fittingId;
    private ItemType itemType;
    private FittingType fittingType;
    private FulfillmentType fulfillmentType;
    private Long b2bReferenceItemId;
    private Channel channel;
    private NavChannel navChannel;
    private ProductDeliveryType productDeliveryType;
    private String shippingDestinationType;
    private DeliveryPriority deliveryPriority;
    private String omaStatus;
    private String jitStatus;
    private Date promisedShipDate;
    private Date promisedDeliveryDate;
    private String deliveryStoreId;
    private String deliveryZipCode;
    private String deliveryCountry;
    private Long uwItemId;
    private OrderItemPowerDto itemPower = new OrderItemPowerDto();
    private OrderItemPricesDto orderItemPrice = new OrderItemPricesDto();

    private List<OrderItemMetaDataDto> orderItemMetaData = new ArrayList<>();
}
