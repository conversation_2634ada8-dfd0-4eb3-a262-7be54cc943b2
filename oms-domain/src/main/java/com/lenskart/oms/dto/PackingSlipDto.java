package com.lenskart.oms.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class PackingSlipDto {
    @JsonProperty("SalesId")
    private String salesId;

    @JsonProperty("PackingSlipId")
    private String packingSlipId;

    @JsonProperty("LegalEntity")
    private String legalEntity;

    @JsonProperty("PackingSlipDate")
    private String packingSlipDate;

    @JsonProperty("SalesInvoiceNo")
    private String salesInvoiceNo;

    @JsonProperty("TrackingNo")
    private String trackingNo;

    @JsonProperty("Description")
    private String description;

    @JsonProperty("SalesLineList")
    private List<PackingSlipItemDto> salesLineList;
}
