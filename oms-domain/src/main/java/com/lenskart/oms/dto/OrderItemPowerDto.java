package com.lenskart.oms.dto;

import com.lenskart.nexs.commons.dto.BaseDto;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
public class OrderItemPowerDto extends BaseDto {

    private String powerType;
    private Long productId;
    private Long shellId;
    private String sph;
    private String cyl;
    private String axis;
    private String ap;
    private String pd;
    private String lensHeight;
    private String lensWidth;
    private String effectiveDia;
    private String nearPD;
    private String edgeDistance;
    private String topDistance;
    private String bottomDistance;
    private String coatingOid;
    private String coatingName;
    private String lensIndex;
    private String lensPackageType;
    private String packageName;
    private String lensPackage;
    private String webPackage;
    private Double packagePrice;
    private String patientName;
    private String patientComments;
    private String prescriptionUrl;
    private String thickness;
    private String tint;
    private String color;
    private String baseCurve;
    private String spvd;
    private Integer boxQty;
}
