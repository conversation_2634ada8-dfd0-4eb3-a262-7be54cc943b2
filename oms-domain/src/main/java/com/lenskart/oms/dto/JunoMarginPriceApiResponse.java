package com.lenskart.oms.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.ArrayList;

@Setter
@Getter
@ToString
public class JunoMarginPriceApiResponse{
    public int orderId;
    public ArrayList<Item> items;
    public String message;
    public int statusCode;

    @Setter
    @Getter
    @ToString
    public static class Item {
        public int itemId;
        public int productId;
        public int qty;
        public MarginPrice marginPrice;
        public TotalMarginPrice totalMarginPrice;
        public ArrayList<Option> options;
    }

    @Setter
    @Getter
    @ToString
    public static class MarginPrice {
        public Double price;
        public String countryCode;
        public String currency;
    }

    @Setter
    @Getter
    @ToString
    public static class Option {
        public String type;
        public MarginPrice marginPrice;
    }

    @Setter
    @Getter
    @ToString
    public static class TotalMarginPrice {
        public Double price;
        public String countryCode;
        public String currency;
    }
}