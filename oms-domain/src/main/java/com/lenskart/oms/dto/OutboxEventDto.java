package com.lenskart.oms.dto;

import com.lenskart.nexs.commons.dto.BaseDto;
import com.lenskart.oms.enums.OutboxEventAggregateType;
import com.lenskart.oms.enums.OutboxEventStatus;
import com.lenskart.oms.enums.OutboxEventType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * DTO for Outbox Event
 */
@Getter
@Setter
@ToString(callSuper = true)
public class OutboxEventDto extends BaseDto {

    private String aggregateId;
    private OutboxEventAggregateType aggregateType;
    private OutboxEventType eventType;
    private OutboxEventStatus eventStatus;
    private String topicName;
    private String partitionKey;
    private String eventPayload;
    private String eventHeaders;
    private Integer retryCount;
    private Integer maxRetryCount;
    private Date scheduledAt;
    private Date processedAt;
    private String errorMessage;
    private String idempotencyKey;

    /**
     * Check if the event can be retried
     */
    public boolean canRetry() {
        return retryCount < maxRetryCount && 
               (eventStatus == OutboxEventStatus.PENDING || eventStatus == OutboxEventStatus.FAILED);
    }

    /**
     * Check if event is ready for processing
     */
    public boolean isReadyForProcessing() {
        return (eventStatus == OutboxEventStatus.PENDING || eventStatus == OutboxEventStatus.FAILED) &&
               scheduledAt != null && scheduledAt.before(new Date());
    }
}
