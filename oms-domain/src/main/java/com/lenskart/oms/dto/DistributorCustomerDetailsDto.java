package com.lenskart.oms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.lenskart.nexs.commons.dto.BaseDto;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@ToString(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class DistributorCustomerDetailsDto extends BaseDto {
    private String name;
    private String code;
    private String email;
    private String pan;
    private String tin;
    private String gstin;
    private String mobile;
    private String fax;
    private String websiteUrl;
    private Double centralSaleTax;
    private Double serviceTax;
    private boolean customerEnabled;
    private boolean taxExempted;
    private boolean registeredDealer;
    private boolean dualCompanyRetail;
    private boolean providesCForm;
    private List<DistributorCustomerAddressDetailsDto> customerAddressDetails = new ArrayList<>();
}
