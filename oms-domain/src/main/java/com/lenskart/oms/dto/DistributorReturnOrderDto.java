package com.lenskart.oms.dto;

import com.lenskart.nexs.commons.dto.BaseDto;
import com.lenskart.oms.enums.*;
import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class DistributorReturnOrderDto extends BaseDto {
    private ReturnType returnType;
    private String scannedBarcode;
    private Long itemId;
    private String wmsOrderCode;
    private QcStatus qcStatus;
    private String qcFailReason;
    private String returnReason;
    private DistributorReturnOrderStatus status;
    private String returnFacility;
    private String putawayCode;
}
