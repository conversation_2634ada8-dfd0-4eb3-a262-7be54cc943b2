package com.lenskart.oms.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.lenskart.nexs.commons.dto.BaseDto;
import com.lenskart.oms.enums.DoStatus;
import com.lenskart.oms.enums.DoType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class DistributorOrdersDto extends BaseDto {

    private String incrementId;
    private String junoOrderId;
    private String poNumber;
    private DistributorCustomerDetailsDto customer;
    private String facility;
    private DoStatus status;
    private String cancellationReason;
    private DoType doType;
    private List<DistributorOrderItemsDto> orderItems;
}
