package com.lenskart.oms.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class PackingSlipItemDto {
    @JsonProperty("SalesId")
    private String salesId;
    @JsonProperty("ItemNumber")
    private String itemNumber;
    @JsonProperty("LineNumber")
    private Long lineNumber;
    @JsonProperty("Quantity")
    private String quantity;
    @JsonProperty("BatchId")
    private String batchId;
    @JsonProperty("SerialId")
    private String serialId;
}
