package com.lenskart.oms.dto;

import lombok.Data;

import java.util.List;

@Data
public class B2bItemPriceDetailsResponseDto {
    String facility_code;
    Integer order_id;
    List<B2bItemPriceDetails> items;

    @Override
    public String toString() {
        return "B2bItemDetailsResponse{" +
                "facility_code='" + facility_code + '\'' +
                ", order_id=" + order_id +
                ", items=" + items +
                '}';
    }
}

