package com.lenskart.oms.dto;

import com.lenskart.nexs.commons.dto.BaseDto;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
public class OrderItemPricesDto extends BaseDto {

    private Double couponDiscount;
    private Double exchangeDiscount;
    private Double fcDiscount;
    private Double gvDiscount;
    private Double implicitDiscount;
    private Double lenskartPlusDiscount;
    private Double lenskartDiscount;
    private Double prepaidDiscount;
    private Double scDiscount;
    private Double walletDiscount;
    private Double walletPlusDiscount;
    private Double itemTotalAfterDiscount;
    private Double itemGrandTotal;
    private Double shippingCharges;
    private Double taxCollected;
    private Double giftCardDiscount;
    private Double itemTotal;
    private Double insuranceBenefitDiscount;
}
