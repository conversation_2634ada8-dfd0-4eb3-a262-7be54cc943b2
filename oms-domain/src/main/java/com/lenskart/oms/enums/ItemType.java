package com.lenskart.oms.enums;

import java.util.HashSet;
import java.util.Set;

public enum ItemType {
    EYEFRAME,
    PACKAGE,
    LEFT_LENS,
    RIGHT_LENS,
    SUNGLASS,
    COATING,
    READING_EYEGLASS,
    LENS_ONLY,
    CONTACT_LENS,
    CONTACT_LENS_SOLUTION,
    PRESCRIPTION_LENS,
    ADD_ON,
    FREE_GIFT,
    SHIPPING_ACCESSORIES,
    SERVICES,
    NON_POWERWISE_CONTACT_LENS,
    SUNGLASSES,
    NON_POWER_READING,
    ACCESSORIES_REVENUE,
    FREEBIE_BARCODE,
    COUPON_OFFERS,
    LOY<PERSON>TY_SERVICES,
    ACCESSORIES,
    HEARING_AID,
    ZEROPOWER,
    SMART_GLASSES,
    LENS_CLEANING_SOLUTION,
    CASES,
    GIFT_VOUCHERS,
    CASES_CONTACT_LENS,
    SPECTACLES,
    CORRECTIVE_SPECTACLES,
    <PERSON>LIP,
    CLIP_ON,
    <PERSON><PERSON>IP_METAL,
    CO<PERSON>UM<PERSON>LE<PERSON>,
    <PERSON><PERSON>R<PERSON>,
    HTO_SERVICES,
    NOTEBOOK,
    <PERSON>LVE<PERSON>,
    <PERSON>WIMMING_GOGGLES;

    public static Set<String> getLensTypesToSkip() {
        Set<String> getLensTypesToSkip = new HashSet<>();

        getLensTypesToSkip.add(ItemType.PACKAGE.name());
        getLensTypesToSkip.add(ItemType.COATING.name());

        return getLensTypesToSkip;
    }

    public static Set<String> getFrameItemTypes() {
        Set<String> getFrameTypesToFindPId = new HashSet<>();

        getFrameTypesToFindPId.add(ItemType.EYEFRAME.name());
        getFrameTypesToFindPId.add(ItemType.SUNGLASS.name());
        getFrameTypesToFindPId.add(ItemType.SUNGLASSES.name());
        getFrameTypesToFindPId.add(ItemType.SMART_GLASSES.name());
        return getFrameTypesToFindPId;
    }
}
