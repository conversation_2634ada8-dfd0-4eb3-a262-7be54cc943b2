package com.lenskart.oms.enums;

public enum PaymentMethod {
    STORECREDIT("storecredit"),
    SC("storecredit"),
    PURCHASEORDER("purchaseorder"),
    CASHONDELIVERY("cashondelivery"),
    LKWALLET("lenskartwallet"),
    FRANCHISECREDIT("franchisecredit"),
    PAYU("payu_shared"),
    CITRUS("citrus"),
    SEAMLESS("seamless"),
    CCAVENUE("ccavenue"),
    MOBIKWIK("mobikwik"),
    PAYTBYB("paytbyb"),
    GIFTVOUCHER("giftvoucher"),
    GIFTCARD("giftcard"),
    EXCHANGEP("exchangep"),
    SOURCE("source");

    private final String value;

    PaymentMethod(String value){
        this.value = value;
    }

    public String getValue(){
        return value;
    }
}
