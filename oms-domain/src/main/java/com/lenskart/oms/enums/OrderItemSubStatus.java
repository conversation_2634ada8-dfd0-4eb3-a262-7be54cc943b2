package com.lenskart.oms.enums;

public enum OrderItemSubStatus {
    PROCESSING_PENDING,
    OMS_REASSIGNED,
    CREATED,
    FULLFILLABLE,
    PROCESSING,
    PICKED,
    QC_DONE,
    INVOICED,
    <PERSON><PERSON>KED,
    READY_TO_SHIP,
    D<PERSON><PERSON>TCHED,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    ORDER_NOT_CONFIRMED,
    PREDELIVERY_REFUND,
    PROCESSING_NOT_IN_STOCK,
    COMPLETE_LOST_BY_COURIER,
    PROCESSING_BLACKLIST,
    PREDELIVERY_CANCELLATION,
    SKIPPED,

    R<PERSON><PERSON>NED
}
