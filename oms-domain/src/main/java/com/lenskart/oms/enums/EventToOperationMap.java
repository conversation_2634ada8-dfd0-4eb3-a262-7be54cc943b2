package com.lenskart.oms.enums;

public enum EventToOperationMap {
    DEFAULT("DEFAULT"),
    CREATED("CREATED"),
    PENDING("PENDING"),
    READY_FOR_WH("READY_FOR_WH"),
    MARK_ITEM_FULFILLABLE("MARK_ITEM_FULFILLABLE"),
    MARK_CREATE_SHIPMENT("MARK_CREATE_SHIPMENT"),
    MARK_SHIPMENT_PICKED("MARK_SHIPMENT_PICKED"),
    MARK_SHIPMENT_QC("MARK_SHIPMENT_QC"),
    CREATE_SHIPMENT_INVOICE("CREATE_SHIPMENT_INVOICE"),
    CREATE_SHIPMENT_AWB("CREATE_SHIPMENT_AWB"),
    MARK_SHIPMENT_MANIFEST("MARK_SHIPMENT_MANIFEST"),
    MARK_SHIPMENT_DISPATCH("MARK_SHIPMENT_DISPATCH"),
    MARK_SHIPMENT_DELIVERED("MARK_SHIPMENT_DELIVERED"),
    CANCELLED("CANCELLED"),
    OMS_REASSIGNED("OMS_REASSIGNED"),
    MARK_ITEM_RETURNED("MARK_ITEM_RETURNED"),
    MARK_OTC_SHIPMENT_DISPATCH("MARK_OTC_SHIPMENT_DISPATCH"),
    CREATE_OTC_SHIPMENT_INVOICE("CREATE_OTC_SHIPMENT_INVOICE"),

    //ADD
    MARK_ITEM_SKIPPED("MARK_ITEM_SKIPPED");

    private final String operation;

    public String getOperation() {
        return this.operation;
    }

    EventToOperationMap(String operation) {
        this.operation = operation;
    }

}
