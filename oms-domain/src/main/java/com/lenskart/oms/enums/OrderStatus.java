package com.lenskart.oms.enums;

import java.util.*;

public enum OrderStatus {
    /** Maintain the order of status as per occurrence in the flow */

    CREATED(Arrays.asList("PROCESSING_PENDING", "PROCESSING_POWER_FOLLOWUP", "PROCESSING_POWER_FOLLOWUP_HTO", "PROCESSING_POWER_FOLLOWUP_VERIFY", "PROCESSING_BLACKLIST")),
    PENDING(Arrays.asList("PROCESSING_PENDING", "PROCESSING_POWER_FOLLOWUP", "PROCESSING_POWER_FOLLOWUP_HTO", "PROCESSING_POWER_FOLLOWUP_VERIFY", "PROCESSING_BLACKLIST")),
    OMS_REASSIGNED(Arrays.asList("OMS_REASSIGNED", "PRODUCT_ID_CRITERIA_FAILED", "SHIP_COUNTRY_CRITERIA_FAILED", "PAYMENT_METHOD_CRITERIA_FAILED", "STORE_FACILITY_CRITERIA_FAILED", "NAV_CHANNEL_CRITERIA_FAILED", "LOCAL_FITTING_CRITERIA_FAILED", "PINCODE_SERVICEABILITY_CRITERIA_FAILED", "INVENTORY_CHECK_CRITERIA_FAILED", "DELAYED_ORDER")),
    READY_FOR_WH(Arrays.asList("READY_FOR_WH", "PROCESSING_POWER_FOLLOWUP", "PROCESSING_POWER_FOLLOWUP_HTO", "PROCESSING_POWER_FOLLOWUP_VERIFY", "PROCESSING_BLACKLIST")),
    PROCESSING(Arrays.asList("PROCESSING", "PROCESSING_POWER_FOLLOWUP", "PROCESSING_POWER_FOLLOWUP_HTO", "PROCESSING_POWER_FOLLOWUP_VERIFY", "PROCESSING_BLACKLIST")),
    COMPLETE(Arrays.asList("COMPLETE")),
    MANIFESTED(Arrays.asList("MANIFESTED")),
    COMPLETE_SHIPPED(Arrays.asList("COMPLETE_SHIPPED")),
    DISPATCHED(Arrays.asList("DISPATCHED")),
    DELIVERED(Arrays.asList("DELIVERED")),
    INVOICED(Arrays.asList("INVOICED")),
    CANCELLED(Arrays.asList("CANCELLED"));

    public List<String> allowedSubStatusList;

    OrderStatus(List<String> allowedSubStatusList) {
        this.allowedSubStatusList = allowedSubStatusList;
    }

    public static Set<String> getNonProcessingStatus() {
        Set<String> nonProcessingStatus = new HashSet<>();
        nonProcessingStatus.add(OrderStatus.CREATED.name());
        nonProcessingStatus.add(OrderStatus.PENDING.name());

        return nonProcessingStatus;
    }

    public static Set<OrderStatus> getNonProcessingStatusEnum() {
        Set<OrderStatus> nonProcessingStatus = new HashSet<>();
        nonProcessingStatus.add(OrderStatus.CREATED);
        nonProcessingStatus.add(OrderStatus.PENDING);

        return nonProcessingStatus;
    }

    public static Set<String> getPreProcessingAndProcessingStatus() {
        Set<String> preProcessingAndProcessingStatus = getNonProcessingStatus();
        preProcessingAndProcessingStatus.add(OrderStatus.READY_FOR_WH.name());
        preProcessingAndProcessingStatus.add(OrderStatus.PROCESSING.name());
        preProcessingAndProcessingStatus.add(OrderStatus.DISPATCHED.name());
        preProcessingAndProcessingStatus.add(OrderStatus.OMS_REASSIGNED.name());

        return preProcessingAndProcessingStatus;
    }
}
