package com.lenskart.oms.enums;

import java.util.Arrays;
import java.util.List;

public enum ItemTypeMapping {

    EYEFRAME(Arrays.asList("eye-frames", "eyeframe")),
    SUNGLASSES(Arrays.asList("sunglasses")),
    ACCESSORIES(Arrays.asList("Accessories", "accessories_revenue")),
    ACCESSORIES_REVENUE(Arrays.asList("accessories_revenue")),
    SHIPPING_ACCESSORIES(Arrays.asList("shipping_accessories")),
    SPECTACLES(Arrays.asList("spectacles")),
    CORRECTIVE_SPECTACLES(Arrays.asList("Corrective spectacles")),
    CLIP(Arrays.asList("Clip")),
    CLIP_ON(Arrays.asList("Eyeframe Flips/ Clip on")),
    CLIP_METAL(Arrays.asList("Metal Clip")),
    GIFT_VOUCHERS(Arrays.asList("Gift Vouchers")),
    CONSUMABLES(Arrays.asList("Consumable")),
    NON_POWER_READING(Arrays.asList("Non-Power Reading", "Non-Power Reading Sunglasses")),
    PHARMA(Arrays.asList("Pharma")),
    SMART_GLASSES(Arrays.asList("smart_glasses", "Smart Glasses")),
    CONTACT_LENS(Arrays.asList("contact-lens")),
    NON_POWERWISE_CONTACT_LENS(Arrays.asList("non_powerwise_contact_lens")),
    CASES(Arrays.asList("cases")),
    CASES_CONTACT_LENS(Arrays.asList("contact-lens-cases")),
    HEARING_AID(Arrays.asList("hearing_aid")),
    HTO_SERVICES(Arrays.asList("hto-services")),
    LENS_CLEANING_SOLUTION(Arrays.asList("lens-cleaning-solution")),
    CONTACT_LENS_SOLUTION(Arrays.asList("contact_lens_solution")),
    LOYALTY_SERVICES(Arrays.asList("loyalty-services", "loyalty_services")),
    NOTEBOOK(Arrays.asList("note-book")),
    PRESCRIPTION_LENS(Arrays.asList("prescription-lens")),
    SELVET(Arrays.asList("selvet")),
    SWIMMING_GOGGLES(Arrays.asList("swimming-goggles")),
    ZEROPOWER(Arrays.asList("zeropower"));

    private List<String> eligibleClassifications;

    ItemTypeMapping(List<String> eligibleClassifications) {
        this.eligibleClassifications = eligibleClassifications;
    }

    public List<String> getEligibleClassifications() {
        return this.eligibleClassifications;
    }

    public static ItemTypeMapping getItemTypeFromName(String value) {
        ItemTypeMapping[] var1 = values();
        int var2 = var1.length;

        for (int var3 = 0; var3 < var2; ++var3) {
            ItemTypeMapping itemType = var1[var3];
            if (itemType.getEligibleClassifications().contains(value)) {
                return itemType;
            }
        }
        return null;
    }
}
