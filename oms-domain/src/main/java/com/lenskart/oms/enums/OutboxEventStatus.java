package com.lenskart.oms.enums;

/**
 * Status of outbox events for tracking event processing lifecycle
 */
public enum OutboxEventStatus {
    /**
     * Event is created and waiting to be processed
     */
    PENDING,
    
    /**
     * Event is currently being processed
     */
    PROCESSING,
    
    /**
     * Event has been successfully processed and published to Kafka
     */
    PROCESSED,
    
    /**
     * Event processing failed but can be retried
     */
    FAILED,
    
    /**
     * Event has exceeded max retry attempts and moved to dead letter
     */
    DEAD_LETTER
}
