package com.lenskart.oms.enums;

public enum OrderSubStatus {
    PROCESSING_PENDING,
    PROCESSING_POWER_FOLLOWUP,
    PROCESSING_POWER_FOLLOWUP_HTO,
    PROCESSING_POWER_FOLLOWUP_VERIFY,
    PROCESSING_B<PERSON>CKLIST,
    PRODUCT_ID_CRITERIA_FAILED,
    SH<PERSON>_COUNTRY_CR<PERSON>ERIA_FAILED,
    PAYMENT_METHOD_CRITERIA_FAILED,
    STORE_FACILITY_CRITERIA_FAILED,
    NAV_<PERSON><PERSON>NE<PERSON>_CRITERIA_FAILED,
    LOCAL_FITTING_CRITERIA_FAILED,
    PINCODE_SERVICEABILITY_CRITERIA_FAILED,
    INVE<PERSON><PERSON>Y_<PERSON><PERSON><PERSON>_CRITERIA_FAILED,
    <PERSON><PERSON>YED_ORDER,
    OMS_REASSIGNED,
    OR<PERSON>R_LIMIT_CRITERIA_FAILED,
    READY_FOR_WH,
    PROCESSING,
    <PERSON><PERSON>LE<PERSON>,
    MANIFESTED,
    COMPLETE_SHIPPED,
    D<PERSON><PERSON>TCHED,
    INVOICED,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>_NOT_CONFIRMED,
    PR<PERSON><PERSON>IVERY_REFUND,
    PROCESSING_NOT_IN_STOCK,
    COMPLETE_LOST_BY_COURIER,
    PREDELIVERY_CANCELLATION,
    UAE_OTC_ORDER_CRITERIA_FAILED,
    CL_ORDER
}
