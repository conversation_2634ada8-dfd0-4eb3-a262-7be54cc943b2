package com.lenskart.oms.enums;

/**
 * Types of outbox events for different business operations
 */
public enum OutboxEventType {
    /**
     * Order creation events
     */
    ORDER_CREATED,
    
    /**
     * Order update events
     */
    ORDER_UPDATED,
    
    /**
     * Post order creation processing events
     */
    POST_ORDER_CREATE,
    
    /**
     * Power update events
     */
    POWER_UPDATE,
    
    /**
     * WMS order events
     */
    WMS_ORDER_EVENT,
    
    /**
     * OTC shipment events
     */
    OTC_SHIPMENT_EVENT,
    
    /**
     * B2B order events
     */
    B2B_ORDER_EVENT,
    
    /**
     * Distributor order events
     */
    DISTRIBUTOR_ORDER_EVENT,
    
    /**
     * Order back sync events
     */
    ORDER_BACK_SYNC,
    
    /**
     * Generic order events
     */
    ORDER_EVENT,
    
    /**
     * Shipment events
     */
    SHIPMENT_EVENT
}
