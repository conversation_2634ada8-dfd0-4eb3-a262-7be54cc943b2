package com.lenskart.oms.enums;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public enum OrderItemStatus {
    /** Maintain the order of status as per occurrence in the flow */
    DEFAULT(Arrays.asList("PROCESSING_PENDING")),
    PENDING(Arrays.asList("PROCESSING_PENDING")),
    CREATED((Arrays.asList("PROCESSING_PENDING"))),
    OMS_REASSIGNED(Arrays.asList("OMS_REASSIGNED")),
    FULLFILLABLE(Arrays.asList("FULLFILLABLE")),
    PROCESSING(Arrays.asList("PROCESSING")),
    PICKED(Arrays.asList("PICKED")),
    QC_DONE(Arrays.asList("QC_DONE")),
    INVOICED(Arrays.asList("INVOICED")),
    PACKED(Arrays.asList("PACKED")),
    READY_TO_SHIP(Arrays.asList("READY_TO_SHIP")),
    DISPATCHED(Arrays.asList("DISPATCHED")),
    DELIVERED(Arrays.asList("DELIVERED")),
    CANCELLED(Arrays.asList("CANCELLED")),
    SKIPPED(Arrays.asList("SKIPPED")),
    RETURNED(Collections.singletonList("RETURNED"));

    public List<String> allowedSubStatusList;

    OrderItemStatus(List<String> allowedSubStatusList) {
        this.allowedSubStatusList = allowedSubStatusList;
    }
}
