package com.lenskart.oms.enums;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public enum ShipmentStatus {
    /** Maintain the order of status as per occurrence in the flow */
    CREATED(Arrays.asList("PROCESSING_PENDING")),
    PENDING(Arrays.asList("PROCESSING_PENDING")),
    OMS_REASSIGNED(Arrays.asList("OMS_REASSIGNED")),
    READY_FOR_WH(Arrays.asList("READY_FOR_WH")),
    PROCESSING(Arrays.asList("PROCESSING")),
    INVOICED(Collections.singletonList("INVOICED")),
    COMPLETE(Arrays.asList("COMPLETE")),
    MANIFESTED(Arrays.asList("MANIFESTED")),
    COMPLETE_SHIPPED(Arrays.asList("COMPLETE_SHIPPED")),
    DISPATCHED(Arrays.asList("DISPATCHED")),
    DELIVERED(Arrays.asList("DELIVERED")),
    CANCELLED(Arrays.asList("CANCELLED"));

    public List<String> allowedSubStatusList;

    ShipmentStatus(List<String> allowedSubStatusList) {
        this.allowedSubStatusList = allowedSubStatusList;
    }

    public static List<ShipmentStatus> getNonProcessingStatus() {
        List<ShipmentStatus> nonProcessingStatus = new ArrayList<>();
        nonProcessingStatus.add(ShipmentStatus.PENDING);
        nonProcessingStatus.add(ShipmentStatus.CREATED);

        return nonProcessingStatus;
    }
}
