package com.lenskart.oms.enums;

public enum ShipmentEvent {
    MARK_ITEM_FULFILLABLE,
    MARK_CREATE_SHIPMENT,
    MARK_SHIPMENT_FITTED,
    MARK_SHIPMENT_PICKED,
    MARK_SHIPMENT_QC,
    CREATE_SHIPMENT_INVOICE,
    CREATE_SHIPMENT_AWB,
    MARK_SHIPMENT_MANIFEST,
    MARK_SHIPMENT_DISPATCH,
    CREATE_ORDER,
    UPDATE_ADDRESS,
    UPDATE_PAYMENT,
    UPDATE_PAYMENT_CAPTURE,
    UPDATE_POWER,
    HOLD,
    <PERSON>HOLD,
    <PERSON>R<PERSON>_ITEM_SKIPPED,
    MARK_ITEM_RETURNED
}
