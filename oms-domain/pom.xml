<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <groupId>com.lenskart</groupId>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>oms-domain</artifactId>
    <version>1.1.8</version>

    <properties>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <java.version>1.8</java.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.24</version>
        </dependency>
        <dependency>
            <groupId>com.lenskart.nexs</groupId>
            <artifactId>nexs-microservice-commons-client</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-validator</artifactId>
            <version>6.2.3.Final</version>
        </dependency>
        <dependency>
            <groupId>com.lenskart.nexs</groupId>
            <artifactId>nexs-cid-domain</artifactId>
            <version>1.2.21</version>
        </dependency>
        <dependency>
            <groupId>com.lenskart</groupId>
            <artifactId>inventory-adapter-model</artifactId>
            <version>7.2.1</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>2.18.3</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

    <repositories>
        <repository>
            <id>nexs-releases</id>
            <url>http://archiva-new.prod.internal:8080/repository/internal/nexs-releases</url>
        </repository>
        <repository>
            <id>internal</id>
            <url>http://archiva-new.prod.internal:8080/repository/internal</url>
        </repository>
    </repositories>

    <distributionManagement>
        <repository>
            <id>nexs-releases</id>
            <url>http://archiva-new.prod.internal:8080/repository/internal</url>
        </repository>
    </distributionManagement>

</project>